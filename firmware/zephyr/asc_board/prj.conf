CONFIG_CAN_AUTO_BUS_OFF_RECOVERY=n

CONFIG_GPIO=y

# Enable networking
CONFIG_NETWORKING=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_TCP=n
CONFIG_NET_UDP=y
CONFIG_NET_SOCKETS=y
CONFIG_NET_SOCKETS_POSIX_NAMES=y
CONFIG_NET_LOG=y
CONFIG_NET_PKT_RX_COUNT=64
CONFIG_NET_BUF_RX_COUNT=256

# Network settings
CONFIG_NET_CONFIG_SETTINGS=y
CONFIG_NET_CONFIG_NEED_IPV4=y
CONFIG_NET_CONFIG_MY_IPV4_ADDR="**********"
CONFIG_NET_CONFIG_PEER_IPV4_ADDR="*********"
CONFIG_NET_CONFIG_MY_IPV4_NETMASK="*************"
# Don't wait for link up to start running
CONFIG_NET_CONFIG_INIT_TIMEOUT=-1
CONFIG_NET_CONNECTION_MANAGER=y
CONFIG_NET_BUF_USER_DATA_SIZE=64

# Port
CONFIG_APP_UDP_PORT=4243

# Syslog
CONFIG_LOG=y
CONFIG_LOG_BACKEND_NET=y
CONFIG_LOG_BACKEND_NET_SERVER="*********:2442"
CONFIG_LOG_BACKEND_NET_AUTOSTART=n

#CONFIG_SYS_CLOCK_HW_CYCLES_PER_SEC=100000000

# Support reboot
CONFIG_REBOOT=y

#UDP
CONFIG_LIB_UDP=y
CONFIG_PB_REQUESTS_BUFFER_SIZE=10
# Nanopb
CONFIG_NANOPB=y
# PTP
CONFIG_LIB_PTP=y

# MCUBoot
CONFIG_BOOTLOADER_MCUBOOT=n

# Enable mcumgr.
CONFIG_MCUMGR=y
CONFIG_MCUMGR_SMP_NOTIFY_CALLBACK=y
CONFIG_LIB_SMP=y
CONFIG_FLASH=y
#CONFIG_MCUBOOT_BOOT_MAX_ALIGN=32
#CONFIG_MCUMGR_BUF_COUNT=4

# Some command handlers require a large stack.
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048

# Enable statistics and statistic names.
CONFIG_STATS=y
CONFIG_STATS_NAMES=y

# Enable most core commands.
#CONFIG_MCUMGR_CMD_IMG_MGMT=y
#CONFIG_MCUMGR_CMD_OS_MGMT=y
#CONFIG_MCUMGR_CMD_STAT_MGMT=y
#CONFIG_MCUMGR_SMP_UDP=y
#CONFIG_MCUMGR_SMP_UDP_IPV4=y

# Required by the `taskstat` command.
CONFIG_THREAD_MONITOR=y
CONFIG_NEWLIB_LIBC=y

CONFIG_LOG_TIMESTAMP_64BIT=y

# Watchdog
CONFIG_LIB_WATCHDOG=y
CONFIG_WATCHDOG=y
CONFIG_WDT_DISABLE_AT_BOOT=y

CONFIG_LOG_BACKEND_UART=n
CONFIG_LOG_BACKEND_UART_ASYNC=n
CONFIG_UART_CONSOLE=n

# SEGGER RTT
CONFIG_SEGGER_RTT_SECTION_DTCM=y
CONFIG_USE_SEGGER_RTT=y
CONFIG_LOG_BACKEND_RTT_BUFFER_SIZE=1024

# Zero latency callbacks
CONFIG_ZLCB=y
CONFIG_ZLCB_STM32=y

CONFIG_CAN=y
CONFIG_CAN_LOG_LEVEL_DBG=n
CONFIG_CAN_FD_MODE=n
CONFIG_CAN_STM32H7=y


# Combined PWMs
#CONFIG_COMBO_PWM=y
#CONFIG_COMBO_PWM_STM32=y
CONFIG_PWM=y
CONFIG_SPI=y
CONFIG_SPI_STM32_USE_HW_SS=y
#CONFIG_SPI_LOG_LEVEL_DBG=y

# EEPROM emulation
#
# The initialization priority is set to the lowest possible (e.g. highest numerical value) such that
# the flash controller the EEPROM emulation depends on will already have been initialized.
# Otherwise we are beholden to the behavior of the linker on how it will order the initialization
# functions for the EEPROM emulation and internal flash which may result in the flash controller not
# being initialized when the EEPROM emulation is.
CONFIG_EEPROM=y
CONFIG_EEPROM_EMULATOR=y
# CONFIG_EEPROM_EMULATOR_INIT_PRIORITY=99
CONFIG_EEPROM_INIT_PRIORITY=99


CONFIG_LOG_PROCESS_THREAD_SLEEP_MS=500
CONFIG_LOG_BUFFER_SIZE=4096

# embiggen the buffer used for RTT logs to host
CONFIG_USE_SEGGER_RTT=y
CONFIG_SEGGER_RTT_SECTION_DTCM=y
CONFIG_RTT_CONSOLE=n
CONFIG_SEGGER_RTT_BUFFER_SIZE_UP=4096
CONFIG_SEGGER_RTT_MAX_NUM_UP_BUFFERS=1
CONFIG_SEGGER_RTT_BUFFER_SIZE_DOWN=16
CONFIG_SEGGER_RTT_MAX_NUM_DOWN_BUFFERS=1

CONFIG_STACK_CANARIES=y
CONFIG_MPU_STACK_GUARD=y

CONFIG_THREAD_STACK_INFO=y
CONFIG_INIT_STACKS=y

CONFIG_THREAD_NAME=y
CONFIG_SCHED_CPU_MASK=y
CONFIG_THREAD_ANALYZER=y

CONFIG_NEWLIB_LIBC=y
CONFIG_NEWLIB_LIBC_NANO=y
