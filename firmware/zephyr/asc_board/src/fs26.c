#include "fs26.h"
#include "threads.h"
#include <logging/log.h>
#include <zephyr/drivers/spi.h>
#include <zephyr/kernel.h>
#include <zephyr/sys/crc.h>
LOG_MODULE_REGISTER(fs26, CONFIG_APP_LOG_LEVEL);
struct device *spi_dev;

// watchdog thread
K_THREAD_STACK_DEFINE(pet_watchdog_thread_stack, 1024);
struct k_thread pet_watchdog_thread_data;

static const struct gpio_dt_spec spi3_cs = GPIO_DT_SPEC_GET(DT_PATH(gpios, spi3_cs), gpios);

uint8_t tx_buffer[4];
uint8_t rx_buffer[4];
const struct spi_buf tx_buf = {
    .buf = tx_buffer,
    .len = sizeof(tx_buffer),
};
const struct spi_buf rx_buf = {
    .buf = rx_buffer,
    .len = sizeof(rx_buffer),
};
const struct spi_buf_set tx_set = {
    .buffers = &tx_buf,
    .count = 1,
};
const struct spi_buf_set rx_set = {
    .buffers = &rx_buf,
    .count = 1,
};

uint16_t fs26_watchdog_token = 0;
uint32_t spi_tx = 0;
uint8_t debug_reg = 0x01;
uint32_t debug_tx = 0x00;
uint32_t debug_rx = 0x00;
uint32_t debug_data = 0x00;
uint8_t debug_mode = 0;
uint16_t debug_safety1 = 0;
uint8_t debug_pet_wd = 0;
uint16_t debug_wd_i_cfg = 0;
uint16_t debug_i_fssm = 0;
uint16_t debug_states = 0;
struct spi_cs_control cs_ctrl = (struct spi_cs_control){
    .gpio = GPIO_DT_SPEC_GET(DT_NODELABEL(spi3), cs_gpios),
    .delay = 0u,
};

// define spi_cfg
static const struct spi_config spi_cfg = {
    .frequency = 5000000,
    //.operation = SPI_OP_MODE_MASTER | SPI_TRANSFER_MSB | SPI_WORD_SET(8) | SPI_LINES_SINGLE,
    .operation = SPI_OP_MODE_MASTER | SPI_TRANSFER_MSB | SPI_WORD_SET(8) | SPI_MODE_CPHA,
    //.operation = SPI_WORD_SET(8) | SPI_TRANSFER_MSB | SPI_MODE_CPOL | SPI_MODE_CPHA,
    .slave = 0,
    .cs = &cs_ctrl,
};

#define FS26_CRC_POLYNOMIAL 0x1D
#define FS26_CRC_SEED 0xFF
/**
 * @brief Calculate CRC-8 for FS26 SPI communication.
 *
 * @param data Pointer to the data buffer.
 * @param length Length of the data buffer.
 * @return Calculated CRC-8 value.
 */
static uint8_t fs26_calculate_crc(const uint8_t *data, size_t length) {
  // CRC-8 polynomial (x^8 + x^4 + x^3 + x^2 + 1)
  uint8_t crc = FS26_CRC_SEED; // Initialize CRC with the seed value.

  for (size_t i = 0; i < length; i++) {
    crc ^= data[i]; // XOR input byte with the CRC register.
    for (uint8_t bit = 0; bit < 8; bit++) {
      if (crc & 0x80) {
        crc = (crc << 1) ^ FS26_CRC_POLYNOMIAL; // Shift and apply polynomial if MSB is 1.
      } else {
        crc <<= 1; // Shift left if MSB is 0.
      }
    }
  }
  return crc;
}
/* CRC-8 polynomial x^8+x^4+x^3+x^2+1 (0x1D) */
#define FS26_CRC8_POLYNOMIAL 0x1D
#define FS26_CRC8_SEED 0xFF

static uint8_t calculate_crc8(const uint8_t *data, size_t len) {
  uint8_t crc = FS26_CRC8_SEED;

  for (size_t i = 0; i < len; i++) {
    crc ^= data[i];
    for (int j = 0; j < 8; j++) {
      if (crc & 0x80) {
        crc = (crc << 1) ^ FS26_CRC8_POLYNOMIAL;
      } else {
        crc <<= 1;
      }
    }
  }
  return crc;
}

static uint32_t fs26_watchdog_calc(uint32_t fs_wd_token) {
  // Calculation on pg. 47 of FS26 Safety Manual
  fs_wd_token *= 4;
  fs_wd_token += 6;
  fs_wd_token -= 4;
  fs_wd_token = ~fs_wd_token;
  fs_wd_token /= 4;
  return fs_wd_token;
}

static uint8_t brute_force_crc8(const uint8_t *data, size_t len) {
  static uint8_t crc = 0;
  return crc++;
}

static int write_register(const struct device *spi_dev, uint8_t reg_address, uint16_t data) {
  // Construct the MOSI message
  tx_buffer[0] = (reg_address << 1) | 1;           // Bits 31-24: M/FS, Register Address, and R/W=1
  tx_buffer[1] = (data >> 8) & 0xFF;               // Bits 23-16: Data MSB
  tx_buffer[2] = data & 0xFF;                      // Bits 15-8: Data LSB
  tx_buffer[3] = fs26_calculate_crc(tx_buffer, 3); // Bits 7-0: CRC

  // Perform the SPI transaction
  int ret = spi_transceive(spi_dev, &spi_cfg, &tx_set, &rx_set);
  if (ret != 0) {
    return ret; // Return error if SPI transaction fails
  }

  // Process MISO response (if needed for debugging or error handling)
  uint8_t received_crc = fs26_calculate_crc(rx_buffer, 4);
  if (received_crc != rx_buffer[3]) {
    // return -EIO; // CRC mismatch error
  }

  // log rx buffer
  // LOG_INF("RX: 0x%02X 0x%02X 0x%02X 0x%02X", rx_buffer[0], rx_buffer[1], rx_buffer[2], rx_buffer[3]);

  return 0; // Success
}

static int read_register(const struct device *spi_dev, uint8_t reg_address, uint16_t *data_out) {
  FS26_SPI_MOSI_Message msg;
  // Construct the message
  msg.reg_address = reg_address << 1;
  msg.rw = 0;   // Read operation
  msg.data = 0; // No data for read operation
  uint8_t message[] = {
      (reg_address << 1) & (0b11111110), // Register address and R/W=0
      0, 0,
      0 // Placeholder for CRC
  };

  // Calculate CRC
  message[3] = fs26_calculate_crc(message, 3);
  // message[3] = calculate_crc8(message, 4);
  // message[3] = brute_force_crc8(message, 4);

  // Populate the SPI transmit buffer
  for (int i = 0; i < 4; i++) {
    tx_buffer[i] = message[i];
  }

  // Perform SPI transfer
  int ret = spi_transceive(spi_dev, &spi_cfg, &tx_set, &rx_set);
  if (ret != 0) {
    return ret; // Return error
  }

  // Decode response
  FS26_SPI_MISO_Message response;
  response.general_status = rx_buffer[0];
  response.extended_status = (rx_buffer[1] << 8) | rx_buffer[2];
  response.crc = rx_buffer[3];

  // Verify CRC
  // uint8_t crc_check = fs26_calculate_crc(rx_buffer, 3);
  // if (crc_check != response.crc) {
  // return -EIO; // CRC mismatch error
  //}

  // Return the read data
  *data_out = response.extended_status;

  // Log raw data for debugging
  // LOG_INF("Read register 0x%02X: 0x%04X", reg_address, *data_out);
  // Log whole tx and rx buffers for debugging
  // LOG_INF("TX: 0x%02X 0x%02X 0x%02X 0x%02X", tx_buffer[0], tx_buffer[1], tx_buffer[2], tx_buffer[3]);
  // LOG_INF("RX: 0x%02X 0x%02X 0x%02X 0x%02X", rx_buffer[0], rx_buffer[1], rx_buffer[2], rx_buffer[3]);

  uint8_t crc_check = fs26_calculate_crc(rx_buffer, 4);
  if (crc_check != response.crc) {
    // return -EIO; // CRC mismatch error
  }

  return 0; // Success
}

uint16_t brute_force_token = 0x0000;

uint16_t brute_force_wd_token() {
  brute_force_token++;
  return brute_force_token;
}

uint16_t wd_token = 0x5AB2;

void fs26_pet_watchdog() {
  /**
  int ret = read_register(spi_dev, FS26_REG_WD_TOKEN, &wd_token);
  if (ret != 0) {
      LOG_ERR("Failed to read watchdog token");
      return;
  }
  LOG_INF("Read watchdog token: 0x%04X", wd_token);
  */

  read_wd_token();
  uint16_t fs_wd_token = fs26_watchdog_calc(wd_token);
  // int ret = write_register(spi_dev, FS26_REG_WD_ANSWER, fs_wd_token);
  // int ret = write_register(spi_dev, FS26_REG_WD_ANSWER, wd_token);
  // int ret = write_register(spi_dev, FS26_REG_WD_ANSWER, brute_force_wd_token());
  int ret = write_register(spi_dev, FS26_REG_WD_ANSWER, 0xFFFF - wd_token);
  if (ret != 0) {
    LOG_ERR("Failed to write watchdog answer");
    return;
  }
  // log tx buffer
  // LOG_INF("TX: 0x%02X 0x%02X 0x%02X 0x%02X", tx_buffer[0], tx_buffer[1], tx_buffer[2], tx_buffer[3]);
}

void read_states() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS26_STATES, &data);
  if (ret != 0) {
    LOG_ERR("Failed to write exit debug mode");
    return;
  }
  debug_states = data;
}

void exit_debug_mode() {
  uint16_t data = 0b0100000000000000;
  int ret = write_register(spi_dev, FS26_STATES, data);
  if (ret != 0) {
    LOG_ERR("Failed to write exit debug mode");
    return;
  }
}

void read_safety1() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS26_DIAG_SAFETY1, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read safety1");
    return;
  }
  debug_safety1 = data;
}

void write_wd_i_cfg() {
  // default is 0b0100001000000000
  uint16_t data = 0b0000000000000000;
  int ret = write_register(spi_dev, FS26_WD_I_CFG, data);
  if (ret != 0) {
    LOG_ERR("Failed to write wd_i_cfg");
    return;
  }
  data = ~data;
  ret = write_register(spi_dev, FS26_NOT_WD_I_CFG, data);
  if (ret != 0) {
    LOG_ERR("Failed to write not_wd_i_cfg");
    return;
  }
}

void read_wd_i_cfg() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS26_WD_I_CFG, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read wd_i_cfg");
    return;
  }
  debug_wd_i_cfg = data;
}

void write_i_ovuv_safe_reaction1() {
  uint16_t data = 0b1001100110011001;
  // uint16_t data = 0b0000000000000000;
  int ret = write_register(spi_dev, FS_I_OVUV_SAFE_REACTION1, data);
  if (ret != 0) {
    LOG_ERR("Failed to write i_ovuv_safe_reaction1");
    return;
  }
  data = ~data;
  ret = write_register(spi_dev, FS_NOT_I_OVUV_SAFE_REACTION1, data);
  if (ret != 0) {
    LOG_ERR("Failed to write not_i_ovuv_safe_reaction1");
    return;
  }
}

void write_i_ovuv_safe_reaction2() {
  // uint16_t data = 0b1001100110011001;
  uint16_t data = 0b0000000000000000;
  int ret = write_register(spi_dev, FS_I_OVUV_SAFE_REACTION2, data);
  if (ret != 0) {
    LOG_ERR("Failed to write i_ovuv_safe_reaction2");
    return;
  }
  data = ~data;
  ret = write_register(spi_dev, FS_NOT_I_OVUV_SAFE_REACTION2, data);
  if (ret != 0) {
    LOG_ERR("Failed to write not_i_ovuv_safe_reaction2");
    return;
  }
}

void write_i_safet_inputs() {
  // no FCCU monitoring
  uint16_t data = 0b0000001110001101;
  int ret = write_register(spi_dev, FS_I_SAFET_INPUTS, data);
  if (ret != 0) {
    LOG_ERR("Failed to write i_safet_inputs");
    return;
  }
  data = ~data;
  ret = write_register(spi_dev, FS_NOT_I_SAFET_INPUTS, data);
  if (ret != 0) {
    LOG_ERR("Failed to write not_i_safet_inputs");
    return;
  }
}

void write_wd_duration() {
  // 256 ms period
  // 50% window
  // infinite recovery
  // uint16_t data = 0b1101000010000000;
  // 3 ms open window
  uint16_t data = 0b0011000010000000;
  int ret = write_register(spi_dev, FS_WDW_DURATION, data);
  if (ret != 0) {
    LOG_ERR("Failed to write wd_duration");
    return;
  }
  // write NOT_WDW_DURATION
  data = ~data;
  ret = write_register(spi_dev, FS_NOT_WDW_DURATION, data);
  if (ret != 0) {
    LOG_ERR("Failed to write not_wd_duration");
    return;
  }
}

void read_i_fssm() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS_I_FSSM, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read i_fssm");
    return;
  }
  debug_i_fssm = data;
}

void write_i_fssm() {
  uint16_t data = 0b110000000010000;
  int ret = write_register(spi_dev, FS_I_FSSM, data);
  if (ret != 0) {
    LOG_ERR("Failed to write i_fssm");
    return;
  }
}

void write_wio_cfg() {
  uint16_t data = 0b11; // wake on wake1 or wake2
  int ret = write_register(spi_dev, M_WIO_CFG, data);
  if (ret != 0) {
    LOG_INF("Failed to write_wio_cfg");
  }
}

uint8_t debug_standby = 0;
void write_standby() {
  int ret = write_register(spi_dev, FS_LP_REQ, debug_standby);
  if (ret != 0) {
    LOG_ERR("Failed to write standby");
    return;
  }
  LOG_INF("Set standby");
}

void clear_all_flags() {
  // uint16_t data = 0b000000000000000;
  uint16_t data = 0b11111111111111111;
  int ret = write_register(spi_dev, FS26_DIAG_SAFETY1, data);
  if (ret != 0) {
    LOG_ERR("Failed to clear all flags");
    return;
  }
  ret = write_register(spi_dev, FS26_DIAG_SAFETY2, data);
  if (ret != 0) {
    LOG_ERR("Failed to clear all flags");
    return;
  }
  ret = write_register(spi_dev, FS_OVUVREG_STATUS, data);
  if (ret != 0) {
    LOG_ERR("Failed to clear all flags");
    return;
  }
}

void read_wd_token() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS26_REG_WD_TOKEN, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read watchdog token");
    return;
  }
  wd_token = data;
}

void write_init_state() {
  // request init state
  uint16_t data = 0b0000000000000001;
  int ret = write_register(spi_dev, FS_SAFE_IOS_1, data);
  if (ret != 0) {
    LOG_ERR("Failed to clear all flags");
    return;
  }
}

uint16_t debug_m_sys_cfg = 0;

void read_m_sys_cfg() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, M_SYS_CFG, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read m_sys_cfg");
    return;
  }
  debug_m_sys_cfg = data;
}

uint16_t debug_grl_flags = 0;
void read_grl_flags() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS_GRL_FLAGS, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read grl_flags");
    return;
  }
  debug_grl_flags = data;
}

uint16_t debug_ovuv_status = 0;
void read_ovuv_status() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, FS_OVUVREG_STATUS, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read ovuv_status");
    return;
  }
  debug_ovuv_status = data;
}

uint16_t wd_good = 0;
void read_everything() {
  read_safety1();
  read_wd_i_cfg();
  read_states();
  read_m_sys_cfg();
  read_grl_flags();
  read_ovuv_status();
  wd_good = (debug_wd_i_cfg >> 4) & 0b111;
}

uint16_t debug_m_status = 0;
uint8_t read_wake1() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, M_STATUS, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read wake1");
    return 0;
  }
  debug_m_status = data;
  return (data & (0b1 << 10)) >> 10;
}
uint8_t read_wake2() {
  uint16_t data = 0;
  int ret = read_register(spi_dev, M_STATUS, &data);
  if (ret != 0) {
    LOG_ERR("Failed to read wake2");
    return 0;
  }
  debug_m_status = data;
  return (data & (0b1 << 11)) >> 11;
}

uint8_t debug_init = 0;

bool previous_wake_state = false;
bool current_wake_state;
int64_t wake_edge_timestamp = 0;

void pet_watchdog_thread() {
  static uint32_t wake_low_cnt = 0;
  static uint32_t mismatch_log_counter = 0; // Counter for timing the log messages
  LOG_INF("Starting pet_watchdog_thread");
  clear_all_flags();
  write_init_state();
  read_everything();
  write_i_ovuv_safe_reaction1();
  read_everything();
  write_i_ovuv_safe_reaction2();
  read_everything();
  write_i_safet_inputs();
  read_everything();
  write_i_fssm();
  read_everything();
  write_wd_i_cfg();
  read_everything();
  write_wd_duration();
  read_everything();
  write_wio_cfg();
  clear_all_flags();
  read_wd_token();

  while (1) {
    if (debug_init == 1) {
      clear_all_flags();
      write_init_state();
      read_everything();
      write_i_ovuv_safe_reaction1();
      read_everything();
      write_i_ovuv_safe_reaction2();
      read_everything();
      write_i_safet_inputs();
      read_everything();
      write_i_fssm();
      read_everything();
      write_wd_i_cfg();
      read_everything();
      write_wd_duration();
      read_everything();
      clear_all_flags();
      read_wd_token();
      debug_init = 0;
    }
    k_msleep(2);
    if (debug_mode == 1) {
      exit_debug_mode();
    }
    if (debug_standby) {
      write_standby();
    }
    // test_read_reg();
    if (debug_pet_wd == 1) {
      fs26_pet_watchdog();
    }
    write_wio_cfg();
    fs26_pet_watchdog();
    // read_everything();

    current_wake_state = !!read_wake1();

    if (previous_wake_state != current_wake_state) {
      wake_edge_timestamp = k_uptime_get();
    }
    previous_wake_state = current_wake_state;

    if (!current_wake_state) {
      wake_low_cnt++;
    }

    if (wake_low_cnt > (10)) {
      debug_standby = 0xAA;
      write_standby();
      wake_low_cnt = 0;
      debug_standby = 0x55;
      write_standby();
    }
    if (wd_good != 0) {
      // LOG_INF("Watchdog good TOKEN: 0x%04X, ANSWER: 0x%04X", wd_token, brute_force_token);
    }
  }
}

void fs26_init() {
  // Initialize spi3
  spi_dev = device_get_binding("SPI_3");
  if (spi_dev == NULL) {
    LOG_ERR("Could not get SPI device");
    return;
  }
  gpio_pin_configure_dt(&spi3_cs, GPIO_OUTPUT);
  gpio_pin_set(spi3_cs.port, spi3_cs.pin, 0);

  // Start watchdog petting thread
  k_tid_t pet_watchdog_thread_id = k_thread_create(
      &pet_watchdog_thread_data, pet_watchdog_thread_stack, K_THREAD_STACK_SIZEOF(pet_watchdog_thread_stack),
      pet_watchdog_thread, NULL, NULL, NULL, K_PRIO_COOP(WATCHDOG_THREAD_PRIORITY), 0, K_NO_WAIT);

  return;
}