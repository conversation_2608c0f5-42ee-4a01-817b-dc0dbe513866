#include <logging/log.h>
LOG_MODULE_REGISTER(udp_server, CONFIG_APP_LOG_LEVEL);

#include <errno.h>
#include <lib/udp/udp.h>
#include <pb_decode.h>
#include <pb_encode.h>
#include <sys/reboot.h>
#include <utils/handle_errors.h>
#include <zephyr.h>

#include "asc.h"
#include "config.h"
#include "generated/lib/drivers/nanopb/proto/cruise.pb.h"
#include "generated/lib/drivers/nanopb/proto/jimbox_board.pb.h"
#include "tractor_can.h"
#include "tractor_state.h"
#include "udp_server.h"

#if IS_ENABLED(CONFIG_NET_TC_THREAD_COOPERATIVE)
#define THREAD_PRIORITY K_PRIO_COOP(CONFIG_NUM_COOP_PRIORITIES - 1)
#else
#define THREAD_PRIORITY K_PRIO_PREEMPT(8)
#endif

#define RECV_BUFFER_SIZE 2048
#define UDP_STACK_SIZE 8192

static void process_udp();

K_THREAD_DEFINE(udp_thread_id, RECV_BUFFER_SIZE, process_udp, NULL, NULL, NULL, 8, 0, K_TICKS_FOREVER);
UDP_SERVER_DEFINE(npb_udp_server, CONFIG_PB_REQUESTS_BUFFER_SIZE, UDP_STACK_SIZE, 8);

static void handle_ping(diagnostic_Ping *req, diagnostic_Pong *resp) { resp->x = req->x; }

void handle_throttle(cruise_Throttle_Request *req, cruise_Throttle_Reply *resp) {
  if (enabled) {
    int32_t change = req->change;

    if (tractor_variant == JD_6PRO || tractor_variant == JD_8PRO) {
      change = change > 1 ? 1 : (change < -1 ? -1 : change);
    }

    if (tractor_state.gear != GEAR_COMMAND_FORWARD) {
      change = 0;
    }
    if ((tractor_state.f1_setpoint >= 300) && (change > 0)) {
      change = 0;
    }
    if (throttle_ticks == 0) {
      if (throttle_ticks) {
        LOG_INF("Throttle request: change=%d", req->change);
      }
      throttle_ticks = change;
    }
  }

  // sleep 500 ms to rate limit
  // k_sleep(K_MSEC(500));
  resp->success = true;
}

void handle_enable(cruise_Enable_Request *req, cruise_Enable_Reply *resp) {
  // LOG_INF("Enable request: enabled=%d", req->enabled);
  enabled = req->enabled;
  resp->enabled = enabled;
}

void handle_status(cruise_Status_Request *req, cruise_Status_Reply *resp) {
  // LOG_INF("Status request received");
  resp->enabled = enabled;
  if (tractor_state.gear != GEAR_COMMAND_FORWARD) {
    resp->speed_ticks = 0;
  } else {
    resp->speed_ticks = tractor_state.f1_setpoint;
  }

  // CommandPro gear control isn't stateful, so it must be read from the tractor
  if (tractor_variant == JD_6PRO || tractor_variant == JD_8PRO) {
    resp->speed_lever_position = tractor_state.detent_active * 0xFA;
    resp->speed_lever_s1 = tractor_state.detent_active;
  } else {
    resp->speed_lever_position = cab_state.speed_lever;
    resp->speed_lever_s1 = (cab_state.speed_lever_s1 >> 4) == 0x9;
  }
}

static void handle_variant(cruise_Variant_Request *req, cruise_Variant_Reply *resp) {
  resp->variant = tractor_variant;

  LOG_INF("Variant request: %d", req->variant);
  int ret = config_write_tractor_variant(req->variant);
  if (ret != 0) {
    LOG_ERR("Failed to write variant to EEPROM: %d", ret);
  }
}

void handle_cruise(cruise_Request *req, cruise_Reply *resp) {
  switch (req->which_request) {
  case cruise_Request_throttle_tag:
    resp->which_reply = cruise_Reply_throttle_tag;
    handle_throttle(&req->request.throttle, &resp->reply.throttle);
    break;
  case cruise_Request_enable_tag:
    resp->which_reply = cruise_Reply_enable_tag;
    handle_enable(&req->request.enable, &resp->reply.enable);
    break;
  case cruise_Request_status_tag:
    resp->which_reply = cruise_Reply_status_tag;
    handle_status(&req->request.status, &resp->reply.status);
    break;
  case cruise_Request_variant_tag:
    resp->which_reply = cruise_Reply_variant_tag;
    handle_variant(&req->request.variant, &resp->reply.variant);
    break;
  }
}

static void serve_request(uint8_t *data, uint16_t received, udp_msg_metadata *metadata) {
  jimbox_board_Request req = jimbox_board_Request_init_zero;
  pb_istream_t istream = pb_istream_from_buffer(data, received);
  if (!pb_decode(&istream, jimbox_board_Request_fields, &req)) {
    LOG_WRN("Failed to decode UDP packet with size %d", received);
    return;
  }

  jimbox_board_Reply resp = jimbox_board_Reply_init_zero;
  resp.has_header = true;
  resp.header.requestId = req.header.requestId;

  switch (req.which_request) {
  case jimbox_board_Request_ping_tag:
    resp.which_reply = jimbox_board_Reply_pong_tag;
    handle_ping(&req.request.ping, &resp.reply.pong);
    break;
  case jimbox_board_Request_reset_tag:
    // sys_reboot(SYS_REBOOT_COLD);
    break;
  case jimbox_board_Request_cruise_tag:
    resp.which_reply = jimbox_board_Reply_cruise_tag;
    handle_cruise(&req.request.cruise, &resp.reply.cruise);
    break;
  }

  pb_ostream_t ostream = pb_ostream_from_buffer(data, udp_buffer_size(&npb_udp_server));
  if (!pb_encode(&ostream, jimbox_board_Reply_fields, &resp)) {
    LOG_WRN("Failed to encode nanoPB response");
    return;
  }
  udp_tx(&npb_udp_server, data, ostream.bytes_written, metadata);
}

static void process_udp() {
  for (;;) {
    uint8_t *data;
    udp_msg_metadata metadata;
    uint16_t received = udp_get_data_no_copy(&npb_udp_server, &data, &metadata, 0);
    serve_request(data, received, &metadata);
    udp_get_data_release(&npb_udp_server, data);
  }
}

void start_udp_server() {
  UDP_SERVER_START(npb_udp_server, CONFIG_APP_UDP_PORT);
  k_thread_name_set(udp_thread_id, "udp");
  k_thread_start(udp_thread_id);
}