#pragma once

#include "threads.h"
#include "tractor_can_messages.h"
#include <device.h>
#include <devicetree.h>
#include <drivers/can.h>
#include <drivers/gpio.h>
#include <lib/ptp/ptp.h>
#include <string.h>
#include <utils/handle_errors.h>
#include <zephyr/kernel.h>

struct can_frame_timestamped {
  struct zcan_frame msg;
  struct net_ptp_time time; // So that CAN log timestamps can be interleaved w/ syslog_server timestamps.
};

typedef void (*can_handler_t)(struct zcan_frame *msg);

void add_tractor_read_handler(can_handler_t handler);
void add_cab_mitm_handler(can_handler_t handler);
void tractor_can_init(void);
bool is_can_bus_active(void);
void can_loopback_test(void);
void tractor_can_reinit(void);
