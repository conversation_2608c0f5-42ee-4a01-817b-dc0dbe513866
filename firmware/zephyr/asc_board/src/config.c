#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(config, CONFIG_APP_LOG_LEVEL);

#include "asc.h"
#include "config.h"
#include "eeprom.h"

// EEPROM block types for different config data
#define CONFIG_BLOCK_WHEEL_SENSOR 0x01
#define CONFIG_BLOCK_TRACTOR_VARIANT 0x02
#define CONFIG_BLOCK_STEERING 0x03

int config_init() {
  // Initialize the EEPROM subsystem
  int ret = eeprom_init();
  if (ret) {
    LOG_ERR("Failed to initialize EEPROM: %d", ret);
    return ret;
  }

  return 0;
}

int config_read_tractor_variant(uint8_t *variant) {
  if (!variant) {
    return -EINVAL;
  }

  size_t size = sizeof(uint8_t);
  return eeprom_read_block(kEepromBlockTypeTractorVariant, variant, &size);
}

int config_write_tractor_variant(uint8_t variant) {
  return eeprom_write_block(kEepromBlockTypeTractorVariant, &variant, sizeof(variant));
}
