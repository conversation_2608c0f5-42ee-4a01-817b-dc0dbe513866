#pragma once
#include "stdbool.h"
#include <zephyr/kernel.h>

extern bool enabled;
extern int32_t throttle_ticks;

#define CONTROL_IS_ENABLED (enabled)
#define OPERATOR_OVERRIDE(text)                                                                                        \
  {                                                                                                                    \
    LOG_INF("Operator Override: %s", text);                                                                            \
    enabled = false;                                                                                                   \
  }

typedef enum tractor_variant_t {
  UNKNOWN = 0,
  JD_6LH = 1,
  JD_6LHM = 2,
  JD_6PRO = 3,
  JD_7RH = 4,
  JD_8PRO = 5,
} tractor_variant_t;

extern tractor_variant_t tractor_variant;

void asc_init();