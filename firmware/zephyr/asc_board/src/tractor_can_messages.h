#pragma once

// compare two message IDs, but ignore first, priority, and EDP/DP bits
#define COMPARE_MSG_ID(actual, expected) ((actual & 0x03FFFFFF) == (expected & 0x03FFFFFF))

// Speed wheel lever write messages
#define LH_SPEED_WHEEL_LEVER_ID (0x0CFFFE8C)
#define LH_SPEED_WHEEL_LEVER_FIRST_BYTE (0x8C)
#define SPEED_WHEEL_OVERFLOW (0xFB)

#define LHM_SPEED_WHEEL_LEVER_ID (0x14FFFE8C)
#define LHM_SPEED_WHEEL_LEVER_FIRST_BYTE (0x8C)

#define CPRO_JOYSTICK_ID (0x0CFFF8F6)
#define CPRO_JOYSTICK_FIRST_BYTE (0xB6)

#define RH7R_SPEED_WHEEL_LEVER_ID (0x0CFFFE8C)
#define RH7R_SPEED_WHEEL_LEVER_FIRST_BYTE (0x8C)

// Transmission read messages
#define LH_TRANSMISSION_INFO_ID (0x18FFFF05)
#define LH_TRANSMISSION_INFO_FIRST_BYTE (0x20)

#define LHM_TRANSMISSION_INFO_ID (0x18FFF805)
#define LHM_TRANSMISSION_INFO_FIRST_BYTE (0xB7)

#define CPRO_TRANSMISSION_INFO_ID (0x18FFF805)
#define CPRO_TRANSMISSION_INFO_FIRST_BYTE (0xB7)

#define RH7R_TRANSMISSION_INFO_ID (0x18FFFF05)
#define RH7R_TRANSMISSION_INFO_FIRST_BYTE (0x20)

// Gear read messages
#define LH_GEAR_INFO_ID (0x0CFFFB05)
#define LH_GEAR_INFO_FIRST_BYTE (0x00)

#define CPRO_GEAR_INFO_ID (0x0CFFF805)
#define CPRO_GEAR_INFO_FIRST_BYTE (0xAE)

#define LHM_GEAR_INFO_ID (0x0CFFF805)
#define LHM_GEAR_INFO_FIRST_BYTE (0xAE)

#define CPRO8R_GEAR_INFO_ID (0x0CFFF805)
#define CPRO8R_GEAR_INFO_FIRST_BYTE (0xAE)

#define RH7R_GEAR_INFO_ID (0x18FFFF05)
#define RH7R_GEAR_INFO_FIRST_BYTE (0x20)

// Brake read messages
#define LH_BRAKE_PEDAL_ID (0x0CFFFF31)
#define LH_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define CPRO8R_BRAKE_PEDAL_ID (0x0CEFFF05)
#define CPRO8R_BRAKE_PEDAL_FIRST_BYTE (0x6E)

#define CPRO_BRAKE_PEDAL_ID (0x08FFFF31)
#define CPRO_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define LHM_BRAKE_PEDAL_ID (0x08FFFF31)
#define LHM_BRAKE_PEDAL_FIRST_BYTE (0x5E)

#define RH7R_BRAKE_PEDAL_ID (0x0CFFFF31)
#define RH7R_BRAKE_PEDAL_FIRST_BYTE (0x5E)
