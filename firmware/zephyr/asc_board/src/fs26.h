#pragma once

#include <drivers/spi.h>
#include <zephyr/kernel.h>

// Register Addresses

#define FS26_REG_WD_TOKEN (0x4E)
#define FS26_REG_WD_ANSWER (0x4D)
#define FS26_REG_OVUV_STATUS (0x50)
#define FS26_STATES (0x57)
#define FS26_DIAG_SAFETY1 (0x54)
#define FS26_DIAG_SAFETY2 (0x55)
#define FS26_WD_I_CFG (0x45)
#define FS26_NOT_WD_I_CFG (0x46)
#define FS_WDW_DURATION (0x4B)
#define FS_NOT_WDW_DURATION (0x4C)
#define FS_I_FSSM (0x49)
#define FS_I_OVUV_SAFE_REACTION1 (0x41)
#define FS_NOT_I_OVUV_SAFE_REACTION1 (0x42)
#define FS_I_OVUV_SAFE_REACTION2 (0x43)
#define FS_NOT_I_OVUV_SAFE_REACTION2 (0x44)
#define FS_I_SAFET_INPUTS (0x47)
#define FS_NOT_I_SAFET_INPUTS (0x48)
#define FS_OVUVREG_STATUS (0x50)
#define FS_SAFE_IOS_1 (0x52)
#define M_SYS_CFG (0x0D)
#define FS_GRL_FLAGS (0x40)
#define FS_LP_REQ (0x58)
#define M_WIO_CFG (0x10)
#define M_STATUS (0x02)

// SPI message structure for MOSI (Master Out, Slave In)
typedef struct {
  uint8_t m_fs;        // Bit 31: Main or fail-safe register selection (0: Main, 1: Fail-safe)
  uint8_t reg_address; // Bits 30-25: Register address
  uint8_t rw;          // Bit 24: Read/Write (0: Read, 1: Write)
  uint16_t data;       // Bits 23-8: Data for write or 0 for read
  uint8_t crc;         // Bits 7-0: Cyclic Redundancy Check (CRC)
} FS26_SPI_MOSI_Message;

// SPI message structure for MISO (Master In, Slave Out)
typedef struct {
  uint8_t general_status;   // Bits 31-24: General device status
  uint16_t extended_status; // Bits 23-8: Extended device status or register content
  uint8_t crc;              // Bits 7-0: CRC from FS26
} FS26_SPI_MISO_Message;

void fs26_init(void);
void fs26_pet_watchdog(void);

extern bool current_wake_state;
extern int64_t wake_edge_timestamp;