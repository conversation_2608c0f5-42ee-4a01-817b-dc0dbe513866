#pragma once

#include <zephyr/kernel.h>
#include <zephyr/sys/atomic.h>

extern struct k_mutex mitm_state_mutex;

typedef enum gear_command_t {
  GEAR_COMMAND_PARK = 0,
  GEAR_COMMAND_REVERSE = 1,
  GEAR_COMMAND_NEUTRAL = 2,
  GEAR_COMMAND_FORWARD = 3,
  GEAR_COMMAND_POWER = 4,
} gear_command_t;

// Canonical state of in-cab HMI
typedef struct cab_state_t {
  uint32_t gear;
  uint32_t speed_wheel_pos;
  uint32_t speed_wheel_neg;
  uint32_t speed_lever;
  uint32_t speed_lever_s1;
  uint32_t speed_lever_s2;
  uint32_t speed_lever_f1_f2_value;
} cab_state_t;

// Canonical state of tractor
typedef struct tractor_state_t {
  gear_command_t gear; // symbolic (uses enum pattern)
  float ground_speed;
  uint32_t transmission_speed;
  uint32_t transmission_speed_rx;
  bool brake_pedal_pressed; // 0 is no brake, 1 is brake
  uint32_t f1_setpoint;
  uint32_t f2_setpoint;
  uint32_t r1_setpoint;
  uint32_t r2_setpoint;
  bool detent_active; // Applies to Command Pro
} tractor_state_t;

typedef struct mitm_state_t {
  uint32_t gear;
  uint32_t speed_wheel_pos;
  uint32_t speed_wheel_neg;
  uint32_t speed_lever;
  uint32_t speed_lever_s1;
  uint32_t speed_lever_f1_f2_value;
} mitm_state_t;

extern tractor_state_t tractor_state;
extern cab_state_t cab_state;
extern mitm_state_t mitm_state;