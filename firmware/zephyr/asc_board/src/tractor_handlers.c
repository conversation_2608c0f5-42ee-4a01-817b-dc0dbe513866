#include "tractor_handlers.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(tractor_handlers, CONFIG_APP_LOG_LEVEL);

void handle_transmission_speed(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, LH_TRANSMISSION_INFO_ID) && msg->data[0] == LH_TRANSMISSION_INFO_FIRST_BYTE &&
      (tractor_variant == JD_6LH)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_TRANSMISSION_INFO_ID) && msg->data[0] == LHM_TRANSMISSION_INFO_FIRST_BYTE &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_TRANSMISSION_INFO_ID) && msg->data[0] == CPRO_TRANSMISSION_INFO_FIRST_BYTE &&
             (tractor_variant == JD_6PRO || tractor_variant == JD_8PRO)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, RH7R_TRANSMISSION_INFO_ID) && msg->data[0] == RH7R_TRANSMISSION_INFO_FIRST_BYTE &&
             tractor_variant == JD_7RH) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool speed_init = 0;
  if (!speed_init) {
    speed_init = true;
    LOG_INF("Transmission Speed handler initialized");
  }

  switch (msg->data[0]) {
  case 0x20:
    tractor_state.f1_setpoint = (((msg->data[2] << 8) | msg->data[1]) + 4) / 8;
    tractor_state.r1_setpoint = (((msg->data[6] << 8) | msg->data[5]) + 4) / 8;
    break;
  case 0xB7:
    if (tractor_variant == JD_6PRO || tractor_variant == JD_8PRO) {
      tractor_state.f1_setpoint = (((msg->data[3] << 8) | msg->data[2]) + 4) / 8;
    } else if (tractor_variant == JD_6LHM) {
      tractor_state.f1_setpoint = (((msg->data[5] << 8) | msg->data[4]) + 4) / 8;
    }
    tractor_state.r1_setpoint = tractor_state.f1_setpoint;
    break;
  default:
    LOG_WRN("Unknown transmission speed message first byte: %x", msg->data[0]);
    return;
  }

  // LOG_INF("F1: %d, R1: %d", tractor_state.f1_setpoint, tractor_state.r1_setpoint);
}

void handle_brake_pedal_pressed(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, CPRO8R_BRAKE_PEDAL_ID) && msg->data[0] == CPRO8R_BRAKE_PEDAL_FIRST_BYTE &&
      tractor_variant == JD_8PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO_BRAKE_PEDAL_ID) && msg->data[0] == CPRO_BRAKE_PEDAL_FIRST_BYTE &&
             tractor_variant == JD_6PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LH_BRAKE_PEDAL_ID) && msg->data[0] == LH_BRAKE_PEDAL_FIRST_BYTE &&
             tractor_variant == JD_6LH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_BRAKE_PEDAL_ID) && msg->data[0] == LHM_BRAKE_PEDAL_FIRST_BYTE &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, RH7R_BRAKE_PEDAL_ID) && msg->data[0] == RH7R_BRAKE_PEDAL_FIRST_BYTE &&
             tractor_variant == JD_7RH) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool brake_init = 0;
  if (!brake_init) {
    brake_init = true;
    LOG_INF("Brake Pedal handler initialized");
  }

  switch (msg->data[0]) {
  case 0x5E:
    tractor_state.brake_pedal_pressed = (msg->data[1] & 0xF0) != 0x00;
    if (tractor_state.brake_pedal_pressed && enabled) {
      OPERATOR_OVERRIDE("Brake Pedal");
    }
    break;
  case 0x6E:
    tractor_state.brake_pedal_pressed = msg->data[5] != 0x01;
    if (tractor_state.brake_pedal_pressed && enabled) {
      OPERATOR_OVERRIDE("Brake Pedal");
    }
    break;
  default:
    LOG_WRN("Unknown brake pedal message first byte: %x", msg->data[0]);
    return;
  }

  // if (tractor_state.brake_pedal_pressed) {
  //     LOG_INF("Brake Pedal Pressed");
  // } else {
  //     LOG_INF("Brake Pedal Not Pressed");
  // }
}

void handle_tractor_gear(struct zcan_frame *msg) {
  bool is_valid = false;
  if (COMPARE_MSG_ID(msg->id, CPRO_GEAR_INFO_ID) && (msg->data[0] == CPRO_GEAR_INFO_FIRST_BYTE) &&
      tractor_variant == JD_6PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_GEAR_INFO_ID) && (msg->data[0] == LHM_GEAR_INFO_FIRST_BYTE) &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, CPRO8R_GEAR_INFO_ID) && (msg->data[0] == CPRO8R_GEAR_INFO_FIRST_BYTE) &&
             tractor_variant == JD_8PRO) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LH_GEAR_INFO_ID) && (msg->data[0] == LH_GEAR_INFO_FIRST_BYTE) &&
             tractor_variant == JD_6LH) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, RH7R_GEAR_INFO_ID) && (msg->data[0] == RH7R_GEAR_INFO_FIRST_BYTE) &&
             tractor_variant == JD_7RH) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool gear_init = 0;
  if (!gear_init) {
    gear_init = true;
    LOG_INF("Gear handler initialized");
  }

  switch (msg->data[0]) {
  case 0xAE:
    if (msg->data[2] == 0x01) {
      tractor_state.gear = GEAR_COMMAND_PARK;
    } else if (msg->data[2] == 0x10) {
      tractor_state.gear = GEAR_COMMAND_NEUTRAL;
    } else if (msg->data[3] == 0x01) {
      tractor_state.gear = GEAR_COMMAND_FORWARD;
    } else if (msg->data[3] == 0x10) {
      tractor_state.gear = GEAR_COMMAND_REVERSE;
    }
    break;
  case 0x00:
    if (msg->data[2] == 0xF4) {
      tractor_state.gear = GEAR_COMMAND_PARK;
    } else if (msg->data[2] == 0xF0) {
      tractor_state.gear = GEAR_COMMAND_NEUTRAL;
    } else if (msg->data[2] == 0xF1) {
      tractor_state.gear = GEAR_COMMAND_FORWARD;
    } else if (msg->data[2] == 0xF2) {
      tractor_state.gear = GEAR_COMMAND_REVERSE;
    } else if (msg->data[2] == 0xFF) {
      tractor_state.gear = GEAR_COMMAND_POWER;
    }
    break;
  case 0x20:
    if (msg->data[7] == 0xD0) {
      tractor_state.gear = GEAR_COMMAND_PARK;
    } else if (msg->data[7] == 0xD4) {
      tractor_state.gear = GEAR_COMMAND_NEUTRAL;
    } else if (msg->data[7] == 0xC0 || msg->data[7] == 0xC4) {
      tractor_state.gear = GEAR_COMMAND_FORWARD;
    } else if (msg->data[7] == 0xE0) {
      tractor_state.gear = GEAR_COMMAND_REVERSE;
    }
    break;
  default:
    LOG_WRN("Unknown gear message first byte: %x", msg->data[0]);
    return;
  }

  if (tractor_state.gear != GEAR_COMMAND_FORWARD) {
    enabled = false;
  }
  if (tractor_variant == JD_6PRO || tractor_variant == JD_8PRO) { // Forward detent check
    tractor_state.detent_active = (msg->data[4] >> 4) == 0xD;
    if (!tractor_state.detent_active) {
      enabled = false;
    }
  }

  // LOG_INF("Gear: %d", tractor_state.gear);
}

void add_tractor_handlers() {
  add_tractor_read_handler(handle_transmission_speed);
  add_tractor_read_handler(handle_brake_pedal_pressed);
  add_tractor_read_handler(handle_tractor_gear);
}