#include <logging/log.h>
#include <sys/reboot.h>
LOG_MODULE_REGISTER(tractor_can, CONFIG_APP_LOG_LEVEL);
#include "asc.h"
#include "fs26.h"
#include "tractor_can.h"
#include <kernel.h>

static const bool MONITOR_CANBUS = false;

static const struct gpio_dt_spec can1_silent = GPIO_DT_SPEC_GET(DT_PATH(gpios, can1_silent), gpios);
static const struct gpio_dt_spec can2_silent = GPIO_DT_SPEC_GET(DT_PATH(gpios, can2_silent), gpios);

static const struct gpio_dt_spec can1_120 = GPIO_DT_SPEC_GET(DT_PATH(gpios, can1_120), gpios);
static const struct gpio_dt_spec can1_60 = GPIO_DT_SPEC_GET(DT_PATH(gpios, can1_60), gpios);
static const struct gpio_dt_spec can2_120 = GPIO_DT_SPEC_GET(DT_PATH(gpios, can2_120), gpios);
static const struct gpio_dt_spec can2_60 = GPIO_DT_SPEC_GET(DT_PATH(gpios, can2_60), gpios);

#define MAILBOX_SIZE (600)
K_MSGQ_DEFINE(tractor_mailbox, sizeof(struct zcan_frame), MAILBOX_SIZE, 4);
K_MSGQ_DEFINE(cab_mailbox, sizeof(struct zcan_frame), MAILBOX_SIZE, 4);

const struct device *tractor_can;
const struct device *cab_can;

#define MAX_HANDLERS (20)
static can_handler_t tractor_read_handlers[MAX_HANDLERS];
static int tractor_read_handler_count = 0;

static can_handler_t cab_mitm_handlers[MAX_HANDLERS];
static int cab_mitm_handler_count = 0;

int tractor_tx_cnt = 0;
int tractor_tx_err_cnt = 0;
int cab_tx_cnt = 0;
int cab_tx_err_cnt = 0;

int tractor_rx_cnt = 0;
int cab_rx_cnt = 0;

static int64_t last_tractor_rx = 0;
static int64_t last_tractor_tx = 0;
static int64_t last_cab_rx = 0;
static int64_t last_cab_tx = 0;

#define HANDLER_TIMEOUT_MS 50 // Maximum time a handler can run
#define HANDLER_STACK_SIZE 1024

static void handler_timeout_handler(struct k_timer *timer);
static K_TIMER_DEFINE(tractor_handler_timer, handler_timeout_handler, NULL);
static K_TIMER_DEFINE(cab_handler_timer, handler_timeout_handler, NULL);

// Add these variables to track current handler
static int current_tractor_handler = -1;
static int current_cab_handler = -1;

static void handler_timeout_handler(struct k_timer *timer) {
  if (timer == &tractor_handler_timer && current_tractor_handler >= 0) {
    LOG_ERR("Tractor handler %d timeout - killing thread", current_tractor_handler);
    // k_thread_abort(k_current_get());
  } else if (timer == &cab_handler_timer && current_cab_handler >= 0) {
    LOG_ERR("Cab handler %d timeout - killing thread", current_cab_handler);
    // k_thread_abort(k_current_get());
  }
}

uint32_t send_tractor_can_frame(struct zcan_frame *frame) {
  int ret;
  ret = can_send(tractor_can, frame, K_NO_WAIT, NULL, NULL);

  if (ret == 0) {
    tractor_tx_cnt++;
    last_tractor_tx = k_uptime_get();
    return 0;
  }

  // Possible errors from can_send() are:
  /*
   * 0 if successful.
   * -EINVAL if an invalid parameter was passed to the function.
   * -ENETDOWN if the CAN controller is in bus-off state.
   * -EBUSY if CAN bus arbitration was lost (only applicable if automatic
   *                retransmissions are disabled).
   * -EIO if a general transmit error occurred (e.g. missing ACK if
   *              automatic retransmissions are disabled).
   * -EAGAIN on timeout.
   */
  if (ret == -EINVAL) {
    LOG_ERR("send_tractor_can_frame, can_send, -EINVAL");
  } else if (ret == -ENETDOWN) {
    LOG_ERR("send_tractor_can_frame, can_send, -ENETDOWN");
  } else if (ret == -EBUSY) {
    LOG_ERR("send_tractor_can_frame, can_send, -EBUSY");
  } else if (ret == -EIO) {
    LOG_ERR("send_tractor_can_frame, can_send, -EIO");
  } else if (ret == -EAGAIN) {
    LOG_ERR("send_tractor_can_frame, can_send, -EAGAIN");
  } else // Return value is not something can_send listed.
  {
    LOG_ERR("send_tractor_can_frame, can_send, OTHER, %d", ret);
  }

  return ret;
}

uint32_t send_cab_can_frame(struct zcan_frame *frame) {
  int ret;
  ret = can_send(cab_can, frame, K_NO_WAIT, NULL, NULL);

  if (ret == 0) {
    cab_tx_cnt++;
    last_cab_tx = k_uptime_get();
    return 0;
  }
  // Possible errors from can_send() are:
  /*
   * 0 if successful.
   * -EINVAL if an invalid parameter was passed to the function.
   * -ENETDOWN if the CAN controller is in bus-off state.
   * -EBUSY if CAN bus arbitration was lost (only applicable if automatic
   *                retransmissions are disabled).
   * -EIO if a general transmit error occurred (e.g. missing ACK if
   *              automatic retransmissions are disabled).
   * -EAGAIN on timeout.
   */
  if (ret == -EINVAL) {
    LOG_ERR("send_cab_can_frame, can_send, -EINVAL");
  } else if (ret == -ENETDOWN) {
    LOG_ERR("send_cab_can_frame, can_send, -ENETDOWN");
  } else if (ret == -EBUSY) {
    LOG_ERR("send_cab_can_frame, can_send, -EBUSY");
  } else if (ret == -EIO) {
    LOG_ERR("send_cab_can_frame, can_send, -EIO");
  } else if (ret == -EAGAIN) {
    LOG_ERR("send_cab_can_frame, can_send, -EAGAIN");
  } else // Return value is not something can_send listed.
  {
    LOG_ERR("send_cab_can_frame, can_send, OTHER, %d", ret);
  }

  return ret;
}

bool seen_cab_message = false;
void tractor_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);
  if (!seen_cab_message) {
    LOG_WRN("Tractor RX before Cab RX. Ignoring message: 0x%x", msg->id);
    return;
  } // Ignore tractor messages until we see a cab message
  last_tractor_rx = k_uptime_get();

  tractor_rx_cnt++;
  if (k_msgq_put(&tractor_mailbox, msg, K_NO_WAIT)) {
    LOG_ERR("Tractor mailbox full, message lost: 0x%x", msg->id);
  } else {
    // LOG_INF("Tractor RX message: 0x%x", msg->id);
  }
}

void cab_rx_callback(const struct device *dev, struct zcan_frame *msg, void *arg) {
  ARG_UNUSED(dev);
  seen_cab_message = true;
  last_cab_rx = k_uptime_get();

  cab_rx_cnt++;
  if (k_msgq_put(&cab_mailbox, msg, K_NO_WAIT)) {
    LOG_ERR("Cab mailbox full, message lost: 0x%x", msg->id);
  } else {
    // LOG_INF("Cab RX message: 0x%x", msg->id);
  }
}

void tractor_can_thread() {
  LOG_INF("Starting tractor_can_thread");
  struct zcan_frame msg;
  while (1) {
    if (k_msgq_get(&tractor_mailbox, &msg, K_FOREVER) == 0) {
      // LOG_INF("%x", msg.id);

      for (int i = 0; i < tractor_read_handler_count; i++) {
        int64_t start_time = k_uptime_get();
        tractor_read_handlers[i](&msg);
        int64_t duration = k_uptime_get() - start_time;
        if (duration > 1) {
          LOG_WRN("Tractor handler %d took %lld ms", i, duration);
        }
      }
      int64_t send_start_time = k_uptime_get();
      if (send_cab_can_frame(&msg)) {
        cab_tx_err_cnt++;
      } else {
        int64_t send_duration = k_uptime_get() - send_start_time;
        if (send_duration > 10) {
          LOG_WRN("Cab frame send took %lld ms", send_duration);
        }
      }
    }
  }
}

void cab_can_thread() {
  LOG_INF("Starting cab_can_thread");
  struct zcan_frame msg;
  while (1) {
    if (k_msgq_get(&cab_mailbox, &msg, K_FOREVER) == 0) {
      // LOG_INF("%x", msg.id);

      for (int i = 0; i < cab_mitm_handler_count; i++) {
        int64_t start_time = k_uptime_get();
        cab_mitm_handlers[i](&msg);
        int64_t duration = k_uptime_get() - start_time;
        if (duration > 1) {
          LOG_WRN("Cab handler %d took %lld ms", i, duration);
        }
      }
      int64_t send_start_time = k_uptime_get();
      if (send_tractor_can_frame(&msg)) {
        tractor_tx_err_cnt++;
      } else {
        int64_t send_duration = k_uptime_get() - send_start_time;
        if (send_duration > 10) {
          LOG_WRN("Tractor frame send took %lld ms", send_duration);
        }
      }
    }
  }
}

void add_tractor_read_handler(can_handler_t handler) {
  if (tractor_read_handler_count < MAX_HANDLERS) {
    tractor_read_handlers[tractor_read_handler_count++] = handler;
  } else {
    LOG_ERR("Tractor read handler list full");
  }
}

void add_cab_mitm_handler(can_handler_t handler) {
  if (cab_mitm_handler_count < MAX_HANDLERS) {
    cab_mitm_handlers[cab_mitm_handler_count++] = handler;
  } else {
    LOG_ERR("Cab MITM handler list full");
  }
}

void can_monitor_thread() {
  LOG_INF("Starting can_monitor_thread");

  static int64_t can_inactive_start = 0;

  while (1) {
    thread_checkin(k_current_get());
    k_msleep(100);

    const uint32_t fdcan1_psr = *((uint32_t *)0x4000A044);
    const uint32_t fdcan2_psr = *((uint32_t *)0x4000A444);

    bool can_active = is_can_bus_active();

    // Track when CAN becomes inactive
    if (!can_active && current_wake_state) {
      if (can_inactive_start == 0) {
        can_inactive_start = k_uptime_get();
      }
      // Only reset if CAN has been inactive for >1s
      else if ((k_uptime_get() - can_inactive_start) > 1000) {
        int64_t now = k_uptime_get();
        LOG_ERR("CAN bus inactive, resetting board");
        LOG_ERR("Tractor last rx: %lld ms ago, last tx: %lld ms ago", now - last_tractor_rx, now - last_tractor_tx);
        LOG_ERR("Cab last rx: %lld ms ago, last tx: %lld ms ago", now - last_cab_rx, now - last_cab_tx);
        sys_reboot(SYS_REBOOT_COLD);
      }
    } else {
      can_inactive_start = 0;
    }

    // Log tx counts every 10 seconds
    static int64_t last_log_time = 0;
    int64_t now = k_uptime_get();
    if (now - last_log_time >= 10000) {
      LOG_INF("CAN counts - tractor: tx=%d rx=%d errors=%d, cab: tx=%d rx=%d errors=%d", tractor_tx_cnt, tractor_rx_cnt,
              tractor_tx_err_cnt, cab_tx_cnt, cab_rx_cnt, cab_tx_err_cnt);
      last_log_time = now;
    }

    if (fdcan1_psr & (1 << 7)) {
      LOG_INF("fdcan1 bus off (%08x)", fdcan1_psr);
      int err = can_recover(tractor_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
    }
    if (fdcan2_psr & (1 << 7)) {
      LOG_INF("fdcan2 bus off (%08x)", fdcan2_psr);
      int err = can_recover(cab_can, K_MSEC(10));
      if (err) {
        LOG_ERR("%s failed: %d", "can_recover", err);
      }
    }
  }
}

void log_cab_handler(struct zcan_frame *msg) { LOG_INF("Cab msg id: [%u]", msg->id); }

void can_loopback_test() {
  LOG_INF("Starting can_loopback_test");
  add_cab_mitm_handler(log_cab_handler);
  // Send a CAN message w/ extended id to tractor bus
  struct zcan_frame msg = {.id_type = CAN_EXTENDED_IDENTIFIER,
                           .rtr = CAN_DATAFRAME,
                           .id = 0x12345678,
                           .dlc = 8,
                           .data = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08}};
  send_tractor_can_frame(&msg);
}

bool is_can_bus_active(void) {
  int64_t now = k_uptime_get();
  int64_t timeout = 100; // 100ms timeout

  // Check if we've had recent activity on both buses
  bool tractor_active = (now - last_tractor_rx < timeout) && (now - last_tractor_tx < timeout);
  bool cab_active = (now - last_cab_rx < timeout) && (now - last_cab_tx < timeout);

  return tractor_active && cab_active;
}

K_THREAD_DEFINE(tractor_can_thread_id, 1024, tractor_can_thread, NULL, NULL, NULL, TRACTOR_CAN_THREAD_PRIORITY, 0,
                INT64_MAX);
K_THREAD_DEFINE(cab_can_thread_id, 1024, cab_can_thread, NULL, NULL, NULL, CAB_CAN_THREAD_PRIORITY, 0, INT64_MAX);
K_THREAD_DEFINE(can_monitor_thread_id, 1024, can_monitor_thread, NULL, NULL, NULL, CAN_MONITOR_THREAD_PRIORITY, 0,
                INT64_MAX);

void tractor_can_init() {
  k_thread_name_set(tractor_can_thread_id, "tractor_can");
  k_thread_name_set(cab_can_thread_id, "cab_can");
  k_thread_name_set(can_monitor_thread_id, "can_monitor");

  int filter_id;
  const struct zcan_filter everything_filter = {
      .id_type = CAN_EXTENDED_IDENTIFIER, .rtr = CAN_DATAFRAME, .id = 0x0, .rtr_mask = 0, .id_mask = 0};

  tractor_can = DEVICE_DT_GET(DT_ALIAS(tcan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(tractor_can), ENODEV);

  filter_id = can_add_rx_filter(tractor_can, tractor_rx_callback, NULL, &everything_filter);
  if (filter_id < 0) {
    LOG_ERR("Unable to attach isr [%d]", filter_id);
  }

  cab_can = DEVICE_DT_GET(DT_ALIAS(ccan));
  HANDLE_UNLIKELY_BOOL(device_is_ready(cab_can), ENODEV);
  filter_id = can_add_rx_filter(cab_can, cab_rx_callback, NULL, &everything_filter);
  if (filter_id < 0) {
    LOG_ERR("Unable to attach isr [%d]", filter_id);
  }

  HANDLE_UNLIKELY(gpio_pin_configure_dt(&can1_silent, GPIO_OUTPUT));
  HANDLE_UNLIKELY(gpio_pin_configure_dt(&can2_silent, GPIO_OUTPUT));
  gpio_pin_set(can1_silent.port, can1_silent.pin, 0);
  gpio_pin_set(can2_silent.port, can2_silent.pin, 0);

  can_set_mode(cab_can, CAN_MODE_NORMAL);
  can_set_mode(tractor_can, CAN_MODE_NORMAL);

  int err = 0;

  err |= gpio_pin_configure_dt(&can1_120, GPIO_OUTPUT);
  err |= gpio_pin_configure_dt(&can1_60, GPIO_OUTPUT);
  err |= gpio_pin_configure_dt(&can2_120, GPIO_OUTPUT);
  err |= gpio_pin_configure_dt(&can2_60, GPIO_OUTPUT);

  if (err) {
    LOG_ERR("Failed to configure CAN termination GPIOs, is this board RevD or above?");
  } else {
    switch (tractor_variant) {
    case JD_6LH:
    case JD_7RH:
      gpio_pin_set(can1_120.port, can1_120.pin, 0);
      gpio_pin_set(can1_60.port, can1_60.pin, 1);
      gpio_pin_set(can2_120.port, can2_120.pin, 0);
      gpio_pin_set(can2_60.port, can2_60.pin, 0);
      break;
    case JD_6LHM:
    case JD_6PRO:
    case JD_8PRO:
      gpio_pin_set(can1_120.port, can1_120.pin, 1);
      gpio_pin_set(can1_60.port, can1_60.pin, 0);
      gpio_pin_set(can2_120.port, can2_120.pin, 1);
      gpio_pin_set(can2_60.port, can2_60.pin, 0);
      break;
    case UNKNOWN:
    default:
      LOG_ERR("Unknown tractor variant %d, using on-board jumper termination", tractor_variant);
      break;
    }
  }

  k_thread_start(cab_can_thread_id);
  k_thread_start(tractor_can_thread_id);
  if (MONITOR_CANBUS) {
    k_thread_start(can_monitor_thread_id);
    register_monitored_thread(can_monitor_thread_id, "can_monitor_thread", 300);
  } else {
    LOG_INF("CAN bus monitoring is disabled");
  }

  return 0;
}

void tractor_can_reinit(void) {
  LOG_INF("Reinitializing CAN subsystem");

  // Stop existing threads
  k_thread_suspend(&tractor_can_thread_id);
  k_thread_suspend(&cab_can_thread_id);
  k_thread_suspend(&can_monitor_thread_id);

  // Purge message queues
  k_msgq_purge(&tractor_mailbox);
  k_msgq_purge(&cab_mailbox);

  // Reset CAN hardware
  can_recover(tractor_can, K_MSEC(100));
  can_recover(cab_can, K_MSEC(100));

  // Reset error counters and flags
  tractor_tx_cnt = 0;
  tractor_tx_err_cnt = 0;
  cab_tx_cnt = 0;
  cab_tx_err_cnt = 0;
  tractor_rx_cnt = 0;
  cab_rx_cnt = 0;

  // Reconfigure CAN hardware
  gpio_pin_set(can1_silent.port, can1_silent.pin, 0);
  gpio_pin_set(can2_silent.port, can2_silent.pin, 0);
  can_set_mode(cab_can, CAN_MODE_NORMAL);
  can_set_mode(tractor_can, CAN_MODE_NORMAL);

  // Start existing threads
  k_thread_resume(&tractor_can_thread_id);
  k_thread_resume(&cab_can_thread_id);
  k_thread_resume(&can_monitor_thread_id);

  LOG_INF("CAN subsystem reinitialized");
}
