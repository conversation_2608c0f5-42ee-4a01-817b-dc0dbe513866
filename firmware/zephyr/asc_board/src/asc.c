#include "asc.h"
#include <logging/log.h>
#include <zephyr/drivers/gpio.h>

LOG_MODULE_REGISTER(asc, CONFIG_APP_LOG_LEVEL);

static const struct gpio_dt_spec led_green = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_green), gpios);
static const struct gpio_dt_spec led_blue = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_blue), gpios);
static const struct gpio_dt_spec led_red = GPIO_DT_SPEC_GET(DT_PATH(gpios, led_red), gpios);

bool enabled = false;
tractor_variant_t tractor_variant = UNKNOWN;
int32_t throttle_ticks = 0;

void asc_init() {
  gpio_pin_configure_dt(&led_green, GPIO_OUTPUT);
  gpio_pin_configure_dt(&led_blue, GPIO_OUTPUT);
  gpio_pin_configure_dt(&led_red, GPIO_OUTPUT);

  gpio_pin_set(led_red.port, led_red.pin, 0);
  gpio_pin_set(led_green.port, led_green.pin, 0);
  gpio_pin_set(led_blue.port, led_blue.pin, 1);
  LOG_INF("ASC initialized");
}