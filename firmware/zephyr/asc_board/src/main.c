#include <logging/log.h>
LOG_MODULE_REGISTER(main, CONFIG_APP_LOG_LEVEL);
extern const struct log_backend *log_backend_net_get(void);

#include "asc.h"
#include "config.h"
#include "fs26.h"
#include "lib/smp/smp.h"
#include "tractor_can.h"
#include "udp_server.h"
#include <lib/watchdog/watchdog.h>
#include <string.h>
#include <utils/adc.h>
#include <utils/handle_errors.h>
#include <zephyr/devicetree.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/logging/log_backend.h>
#include <zephyr/logging/log_ctrl.h>
#include <zephyr/zephyr.h>

//#include "oss_sensor.h"

static int boot() {
  LOG_INF("ASC booting");

  const struct log_backend *backend = log_backend_net_get();

  if (!log_backend_is_active(backend)) {
    if (backend->api->init != NULL) {
      backend->api->init(backend);
    }

    log_backend_activate(backend, NULL);
  }

  HANDLE_UNLIKELY(config_init());
  config_read_tractor_variant(&tractor_variant);
  LOG_INF("Tractor variant: %s", tractor_variant == UNKNOWN
                                     ? "UNKNOWN"
                                     : tractor_variant == JD_6LH
                                           ? "JD_6LH"
                                           : tractor_variant == JD_6LHM
                                                 ? "JD_6LHM"
                                                 : tractor_variant == JD_6PRO
                                                       ? "JD_6PRO"
                                                       : tractor_variant == JD_7RH
                                                             ? "JD_7RH"
                                                             : tractor_variant == JD_8PRO ? "JD_8PRO" : "INVALID");

  fs26_init();
  asc_init();
  add_tractor_handlers();
  add_cab_handlers();
  tractor_can_init();
  init_thread_monitoring();
  return 0;
}

void main() {
  start_watchdog();
  start_smp_lib();
  HANDLE_CRITICAL(boot());
  k_msleep(2000);
  LOG_INF("Starting UDP server");
  start_udp_server();

  LOG_INF("ASC finished booting");
  for (;;) {
    k_msleep(10);
  }
}
