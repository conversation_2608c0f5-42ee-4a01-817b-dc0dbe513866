#include "threads.h"
#include "asc.h"
#include "tractor_state.h"
#include <logging/log.h>
#include <sys/reboot.h>
#include <zephyr/kernel.h>

LOG_MODULE_REGISTER(threads, CONFIG_APP_LOG_LEVEL);

// Thread monitoring data structure
typedef struct thread_monitor_data {
  k_tid_t thread_id;
  const char *name;
  uint32_t last_checkin;
  uint32_t max_interval_ms;
  bool active;
} thread_monitor_data_t;

// Mutex monitoring data structure
typedef struct mutex_monitor_data {
  struct k_mutex *mutex;
  const char *name;
  uint32_t lock_time;
  k_tid_t owner;
} mutex_monitor_data_t;

#define MAX_MONITORED_THREADS 20
#define MAX_MONITORED_MUTEXES 5
#define THREAD_MONITOR_STACK_SIZE 1024
#define THREAD_MONITOR_PRIORITY 6

static thread_monitor_data_t monitored_threads[MAX_MONITORED_THREADS];
static int num_monitored_threads = 0;

static mutex_monitor_data_t monitored_mutexes[MAX_MONITORED_MUTEXES];
static int num_monitored_mutexes = 0;

K_THREAD_STACK_DEFINE(thread_monitor_stack, THREAD_MONITOR_STACK_SIZE);
static struct k_thread thread_monitor_data;

void check_stack_usage(void) {
  struct k_thread *thread;

  // Iterate through all threads
  for (thread = _kernel.threads; thread; thread = thread->next_thread) {
    // Skip system idle threads
    if (thread->base.prio == K_IDLE_PRIO) {
      continue;
    }

    // Get thread name
    const char *name = k_thread_name_get(thread);
    if (name == NULL) {
      name = "unnamed";
    }

    // Get stack usage info
    size_t unused;
    k_thread_stack_space_get(thread, &unused);
    size_t total = thread->stack_info.size;
    size_t used = total - unused;

    // Calculate usage percentage
    uint32_t usage_percent = (used * 100) / total;

    // Warning threshold at 80%
    if (usage_percent > 80) {
      LOG_WRN("High stack usage for thread '%s': %u%% (%zu/%zu bytes)", name, usage_percent, used, total);
    }

    // Critical threshold at 95%
    if (usage_percent > 95) {
      LOG_ERR("Critical stack usage for thread '%s': %u%% (%zu/%zu bytes)", name, usage_percent, used, total);
    }
  }
}

// Register a thread to be monitored
int register_monitored_thread(k_tid_t thread_id, const char *name, uint32_t max_interval_ms) {
  if (num_monitored_threads >= MAX_MONITORED_THREADS) {
    LOG_ERR("Cannot monitor more threads - increase MAX_MONITORED_THREADS");
    return -1;
  }

  thread_monitor_data_t *monitor = &monitored_threads[num_monitored_threads++];
  monitor->thread_id = thread_id;
  monitor->name = name;
  monitor->last_checkin = k_uptime_get_32();
  monitor->max_interval_ms = max_interval_ms;
  monitor->active = true;

  LOG_INF("Registered thread %s for monitoring", name);
  return 0;
}

// Register a mutex to be monitored
int register_monitored_mutex(struct k_mutex *mutex, const char *name) {
  if (num_monitored_mutexes >= MAX_MONITORED_MUTEXES) {
    LOG_ERR("Cannot monitor more mutexes - increase MAX_MONITORED_MUTEXES");
    return -1;
  }

  mutex_monitor_data_t *monitor = &monitored_mutexes[num_monitored_mutexes++];
  monitor->mutex = mutex;
  monitor->name = name;
  monitor->lock_time = 0;
  monitor->owner = NULL;

  LOG_INF("Registered mutex %s for monitoring", name);
  return 0;
}

// Thread checkin function
void thread_checkin(k_tid_t thread_id) {
  for (int i = 0; i < num_monitored_threads; i++) {
    if (monitored_threads[i].thread_id == thread_id) {
      monitored_threads[i].last_checkin = k_uptime_get_32();
      return;
    }
  }
}

static void restart_thread(thread_monitor_data_t *monitor);

static void thread_monitor_fn(void *arg1, void *arg2, void *arg3) {
  LOG_INF("Thread monitor started");

  k_msleep(50); // Sleep first to give threads time to check in
  while (1) {
    k_msleep(20);

    check_stack_usage();

    uint32_t now = k_uptime_get_32();
    bool has_error = false;

    // Monitor threads
    for (int i = 0; i < num_monitored_threads; i++) {
      thread_monitor_data_t *monitor = &monitored_threads[i];

      if (!monitor->active) {
        continue;
      }

      // Handle 32-bit timer wraparound
      uint32_t elapsed;
      if (now >= monitor->last_checkin) {
        elapsed = now - monitor->last_checkin;
      } else {
        elapsed = 0;
      }

      if (elapsed > monitor->max_interval_ms) {
        LOG_ERR("Thread %s timing: now=%u, last_checkin=%u, elapsed=%u, max=%u", monitor->name, now,
                monitor->last_checkin, elapsed, monitor->max_interval_ms);

        monitor->active = false;
        has_error = true;
      }
    }

    // Monitor mutexes for deadlocks
    for (int i = 0; i < num_monitored_mutexes; i++) {
      mutex_monitor_data_t *monitor = &monitored_mutexes[i];

      // Check if mutex is locked by trying to lock it with K_NO_WAIT
      if (k_mutex_lock(monitor->mutex, K_NO_WAIT) == 0) {
        // Mutex was available - unlock it and reset tracking
        k_mutex_unlock(monitor->mutex);
        monitor->lock_time = 0;
        monitor->owner = NULL;
      } else {
        // Mutex is locked
        if (monitor->owner == NULL) {
          // First time we've seen it locked
          monitor->lock_time = now;
          monitor->owner = (k_tid_t)1; // Just mark as owned
        }

        // Check for long mutex holds
        uint32_t lock_duration = now - monitor->lock_time;
        if (lock_duration > 20) { // Alert if held > 1 second
          LOG_WRN("Mutex %s held for %u ms", monitor->name, lock_duration);
        }
        if (lock_duration > 5000) { // E-Stop if held > 5 seconds
          LOG_ERR("Potential deadlock detected on mutex %s - restarting", monitor->name);
          has_error = true;
          // reboot
          sys_reboot(SYS_REBOOT_COLD);
        }
      }
    }

    // Update software error flag
    // hh_state_flags.software_error = has_error;
  }
}

// Initialize thread monitoring
void init_thread_monitoring(void) {
  // Register mutexes
  register_monitored_mutex(&mitm_state_mutex, "mitm_state_mutex");

  // Start monitor thread
  k_tid_t monitor_tid =
      k_thread_create(&thread_monitor_data, thread_monitor_stack, K_THREAD_STACK_SIZEOF(thread_monitor_stack),
                      thread_monitor_fn, NULL, NULL, NULL, THREAD_MONITOR_PRIORITY, 0, K_NO_WAIT);

  k_thread_name_set(monitor_tid, "thread_monitor");
}
