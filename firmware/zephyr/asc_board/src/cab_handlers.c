#include "cab_handlers.h"
#include <logging/log.h>
LOG_MODULE_REGISTER(cab_handlers, CONFIG_APP_LOG_LEVEL);

void handle_cpro_joystick(struct zcan_frame *msg) {
  if (!COMPARE_MSG_ID(msg->id, CPRO_JOYSTICK_ID) || !(msg->data[0] == CPRO_JOYSTICK_FIRST_BYTE) ||
      !(tractor_variant == JD_6PRO || tractor_variant == JD_8PRO)) {
    return;
  }

  static bool joystick_init = 0;
  uint32_t gear_selection = msg->data[1] << 24 | msg->data[2] << 16 | msg->data[3] << 8 | msg->data[4];
  uint8_t speed_wheel_pos = msg->data[5] >= 0xFB ? 0 : msg->data[5];
  uint8_t speed_wheel_neg = msg->data[6] >= 0xFB ? 0 : msg->data[6];
  if (!joystick_init) {
    joystick_init = true;
    cab_state.gear = gear_selection;
    cab_state.speed_wheel_pos = speed_wheel_pos;
    cab_state.speed_wheel_neg = speed_wheel_neg;
    mitm_state.speed_wheel_pos = 0xFB;
    mitm_state.speed_wheel_neg = 0xFB;
    LOG_INF("CPRO Gear handler initialized");
  }
  if (cab_state.gear != gear_selection) {
    // Operator touched gear selector
    LOG_INF("Operator Override: CommandPro Joystick");
    OPERATOR_OVERRIDE("CommandPro Joystick");
  }
  if (speed_wheel_pos != cab_state.speed_wheel_pos) {
    // operator touched speed wheel pos
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_pos < cab_state.speed_wheel_pos) {
      mitm_state.speed_wheel_pos += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_pos += speed_wheel_pos - cab_state.speed_wheel_pos;
    mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
  }
  if (speed_wheel_neg != cab_state.speed_wheel_neg) {
    // operator touched speed wheel neg
    OPERATOR_OVERRIDE("Speed Wheel");
    if (speed_wheel_neg < cab_state.speed_wheel_neg) {
      mitm_state.speed_wheel_neg += SPEED_WHEEL_OVERFLOW;
    }
    mitm_state.speed_wheel_neg += speed_wheel_neg - cab_state.speed_wheel_neg;
    mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
  }

  cab_state.gear = gear_selection;
  cab_state.speed_wheel_pos = speed_wheel_pos;
  cab_state.speed_wheel_neg = speed_wheel_neg;

  // Handle throttle ticks from UDP commands
  if (enabled) {
    if (throttle_ticks > 0) {
      mitm_state.speed_wheel_pos += throttle_ticks;
      mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
    } else if (throttle_ticks < 0) {
      mitm_state.speed_wheel_neg -= throttle_ticks;
      mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
    }
  } else if (msg->data[5] == 0xFB && msg->data[6] == 0xFB) {
    mitm_state.speed_wheel_neg += 3;
    mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
  }
  throttle_ticks = 0;

  msg->data[5] = mitm_state.speed_wheel_pos;
  msg->data[6] = mitm_state.speed_wheel_neg;
}

void handle_speed_wheel_lever(struct zcan_frame *msg) {
  bool is_valid = false;

  if (COMPARE_MSG_ID(msg->id, LH_SPEED_WHEEL_LEVER_ID) && (msg->data[0] == LH_SPEED_WHEEL_LEVER_FIRST_BYTE) &&
      (tractor_variant == JD_6LH)) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, LHM_SPEED_WHEEL_LEVER_ID) && (msg->data[0] == LHM_SPEED_WHEEL_LEVER_FIRST_BYTE) &&
             tractor_variant == JD_6LHM) {
    is_valid = true;
  } else if (COMPARE_MSG_ID(msg->id, RH7R_SPEED_WHEEL_LEVER_ID) &&
             (msg->data[0] == RH7R_SPEED_WHEEL_LEVER_FIRST_BYTE) && tractor_variant == JD_7RH) {
    is_valid = true;
  }

  if (!is_valid) {
    return;
  }

  static bool speed_wheel_init = 0;
  uint32_t speed_lever = msg->data[1];
  uint32_t speed_lever_f1_f2_value = msg->data[2];
  uint32_t speed_wheel_pos = msg->data[3];
  uint32_t speed_wheel_neg = msg->data[4];

  // Handle initialization
  if (speed_wheel_pos == 0xFB && speed_wheel_neg == 0xFB && cab_state.speed_wheel_pos != 0xFB &&
      cab_state.speed_wheel_neg != 0xFB) {
    speed_wheel_init = 0;
  }

  if (speed_wheel_init == 0 && !enabled) {
    LOG_INF("Speed wheel initialized");
    mitm_state.speed_wheel_pos = 0xFB;
    cab_state.speed_wheel_pos = speed_wheel_pos;
    mitm_state.speed_wheel_neg = 0xFB;
    cab_state.speed_wheel_neg = speed_wheel_neg;
    cab_state.speed_lever = speed_lever;
    cab_state.speed_lever_s1 = speed_lever_f1_f2_value;
    speed_wheel_init = 1;
    throttle_ticks = 0;
  }

  if ((cab_state.speed_lever != speed_lever) && enabled) {
    // Operator touched speed wheel
    OPERATOR_OVERRIDE("Speed Lever");
  }

  /**
    if (enabled) {
      // Just use top of F1 for all remote speed control
      mitm_state.speed_lever = 0xFA;
      mitm_state.speed_lever_f1_f2_value = 0x9F;
    }
    */

  if (speed_wheel_pos != cab_state.speed_wheel_pos) {
    // operator touched speed wheel
    if (enabled) {
      OPERATOR_OVERRIDE("Speed Wheel");
    }
    if ((cab_state.speed_wheel_pos == 0xFB) && (speed_wheel_pos != 0xFB)) {
      // just skip a tick in this case
      cab_state.speed_wheel_pos = speed_wheel_pos;
    } else {
      if (speed_wheel_pos >= cab_state.speed_wheel_pos) {
        mitm_state.speed_wheel_pos += speed_wheel_pos - cab_state.speed_wheel_pos;
      } else {
        mitm_state.speed_wheel_pos += (0xFA - cab_state.speed_wheel_pos) + (speed_wheel_pos + 1);
      }
      mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
    }
  }

  if (speed_wheel_neg != cab_state.speed_wheel_neg) {
    // operator touched speed wheel
    if (enabled) {
      OPERATOR_OVERRIDE("Speed Wheel");
    }
    if ((cab_state.speed_wheel_neg == 0xFB) && (speed_wheel_neg != 0xFB)) {
      // just skip a tick in this case
      cab_state.speed_wheel_neg = speed_wheel_neg;
    } else {
      if (speed_wheel_neg >= cab_state.speed_wheel_neg) {
        mitm_state.speed_wheel_neg += speed_wheel_neg - cab_state.speed_wheel_neg;
      } else {
        mitm_state.speed_wheel_neg += (0xFA - cab_state.speed_wheel_neg) + (speed_wheel_neg + 1);
      }
      mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
    }
  }

  cab_state.speed_lever = speed_lever;
  cab_state.speed_lever_s1 = speed_lever_f1_f2_value;
  cab_state.speed_wheel_pos = speed_wheel_pos;
  cab_state.speed_wheel_neg = speed_wheel_neg;

  // Handle throttle ticks from UDP commands
  if (enabled && throttle_ticks != 0) {
    if (throttle_ticks > 0) {
      mitm_state.speed_wheel_pos += throttle_ticks;
      mitm_state.speed_wheel_pos %= SPEED_WHEEL_OVERFLOW;
    } else {
      mitm_state.speed_wheel_neg += -throttle_ticks;
      mitm_state.speed_wheel_neg %= SPEED_WHEEL_OVERFLOW;
    }
    throttle_ticks = 0;
  }

  /**
  if (enabled) {
    msg->data[1] = mitm_state.speed_lever;
    msg->data[2] = mitm_state.speed_lever_f1_f2_value;
  } else {
    msg->data[1] = speed_lever;
    msg->data[2] = speed_lever_f1_f2_value;
  }
  */
  msg->data[3] = mitm_state.speed_wheel_pos;
  msg->data[4] = mitm_state.speed_wheel_neg;
}

void filter_log(struct zcan_frame *msg) {
  uint32_t include[] = {CPRO_JOYSTICK_ID};
  uint8_t first_byte[] = {CPRO_JOYSTICK_FIRST_BYTE};

  for (int i = 0; i < sizeof(include) / sizeof(include[0]); i++) {
    if (COMPARE_MSG_ID(msg->id, include[i]) && (msg->data[0] == first_byte[i])) {
      LOG_INF("ID: %x --- MSG: %x%x%x%x%x%x%x%x", msg->id, msg->data[0], msg->data[1], msg->data[2], msg->data[3],
              msg->data[4], msg->data[5], msg->data[6], msg->data[7]);
    }
  }
}

void add_cab_handlers() {
  switch (tractor_variant) {
  case JD_6PRO:
  case JD_8PRO:
    add_cab_mitm_handler(handle_cpro_joystick);
    break;
  case JD_6LH:
  case JD_6LHM:
  case JD_7RH:
    add_cab_mitm_handler(handle_speed_wheel_lever);
    break;
  default:
    LOG_WRN("Unknown tractor variant");
    break;
  }
}
