#ifdef CONFIG_CARBON_PTP_USE_INTERCEPT

#include "ptp_intercept.h"

static struct {
  bool active;
  struct net_if *iface;
  int (*process_ptp_packet)(struct net_pkt *pkt); // So that ptp_master and ptp_slave can each call their own handler.
} state;

void ptp_intercept_init(struct net_if *iface, void *process_ptp_packet_func_ptr) {
  state.iface = iface;
  state.process_ptp_packet = process_ptp_packet_func_ptr;
}

bool ptp_intercept_is_active(void) { return state.active; }

void ptp_intercept_set_active(bool is_active) { state.active = is_active; }

/**
 * @return -1 if not a PTP packet, 0 if packet was processed
 */
int ptp_intercept_process_packet(struct net_if *iface, struct net_pkt *pkt) {
  if (!state.active || state.iface != iface) {
    return -1; /* Not our packet */
  }

  if (ntohs(NET_ETH_HDR(pkt)->type) != NET_ETH_PTYPE_PTP) {
    return -1; /* Not a PTP packet */
  }

  (*state.process_ptp_packet)(pkt);
  net_pkt_unref(pkt);
  return 0; /* We handled the packet */
}

#endif