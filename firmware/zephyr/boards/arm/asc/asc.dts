/dts-v1/;
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

#include <zephyr/dt-bindings/adc/adc.h>
#include <mem.h>

//#include "hasselhoff-pinctrl.dtsi"

/ {
    model = "ASC Board";
    compatible = "carbon,asc";

    chosen {
		zephyr,sram = &sram0;
		zephyr,flash = &flash0;
		zephyr,dtcm = &dtcm;
		zephyr,code-partition = &slot0_partition;
    };

    aliases {
        watchdog0 = &iwdg1;
        zlcb0 = &zlcb0;
        ccan = &can1;
        tcan = &can2;
    };
    emu_eeprom: emu_eeprom {
        status = "okay";
        compatible = "zephyr,emu-eeprom";
        label = "emulated eeprom";

        size = <DT_SIZE_K(4)>;
        pagesize = <DT_SIZE_K(8)>;
        partition = <&storage_partition>;

        // shadow in RAM; also delay erase until entire block is used
        rambuf;
        partition-erase;
    };

    gpios {
        compatible = "gpio-keys";
        led_green {
            label = "LED_GREEN";
            gpios = <&gpiog 1 GPIO_ACTIVE_HIGH>;
        };
        led_blue {
            label = "LED_BLUE";
            gpios = <&gpiof 15 GPIO_ACTIVE_HIGH>;
        };
        led_red {
            label = "LED_RED";
            gpios = <&gpiog 0 GPIO_ACTIVE_HIGH>;
        };

		// Armrest connection
		can1_silent {
			label = "CAN1_SILENT";
			gpios = <&gpioa 8 GPIO_ACTIVE_HIGH>;
		};
		can2_silent {
			label = "CAN2_SILENT";
			gpios = <&gpiob 14 GPIO_ACTIVE_HIGH>;
		};

		spi3_cs {
			label = "SPI3_CS";
			gpios = <&gpioa 6 GPIO_ACTIVE_LOW>;
		};

        // Active CAN termination
        can1_120 {
            label = "CAN1_120";
            gpios = <&gpiod 10 GPIO_ACTIVE_HIGH>;
        };
        can1_60 {
            label = "CAN1_60";
            gpios = <&gpiod 11 GPIO_ACTIVE_HIGH>;
        };
        can2_120 {
            label = "CAN2_120";
            gpios = <&gpiod 12 GPIO_ACTIVE_HIGH>;
        };
        can2_60 {
            label = "CAN2_60";
            gpios = <&gpiod 13 GPIO_ACTIVE_HIGH>;
        };
    };

    soc {
    };

};

&clk_hse {
	clock-frequency = <DT_FREQ_M(8)>; /* STLink 8MHz clock */
	status = "okay";
};

&pll {
	div-m = <1>;
	mul-n = <24>;
	div-p = <2>;
	div-q = <4>;
	div-r = <2>;
	clocks = <&clk_hse>;
	status = "okay";
};

&rcc {
	clocks = <&pll>;
	clock-frequency = <DT_FREQ_M(96)>;
	d1cpre = <1>;
	hpre = <1>;
	d1ppre = <1>;
	d2ppre1 = <1>;
	d2ppre2 = <1>;
	d3ppre = <1>;
};

&mac {
	status = "okay";
	pinctrl-0 = <&eth_mdc_pc1
		     &eth_rxd0_pc4
		     &eth_rxd1_pc5
		     &eth_ref_clk_pa1
		     &eth_mdio_pa2
		     &eth_crs_dv_pa7
		     &eth_tx_en_pb11
		     &eth_txd0_pg13
		     &eth_txd1_pg12>;
	pinctrl-names = "default";
};

&rtc {
	status = "okay";
};

&rng {
	status = "okay";
};

&iwdg1 {
    status = "okay";
};

&gpioa {
    status = "okay";
};

&gpiob {
    status = "okay";
};

&gpioc {
    status = "okay";
};

&gpiod {
    status = "okay";
};

&gpioe {
    status = "okay";
};

&gpiof {
    status = "okay";
};

&gpiog {
    status = "okay";
};

// Cab CAN
&can1 {
	pinctrl-0 = <&fdcan1_rx_pa11 &fdcan1_tx_pa12>;
	pinctrl-names = "default";
	bus-speed = <500000>;
	bus-speed-data = <1000000>;
	status = "okay";
};

// Tractor CAN
&can2 {
	pinctrl-0 = <&fdcan2_rx_pb12 &fdcan2_tx_pb13>;
	pinctrl-names = "default";
	bus-speed = <500000>;
	bus-speed-data = <1000000>;
	status = "okay";
};

// FS26 Safety PMIC SPI
&spi3 {
	// PA6 is the chip select for the FS26
	status = "okay";
	pinctrl-0 = <&spi3_sck_pc10 &spi3_miso_pc11 &spi3_mosi_pb2>;
	pinctrl-names = "default";
	clock-frequency = <1000000>;
	cs-gpios = <&gpioa 6 (GPIO_ACTIVE_LOW | GPIO_PULL_UP)>;

	gendev: gendev@0 {
			compatible = "vnd,spi-device";
			reg = <0>;
			spi-max-frequency = <1600000>;
			label = "GenDev";
	};
};


/**
&timers1 {
    status = "okay";
    st,prescaler = <1199>;
    combo_pwm1: combo_pwm {
        compatible = "st,stm32-combo-pwm";
        status = "okay";
        label = "PWM1";
        pinctrl-0 = <&tim1_ch1_pe9>;
        pinctrl-names = "default";
        sync = "MASTER";
        #pwm-cells = <2>;
    };
};
*/


/**
&pinctrl {
    tim1_ch1_pe9: tim1_ch1_pe9 {
        pins {
            pinmux = <STM32_PINMUX('E', 8, AF1)>;
            bias-disable;
            drive-push-pull;
            slew-rate = "very-high-speed";
        };
    };
};
*/

&timers8 {
    status = "okay";
    st,prescaler = <119>;
    zlcb0: zlcb {
        compatible = "st,stm32-zlcb";
        status = "okay";
        label = "ZLCB0";
    };
};

&flash0 {
    partitions {
        compatible = "fixed-partitions";
        #address-cells = <1>;
        #size-cells = <1>;

        // bootloader
        /*
        boot_partition: partition@0 {
            label = "mcuboot";
            reg = <0x00000000 DT_SIZE_K(256)>;
            read-only;
        };
        */

        // priamry app (640K)
        slot0_partition: partition@0 {
            label = "image-0";
            reg = <0x00000000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // backup app (640K)
        slot1_partition: partition@100000 {
            label = "image-1";
            reg = <0x00100000 DT_SIZE_K(640)>;
        };

        // (128K unused)

        // EEPROM emulation (128K)
        storage_partition: partition@1c0000 {
            label = "storage";
            reg = <0x001c0000 DT_SIZE_K(128)>;
        };

        // swap slot (128K)
        scratch_partition: partition@1e0000 {
            label = "image-scratch";
            reg = <0x001e0000 DT_SIZE_K(128)>;
        };
    };
};