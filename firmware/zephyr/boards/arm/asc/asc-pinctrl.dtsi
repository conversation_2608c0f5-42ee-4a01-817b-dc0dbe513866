///dts-v1/;
//#include <dt-bindings/pinctrl/gd32f450z(e-g-i-k)xx-pinctrl.h>
#include <st/h7/stm32h753Xi.dtsi>
#include <st/h7/stm32h753zitx-pinctrl.dtsi>

&pinctrl {
    can1_default: can1_default {
        group1 {
            pinmux = <CAN1_RX_PA11>, <CAN1_TX_PA12>;
        };
    };

    can2_default: can2_default {
        group1 {
            pinmux = <CAN2_RX_PB12>, <CAN2_TX_PB13>;
        };
    };

    spi3_default: spi3_default {
        group1 {
            pinmux = <SPI3_NSS_PA6>, <SPI3_MISO_PC11>,
                <SPI3_MOSI_PB2>, <SPI3_SCK_PC10>;
        };
    };

    enet_default: enet_default {
        group1 {
            pinmux = <ETH_RMII_REF_CLK_PA1>, <ETH_MDIO_PA2>,
                 <ETH_RMII_CRS_DV_PA7>, <ETH_MDC_PC1>,
                 <ETH_RMII_RXD0_PC4>, <ETH_RMII_RXD1_PC5>,
                 <ETH_RMII_TX_EN_PB11>, <ETH_RMII_TXD0_PG13>,
                 <ETH_RMII_TXD1_PG12>;
        };
    };

    /**
    pwm0_default: pwm0_default {
        group1 {
            pinmux = <TIMER0_CH0_PA8>;
        };
    };
    */
};
