/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

/*
 * epos.h and epos.c are only meant to provide aliases fop the firmware spec:
 * https://www.maxongroup.com/medias/sys_master/root/8834324856862/EPOS4-Firmware-Specification-En.pdf
 */

#pragma once

#include "can_open.h"
#include "stdint.h"
#include <utils/carbon_response_codes.h>

#define EPOS_STATUS_REFERENCE_TO_HOME 0x8000

#define EPOS_CONTROL_ENDLESS_MOVEMENT 0x8000

// **** Epos Configuration ****
static inline CARBON_RESPONSE_CODE EPOS_Read_Power_Supply_Voltage(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x2200, 0x01);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Sensor_Resolution(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3000, 0x05);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Motor_Nominal_Current(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3001, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Motor_Output_Current_Limit(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3001, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Digital_Encoder_Pulse_Resolution(uint8_t node_id,
                                                                              CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3010, 0x01);
} // uint32 return value

// **** PID Configuration ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Current_Control_P(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A0, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Current_Control_I(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A0, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Position_Control_P(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A1, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Position_Control_I(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A1, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Position_Control_D(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A1, 0x03, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Position_Control_FFV(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A1, 0x04, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Position_Control_FFA(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A1, 0x05, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Velocity_Control_P(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A2, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Velocity_Control_I(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A2, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Velocity_Control_FFV(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A2, 0x03, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Velocity_Control_FFA(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A2, 0x04, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Current_Control_P(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A0, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Current_Control_I(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A0, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Control_P(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A1, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Control_I(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A1, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Control_D(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A1, 0x03);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Control_FFV(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A1, 0x04);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Control_FFA(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A1, 0x05);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Control_P(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A2, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Control_I(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A2, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Control_FFV(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A2, 0x03);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Control_FFA(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A2, 0x04);
} // uint32 return value

// **** Velocity Observer ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Observer_Position_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A3, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Observer_Velocity_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A3, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Observer_Load_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A3, 0x03, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Observer_Friction(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A3, 0x04, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Observer_Inertia(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30A3, 0x05, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Observer_Position_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A3, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Observer_Velocity_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A3, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Observer_Load_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A3, 0x03);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Observer_Friction(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A3, 0x04);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Observer_Inertia(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30A3, 0x05);
} // uint32 return value

// **** Dual Loop Configuration ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_P_Gain_Low(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_P_Gain_High(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Scheduling_Weight(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x03, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Filter_A(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x10, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Filter_B(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x11, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Filter_C(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x12, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Filter_D(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x13, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Main_Loop_Filter_E(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x14, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_P_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x20, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_I_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x21, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_FFV_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x22, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_FFA_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x23, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_Position_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x30, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_Velocity_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x31, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_Load_Gain(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x32, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_Friction(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x33, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Aux_Loop_Inertia(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30AE, 0x34, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_P_Gain_Low(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_P_Gain_HIGH(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x02);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Scheduling_Weight(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x03);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Filter_A(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x10);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Filter_B(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x11);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Filter_C(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x12);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Filter_D(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x13);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Main_Loop_Filter_E(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x14);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_P_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x20);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_I_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x21);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_FFV_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x22);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_FFA_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x23);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_Position_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x30);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_Velocity_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x31);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_Load_Gain(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x32);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_Friction(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x33);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Aux_Loop_Inertia(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30AE, 0x34);
} // uint32 return value

// **** Measurement ****
static inline CARBON_RESPONSE_CODE EPOS_Read_Current_Demand(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30D0, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Current_Average(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30D1, 0x01);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Current_Actual(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30D1, 0x02);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Torque_Average(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30D2, 0x01);
} // int16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Average(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30D3, 0x01);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_I2T_Level_Motor(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3200, 0x01);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_I2T_Level_Controller(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3200, 0x02);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Temperature_Power_Stage(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3201, 0x01);
} // int16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Demand(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6062, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Position_Actual(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6064, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Demand(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x606B, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Velocity_Actual(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x606C, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Target_Torque(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6071, 0x00);
} // int16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Motor_Rated_Torque(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6076, 0x00);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Torque_Actual(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x6077, 0x00);
} // int16 return value

// **** Standstill Configuration ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Standstill_Window(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30E0, 0x01, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Standstill_Window_Time(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x30E0, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Standstill_Window_Timeout(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x30E0, 0x03, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Standstill_Window(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30E0, 0x01);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Standstill_Window_Time(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30E0, 0x02);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Standstill_Window_Timeout(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30E0, 0x03);
} // uint16 return value

// **** Digital Inputs ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Digital_Input_Polarity(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x3141, 0x02, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Digital_Input_Config(uint8_t node_id, uint8_t digital_input_id,
                                                                   uint8_t value) {
  return Send_SDO_Download(node_id, 0x3141, digital_input_id, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Digital_Input_State(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3141, 0x01);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Digital_Input_State_Polarized(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x60FD, 0x00);
} // uint32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Digital_Input_Polarity(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3141, 0x02);
} // uint16 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Digital_Input_Config(uint8_t node_id, CAN_Open_Message_t *msg,
                                                                  uint8_t digital_input_id) {
  return Send_SDO_Upload_Request(node_id, msg, 0x3141, digital_input_id);
} // uint8 return value

#define EPOS_DIGITAL_INPUT_HS_4 0x80
#define EPOS_DIGITAL_INPUT_HS_3 0x40
#define EPOS_DIGITAL_INPUT_HS_2 0x20
#define EPOS_DIGITAL_INPUT_HS_1 0x10
#define EPOS_DIGITAL_INPUT_4 0x08
#define EPOS_DIGITAL_INPUT_3 0x04
#define EPOS_DIGITAL_INPUT_2 0x02
#define EPOS_DIGITAL_INPUT_1 0x01

#define EPOS_DIGITAL_INPUT_ID_HS_4 8
#define EPOS_DIGITAL_INPUT_ID_HS_3 7
#define EPOS_DIGITAL_INPUT_ID_HS_2 6
#define EPOS_DIGITAL_INPUT_ID_HS_1 5
#define EPOS_DIGITAL_INPUT_ID_4 4
#define EPOS_DIGITAL_INPUT_ID_3 3
#define EPOS_DIGITAL_INPUT_ID_2 2
#define EPOS_DIGITAL_INPUT_ID_1 1

#define EPOS_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH 0
#define EPOS_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH 1
#define EPOS_DIGITAL_INPUT_HOME_SWITCH 2
#define EPOS_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH_NO_ERRORS 24
#define EPOS_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH_NO_ERRORS 25
#define EPOS_DIGITAL_INPUT_DRIVE_ENABLE 27
#define EPOS_DIGITAL_INPUT_QUICK_STOP 28
#define EPOS_DIGITAL_INPUT_NONE 255

#define EPOS_DI_QUICK_STOP (1 << EPOS_DIGITAL_INPUT_QUICK_STOP)
#define EPOS_DI_DRIVE_ENABLE (1 << EPOS_DIGITAL_INPUT_DRIVE_ENABLE)
#define EPOS_DI_POSITIVE_LIMIT_SWITCH_NO_ERROR (1 << EPOS_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH_NO_ERRORS)
#define EPOS_DI_NEGATIVE_LIMIT_SWITCH_NO_ERROR (1 << EPOS_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH_NO_ERRORS)
#define EPOS_DI_HOME_SWITCH (1 << EPOS_DIGITAL_INPUT_HOME_SWITCH)
#define EPOS_DI_POSITIVE_LIMIT_SWITCH (1 << EPOS_DIGITAL_INPUT_POSITIVE_LIMIT_SWITCH)
#define EPOS_DI_NEGATIVE_LIMIT_SWITCH (1 << EPOS_DIGITAL_INPUT_NEGATIVE_LIMIT_SWITCH)

// **** Epos Errors ****
#define EPOS_ERROR_MOTION 0x80
#define EPOS_ERROR_DEVICE 0x20
#define EPOS_ERROR_COMMUNICATION 0x10
#define EPOS_ERROR_TEMPERATURE 0x08
#define EPOS_ERROR_VOLTAGE 0x04
#define EPOS_ERROR_CURRENT 0x02
#define EPOS_ERROR_GENERIC 0x01

// **** Homing ****
static inline CARBON_RESPONSE_CODE EPOS_Write_Home_Position(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x30B0, 0x00, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Home_Offset(uint8_t node_id,
                                                          uint32_t value) { // Different than cia402 standard
  return Send_SDO_Download(node_id, 0x30B1, 0x00, value);
}
static inline CARBON_RESPONSE_CODE EPOS_Write_Homing_Current_Threshold(uint8_t node_id, uint16_t value) {
  return Send_SDO_Download(node_id, 0x30B2, 0x00, value);
}

static inline CARBON_RESPONSE_CODE EPOS_Read_Home_Position(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30B0, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Home_Offset(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30B1, 0x00);
} // int32 return value
static inline CARBON_RESPONSE_CODE EPOS_Read_Homing_Current_Threshold(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x30B2, 0x00);
} // uint16 return value

// **** Units ****

#define EPOS_Velocity_RPM 0x00B44700
#define EPOS_Velocity_DECI_RPM 0xFFB44700
#define EPOS_Velocity_CENTI_RPM 0xFEB44700
#define EPOS_Velocity_MILLI_RPM 0xFDB44700
#define EPOS_Velocity_M4_RPM 0xFCB44700
#define EPOS_Velocity_M5_RPM 0xFBB44700
#define EPOS_Velocity_MICRO_RPM 0xFAB44700