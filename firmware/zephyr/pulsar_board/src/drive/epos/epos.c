/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#include "epos.h"

const uint8_t EPOS_SAVE_PARAM_VALUE[4] = {'s', 'a', 'v', 'e'};
const uint8_t EPOS_RESTORE_PARAM_VALUE[4] = {'l', 'o', 'a', 'd'};
const uint32_t *EPOS_SAVE_PARAM_VALUE_POINTER = (uint32_t *)&EPOS_SAVE_PARAM_VALUE;
const uint32_t *EPOS_RESTORE_PARAM_VALUE_POINTER = (uint32_t *)&EPOS_RESTORE_PARAM_VALUE;

/* [] END OF FILE */
