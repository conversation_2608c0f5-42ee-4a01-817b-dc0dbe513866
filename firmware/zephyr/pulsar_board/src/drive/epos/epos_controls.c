/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#ifdef USE_MAXON
#include <logging/log.h>
LOG_MODULE_REGISTER(epos_controls, CONFIG_APP_LOG_LEVEL);

#include "can_open.h"
#include "can_open_msg.h"
#include "cia_402.h"
#include "eeprom.h"
#include "epos.h"
#include "epos_controls.h"
#include "gimbal_handler.h"
#include "qdec.h"
#include <stdlib.h>
#include <string.h>
#include <zephyr.h>

#define EPOS_DEFAULT_HOMING_CURRENT 1000

static CARBON_RESPONSE_CODE EPOS_Hard_Stop_Home_Imp(uint8_t node_id, int16_t step_size, uint16_t offset,
                                                    int32_t min_position, int32_t max_position,
                                                    uint32_t profile_velocity, uint16_t settle_timeout_ms,
                                                    int32_t *out_limit, uint8_t enc_id, bool can_retry) {
  // Set Homing Mode
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_HOMING_MODE))
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Home_Offset(node_id, offset))
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Homing_Current_Threshold(node_id, EPOS_DEFAULT_HOMING_CURRENT))

  CARBON_RESPONSE_CODE resp = MC_get()->Home_With_Method(node_id, CIA402_HOMING_METHOD_CURRENT_POSITIVE_SPEED);
  if (resp != CARBON_RESPONSE_OK) {
    if (can_retry) {
      LOG_INF("Attempting to move back and re-home");
      RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                                   CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                                   CIA402_CONTROL_QUICK_STOP));
      RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_PROFILE_POSITION_MODE))
      int32_t position;
      RETURN_CODE_IF_NOT_OK(MC_get()->Get_Actual_Position_Velocity(node_id, &position, NULL))
      position -= 1000;
      RETURN_CODE_IF_NOT_OK(MC_get()->Go_To_Position(node_id, position, profile_velocity))
      k_sleep(K_MSEC(50)); // Give time to settle
      // try again here
      return EPOS_Hard_Stop_Home_Imp(node_id, step_size, offset, min_position, max_position, profile_velocity,
                                     settle_timeout_ms, out_limit, enc_id, false);
    } else {
      LOG_INF("Homing failed err %d", resp);
      return resp;
    }
  }

  // Stop Movement
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));
  int64_t endstop = qdec_get_latest(enc_id);
  // Actual Homing
  RETURN_CODE_IF_NOT_OK(MC_get()->Home_With_Method(node_id, CIA402_HOMING_METHOD_CURRENT_NEGATIVE_SPEED))

  // Stop Movement
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));

  int64_t home = qdec_get_latest(enc_id);
  *out_limit = abs((int32_t)(endstop - home));
  if (*out_limit < 3 * offset) {
    return CARBON_EPOS_HARD_LIMITS_RANGE_TOO_SMALL;
  }
  *out_limit -= (2 * offset);
  // Profile Position Mode Ready
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_PROFILE_POSITION_MODE))

  return CARBON_RESPONSE_OK;
}
CARBON_RESPONSE_CODE EPOS_Hard_Stop_Home(uint8_t node_id, int16_t step_size, uint16_t offset, int32_t min_position,
                                         int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms,
                                         int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale) {

  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Switch_Search_Speed(node_id, profile_velocity))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Zero_Search_Speed(node_id, profile_velocity))
  return EPOS_Hard_Stop_Home_Imp(node_id, step_size, offset, min_position, max_position, profile_velocity,
                                 settle_timeout_ms, out_limit, enc_id, true);
}

CARBON_RESPONSE_CODE EPOS_set_pid(uint8_t node_id, pid_config *cfg) {
  if (cfg->gain_p == 0) {
    // Do not send invalid pid
    return CARBON_RESPONSE_OK;
  }
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Current_Control_P(node_id, cfg->current_p));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Current_Control_I(node_id, cfg->current_i));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Position_Control_P(node_id, cfg->gain_p));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Position_Control_I(node_id, cfg->gain_i));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Position_Control_D(node_id, cfg->gain_d));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Position_Control_FFA(node_id, cfg->gain_ffa));
  RETURN_CODE_IF_NOT_OK(EPOS_Write_Position_Control_FFV(node_id, cfg->gain_ffv));
  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE EPOS_handle_pid(uint8_t node_id, epos_Set_PID_V2_Request *request) {
  if (request->which_pid != epos_Set_PID_V2_Request_epos_tag) {
    return CARBON_EPOS_NOT_SUPPORTED;
  }
  pid_config *cfg = gimbal_get_pid_cfg(node_id);
  if (cfg == NULL) {
    return CARBON_EPOS_ERROR_UNKNOWN;
  }
  if (request->type != epos_PID_Request_Type_PID_REQUEST_FALLBACK || cfg->gain_p == 0) {
    cfg->gain_p = request->pid.epos.gain_p;
    cfg->gain_i = request->pid.epos.gain_i;
    cfg->gain_d = request->pid.epos.gain_d;
    cfg->gain_ffa = request->pid.epos.gain_ffa;
    cfg->gain_ffv = request->pid.epos.gain_ffv;
    cfg->current_p = request->pid.epos.current_p;
    cfg->current_i = request->pid.epos.current_i;
  }
  if (gimbal_servo_booted(node_id)) {
    RETURN_CODE_IF_NOT_OK(EPOS_set_pid(node_id, cfg))
  }
  if (request->type == epos_PID_Request_Type_PID_REQUEST_SAVE) {
    if (eeprom_save_pid(node_id, cfg) != 0) {
      return CARBON_EPOS_ERROR_UNKNOWN;
    }
  }
  return CARBON_RESPONSE_OK;
}
CARBON_RESPONSE_CODE EPOS_handle_get_pid(uint8_t node_id, epos_Get_PID_Reply *reply) {
  pid_config *cfg = gimbal_get_pid_cfg(node_id);
  if (cfg == NULL) {
    return CARBON_ERROR_UNKNOWN;
  }
  reply->which_pid = epos_Get_PID_Reply_epos_tag;
  if (cfg->gain_p == 0) {
    reply->valid = false;
    return CARBON_RESPONSE_OK;
  }
  reply->valid = true;
  reply->pid.epos.gain_p = cfg->gain_p;
  reply->pid.epos.gain_i = cfg->gain_i;
  reply->pid.epos.gain_d = cfg->gain_d;
  reply->pid.epos.gain_ffa = cfg->gain_ffa;
  reply->pid.epos.gain_ffv = cfg->gain_ffv;
  reply->pid.epos.current_p = cfg->current_p;
  reply->pid.epos.current_i = cfg->current_i;
  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE EPOS_Actual_Position_Home(uint8_t node_id, uint16_t range, uint32_t profile_velocity,
                                               int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale) {
  // TODO implement me
  return CARBON_RESPONSE_OK;
}

#endif