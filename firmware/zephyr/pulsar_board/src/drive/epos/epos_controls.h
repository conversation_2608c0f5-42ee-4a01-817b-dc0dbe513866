/* ========================================
 *
 * Maka ARS, 2020
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#pragma once
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"
#include "motion_controller.h"
#include <utils/carbon_response_codes.h>
struct __attribute__((packed)) pid_config_s {
  uint32_t gain_p;
  uint32_t gain_i;
  uint32_t gain_d;
  uint32_t gain_ffa;
  uint32_t gain_ffv;
  uint32_t current_p;
  uint32_t current_i;
};
#define DEFAULT_PID                                                                                                    \
  { .gain_p = 0, .gain_i = 0, .gain_d = 0, .gain_ffa = 0, .gain_ffv = 0, .current_p = 0, .current_i = 0 }

CARBON_RESPONSE_CODE EPOS_Actual_Position_Home(uint8_t node_id, uint16_t range, uint32_t profile_velocity,
                                               int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);
CARBON_RESPONSE_CODE EPOS_Hard_Stop_Home(uint8_t node_id, int16_t step_size, uint16_t offset, int32_t min_position,
                                         int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms,
                                         int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);

CARBON_RESPONSE_CODE EPOS_set_pid(uint8_t node_id, pid_config *cfg);
CARBON_RESPONSE_CODE EPOS_handle_pid(uint8_t node_id, epos_Set_PID_V2_Request *request);
CARBON_RESPONSE_CODE EPOS_handle_get_pid(uint8_t node_id, epos_Get_PID_Reply *reply);