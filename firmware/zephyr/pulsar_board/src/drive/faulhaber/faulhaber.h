/* ========================================
 *
 * Carbon Robotics, 2022
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#pragma once

#include "can_open.h"
#include "stdint.h"
#include <utils/carbon_response_codes.h>

#define FH_POLARITY_INVERTED (0x00)
#define FH_POLARITY_NORMAL (0xC0)

/// SDO index for the voltage monitor object (in 10mV units)
#define FH_SDO_VOLTAGE_MONITOR (0x2325)
/// Electronics supply voltage subindex
#define FH_SDO_VOLTAGE_MONITOR_ELECTRONICS (0x06)
/// Motor supply voltage subindex
#define FH_SDO_VOLTAGE_MONITOR_MOTOR (0x07)

/// SDO index for device temperature object (in °C)
#define FH_SDO_DEVICE_TEMP (0x2326)
/// CPU temperature subindex
#define FH_SDO_DEVICE_TEMP_CPU (0x01)
/// Output/power stage temperature subindex
#define FH_SDO_DEVICE_TEMP_OUTPUT (0x02)
/// Windings temperature subindex (from model)
#define FH_SDO_DEVICE_TEMP_WINDINGS (0x03)

/// Device rating object
#define FH_SDO_DEVICE_RATINGS (0x2327)
/// Nominal (continuous) current rating
#define FH_SDO_DEVICE_RATINGS_I_CONTINUOUS (0x01)
/// Peak current rating
#define FH_SDO_DEVICE_RATINGS_I_PEAK (0x02)
/// Rated motor supply voltage
#define FH_SDO_DEVICE_RATINGS_VOLTAGE (0x03)

/// Motor thermal model object
#define FH_SDO_MOTOR_THERMAL_MODEL (0x232A)
/// Motor ambient temperature (in °C, u8)
#define FH_SDO_MOTOR_THERMAL_MODEL_AMBIENT_TEMP (0x08)

/// Actual motor state values
#define FH_SDO_ACTUAL (0x2360)
/// Motor DC current (I_DC)
#define FH_SDO_ACTUAL_MOTOR_IDC (0x04)

static inline CARBON_RESPONSE_CODE FH_Read_Behaviour_Configuration(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x233F, 0x00);
}
static inline CARBON_RESPONSE_CODE FH_Write_Behavior_Configuration(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x233F, 0x00, value);
}
static inline CARBON_RESPONSE_CODE FH_Write_Home_Torque_Limit_Positive(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x2350, 0x00, value);
}
static inline CARBON_RESPONSE_CODE FH_Write_Home_Torque_Limit_Negative(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x2351, 0x00, value);
}
static inline CARBON_RESPONSE_CODE FH_Write_Velocity_Control_KP(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x2344, 0x01, value);
}
static inline CARBON_RESPONSE_CODE FH_Write_Position_Setpoint(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x607A, 0x00, value);
}
static inline CARBON_RESPONSE_CODE FH_Read_Velocity_Control_KP(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x2344, 0x01);
}
static inline CARBON_RESPONSE_CODE FH_Write_Polarity(uint8_t node_id, uint32_t value) {
  return Send_SDO_Download(node_id, 0x607E, 0x00, value);
}
static inline CARBON_RESPONSE_CODE FH_Read_Polarity(uint8_t node_id, CAN_Open_Message_t *msg) {
  return Send_SDO_Upload_Request(node_id, msg, 0x607E, 0x00);
}
static inline CARBON_RESPONSE_CODE FH_Write_Home_Limit_Check_Time(const uint8_t node_id, const uint16_t value) {
  return Send_SDO_Download(node_id, 0x2324, 0x02, value);
}
static inline CARBON_RESPONSE_CODE FH_Write_Motor_Ambient_Temp(const uint8_t node_id, const uint8_t deg_c) {
  return Send_SDO_Download(node_id, FH_SDO_MOTOR_THERMAL_MODEL, FH_SDO_MOTOR_THERMAL_MODEL_AMBIENT_TEMP, deg_c);
}
