#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(fh_ambient, CONFIG_APP_LOG_LEVEL);

#include <zephyr/drivers/sensor.h>
#include <zephyr/kernel.h>

#include <utils/handle_errors.h>

#include <math.h>

#include "generated/lib/drivers/nanopb/proto/pulczar_board.pb.h"

#if USE_REAPER
#include "laser/reaper.h"
#endif

#include "ambient_temp.h"
#include "can_open.h"
#include "drive/faulhaber/faulhaber.h"
#include "laser.h"
#include "nanopb_server.h"

/// Minimum valid thermistor temperature
#define THERM_MIN (5.f)
/// Maximum valid thermistor temperature
#define THERM_MAX (50.f)
/// Temperature offset to subtract from die temp to estimate ambient
#define DIE_AMBIENT_OFFSET (5.f)

/**
 * @brief How often the ambient temperature is updated to drives
 *
 * This is when the ambient temperature is sampled (from thermistors) and written to both drives
 * for their protection models.
 */
#define AMBIENT_UPDATE_INTERVAL K_SECONDS(30)

/**
 * @brief Delay for initial update when enabling
 *
 * If the ambient temperature reporting feature is just being enabled, how long to wait before the
 * first update.
 */
#define AMBIENT_INITIAL_INTERVAL K_MSEC(250)

static void ambient_update_timer_expired(struct k_timer *);

static void handle_enable_changed(const bool isEnabled);

static int get_die_temp(float *outTempC);
static int get_ambient_temp(float *outTempC);
static int get_ambient_temp_thermistor(float *outTempC);

static int handle_set_config(const pulczar_AmbientTempConfig *newConfig);
static int handle_get_status(pulczar_AmbientTempReply *reply);

// from sensors.c
extern int sensors_request_ambient_update();

static __dtcm_bss_section struct {
  struct k_mutex lock;

  // is reporting to the FHs enabled?
  bool enabled;
  // use the (statically set) override temp instead of thermistors
  bool useOverride;

  // temperature to use instead of the thermistors
  float overrideTemp;
  // last temp value written to FHs
  float lastTemp;
  // TODO: offset for temperature
} gFhAmbientState;

// Triggers periodic updating
K_TIMER_DEFINE(gAmbientUpdateTimer, ambient_update_timer_expired, NULL);

static const struct device *gDieTemp = DEVICE_DT_GET_OR_NULL(DT_ALIAS(die_temp));

/**
 * @brief Initialize the ambient temperature reporting feature
 */
int fh_ambient_init() {
  k_mutex_init(&gFhAmbientState.lock);

  if (gDieTemp) {
    HANDLE_UNLIKELY_BOOL(device_is_ready(gDieTemp), ENODEV);
  } else {
    LOG_INF("No die temp sensor!");
  }

  return 0;
}

/**
 * @brief Update ambient temperature on motor drives
 *
 * Read the current values for the ambient temperature and apply them to the drives.
 */
int fh_ambient_update() {
  int err = 0;
  float temp = NAN;

  HANDLE_UNLIKELY(k_mutex_lock(&gFhAmbientState.lock, K_FOREVER));
  {
    // bail if not enabled
    if (!gFhAmbientState.enabled) {
      return 0;
    }

    // get current temp based on config
    err = get_ambient_temp(&temp);
    if (err) {
      LOG_WRN("%s failed: %d", "get_ambient_temp", err);
    } else {
      LOG_INF("Ambient temp: %g C", temp);
    }
  }
  k_mutex_unlock(&gFhAmbientState.lock);

  if (err) {
    return err;
  } else {
    gFhAmbientState.lastTemp = temp;
  }

  // apply to drives
  if (temp > UINT8_MAX || temp <= 0) {
    LOG_WRN("preposterous ambient temp: %g", temp);
    return -ERANGE;
  }

  const uint8_t temp_c = (uint8_t)temp;

  HANDLE_UNLIKELY(FH_Write_Motor_Ambient_Temp(NODE_ID_PAN_SERVO, temp_c));
  HANDLE_UNLIKELY(FH_Write_Motor_Ambient_Temp(NODE_ID_TILT_SERVO, temp_c));

  return 0;
}

/**
 * @brief Process an ambient temperature update request
 */
void fh_ambient_handle_nanopb(npb_request_data *req) {
  int err = 0;

  // prepare reply
  pulczar_board_Reply reply = pulczar_board_Reply_init_zero;

  reply.has_header = true;
  reply.header.requestId = req->request.header.requestId;
  reply.which_reply = pulczar_board_Reply_pulczar_tag;
  reply.reply.pulczar.which_reply = pulczar_Reply_ack_tag;

  // get request
  const pulczar_AmbientTempRequest *request = &req->request.request.pulczar.request.ambient_temp;
  pulczar_AmbientTempReply *ambient_reply = &reply.reply.pulczar.reply.ambient_temp;

  switch (request->which_request) {
  case pulczar_AmbientTempRequest_set_config_tag:
    err = handle_set_config(&request->request.set_config);
    break;

  case pulczar_AmbientTempRequest_get_config_tag:
    reply.reply.pulczar.which_reply = pulczar_Reply_ambient_temp_tag;
    err = handle_get_status(ambient_reply);
    break;

  default:
    LOG_WRN("unknown request %u", request->which_request);
    return;
  }

  if (err) {
    LOG_WRN("request %u failed: %d", request->which_request, err);
    return;
  }

  // send reply message
  npb_send_reply(&reply, &req->metadata);
}

/**
 * @brief Update the periodic background tasks as enabled state changes
 */
static void handle_enable_changed(const bool isEnabled) {
  if (isEnabled) {
    k_timer_start(&gAmbientUpdateTimer, AMBIENT_INITIAL_INTERVAL, AMBIENT_UPDATE_INTERVAL);
  } else {
    k_timer_stop(&gAmbientUpdateTimer);
  }
}

/**
 * @brief Get ambient temperature for MCs
 *
 * Read thermistor or use a fixed value sent via the config.
 *
 * @remark Caller should hold lock over the state structure
 */
static int get_ambient_temp(float *outTempC) {
  if (!outTempC) {
    return -EFAULT;
  }

  // use fixed temperature value
  if (gFhAmbientState.useOverride) {
    *outTempC = gFhAmbientState.overrideTemp;
    return 0;
  }
  // read thermistors
  else {
    return get_ambient_temp_thermistor(outTempC);
  }
}

/**
 * @brief Determine the ambient temperature of the system
 *
 * Attempts to use a thermistor (for Slayer, the laser power meter's ambient sensor; for Reaper,
 * the lower of either the fibre or collimator thermistor) to determine the system ambient
 * tenperature.
 *
 * Sanity check against the die temperature sensor.
 */
static int get_ambient_temp_thermistor(float *outTempC) {
  float ambient = NAN, die = NAN;

  if (!outTempC) {
    return -EFAULT;
  }

  // read the MCU die temp sensor if it's present
  if (gDieTemp) {
    HANDLE_UNLIKELY(get_die_temp(&die));
    LOG_DBG("Die temp: %g C", die);
  }

#if USE_REAPER
  float collimator = 0, fibre = 0;
  HANDLE_UNLIKELY(laser_get_reaper_therm(&collimator, &fibre));

  if (collimator < THERM_MIN || collimator > THERM_MAX) {
    LOG_WRN("%s thermistor is invalid (%g C)", "collimator", collimator);
    collimator = NAN;
  }
  if (fibre < THERM_MIN || fibre > THERM_MAX) {
    LOG_WRN("%s thermistor is invalid (%g C)", "fibre", fibre);
    fibre = NAN;
  }

  if (!isnan(collimator) && !isnan(fibre)) {
    ambient = fminf(collimator, fibre);
  } else if (!isnan(collimator)) {
    ambient = collimator;
  } else if (!isnan(fibre)) {
    ambient = fibre;
  }
#else
  HANDLE_UNLIKELY(laser_get_slayer_therm(&ambient, NULL));
  // TODO: extra sanity checking for this - read both?
#endif

  // sanity checking/fallback with die temp, if available
  if (!isnan(die)) {
    // if reading sensors gave invalid readings, fall back to die sensor
    if (isnan(ambient)) {
      ambient = die - DIE_AMBIENT_OFFSET;
      LOG_WRN("Invalid ambient temp, using MCU die temp (%g C)", ambient);
    }
    // otherwise, sanity check against die temp sensor
    else {
      // TODO: implement this - check so thermistor is within 15 degC?
    }
  }

  // failed to read ambient temp (wtf)
  if (isnan(ambient)) {
    return -ENXIO;
  }

  *outTempC = ambient;

  return 0;
}

/**
 * @brief Periodic timer to update the ambient temp expired
 *
 * This will pend a message onto the sensor worker thread's work queue, which will then call back
 * into our work handler
 */
static void ambient_update_timer_expired(struct k_timer *) { sensors_request_ambient_update(); }

/**
 * @brief Update the configuration of the ambient subsystem
 */
static int handle_set_config(const pulczar_AmbientTempConfig *newConfig) {
  bool oldEnabled, forceUpdate = false;

  // validate the input args
  if (!newConfig) {
    return -EFAULT;
  }

  if (!newConfig->use_thermistor) {
    if (newConfig->_temp.temp > UINT8_MAX || newConfig->_temp.temp < 0) {
      return -EINVAL;
    }
  }

  // apply the config
  HANDLE_UNLIKELY(k_mutex_lock(&gFhAmbientState.lock, K_FOREVER));
  {
    oldEnabled = gFhAmbientState.enabled;

    gFhAmbientState.enabled = newConfig->enabled;
    gFhAmbientState.useOverride = !newConfig->use_thermistor;
    if (!newConfig->use_thermistor && newConfig->which__temp) {
      gFhAmbientState.overrideTemp = newConfig->_temp.temp;
      forceUpdate = true;
    }
  }
  k_mutex_unlock(&gFhAmbientState.lock);

  // handle state changes
  if (oldEnabled != newConfig->enabled) {
    handle_enable_changed(newConfig->enabled);
  }
  if (newConfig->enabled && forceUpdate) {
    HANDLE_UNLIKELY(sensors_request_ambient_update());
  }

  return 0;
}

/**
 * @brief Get status and current config
 */
static int handle_get_status(pulczar_AmbientTempReply *reply) {
  if (!reply) {
    return -EFAULT;
  }

  HANDLE_UNLIKELY(k_mutex_lock(&gFhAmbientState.lock, K_FOREVER));
  {
    reply->sense_temp = gFhAmbientState.lastTemp;
    reply->config.enabled = gFhAmbientState.enabled;
    reply->config.use_thermistor = !gFhAmbientState.useOverride;
    if (gFhAmbientState.useOverride) {
      reply->config.which__temp = pulczar_AmbientTempConfig_temp_tag;
      reply->config._temp.temp = gFhAmbientState.overrideTemp;
    }
  }
  k_mutex_unlock(&gFhAmbientState.lock);

  return 0;
}

/**
 * @brief Read out the die temperature sensor
 */
static int get_die_temp(float *outTempC) {
  struct sensor_value val;

  if (!outTempC) {
    return -EFAULT;
  } else if (!gDieTemp) {
    return -ENODEV;
  }

  HANDLE_UNLIKELY(sensor_sample_fetch(gDieTemp));
  HANDLE_UNLIKELY(sensor_channel_get(gDieTemp, SENSOR_CHAN_DIE_TEMP, &val));

  *outTempC = (float)sensor_value_to_double(&val);

  return 0;
}
