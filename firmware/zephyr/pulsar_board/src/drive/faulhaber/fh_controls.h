/* ========================================
 *
 * Carbon Robotics, 2022
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */

#pragma once
#include "generated/lib/drivers/nanopb/proto/epos.pb.h"
#include "motion_controller.h"
#include <utils/carbon_response_codes.h>
struct __attribute__((packed)) pid_config_s {};
#define DEFAULT_PID                                                                                                    \
  {}

CARBON_RESPONSE_CODE FH_Actual_Position_Home(uint8_t node_id, uint16_t range, uint32_t profile_velocity,
                                             int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);
CARBON_RESPONSE_CODE FH_Hard_Stop_Home(uint8_t node_id, int16_t step_size, uint16_t offset, int32_t min_position,
                                       int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms,
                                       int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale);

CARBON_RESPONSE_CODE FH_set_pid(uint8_t node_id, pid_config *cfg);
CARBON_RESPONSE_CODE FH_handle_pid(uint8_t node_id, epos_Set_PID_V2_Request *request);
CARBON_RESPONSE_CODE FH_handle_get_pid(uint8_t node_id, epos_Get_PID_Reply *reply);
CARBON_RESPONSE_CODE FH_set_inverted(uint8_t node_id, uint8_t inverted);