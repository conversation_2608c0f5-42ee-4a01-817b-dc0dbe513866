/**
 * @file
 *
 * @brief <PERSON><PERSON><PERSON><PERSON> drive ambient temperature reporting
 *
 * FH drives derive the motor winding temperature (which in turn is used to fold back current; this
 * is its only protection mechanism!) by means of a model, one of the inputs to which is the
 * ambient temperature.
 */
#pragma once

#include "nanopb_server.h"

int fh_ambient_init();

int fh_ambient_update();
void fh_ambient_handle_nanopb(npb_request_data *req);
