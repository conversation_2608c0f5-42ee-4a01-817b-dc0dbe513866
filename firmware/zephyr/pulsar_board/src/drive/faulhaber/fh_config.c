#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(fh_config, CONFIG_APP_LOG_LEVEL);

#include <zephyr/kernel.h>
#include <zephyr/sys/byteorder.h>

#include <utils/handle_errors.h>

#include "can_open.h"
#include "can_open_msg.h"
#include "cia_402.h"
#include "fh_config.h"

typedef struct {
  const uint16_t object;
  const uint8_t subindex;
  const uint32_t value;
} fh_config_entry_t;

/**
 * @brief Config for the pan node (id 1)
 */
static const fh_config_entry_t gFhConfigPan[] = {
    // Fix baud rate to 1M
    {
        .object = 0x2400,
        .subindex = 0x01,
        .value = 0,
    },
    // Enable EMCY for everything but dynamic limits
    {
        .object = 0x2321,
        .subindex = 0x01,
        .value = 0xDFFF,
    },
    // COB ID for EMCY
    {
        .object = 0x1014,
        .subindex = 0x00,
        .value = CAN_OPEN_COB_ID_CONSTRUCT(NODE_ID_PAN_SERVO, CAN_OPEN_FUNC_CODE_EMERGENCY),
    },
};

/**
 * @brief Config for the tilt node (id 2)
 */
static const fh_config_entry_t gFhConfigTilt[] = {
    // Fix baud rate to 1M
    {
        .object = 0x2400,
        .subindex = 0x01,
        .value = 0,
    },
    // Enable EMCY for everything but dynamic limits
    {
        .object = 0x2321,
        .subindex = 0x01,
        .value = 0xDFFF,
    },
    // COB ID for EMCY
    {
        .object = 0x1014,
        .subindex = 0x00,
        .value = CAN_OPEN_COB_ID_CONSTRUCT(NODE_ID_TILT_SERVO, CAN_OPEN_FUNC_CODE_EMERGENCY),
    },
};

static int apply_config(const uint8_t node_id, const fh_config_entry_t *entries, const size_t numEntries,
                        bool *outChanged);
static int apply_config_single(const uint8_t node_id, const fh_config_entry_t *entry, bool *outChanged);

/**
 * @brief Validate the FH drive config
 *
 * Ensure the following values are set properly:
 *
 * - Transmit EMCY for all states except dynamic
 * - EMCY COB-ID
 * - Baud rate setting (1M)
 */
int FH_Config_Validate(const uint8_t node_id) {
  bool changed = false;

  const fh_config_entry_t *entries;
  size_t numEntries;

  switch (node_id) {
  case NODE_ID_PAN_SERVO:
    entries = gFhConfigPan;
    numEntries = ARRAY_SIZE(gFhConfigPan);
    break;
  case NODE_ID_TILT_SERVO:
    entries = gFhConfigTilt;
    numEntries = ARRAY_SIZE(gFhConfigTilt);
    break;
  default:
    LOG_WRN("Invalid node id %u", node_id);
    return -EINVAL;
  }

  HANDLE_UNLIKELY(apply_config(node_id, entries, numEntries, &changed));

  if (changed) {
    // TODO: we could write to FH EEPROM here
    LOG_WRN("Configs updated for node %u", node_id);
  }

  return 0;
}

/**
 * @brief Apply the specified series of configs to the FH
 */
static int apply_config(const uint8_t node_id, const fh_config_entry_t *entries, const size_t numEntries,
                        bool *outChanged) {
  if (!entries || !outChanged) {
    return -EFAULT;
  } else if (!numEntries) {
    return -EINVAL;
  }

  for (size_t i = 0; i < numEntries; i++) {
    HANDLE_UNLIKELY(apply_config_single(node_id, &entries[i], outChanged));

    // load bearing delay to avoid overloading FH
    k_sleep(K_MSEC(10));
  }

  return 0;
}

/**
 * @brief Apply a single config to the FH
 */
static int apply_config_single(const uint8_t node_id, const fh_config_entry_t *entry, bool *outChanged) {
  uint32_t current;
  CAN_Open_Message_t message;
  memset(&message, 0, sizeof(message));

  // read the current value of this config
  HANDLE_UNLIKELY(Send_SDO_Upload_Request(node_id, &message, entry->object, entry->subindex));
  memcpy(&current, message.pkt.sdo.data, sizeof(current));
  current = sys_le32_to_cpu(current);

  // apply if different
  if (current != entry->value) {
    LOG_INF("Updating SDO %04x.%02x for node %u: %08x -> %08x", entry->object, entry->subindex, node_id, current,
            entry->value);
    HANDLE_UNLIKELY(Send_SDO_Download(node_id, entry->object, entry->subindex, entry->value));

    *outChanged = true;
  }

  return 0;
}
