/* ========================================
 *
 * Carbon Robotics, 2022
 * All Rights Reserved
 * UNPUBLISHED, LICENSED SOFTWARE.
 *
 * CONFIDENTIAL AND PROPRIETARY INFORMATION
 * WHICH IS THE PROPERTY OF your company.
 *
 * ========================================
 */
#include <logging/log.h>
LOG_MODULE_REGISTER(fh_controls, CONFIG_APP_LOG_LEVEL);

#include "can_open.h"
#include "can_open_msg.h"
#include "cia_402.h"
#include "faulhaber.h"
#include "fh_controls.h"
#include "gimbal_handler.h"
#include "motion_controller.h"
#include "qdec.h"
#include <stdlib.h>
#include <string.h>
#include <zephyr.h>

#ifdef USE_REAPER
#define FH_DEFAULT_HOMING_TORQUE 500
#define FH_DEFAULT_HOMING_KP 4000
#else
#define FH_DEFAULT_HOMING_TORQUE 700
#define FH_DEFAULT_HOMING_KP 4000
#endif

/**
 * @brief Homing by detecting hard stops via torque limit
 *
 * Determines the movement range of the servo by finding the positive and negative hard stops; this
 * detects blockages (e.g. the hard stops) by means of the motor current consumed. This is
 * implemented as several discrete steps:
 *
 * 1. Configure motor controller for homing
 *    Apply a lower velocity control loop Kp, set limit check time (e.g. how long the current
 *    must be sustained for) and the homing offsets, torques and speeds. These define what the motor
 *    controller interprets as a hard stop/blockage.
 * 2. Determine homing direction
 *    If booting not inverted, go first in the positive direction, then negative.
 * 3. Find first hard stop
 *    Use the first homing method until the hard stop is reached, then record the encoder ticks
 *    value.
 * 4. Find second hard stop
 *    Use the second homing method, which will move in the opposite direction from this first
 *    one. When complete, record the encoder ticks value.
 * 5. Calculate gimbal limits range
 *    Take the absolute difference between the two hard stops that were found.
 * 6. Set up profile position mode
 *    With the homing parameters acquired, enter the profile position mode to allow the servos
 *    to be positioned.
 *
 * Note that homing can fail for a variety of reasons, which typically manifest as a really low
 * range between the hard stops: we check for that case, and retry once as this seems to be a
 * spurious error.
 */
CARBON_RESPONSE_CODE FH_Hard_Stop_Home(uint8_t node_id, int16_t step_size, uint16_t offset, int32_t min_position,
                                       int32_t max_position, uint32_t profile_velocity, uint16_t settle_timeout_ms,
                                       int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale) {
  size_t attempts = 0;

  // Scale for FH servo
  offset /= tick_scale;
  max_position /= tick_scale;
  step_size /= tick_scale;

  // get current velocity gain
  CAN_Open_Message_t kp_msg;
  uint32_t *kp_ptr = (uint32_t *)kp_msg.pkt.sdo.data;
  RETURN_CODE_IF_NOT_OK(FH_Read_Velocity_Control_KP(node_id, &kp_msg));

  // Set low gains for homing and blockage detection time
retry:;
  RETURN_CODE_IF_NOT_OK(FH_Write_Velocity_Control_KP(node_id, FH_DEFAULT_HOMING_KP));
  RETURN_CODE_IF_NOT_OK(FH_Write_Home_Limit_Check_Time(node_id, 100));

  // initialize homing parameters
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_HOMING_MODE));
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Home_Offset(node_id, offset));

  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Switch_Search_Speed(node_id, 2000));
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Zero_Search_Speed(node_id, 2000));
  RETURN_CODE_IF_NOT_OK(FH_Write_Home_Torque_Limit_Positive(node_id, FH_DEFAULT_HOMING_TORQUE));
  RETURN_CODE_IF_NOT_OK(FH_Write_Home_Torque_Limit_Negative(node_id, FH_DEFAULT_HOMING_TORQUE));

  // get current movement polarity, then base homing directions on that
  CAN_Open_Message_t polarity_msg;
  uint32_t first_method = CIA402_HOMING_METHOD_CURRENT_POSITIVE_SPEED;
  uint32_t second_method = CIA402_HOMING_METHOD_CURRENT_NEGATIVE_SPEED;

  uint32_t polarity = FH_Read_Polarity(node_id, &polarity_msg);
  if (polarity_msg.pkt.sdo.data[0] == FH_POLARITY_INVERTED) {
    first_method = CIA402_HOMING_METHOD_CURRENT_NEGATIVE_SPEED;
    second_method = CIA402_HOMING_METHOD_CURRENT_POSITIVE_SPEED;
  }

  // find first end stop
  RETURN_CODE_IF_NOT_OK(MC_get()->Home_With_Method(node_id, first_method));
  int64_t endstop = qdec_get_latest(enc_id);

  // Stop Movement
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));
  // home again, to find second end stop
  RETURN_CODE_IF_NOT_OK(MC_get()->Home_With_Method(node_id, second_method));
  int64_t home = qdec_get_latest(enc_id);

  // stop motor movement
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));

  const int32_t rawLimits = abs((int32_t)(endstop - home));
  if (rawLimits < 3 * offset) {
    /*
     * Sometimes the FH homing barely moves, leading to this failure case; if this is the first
     * attempt at booting the FH, and the limits are really low, retry once.
     *
     * Otherwise ensure that the controller is shut down to stop the horrifying screeching.
     */
    LOG_WRN("node(%u): Hard limits range too small: limit=%d, expected=%d; endstop=%lld, home=%lld (attempt %u)",
            node_id, *out_limit, (3 * offset), endstop, home, (attempts + 1));

    // this will stop driving motors immediately
    CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SHUTDOWN);

    if (attempts++ == 0) {
      // wait for settling
      k_sleep(K_MSEC(250));
      goto retry;
    }

    *out_limit = 0;
    return CARBON_EPOS_HARD_LIMITS_RANGE_TOO_SMALL;
  }

  // output distance between two endstops, subtracting the homing offset
  *out_limit = rawLimits - (2 * offset * tick_scale);

  // go into profile position mode
  CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE);
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_PROFILE_POSITION_MODE));

  // move motors to zero position; restore movement Kp
  MC_get()->Go_To_Position(node_id, 0, profile_velocity);
  k_sleep(K_MSEC(200));

  RETURN_CODE_IF_NOT_OK(FH_Write_Velocity_Control_KP(node_id, *kp_ptr));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Set current position as reference
 *
 * Set the current position of the servo as the reference (zero) point.
 */
CARBON_RESPONSE_CODE FH_Actual_Position_Home(uint8_t node_id, uint16_t range, uint32_t profile_velocity,
                                             int32_t *out_limit, uint8_t enc_id, uint32_t tick_scale) {
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));

  // Set Homing Mode
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_HOMING_MODE))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Home_Offset(node_id, 0))

  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Switch_Search_Speed(node_id, 2000))
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Homing_Zero_Search_Speed(node_id, 2000))
  RETURN_CODE_IF_NOT_OK(FH_Write_Home_Torque_Limit_Positive(node_id, FH_DEFAULT_HOMING_TORQUE))
  RETURN_CODE_IF_NOT_OK(FH_Write_Home_Torque_Limit_Negative(node_id, FH_DEFAULT_HOMING_TORQUE))

  uint32_t first_method = CIA402_HOMING_METHOD_ACTUAL_POSITION;

  // Ignore return code because Faulhaber actual position homing doesn't follow homing signaling properly
  MC_get()->Home_With_Method(node_id, first_method);

  int64_t home = qdec_get_latest(enc_id);
  // Stop Movement
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));

  // Profile Position Mode Ready
  CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE);
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Mode_Of_Operation(node_id, CIA402_PROFILE_POSITION_MODE))

  MC_get()->Go_To_Position(node_id, range, profile_velocity);
  k_sleep(K_MSEC(200));

  int64_t endstop = qdec_get_latest(enc_id);
  RETURN_CODE_IF_NOT_OK(CIA402_Write_Control_Word(node_id, CIA402_CONTROL_COMMAND_SWITCH_ON_ENABLE |
                                                               CIA402_CONTROL_CHANGE_SET_IMMEDIATELY |
                                                               CIA402_CONTROL_QUICK_STOP));

  *out_limit = abs((int32_t)(endstop - home));
  MC_get()->Go_To_Position(node_id, 0, profile_velocity);
  k_sleep(K_MSEC(200));

  return CARBON_RESPONSE_OK;
}

/**
 * @brief Set FH movement polarity
 */
CARBON_RESPONSE_CODE FH_set_inverted(uint8_t node_id, uint8_t inverted) {
  RETURN_CODE_IF_NOT_OK(FH_Write_Polarity(node_id, inverted ? FH_POLARITY_INVERTED : FH_POLARITY_NORMAL))
  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE FH_set_pid(uint8_t node_id, pid_config *cfg) {
  // TODO implement me
  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE FH_handle_pid(uint8_t node_id, epos_Set_PID_V2_Request *request) {
  // TODO implement me
  return CARBON_RESPONSE_OK;
}

CARBON_RESPONSE_CODE FH_handle_get_pid(uint8_t node_id, epos_Get_PID_Reply *reply) {
  // TODO implement me
  return CARBON_RESPONSE_OK;
}
