#include <zephyr/logging/log.h>
#include <zephyr/logging/log_ctrl.h>
LOG_MODULE_REGISTER(coredump, CONFIG_APP_LOG_LEVEL);

#include <utils/handle_errors.h>
#include <zephyr/debug/coredump.h>
#include <zephyr/drivers/coredump.h>

#include <pb_encode.h>

#include "coredump.h"
#include "generated/lib/drivers/nanopb/proto/reaper_module_controller.pb.h"
#include "rpc/pc_serial.h"
#include "watchdog.h"

/**
 * @brief Size, in bytes, of temporary buffer for encoding message
 *
 * The maximum payload length of a coredump frame is 256 bytes, so this accounts for the overhead of
 * protobuf serialization and COBS framing.
 */
#define MAX_COREDUMP_TX_SIZE (320)

#define LOG_COREDUMP_TX (0)

static void handle_coredump_start(void);
static void handle_coredump_end(void);
static void handle_coredump_output(uint8_t *buf, size_t buflen);
static int handle_coredump_query(enum coredump_query_id query_id, void *arg);
static int handle_coredump_cmd(enum coredump_cmd_id cmd_id, void *arg);

static int register_memory_regions();
static int send_reply();

// Linker defined symbols for memory regions
extern char __dtcm_start[], __dtcm_end[];
extern char __SRAM3_start[];

/// Shared state for the coredump handler
__dtcm_bss_section static struct {
  /// Whether coredump has been output completely
  bool isValid;
  /// Last coredump error
  int error;

  /// Sequence number for coredump packet
  size_t sequence;

  /// Buffer for messages to be sent to host
  uint8_t encodeBuf[MAX_COREDUMP_TX_SIZE];
  /// Protobuf reply message
  reaper_module_controller_OobReply reply;
} gState;

/// Coredump device
static const struct device *const gCoredumpDev = DEVICE_DT_GET(DT_NODELABEL(coredump));

/**
 * @brief Initialize the coredump subsystem
 */
int coredump_init() {
  HANDLE_UNLIKELY_BOOL(device_is_ready(gCoredumpDev), ENXIO);

  HANDLE_UNLIKELY(register_memory_regions());

  return 0;
}

/**
 * @brief Register additional memory regions to be included in a coredump
 */
static int register_memory_regions() {
  /*
   * Register DTCM
   *
   * The entirety of DTCM (noinit, bss, data) is dumped.
   */
  static struct coredump_mem_region_node gDtcmNode = {
      .start = ((uintptr_t)&__dtcm_start),
  };
  gDtcmNode.size = ((uintptr_t)&__dtcm_end) - ((uintptr_t)&__dtcm_start);

  LOG_DBG("Adding coredump region: %u bytes@%p", gDtcmNode.size, (void *)gDtcmNode.start);
  HANDLE_UNLIKELY_BOOL(coredump_device_register_memory(gCoredumpDev, &gDtcmNode), EINVAL);

  /*
   * Register SRAM3
   *
   * This is wher Ethernet DMA buffers live. Include these so that we can inspect the state of
   * descriptors later.
   */
  static struct coredump_mem_region_node gSram3Node = {
      .start = ((uintptr_t)&__SRAM3_start),
  };
  gSram3Node.size = 16 * 1024;

  LOG_DBG("Adding coredump region: %u bytes@%p", gSram3Node.size, (void *)gSram3Node.start);
  HANDLE_UNLIKELY_BOOL(coredump_device_register_memory(gCoredumpDev, &gSram3Node), EINVAL);

  return 0;
}
/**
 * @brief Serialize the reply message and send it via coredump interface
 */
static int send_reply() {
  // ensure fields are set
  gState.reply.which_reply = reaper_module_controller_OobReply_coredump_tag;

  // it is convention that message IDs above 0x800000 are unsolicited
  gState.reply.has_header = true;
  gState.reply.header.requestId = 0x01000000 | (gState.sequence++ & 0xFFFFFF);

  // encode and send
  pb_ostream_t ostream = pb_ostream_from_buffer(gState.encodeBuf, sizeof(gState.encodeBuf));
  if (!pb_encode(&ostream, reaper_module_controller_OobReply_fields, &gState.reply)) {
    LOG_WRN("pb_encode failed: %s", PB_GET_ERROR(&ostream));
    return -EINVAL;
  }

#if LOG_COREDUMP_TX
  LOG_HEXDUMP_DBG(gState.encodeBuf, ostream.bytes_written, "coredump tx");
#endif
  return pc_serial_send_coredump(gState.encodeBuf, ostream.bytes_written);
}

/**
 * @brief Called to begin a coredump
 */
static void handle_coredump_start(void) {
  gState.isValid = false;
  gState.error = 0;

  // ensure all pending log messages get output
  LOG_DBG("Switching logger to panic mode");

  watchdog_coredump_callback();
  // while(log_process());
  log_panic();

  // stop pc_serial stuff
  gState.error = pc_serial_start_coredump();
  if (gState.error) {
    LOG_ERR("Failed to pause PC serial endpoint!");
  }

  // need to appease watchdog
  watchdog_coredump_callback();

  // send notificaiton coredump is starting
  memset(&gState.reply, 0, sizeof(gState.reply));
  gState.reply.which_reply = reaper_module_controller_OobReply_coredump_tag;
  gState.reply.reply.coredump.which_payload = reaper_module_controller_CoreDumpReply_start_tag;

  gState.error = send_reply();
  if (gState.error) {
    LOG_ERR("failed to send %s: %d", "coredump start", gState.error);
  }

  // perform baud rate switch to higher rate (so it doesn't take 10 minutes for a coredump…)
  gState.error = pc_serial_switch_rate();
  if (gState.error) {
    LOG_ERR("%s failed: %d", "pc_serial_switch_rate", gState.error);
  }

  // load bearing delay to give PC time to update its serial port
  LOG_INF("Giving PC time to switch rate before continuing coredump");

  watchdog_coredump_callback();
  k_sleep(K_SECONDS(1));
  watchdog_coredump_callback();

  LOG_INF("Coredump starts now!");
}

/**
 * @brief Called to finish a coredump
 */
static void handle_coredump_end(void) {
  gState.isValid = true;

  // service watchdog one more time
  watchdog_coredump_callback();

  // send notification coredump is complete
  memset(&gState.reply, 0, sizeof(gState.reply));
  gState.reply.reply.coredump.which_payload = reaper_module_controller_CoreDumpReply_end_tag;

  gState.error = send_reply();
  if (gState.error) {
    LOG_WRN("failed to send %s: %d", "coredump start", gState.error);
  }

  /*
   * At this point, the entire coredump has been transferred to the PC and is being processed.
   *
   * The watchdog will reset the system once we return here, so add some bonus delay to ensure the
   * coredump is committed to persistent storage on the PC.
   */
  LOG_INF("Coredump complete");

  for (size_t i = 0; i < 15; i++) {
    watchdog_coredump_callback();

    k_busy_wait(1 * USEC_PER_SEC);
  }

  LOG_WRN("Waiting for watchdog reset");
}

/**
 * @brief Write out coredump data
 *
 * Coredump records are sent as chunks to fit in the limited payload capacity of the proto
 * messages.
 */
static void handle_coredump_output(uint8_t *buf, size_t buflen) {
  LOG_DBG("%s: %u bytes@%p", __FUNCTION__, buflen, buf);

  if (!buf) {
    gState.error = -EFAULT;
    return;
  } else if (!buflen) {
    gState.error = -EINVAL;
    return;
  }

  size_t offset = 0;
  size_t remaining = buflen;

  // TODO: attempt to compress the chunks?

  // max number of bytes we can send in a single message
  static const size_t kDataBytes = sizeof(gState.reply.reply.coredump.payload.data.data.bytes);
  LOG_DBG("Coredump chunk size = %u bytes", kDataBytes);

  // send chunks for as long as there is data left
  while (remaining > 0) {
    watchdog_coredump_callback();

    const size_t toCopy = MIN(kDataBytes, remaining);
    LOG_DBG("Writing %u bytes of coredump (offset %u/%u)", toCopy, offset, buflen);

    // prepare the message
    memset(&gState.reply, 0, sizeof(gState.reply));
    gState.reply.reply.coredump.which_payload = reaper_module_controller_CoreDumpReply_data_tag;

    gState.reply.reply.coredump.payload.data.is_last = (remaining <= toCopy);
    // NOTE: the offset is within this record, not the whole coredump file
    gState.reply.reply.coredump.payload.data.offset = offset;
    gState.reply.reply.coredump.payload.data.data.size = toCopy;

    memmove(gState.reply.reply.coredump.payload.data.data.bytes, buf + offset, toCopy);

    // send it!
    gState.error = send_reply();
    if (gState.error) {
      LOG_ERR("%s failed: %d", "send_reply", gState.error);
      return;
    }

    // update for next iteration
    offset += toCopy;
    remaining -= toCopy;
  }
}

/**
 * @brief Respond to a backend info query
 */
static int handle_coredump_query(enum coredump_query_id query_id, void *arg) {
  int ret;

  switch (query_id) {
  case COREDUMP_QUERY_GET_ERROR:
    ret = gState.error;
    break;

  default:
    ret = -ENOTSUP;
    LOG_ERR("Unhandled coredump %s %u", "query", query_id);
    break;
  }

  return ret;
}

/**
 * @brief Execute a coredump backend command
 */
static int handle_coredump_cmd(enum coredump_cmd_id cmd_id, void *arg) {
  int ret;

  switch (cmd_id) {
  case COREDUMP_CMD_CLEAR_ERROR:
    ret = 0;
    gState.error = 0;
    break;

  default:
    ret = -ENOTSUP;
    LOG_ERR("Unhandled coredump %s %u", "command", cmd_id);
    break;
  }

  return ret;
}

/**
 * @brief Coredump API
 *
 * Name of the struct is fixed by the rest of the coredump subsystem.
 */
const struct coredump_backend_api coredump_backend_other = {
    .start = handle_coredump_start,
    .end = handle_coredump_end,
    .buffer_output = handle_coredump_output,
    .query = handle_coredump_query,
    .cmd = handle_coredump_cmd,
};
