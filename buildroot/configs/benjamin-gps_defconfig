BR2_arm=y
BR2_cortex_a8=y
BR2_CCACHE=y
BR2_GLOBAL_PATCH_DIR="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/patches"
BR2_TOOLCHAIN_BUILDROOT_GLIBC=y
BR2_KERNEL_HEADERS_5_4=y
BR2_TOOLCHAIN_BUILDROOT_CXX=y
BR2_TARGET_GENERIC_HOSTNAME="benjamin-gps"
BR2_TARGET_GENERIC_ISSUE="Benjamin GPS"
BR2_INIT_SYSTEMD=y
BR2_TARGET_GENERIC_ROOT_PASSWD="a"
BR2_SYSTEM_BIN_SH_BASH=y
BR2_SYSTEM_DEFAULT_PATH="/bin:/sbin:/usr/bin:/usr/sbin"
BR2_ROOTFS_OVERLAY="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/rootfs-overlay"
BR2_ROOTFS_POST_BUILD_SCRIPT="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/post-build.sh"
BR2_ROOTFS_POST_IMAGE_SCRIPT="support/scripts/genimage.sh"
BR2_ROOTFS_POST_SCRIPT_ARGS="-c $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/genimage.cfg"
BR2_LINUX_KERNEL=y
BR2_LINUX_KERNEL_CUSTOM_VERSION=y
BR2_LINUX_KERNEL_CUSTOM_VERSION_VALUE="5.4.84"
BR2_LINUX_KERNEL_USE_CUSTOM_CONFIG=y
BR2_LINUX_KERNEL_CUSTOM_CONFIG_FILE="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/linux.config"
BR2_LINUX_KERNEL_DTS_SUPPORT=y
BR2_LINUX_KERNEL_INTREE_DTS_NAME="am335x-boneblack"
BR2_LINUX_KERNEL_CUSTOM_DTS_PATH="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-pps.dts $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-uart.dts $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-rtc.dts $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-leds.dts $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-spi.dts $(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/overlay-eth.dts"
BR2_LINUX_KERNEL_DTB_OVERLAY_SUPPORT=y
BR2_LINUX_KERNEL_NEEDS_HOST_OPENSSL=y
# BR2_PACKAGE_BUSYBOX is not set
BR2_PACKAGE_FINDUTILS=y
BR2_PACKAGE_GREP=y
BR2_PACKAGE_I2C_TOOLS=y
BR2_PACKAGE_PPS_TOOLS=y
BR2_PACKAGE_SETSERIAL=y
BR2_PACKAGE_SPI_TOOLS=y
BR2_PACKAGE_OPENSSL=y
BR2_PACKAGE_LIBGPIOD_TOOLS=y
BR2_PACKAGE_DROPBEAR=y
# BR2_PACKAGE_DROPBEAR_CLIENT is not set
BR2_PACKAGE_DROPBEAR_DISABLE_REVERSEDNS=y
BR2_PACKAGE_ETHTOOL=y
BR2_PACKAGE_LINUXPTP=y
BR2_PACKAGE_NET_TOOLS=y
BR2_PACKAGE_SCREEN=y
BR2_PACKAGE_COREUTILS=y
BR2_PACKAGE_HTOP=y
BR2_PACKAGE_PROCPS_NG=y
# BR2_PACKAGE_SYSTEMD_PSTORE is not set
# BR2_PACKAGE_SYSTEMD_HOSTNAMED is not set
# BR2_PACKAGE_SYSTEMD_HWDB is not set
# BR2_PACKAGE_SYSTEMD_MYHOSTNAME is not set
# BR2_PACKAGE_SYSTEMD_RESOLVED is not set
# BR2_PACKAGE_SYSTEMD_TIMEDATED is not set
# BR2_PACKAGE_SYSTEMD_TIMESYNCD is not set
# BR2_PACKAGE_SYSTEMD_TMPFILES is not set
# BR2_PACKAGE_SYSTEMD_VCONSOLE is not set
BR2_PACKAGE_UTIL_LINUX_BINARIES=y
BR2_PACKAGE_UTIL_LINUX_HWCLOCK=y
BR2_PACKAGE_UTIL_LINUX_KILL=y
BR2_PACKAGE_UTIL_LINUX_LOGIN=y
BR2_PACKAGE_LESS=y
BR2_PACKAGE_VIM=y
# BR2_PACKAGE_VIM_RUNTIME is not set
BR2_TARGET_ROOTFS_EXT2=y
BR2_TARGET_ROOTFS_EXT2_4=y
BR2_TARGET_ROOTFS_EXT2_SIZE="64M"
# BR2_TARGET_ROOTFS_TAR is not set
BR2_TARGET_UBOOT=y
BR2_TARGET_UBOOT_BUILD_SYSTEM_KCONFIG=y
BR2_TARGET_UBOOT_CUSTOM_VERSION=y
BR2_TARGET_UBOOT_CUSTOM_VERSION_VALUE="2020.04"
BR2_TARGET_UBOOT_BOARD_DEFCONFIG="am335x_boneblack_vboot"
BR2_TARGET_UBOOT_CONFIG_FRAGMENT_FILES="$(BR2_EXTERNAL_MAKA_PATH)/board/benjamin-gps/uboot-fdt-overlay.fragment"
BR2_TARGET_UBOOT_NEEDS_DTC=y
# BR2_TARGET_UBOOT_FORMAT_BIN is not set
BR2_TARGET_UBOOT_FORMAT_IMG=y
BR2_TARGET_UBOOT_SPL=y
BR2_TARGET_UBOOT_SPL_NAME="MLO"
BR2_PACKAGE_HOST_DOSFSTOOLS=y
BR2_PACKAGE_HOST_GENIMAGE=y
BR2_PACKAGE_HOST_MTOOLS=y
BR2_PACKAGE_BENJAMIN_GPS=y
