BENJAMIN_GPS_SITE = $(BR2_EXTERNAL_MAKA_PATH)/..
BENJAMIN_GPS_SITE_METHOD = local
BENJAMIN_GPS_OVERRIDE_SRCDIR_RSYNC_EXCLUSIONS = \
	--exclude build --exclude firmware --exclude unity

define BENJAMIN_GPS_BUILD_CMDS
	$(MAKE) $(TARGET_CONFIGURE_OPTS) -C $(@D)/lib/drivers/benjamin_gps/server all
endef

define BENJAMIN_GPS_INSTALL_STAGING_CMDS
	$(INSTALL) -D -m 0755 $(@D)/bin/benjamin_gps $(STAGING_DIR)/usr/sbin
endef

define BENJAMIN_GPS_INSTALL_TARGET_CMDS
	$(INSTALL) -D -m 0755 $(@D)/bin/benjamin_gps $(TARGET_DIR)/usr/sbin
endef

$(eval $(generic-package))
