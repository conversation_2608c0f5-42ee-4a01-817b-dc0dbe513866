FROM ubuntu:20.04

RUN apt update -qq && \
    apt install -y build-essential \
        libncurses-dev \
        file \
        wget \
        cpio \
        unzip \
        rsync \
        bc \
        git \
        python3 \
        libssl-dev \
        vim \
        silversearcher-ag

RUN wget -O /tmp/buildroot-2021.02.1.tar.gz https://buildroot.org/downloads/buildroot-2021.02.1.tar.gz && \
    tar -zxvf /tmp/buildroot-2021.02.1.tar.gz && \
    rm /tmp/buildroot-2021.02.1.tar.gz

ENV BR2_EXTERNAL=/robot/buildroot

WORKDIR /buildroot-2021.02.1
