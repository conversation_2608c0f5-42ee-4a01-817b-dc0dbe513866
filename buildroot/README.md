# Buildroot

Buildroot is a tool to make custom Linux image for an embedded system.

User manual: https://buildroot.org/downloads/manual/manual.html

Free training: https://bootlin.com/training/buildroot/

## Producing Benjamin GPS image

1. `./docker/build_docker.sh`

2. `./docker/run_docker.sh`

3. `make BR2_EXTERNAL=/robot/buildroot benjamin-gps_defconfig`

4. `make clean all`

5. Grab a coffee, after you come back you should find `output/images/sdcard.img`.
