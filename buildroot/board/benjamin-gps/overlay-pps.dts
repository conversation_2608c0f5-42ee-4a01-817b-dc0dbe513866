/dts-v1/;
/plugin/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/am33xx.h>

// Good article about device trees and overlays:
// https://www.raspberrypi.org/documentation/configuration/device-tree.md

/ {
	fragment@0 {
		target = <&am33xx_pinmux>;
		__overlay__ {
			pps_pins: pps_pins {
				pinctrl-single,pins = <
					AM33XX_PADCONF(0x878, PIN_INPUT_PULLDOWN, MUX_MODE7)
				>;
			};
		};
	};

	fragment@1 {
		target-path = "/";
		__overlay__ {
			pps: pps {
				compatible = "pps-gpio";
				status = "okay";
				pinctrl-names = "default";
				pinctrl-0 = <&pps_pins>;

				gpios = <&gpio1 28 GPIO_ACTIVE_HIGH>;
				assert-rising-edge;
			};
		};
	};
};
