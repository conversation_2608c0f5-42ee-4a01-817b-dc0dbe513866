/dts-v1/;
/plugin/;

// Good article about device trees and overlays:
// https://www.raspberrypi.org/documentation/configuration/device-tree.md

/ {
	fragment@0 {
		target = <&i2c2>;
		__overlay__ {
			tlc59116@61 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "ti,tlc59116";
				reg = <0x61>;

				led0@0 {
					label = "status:led0";
					reg = <0x0>;
				};

				led1@1 {
					label = "status:led1";
					reg = <0x1>;
				};

				led2@2 {
					label = "status:led2";
					reg = <0x2>;
				};

				led3@3 {
					label = "status:led3";
					reg = <0x3>;
				};

				led4@4 {
					label = "status:led4";
					reg = <0x4>;
				};

				led5@5 {
					label = "status:led5";
					reg = <0x5>;
				};
			};
		};
	};
};
