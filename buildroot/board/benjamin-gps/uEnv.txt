bootdir=
bootpart=0:1
devtype=mmc
console=ttyS0,115200n8
set_bootargs=setenv bootargs console=${console} ${optargs} root=/dev/mmcblk0p2 rw rootfstype=ext4
# Applying overlays in U-Boot:
# https://github.com/u-boot/u-boot/blob/master/doc/README.fdt-overlays
fdtaddr=0x88000000
fdtovaddr=0x880c0000
loadfdt=load ${devtype} ${bootpart} ${fdtaddr} ${bootdir}/am335x-boneblack.dtb;fdt addr $fdtaddr;fdt resize 8192
loadfdtpps=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-pps.dtb;fdt apply $fdtovaddr
loadfdtuart=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-uart.dtb;fdt apply $fdtovaddr
loadfdtrtc=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-rtc.dtb;fdt apply $fdtovaddr
loadfdtleds=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-leds.dtb;fdt apply $fdtovaddr
loadfdtspi=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-spi.dtb;fdt apply $fdtovaddr
loadfdteth=load ${devtype} ${bootpart} ${fdtovaddr} ${bootdir}/overlay-eth.dtb;fdt apply $fdtovaddr
loadfdtall=run loadfdt;run loadfdtpps;run loadfdtuart;run loadfdtrtc;run loadfdtleds;run loadfdtspi;run loadfdteth
uenvcmd=run set_bootargs;run loadimage;run loadramdisk;run loadfdtall;bootz ${loadaddr} - ${fdtaddr}
