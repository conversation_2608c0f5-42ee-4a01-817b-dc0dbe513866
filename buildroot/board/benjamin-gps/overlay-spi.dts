/dts-v1/;
/plugin/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/am33xx.h>

// Good article about device trees and overlays:
// https://www.raspberrypi.org/documentation/configuration/device-tree.md

/ {
	fragment@0 {
		target = <&am33xx_pinmux>;
		__overlay__ {
			spi0_pins: spi0_pins {
				pinctrl-single,pins = <
					AM33XX_PADCONF(0x950, PIN_INPUT_PULLUP, MUX_MODE0) /* sclk */
					AM33XX_PADCONF(0x958, PIN_OUTPUT_PULLUP, MUX_MODE0) /* mosi */
					AM33XX_PADCONF(0x954, PIN_INPUT_PULLUP, MUX_MODE0) /* miso */
					AM33XX_PADCONF(0x95c, PIN_OUTPUT_PULLUP, MUX_MODE0) /* cs0 */
				>;
			};

			spi1_pins: spi1_pins {
				pinctrl-single,pins = <
					AM33XX_PADCONF(0x990, PIN_INPUT_PULLUP, MUX_MODE3) /* sclk */
					AM33XX_PADCONF(0x998, PIN_OUTPUT_PULLUP, MUX_MODE3) /* mosi */
					AM33XX_PADCONF(0x994, PIN_INPUT_PULLUP, MUX_MODE3) /* miso */
					AM33XX_PADCONF(0x99c, PIN_OUTPUT_PULLUP, MUX_MODE3) /* cs0 */
				>;
			};
		};
	};

	fragment@1 {
		target = <&spi0>;
		__overlay__ {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&spi0_pins>;

			spidev@0 {
				spi-max-frequency = <4000000>;
				reg = <0>;
				compatible = "rohm,dh2228fv";
			};
		};
	};

	fragment@2 {
		target = <&spi1>;
		__overlay__ {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&spi1_pins>;

			spidev@0 {
				spi-max-frequency = <4000000>;
				reg = <0>;
				compatible = "rohm,dh2228fv";
			};
		};
	};
};
