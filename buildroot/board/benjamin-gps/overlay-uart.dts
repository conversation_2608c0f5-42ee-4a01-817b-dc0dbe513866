/dts-v1/;
/plugin/;

#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/pinctrl/am33xx.h>

// Good article about device trees and overlays:
// https://www.raspberrypi.org/documentation/configuration/device-tree.md

/ {
	fragment@0 {
		target = <&am33xx_pinmux>;
		__overlay__ {
			uart4_pins: uart4_pins {
				pinctrl-single,pins = <
					AM33XX_PADCONF(0x874, PIN_OUTPUT_PULLDOWN, MUX_MODE6) /* tx */
					AM33XX_PADCONF(0x870, PIN_INPUT_PULLDOWN, MUX_MODE6) /* rx */
					AM33XX_PADCONF(0x8d0, PIN_INPUT_PULLUP, MUX_MODE6) /* cts */
					AM33XX_PADCONF(0x8d4, PIN_OUTPUT_PULLUP, MUX_MODE6) /* rts */
				>;
			};
		};
	};

	fragment@1 {
		target = <&uart4>;
		__overlay__ {
			status = "okay";
			pinctrl-names = "default";
			pinctrl-0 = <&uart4_pins>;
			uart-has-rtscts;
		};
	};
};
