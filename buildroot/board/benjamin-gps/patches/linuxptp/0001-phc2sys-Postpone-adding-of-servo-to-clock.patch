From 194dc845103c783e5f9bd6f9f025eedd6a5f3d36 Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON><PERSON> <<EMAIL>>
Date: Thu, 6 Aug 2020 16:16:10 +0200
Subject: [PATCH] phc2sys: Postpone adding of servo to clock.

Instead of unconditionally creating a servo for each clock in their
initialization, add the servo later on the first update of the clock,
when it is known the clock needs to be synchronized.

This fixes an issue with phc2sys disrupting the system clock when
it is synchronized by another process and should be used by phc2sys only
as a source. Creating a servo for the system clock caused a reset of its
frequency and status.

Reported-by: <PERSON> <<EMAIL>>
Signed-off-by: <PERSON><PERSON><PERSON> <mlich<PERSON>@redhat.com>
---
 phc2sys.c | 24 +++++++++++-------------
 1 <USER> <GROUP>, 11 insertions(+), 13 deletions(-)

diff --git a/phc2sys.c b/phc2sys.c
index 87c7e6f..3690b72 100644
--- a/phc2sys.c
+++ b/phc2sys.c
@@ -220,9 +220,6 @@ static struct clock *clock_add(struct phc2sys_private *priv, char *device)
 		}
 	}
 
-	if (clkid != CLOCK_INVALID)
-		c->servo = servo_add(priv, c);
-
 	if (clkid != CLOCK_INVALID && clkid != CLOCK_REALTIME)
 		c->sysoff_method = sysoff_probe(CLOCKID_TO_FD(clkid),
 						priv->phc_readings);
@@ -317,7 +314,6 @@ static void clock_reinit(struct phc2sys_private *priv, struct clock *clock,
 	int phc_index = -1, phc_switched = 0;
 	int state, timestamping, ret = -1;
 	struct port *p;
-	struct servo *servo;
 	struct sk_ts_info ts_info;
 	char iface[IFNAMSIZ];
 	clockid_t clkid = CLOCK_INVALID;
@@ -349,10 +345,9 @@ static void clock_reinit(struct phc2sys_private *priv, struct clock *clock,
 			clock->clkid = clkid;
 			clock->phc_index = phc_index;
 
-			servo = servo_add(priv, clock);
-			if (servo) {
+			if (clock->servo) {
 				servo_destroy(clock->servo);
-				clock->servo = servo;
+				clock->servo = NULL;
 			}
 
 			phc_switched = 1;
@@ -360,7 +355,8 @@ static void clock_reinit(struct phc2sys_private *priv, struct clock *clock,
 	}
 
 	if (new_state == PS_MASTER || phc_switched) {
-		servo_reset(clock->servo);
+		if (clock->servo)
+			servo_reset(clock->servo);
 		clock->servo_state = SERVO_UNLOCKED;
 
 		if (clock->offset_stats) {
@@ -571,6 +567,12 @@ static void update_clock(struct phc2sys_private *priv, struct clock *clock,
 	enum servo_state state;
 	double ppb;
 
+	if (!clock->servo) {
+		clock->servo = servo_add(priv, clock);
+		if (!clock->servo)
+			return;
+	}
+
 	if (clock_handle_leap(priv, clock, offset, ts))
 		return;
 
@@ -756,11 +758,6 @@ static int do_loop(struct phc2sys_private *priv, int subscriptions)
 			    !strcmp(clock->device, priv->master->device))
 				continue;
 
-			if (!clock->servo) {
-				pr_err("cannot update clock without servo");
-				return -1;
-			}
-
 			if (clock->clkid == CLOCK_REALTIME &&
 			    priv->master->sysoff_method >= 0) {
 				/* use sysoff */
@@ -1652,6 +1649,7 @@ int main(int argc, char *argv[])
 	if (pps_fd >= 0) {
 		/* only one destination clock allowed with PPS until we
 		 * implement a mean to specify PTP port to PPS mapping */
+		dst->servo = servo_add(&priv, dst);
 		servo_sync_interval(dst->servo, 1.0);
 		r = do_pps_loop(&priv, dst, pps_fd);
 	} else {
-- 
2.24.0

