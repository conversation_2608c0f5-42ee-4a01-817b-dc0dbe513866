From 1c64151eb430428a261d71a61be938b0f4085f4d Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Sun, 21 Feb 2021 12:30:17 -0800
Subject: [PATCH] Increase RTC sync tolerance to 20 ticks

Beagle<PERSON><PERSON> Black is unable to schedule sync accurately enough and in
certain cases keeps failing to sync indefinitely.
---
 include/linux/rtc.h | 4 ++--
 1 file changed, 2 insertions(+), 2 deletions(-)

diff --git a/include/linux/rtc.h b/include/linux/rtc.h
index df666cf29ef1..843f925f2a89 100644
--- a/include/linux/rtc.h
+++ b/include/linux/rtc.h
@@ -228,8 +228,8 @@ static inline bool rtc_tv_nsec_ok(s64 set_offset_nsec,
 				  struct timespec64 *to_set,
 				  const struct timespec64 *now)
 {
-	/* Allowed error in tv_nsec, arbitarily set to 5 jiffies in ns. */
-	const unsigned long TIME_SET_NSEC_FUZZ = TICK_NSEC * 5;
+	/* Allowed error in tv_nsec, arbitarily set to 20 jiffies in ns. */
+	const unsigned long TIME_SET_NSEC_FUZZ = TICK_NSEC * 20;
 	struct timespec64 delay = {.tv_sec = 0,
 				   .tv_nsec = set_offset_nsec};
 
-- 
2.17.1

