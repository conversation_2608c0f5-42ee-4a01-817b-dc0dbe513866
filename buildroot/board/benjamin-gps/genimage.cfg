image boot.vfat {
	vfat {
		files = {
			"MLO",
			"u-boot.img",
			"zImage",
			"uEnv.txt",
			"am335x-boneblack.dtb",
			"overlay-pps.dtb",
			"overlay-uart.dtb",
			"overlay-rtc.dtb",
			"overlay-leds.dtb",
			"overlay-spi.dtb",
			"overlay-eth.dtb"
		}
	}
	size = 8M
}

image sdcard.img {
	hdimage {
	}

	partition u-boot {
		partition-type = 0xC
		bootable = "true"
                image = "boot.vfat"
	}

	partition rootfs {
		partition-type = 0x83
		image = "rootfs.ext4"
		size = 64M
	}
}
