#include <config/convert/cpp/convert.hpp>
#include <fmt/format.h>
#include <lib/common/cpp/time.h>
#include <spdlog/spdlog.h>

namespace carbon {
namespace config {

ConfigType convert_proto_type(proto::ConfigType proto_type) {
  switch (proto_type) {
  case proto::ConfigType::NODE:
    return ConfigType::NODE;
  case proto::ConfigType::LIST:
    return ConfigType::LIST;
  case proto::ConfigType::BOOL:
    return ConfigType::BOOL;
  case proto::ConfigType::STRING:
    return ConfigType::STRING;
  case proto::ConfigType::INT:
    return ConfigType::INT;
  case proto::ConfigType::UINT:
    return ConfigType::UINT;
  case proto::ConfigType::FLOAT:
    return ConfigType::FLOAT;
  default:
    throw config_convert_error(fmt::format("this Proto Type Really shouldn't happen: {}", proto_type));
  }
}

void fill_tree_node(std::shared_ptr<ConfigTree> config_tree, const proto::ConfigNode &node, bool overwrite) {
  std::vector<std::string> reconciliation_trees;
  return fill_tree_node(config_tree, node, overwrite, reconciliation_trees, "");
}

void fill_tree_node(std::shared_ptr<ConfigTree> config_tree, const proto::ConfigNode &node, bool overwrite,
                    std::vector<std::string> &reconciliation_trees, std::string local_prefix, bool first) {
  if (node.name() != config_tree->get_name()) {
    spdlog::error("Config Names Not Matching: {}, Expected: {}", node.name(), config_tree->get_name());
    throw config_convert_error(
        fmt::format("Config Names Not Matching: {}, Expected: {}", node.name(), config_tree->get_name()));
  }

  ConfigType proto_type = convert_proto_type(node.def().type());

  if (proto_type != config_tree->get_def()->get_type()) {
    spdlog::error("Mismatched Node Types: {}, Expected: {}", node.def().type(), config_tree->get_def()->get_type());
    throw config_convert_error(
        fmt::format("Mismatched Node Types: {}, Expected: {}", node.def().type(), config_tree->get_def()->get_type()));
  }

  bool change_succeeded = true;
  std::string path = first ? ""
                           : (local_prefix == "" ? config_tree->get_name()
                                                 : fmt::format("{}/{}", local_prefix, config_tree->get_name()));

  switch (proto_type) {
  case ConfigType::NODE: {
    for (auto child_node : node.children()) {
      auto child_config_tree = config_tree->get_node(child_node.name());
      if (child_config_tree == nullptr) {
        spdlog::warn(fmt::format("Node Not Found in Tree: {}", child_node.name()));
        continue;
      }
      fill_tree_node(child_config_tree, child_node, overwrite, reconciliation_trees, path, false);
    }
    break;
  }
  case ConfigType::LIST: {
    std::set<std::string> valid;
    for (auto child_node : node.children()) {
      auto child_config_tree = config_tree->get_node(child_node.name());
      // Add New List Items
      if (child_config_tree == nullptr) {
        if (!overwrite &&
            config_tree->child_has_been_removed_after(child_node.name(), child_node.value().timestamp_ms())) {
          change_succeeded = false;
          continue; // do not add it as we already removed this locally
        }
        auto config_def = config_tree->get_def()->get_child("item");
        if (config_def == nullptr) {
          throw config_convert_error(fmt::format("Invalid List Node Def: {}", config_tree->get_name()));
        }
        child_config_tree = std::make_shared<ConfigTree>(child_node.name(), config_def);
        config_tree->add_child(child_config_tree);
      }

      // Propagate New Config
      fill_tree_node(child_config_tree, child_node, overwrite, reconciliation_trees, path, false);
      valid.insert(child_config_tree->get_name());
    }

    // Remove Items No Longer in List
    for (auto known_child : config_tree->get_children_nodes()) {
      if (valid.count(known_child->get_name()) == 0) {
        if (overwrite || node.value().timestamp_ms() > known_child->get_timestamp_ms()) {
          config_tree->remove_child(known_child->get_name());
        } else {
          change_succeeded = false;
        }
      }
    }
    if (node.value().timestamp_ms() > config_tree->get_timestamp_ms()) {
      config_tree->touch("", node.value().timestamp_ms());
    }
    config_tree->syncronized();
    break;
  }
  case ConfigType::INT:
    change_succeeded =
        config_tree->set_value<int64_t>(node.value().int64_val(), node.value().timestamp_ms(), !overwrite);
    break;
  case ConfigType::UINT:
    change_succeeded =
        config_tree->set_value<uint64_t>(node.value().uint64_val(), node.value().timestamp_ms(), !overwrite);
    break;
  case ConfigType::BOOL:
    change_succeeded = config_tree->set_value<bool>(node.value().bool_val(), node.value().timestamp_ms(), !overwrite);
    break;
  case ConfigType::FLOAT:
    change_succeeded =
        config_tree->set_value<double>(node.value().float_val(), node.value().timestamp_ms(), !overwrite);
    break;
  case ConfigType::STRING:
    change_succeeded =
        config_tree->set_value<std::string>(node.value().string_val(), node.value().timestamp_ms(), !overwrite);
    break;
  }

  if (!change_succeeded) {
    spdlog::warn("Needs Reconciliation: {}", node.name());
    reconciliation_trees.push_back(path);
  }
}

void touch_node(proto::ConfigNode *proto_node) {
  auto proto_value = proto_node->mutable_value();
  proto_value->set_timestamp_ms(maka_control_timestamp_ms());

  for (auto &child_node : *proto_node->mutable_children()) {
    touch_node(&child_node);
  }
}

void fill_proto_node(proto::ConfigNode *proto_node, std::shared_ptr<ConfigTree> tree_node) {
  proto_node->set_name(tree_node->get_name());
  auto proto_value = proto_node->mutable_value();
  auto proto_def = proto_node->mutable_def();

  auto tree_def = tree_node->get_def();
  proto_def->set_hint(tree_def->get_hint());
  proto_def->set_units(tree_def->get_units());
  proto_def->set_default_recommended(tree_def->is_default_recommended());
  switch (tree_def->get_complexity()) {
  case ConfigComplexity::USER:
    proto_def->set_complexity(proto::ConfigComplexity::USER);
    break;
  case ConfigComplexity::ADVANCED:
    proto_def->set_complexity(proto::ConfigComplexity::ADVANCED);
    break;
  case ConfigComplexity::EXPERT:
    proto_def->set_complexity(proto::ConfigComplexity::EXPERT);
    break;
  case ConfigComplexity::DEVELOPER:
    proto_def->set_complexity(proto::ConfigComplexity::DEVELOPER);
    break;
  }
  proto_value->set_timestamp_ms(tree_node->get_timestamp_ms());
  switch (tree_def->get_type()) {
  case ConfigType::STRING: {
    proto_value->set_string_val(tree_node->get_value<std::string>());
    proto_def->set_type(proto::ConfigType::STRING);
    auto string_def = proto_def->mutable_string_def();
    for (auto &choice : tree_def->get_choices()) {
      *string_def->add_choices() = choice;
    }
    string_def->set_size_limit(tree_def->get_string_size_limit());
    break;
  }
  case ConfigType::INT: {
    proto_value->set_int64_val(tree_node->get_value<int64_t>());
    proto_def->set_type(proto::ConfigType::INT);
    auto int_def = proto_def->mutable_int_def();
    int_def->set_max(tree_def->get_max<int64_t>());
    int_def->set_min(tree_def->get_min<int64_t>());
    int_def->set_step(tree_def->get_step<int64_t>());
    break;
  }
  case ConfigType::UINT: {
    proto_value->set_uint64_val(tree_node->get_value<uint64_t>());
    proto_def->set_type(proto::ConfigType::UINT);
    auto uint_def = proto_def->mutable_uint_def();
    uint_def->set_max(tree_def->get_max<uint64_t>());
    uint_def->set_min(tree_def->get_min<uint64_t>());
    uint_def->set_step(tree_def->get_step<uint64_t>());
    break;
  }
  case ConfigType::FLOAT: {
    proto_value->set_float_val(tree_node->get_value<double>());
    proto_def->set_type(proto::ConfigType::FLOAT);
    auto float_def = proto_def->mutable_float_def();
    float_def->set_max(tree_def->get_max<double>());
    float_def->set_min(tree_def->get_min<double>());
    float_def->set_step(tree_def->get_step<double>());
    break;
  }
  case ConfigType::BOOL: {
    proto_value->set_bool_val(tree_node->get_value<bool>());
    proto_def->set_type(proto::ConfigType::BOOL);
    break;
  }
  case ConfigType::NODE:
    proto_def->set_type(proto::ConfigType::NODE);
    break;
  case ConfigType::LIST:
    proto_def->set_type(proto::ConfigType::LIST);
    break;
  }

  for (auto child : tree_node->get_children_nodes()) {
    auto child_proto_node = proto_node->add_children();
    fill_proto_node(child_proto_node, child);
  }
}

} // namespace config
} // namespace carbon
