#pragma once

#include "generated/config/api/proto/config_service.grpc.pb.h"
#include "lib/common/cpp/exceptions.h"
#include <config/tree/cpp/config_tree.hpp>

namespace carbon {
namespace config {

class config_convert_error : public maka_error {
public:
  explicit config_convert_error(const std::string &what, size_t stack_skip = 1) : maka_error(what, stack_skip + 1) {}
};

ConfigType convert_proto_type(proto::ConfigType proto_type);

void fill_tree_node(std::shared_ptr<ConfigTree> config_tree, const proto::ConfigNode &node, bool overwrite,
                    std::vector<std::string> &reconciliation_trees, std::string local_prefix, bool first = true);
void fill_tree_node(std::shared_ptr<ConfigTree> config_tree, const proto::ConfigNode &node, bool overwrite);
void fill_proto_node(proto::ConfigNode *proto_node, std::shared_ptr<ConfigTree> tree_node);
void touch_node(proto::ConfigNode *proto_node);

} // namespace config
} // namespace carbon