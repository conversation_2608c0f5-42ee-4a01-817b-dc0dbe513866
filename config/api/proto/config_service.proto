syntax = "proto3";

package carbon.config.proto;
option go_package = "proto/config_service";

message PingRequest {
  int32 x = 1;
}

message PongResponse {
  int32 x = 1;
}

message ConfigValue {
  oneof value {
    int64 int64_val = 1;
    uint64 uint64_val = 2;
    bool bool_val = 3;
    double float_val = 4;
    string string_val = 5;
  }
  uint64 timestamp_ms = 6;
}

enum ConfigType {
  NODE = 0;
  LIST = 1;
  STRING = 2;
  INT = 3;
  UINT = 4;
  FLOAT = 5;
  BOOL = 6;
}

enum ConfigComplexity {
  USER = 0;
  ADVANCED = 1;
  EXPERT = 2;
  DEVELOPER = 3;
}

message IntConfigDef {
  int64 min = 1;
  int64 max = 2;
  int64 step = 3;
}

message UIntConfigDef {
  uint64 min = 1;
  uint64 max = 2;
  uint64 step = 3;
}

message FloatConfigDef {
  double min = 1;
  double max = 2;
  double step = 3;
}

message StringConfigDef {
  uint32 size_limit = 1;
  repeated string choices = 3;
}

message ConfigDef {
  ConfigType type = 1;
  ConfigComplexity complexity = 2;
  oneof extra {
    IntConfigDef int_def = 3;
    UIntConfigDef uint_def = 4;
    FloatConfigDef float_def = 5;
    StringConfigDef string_def = 6;
  }
  string hint = 7;
  bool default_recommended = 8;
  string units = 9;
}

message ConfigNode {
  string name = 1;
  ConfigValue value = 2;
  ConfigDef def = 3;
  repeated ConfigNode children = 4;
}

message ConfigLeaf {
  string key = 1;
  ConfigValue value = 2;
}

message SetValueRequest {
  string key = 1;
  ConfigValue value = 2;
}

message SetValueResponse {}

message GetTreeRequest {
  string key = 1;
}

message GetTreeResponse {
  ConfigNode node = 1;
}

message SetTreeRequest {
  string key = 1;
  ConfigNode node = 2;
}

message SetTreeResponse {

}

message GetLeavesRequest {
  string key = 1;
}

message GetLeavesResponse {
  repeated ConfigLeaf leaves = 1;
}

message AddToListRequest {
  string key = 1;
  string name = 2;
}

message AddToListResponse {

}

message RemoveFromListRequest {
  string key = 1;
  string name = 2;
}

message RemoveFromListResponse {

}

message SubscriptionRequest {
  repeated string keys = 1;
}

message SubscriptionNotifyMessage {
  string subscription_key = 1;
  string notify_key = 2;
}

message UpgradeCloudConfigRequest {
  string robot = 1;
  string to_template = 2;
  string from_template = 3;
}

message UpgradeCloudConfigResponse {

}

service ConfigService {
  rpc Ping(PingRequest) returns (PongResponse);
  rpc SetValue(SetValueRequest) returns (SetValueResponse);
  rpc GetTree(GetTreeRequest) returns (GetTreeResponse);
  rpc SetTree(SetTreeRequest) returns (SetTreeResponse);
  rpc GetLeaves(GetLeavesRequest) returns (GetLeavesResponse);
  rpc AddToList(AddToListRequest) returns (AddToListResponse);
  rpc RemoveFromList(RemoveFromListRequest) returns (RemoveFromListResponse);
  rpc UpgradeCloudConfig(UpgradeCloudConfigRequest) returns (UpgradeCloudConfigResponse);
}

service ConfigNotificationService {
  rpc Subscribe(SubscriptionRequest) returns (stream SubscriptionNotifyMessage);
}