CompileProto(./config_service.proto GENERATED_PATH GOPKG proto/config_service LANGS grpc_python python mypy grpc cpp go go-grpc)

add_library(config_service_proto SHARED ${GENERATED_PATH}/config_service.grpc.pb.cc ${GENERATED_PATH}/config_service.pb.cc)
add_dependencies(config_service_proto generate_config_api_proto_config_service)
target_compile_options(config_service_proto PRIVATE "-w")
target_link_libraries(config_service_proto grpc++ protobuf)
