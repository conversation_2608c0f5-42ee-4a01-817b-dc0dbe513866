#include <config/client/cpp/config_subscriber.hpp>
#include <config/client/cpp/exceptions.hpp>
#include <fmt/format.h>
#include <lib/common/cpp/utils/generation.hpp>
#include <spdlog/spdlog.h>

namespace carbon {
namespace config {

constexpr int kReconnectIntervalSec = 900;

std::string make_robot_local_addr(uint16_t port) {
  char *role_ptr = std::getenv("MAKA_ROLE");
  std::string role(common::GEN_BUD);
  if (role_ptr != NULL) {
    role = role_ptr;
  }
  std::string ip = "127.0.0.1";
  if (role == "row" || role == "row-primary" || role == "row-secondary" || role == "module") {
    ip = "*********";
  }
  if (port == 0) {
    return ip;
  }
  return fmt::format("{}:{}", ip, port);
}

std::string get_computer_config_prefix() {
  char *role_ptr = std::getenv("MAKA_ROLE");
  std::string role(common::GEN_BUD);
  if (role_ptr != NULL) {
    role = role_ptr;
  }

  char *row_ptr = std::getenv("MAKA_ROW");
  std::string row = "1";
  if (row_ptr != NULL) {
    row = row_ptr;
  }

  char *container_ptr = std::getenv("HOSTNAME");
  std::string container = "";
  if (container_ptr != NULL) {
    container = container_ptr;
  }

  if (common::is_slayer()) {
    if (role == "simulator" || role == "simulator_minicomputers") {
      if (container == "aimbot" || container == "cv" || container == "cv2" || container == "controls" ||
          container == "broadcaster") {
        return fmt::format("row{}", row);
      } else {
        return "command";
      }
    } else if (role == "row" || role == "row-primary" || role == "row-secondary") {
      return fmt::format("row{}", row);
    } else {
      return "command";
    }
  } else if (common::is_reaper()) {
    if (role == "simulator") {
      if (container == "aimbot" || container == "cv") {
        return fmt::format("rows/row{}", row);
      } else {
        return "command";
      }
    } else if (role == "module") {
      return fmt::format("rows/row{}", row);
    } else {
      return "command";
    }
  } else if (common::is_rtc()) {
    return "rtc";
  } else {
    return std::string(common::GEN_BUD);
  }
}

std::string get_row_computer_config_prefix(int row_id) {
  if (common::is_slayer()) {
    return fmt::format("row{}", row_id);
  } else if (common::is_reaper()) {
    return fmt::format("rows/row{}", row_id);
  } else {
    return std::string(common::GEN_BUD);
  }
}

std::string get_command_computer_config_prefix() {
  if (common::is_slayer() || common::is_reaper()) {
    return "command";
  } else {
    return std::string(common::GEN_BUD);
  }
}

std::shared_ptr<ConfigSubscriber> get_global_config_subscriber() {
  static std::shared_ptr<ConfigSubscriber> inst{new ConfigSubscriber(make_robot_local_addr(DEFAULT_PORT))};
  return inst;
}

ConfigSubscriber::ConfigSubscriber(const std::string &addr)
    : context_(std::make_unique<grpc::ClientContext>()), client_(std::make_shared<ConfigClient>(addr)),
      destroyed_(false), started_(false), ready_(false) {}

ConfigSubscriber::~ConfigSubscriber() {
  this->destroyed_ = true;
  if (this->context_ != nullptr) {
    this->context_->TryCancel();
  }
  if (this->subscription_thread_.joinable()) {
    this->subscription_thread_.join();
  }
}

std::shared_ptr<ConfigClient> ConfigSubscriber::get_client() { return this->client_; }

void ConfigSubscriber::add_config_tree(std::string name, std::string key, std::string def_path,
                                       std::optional<std::string> unique_key) {
  if (this->started_) {
    throw config_client_error(fmt::format("Subscriber already started, can't add new config tree: {}", name));
  }
  spdlog::info("Adding Config Tree to Subscriber: Name: {}, key: {}", name, key);
  const std::string &mapping_name(unique_key ? unique_key.value() : name);
  this->key_name_mapping_[key] = mapping_name;
  this->name_key_mapping_[mapping_name] = key;
  auto def_tree = std::make_shared<ConfigDefTree>(name, def_path);
  // TODO add cache path if we think that it's useful
  auto conf_tree = std::make_shared<ConfigTree>(name, def_tree);
  this->config_trees_[mapping_name] = conf_tree;
}

void ConfigSubscriber::add_existing_config_tree(std::string name, std::string key, std::shared_ptr<ConfigTree> tree) {
  if (this->started_) {
    throw config_client_error(fmt::format("Subscriber already started, can't add new config tree: {}", name));
  }
  spdlog::info("Adding Existing Config Tree to Subscriber: Name: {}, key: {}", name, key);
  this->key_name_mapping_[key] = name;
  this->name_key_mapping_[name] = key;
  this->config_trees_[name] = tree;
}

void ConfigSubscriber::start() {
  if (this->started_) {
    throw config_client_error("Subscriber already started");
  }
  this->subscription_thread_ = std::thread(&ConfigSubscriber::update_loop, this);
  this->started_ = true;
}

std::shared_ptr<ConfigTree> ConfigSubscriber::get_config_node(std::string name, std::string key) {
  if (this->config_trees_.count(name) == 0) {
    throw config_client_error(fmt::format("Config Tree Does not exist: {}", name));
  }
  if (key == "") {
    return this->config_trees_[name];
  }
  auto node = this->config_trees_[name]->get_node(key);
  if (node == nullptr) {
    throw config_client_error(fmt::format("Key {} not found in config tree: {}", key, name));
  }
  return node;
}

void ConfigSubscriber::notify(std::string subscription_key, std::string key) {
  if (this->key_name_mapping_.count(subscription_key) == 0) {
    spdlog::warn("Received Weird Subscription Notification: {}, for key {}", subscription_key, key);
    return;
  }

  std::string name = this->key_name_mapping_[subscription_key];

  if (this->config_trees_.count(name) == 0) {
    spdlog::error("Name Not found in config trees: {}", name);
    return;
  }

  spdlog::info("Received Notification: {}", key);
  auto config_tree = this->config_trees_[name];
  std::stringstream complete_key;
  complete_key << subscription_key << "/" << key;
  auto sub_tree = config_tree->get_node(key);
  if (sub_tree == nullptr || key == "") {
    this->client_->fill_tree(subscription_key, config_tree);
  } else {
    this->client_->fill_tree(complete_key.str(), sub_tree);
  }
}

void ConfigSubscriber::wait_until_ready() {
  std::unique_lock<std::mutex> lock(this->ready_mutex_);
  while (!this->ready_) {
    this->ready_cv_.wait(lock);
  }
}

bool ConfigSubscriber::refill() {
  // TODO at some point we can add some timestamp handling to optimize
  spdlog::info("Refilling...");
  try {
    for (auto &key : this->name_key_mapping_) {
      auto config_tree = this->config_trees_[key.first];
      this->client_->fill_tree(key.second, config_tree);
    }
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    spdlog::error("Failed to Refill {}", ex.what());
    return false;
  }
  {
    std::lock_guard<std::mutex> lock(this->ready_mutex_);
    this->ready_ = true;
    this->ready_cv_.notify_all();
  }
  return true;
}

void ConfigSubscriber::reset() {
  spdlog::info("Resetting Stream...");
  this->context_->TryCancel();
  this->stream_->Finish();
  this->stream_ = nullptr;
  spdlog::info("Resetting Stream Complete");
}

void ConfigSubscriber::update_loop() {
  std::vector<std::string> keys;

  for (auto &tree : this->name_key_mapping_) {
    keys.push_back(tree.second);
  }
  proto::SubscriptionNotifyMessage message;

  // TODO maybe once in a while we should do a periodic refill?
  // Maybe this periodic refill can be sent from the server so it's everywhere?
  while (!this->destroyed_) {
    try {
      if (this->stream_ == nullptr || this->context_ == nullptr) {
        this->client_->await_connection();
        this->context_ = std::make_unique<grpc::ClientContext>();
        std::chrono::system_clock::time_point deadline =
            std::chrono::system_clock::now() + std::chrono::seconds(kReconnectIntervalSec);
        this->context_->set_deadline(deadline);
        this->stream_ = this->client_->subscribe(*this->context_, keys);
        if (!this->refill()) {
          this->reset();
          std::this_thread::sleep_for(std::chrono::seconds(1));
          continue;
        }
      }

      if (!this->stream_->Read(&message)) {
        // This likely means that either deadline is passed and we need to reconnect,
        // or something went wrong with the connection, e.g. the server stopped running etc.
        this->reset();
        continue;
      }
      this->notify(message.subscription_key(), message.notify_key());
      if (this->notification_callback_ != nullptr) {
        (*this->notification_callback_)(message.notify_key());
      }
    } catch (std::exception &e) {
      spdlog::warn("Config subscriber update failed: {}", e.what());
      this->reset();
      continue;
    }
  }
}

void ConfigSubscriber::set_notification_callback(std::function<void(const std::string)> callback) {
  this->notification_callback_ = std::make_unique<std::function<void(const std::string)>>(callback);
}

} // namespace config
} // namespace carbon
