#pragma once

#include <fmt/format.h>
#include <grpcpp/grpcpp.h>

#include "lib/common/cpp/exceptions.h"

namespace carbon {
namespace config {

class config_client_error : public maka_error {
public:
  explicit config_client_error(const std::string &what, bool include_stacktrace = true, size_t stack_skip = 1)
      : maka_error(what, include_stacktrace, stack_skip + 1) {}

  static config_client_error from_status(const std::string &description, const grpc::Status &status) {
    const std::string message =
        fmt::format("{}; code: {}, message: {}", description, status.error_code(), status.error_message());
    return config_client_error(message, /*include_stacktrace=*/false);
  }
};

} // namespace config
} // namespace carbon