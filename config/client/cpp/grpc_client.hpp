#pragma once

#include "generated/config/api/proto/config_service.grpc.pb.h"
#include <config/tree/cpp/config_tree.hpp>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <string>

namespace carbon {
namespace config {

class ConfigClient {
protected:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<proto::ConfigService::Stub> stub_;
  std::shared_ptr<proto::ConfigNotificationService::Stub> notification_stub_;
  uint32_t max_backoff_ms_;
  double backoff_multiplier_;
  std::mutex channel_setup_mutex_;

public:
  ConfigClient(const std::string &addr = "127.0.0.1:61001", uint32_t max_backoff_ms = 30000,
               double backoff_multiplier = 1.5);
  ~ConfigClient();

  void ping();
  bool await_connection(uint64_t timeout_ms = 0);
  void reset();
  void fill_tree(std::string key, std::shared_ptr<ConfigTree> config_tree);
  ConfigType get_config_type(std::string key);
  template <typename T>
  void set_value(std::string key, T val);
  std::string get_list_dump(std::string key);
  std::vector<std::string> get_keys(std::string key);
  void add_to_list(std::string key, std::string name);
  void copy(std::string source, std::string target);
  void remove_from_list(std::string key, std::string name);
  std::unique_ptr<::grpc::ClientReader<::carbon::config::proto::SubscriptionNotifyMessage>>
  subscribe(grpc::ClientContext &context, std::vector<std::string> &keys);
  void set_tree(std::string key, std::shared_ptr<ConfigTree> tree);
  std::string get_addr() const;
  void cloud_upgrade(std::string robot, std::string from_template, std::string to_template);

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void reconcile(std::string prefix, std::shared_ptr<ConfigTree> config_tree, std::string config_path);
  void setup_grpc(bool reconnect_if_down = true);
  std::shared_ptr<proto::ConfigService::Stub> get_grpc_stub(bool reconnect_if_down = true);
  std::shared_ptr<proto::ConfigNotificationService::Stub> get_notification_stub();
  void reset_stub();
  void set_value_internal(std::string key, proto::SetValueRequest &request);
};

} // namespace config
} // namespace carbon
