#pragma once

#include <config/client/cpp/grpc_client.hpp>
#include <functional>
#include <optional>
#include <string>
#include <thread>

namespace carbon {
namespace config {

std::string make_robot_local_addr(uint16_t port);
std::string get_computer_config_prefix();
std::string get_command_computer_config_prefix();
std::string get_row_computer_config_prefix(int row_id);
static constexpr uint16_t DEFAULT_PORT = 61001;

class ConfigSubscriber {
private:
  std::unique_ptr<grpc::ClientContext> context_;
  std::shared_ptr<ConfigClient> client_;
  std::atomic<bool> destroyed_;
  std::atomic<bool> started_;
  std::unordered_map<std::string, std::shared_ptr<ConfigTree>> config_trees_;
  std::unordered_map<std::string, std::string> key_name_mapping_;
  std::unordered_map<std::string, std::string> name_key_mapping_;
  std::unique_ptr<::grpc::ClientReader<::carbon::config::proto::SubscriptionNotifyMessage>> stream_;
  std::thread subscription_thread_;
  std::unique_ptr<std::function<void(const std::string)>> notification_callback_;

  std::condition_variable ready_cv_;
  std::mutex ready_mutex_;
  bool ready_;

  void notify(std::string subscription_key, std::string key);
  void update_loop();
  void reset();

public:
  ConfigSubscriber(const std::string &addr = "127.0.0.1:61001");
  ~ConfigSubscriber();
  void add_config_tree(std::string name, std::string key, std::string def_path,
                       std::optional<std::string> unique_key = {});
  void add_existing_config_tree(std::string name, std::string key, std::shared_ptr<ConfigTree> tree);
  void start();
  bool refill();
  std::shared_ptr<ConfigTree> get_config_node(std::string name, std::string key);
  void wait_until_ready();
  std::shared_ptr<ConfigClient> get_client();
  inline bool started() { return started_; }
  void set_notification_callback(std::function<void(const std::string)> callback);
};

std::shared_ptr<ConfigSubscriber> get_global_config_subscriber();

} // namespace config
} // namespace carbon