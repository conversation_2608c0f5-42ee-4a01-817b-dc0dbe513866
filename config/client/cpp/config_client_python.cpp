#include <config/client/cpp/config_subscriber.hpp>
#include <config/client/cpp/exceptions.hpp>
#include <config/client/cpp/grpc_client.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

namespace py = pybind11;

namespace carbon {
namespace config {
template <typename T>
void declare_atomic_accessor(py::module &m, const std::string &typestr) {
  using Class = ConfigAtomicAccessor<T>;
  std::string pyclass_name = std::string("ConfigAtomicAccessor") + typestr;
  py::class_<Class, std::shared_ptr<Class>>(m, pyclass_name.c_str())
      .def(py::init<std::shared_ptr<ConfigTree>, std::optional<std::function<T(T)>>>(), py::arg("tree"),
           py::arg("initializer") = std::nullopt, py::call_guard<py::gil_scoped_release>())
      .def("get_value", &Class::get_value, py::call_guard<py::gil_scoped_release>());
}

PYBIND11_MODULE(config_client_python, m) {

  m.def("make_robot_local_addr", &make_robot_local_addr, py::arg("port"), py::call_guard<py::gil_scoped_release>());
  m.def("get_computer_config_prefix", &get_computer_config_prefix, py::call_guard<py::gil_scoped_release>());
  m.def("get_command_computer_config_prefix", &get_command_computer_config_prefix,
        py::call_guard<py::gil_scoped_release>());
  m.def("get_global_config_subscriber", &get_global_config_subscriber, py::call_guard<py::gil_scoped_release>());
  m.attr("DEFAULT_PORT") = py::int_(DEFAULT_PORT);

  py::register_local_exception<config_client_error>(m, "ConfigClientError", PyExc_RuntimeError);

  py::enum_<ConfigType>(m, "ConfigType")
      .value("NODE", ConfigType::NODE)
      .value("LIST", ConfigType::LIST)
      .value("STRING", ConfigType::STRING)
      .value("INT", ConfigType::INT)
      .value("UINT", ConfigType::UINT)
      .value("FLOAT", ConfigType::FLOAT)
      .value("BOOL", ConfigType::BOOL)
      .export_values();

  py::class_<ConfigDefTree, std::shared_ptr<ConfigDefTree>>(m, "ConfigDefTree")
      .def(py::init<std::string, std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("get_type", &ConfigDefTree::get_type, py::call_guard<py::gil_scoped_release>())
      .def("is_default_recommended", &ConfigDefTree::is_default_recommended, py::call_guard<py::gil_scoped_release>())
      .def("get_int_value", &ConfigDefTree::get_default_value<int64_t>, py::call_guard<py::gil_scoped_release>())
      .def("get_uint_value", &ConfigDefTree::get_default_value<uint64_t>, py::call_guard<py::gil_scoped_release>())
      .def("get_float_value", &ConfigDefTree::get_default_value<double>, py::call_guard<py::gil_scoped_release>())
      .def("get_bool_value", &ConfigDefTree::get_default_value<bool>, py::call_guard<py::gil_scoped_release>())
      .def("get_string_value", &ConfigDefTree::get_default_value<std::string>,
           py::call_guard<py::gil_scoped_release>());

  py::class_<ConfigTree, std::shared_ptr<ConfigTree>>(m, "ConfigTree")
      .def(py::init<std::string, std::shared_ptr<ConfigDefTree>>(), py::call_guard<py::gil_scoped_release>())
      .def(py::init<std::string, std::shared_ptr<ConfigDefTree>, std::string>(),
           py::call_guard<py::gil_scoped_release>())
      .def("get_node", py::overload_cast<std::string>(&ConfigTree::get_node), py::arg("key"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_children_nodes", &ConfigTree::get_children_nodes, py::call_guard<py::gil_scoped_release>())
      .def("get_name", &ConfigTree::get_name, py::call_guard<py::gil_scoped_release>())
      .def("get_timestamp_ms", &ConfigTree::get_timestamp_ms, py::call_guard<py::gil_scoped_release>())
      .def("get_def", &ConfigTree::get_def, py::call_guard<py::gil_scoped_release>())
      .def("get_int_value", &ConfigTree::get_value<int64_t>, py::call_guard<py::gil_scoped_release>())
      .def("get_uint_value", &ConfigTree::get_value<uint64_t>, py::call_guard<py::gil_scoped_release>())
      .def("get_float_value", &ConfigTree::get_value<double>, py::call_guard<py::gil_scoped_release>())
      .def("get_bool_value", &ConfigTree::get_value<bool>, py::call_guard<py::gil_scoped_release>())
      .def("get_string_value", &ConfigTree::get_value<std::string>, py::call_guard<py::gil_scoped_release>())
      .def("set_int_value", &ConfigTree::set_value<int64_t>, py::arg("value"), py::arg("timestamp_ms"),
           py::arg("timestamp_priority"), py::call_guard<py::gil_scoped_release>())
      .def("set_uint_value", &ConfigTree::set_value<uint64_t>, py::arg("value"), py::arg("timestamp_ms"),
           py::arg("timestamp_priority"), py::call_guard<py::gil_scoped_release>())
      .def("set_float_value", &ConfigTree::set_value<double>, py::arg("value"), py::arg("timestamp_ms"),
           py::arg("timestamp_priority"), py::call_guard<py::gil_scoped_release>())
      .def("set_bool_value", &ConfigTree::set_value<bool>, py::arg("value"), py::arg("timestamp_ms"),
           py::arg("timestamp_priority"), py::call_guard<py::gil_scoped_release>())
      .def("set_string_value", &ConfigTree::set_value<std::string>, py::arg("value"), py::arg("timestamp_ms"),
           py::arg("timestamp_priority"), py::call_guard<py::gil_scoped_release>())
      .def("to_string", &ConfigTree::to_string, py::call_guard<py::gil_scoped_release>())
      .def("get_path", &ConfigTree::get_path, py::call_guard<py::gil_scoped_release>())
      .def("register_callback", &ConfigTree::register_callback, py::arg("callback"),
           py::call_guard<py::gil_scoped_release>());

  py::class_<ConfigClient, std::shared_ptr<ConfigClient>>(m, "ConfigClient")
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("ping", &ConfigClient::ping, py::call_guard<py::gil_scoped_release>())
      .def("reset", &ConfigClient::reset, py::call_guard<py::gil_scoped_release>())
      .def("fill_tree", &ConfigClient::fill_tree, py::arg("key"), py::arg("config_tree"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_config_type", &ConfigClient::get_config_type, py::arg("key"), py::call_guard<py::gil_scoped_release>())
      .def("set_int_value", &ConfigClient::set_value<int64_t>, py::arg("key"), py::arg("value"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_uint_value", &ConfigClient::set_value<uint64_t>, py::arg("key"), py::arg("value"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_float_value", &ConfigClient::set_value<double>, py::arg("key"), py::arg("value"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_bool_value", &ConfigClient::set_value<bool>, py::arg("key"), py::arg("value"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_string_value", &ConfigClient::set_value<std::string>, py::arg("key"), py::arg("value"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_list_dump", &ConfigClient::get_list_dump, py::arg("key"), py::call_guard<py::gil_scoped_release>())
      .def("get_keys", &ConfigClient::get_keys, py::arg("key"), py::call_guard<py::gil_scoped_release>())
      .def("add_to_list", &ConfigClient::add_to_list, py::arg("key"), py::arg("name"),
           py::call_guard<py::gil_scoped_release>())
      .def("remove_from_list", &ConfigClient::remove_from_list, py::arg("key"), py::arg("name"),
           py::call_guard<py::gil_scoped_release>())
      .def("copy", &ConfigClient::copy, py::arg("source"), py::arg("target"), py::call_guard<py::gil_scoped_release>())
      .def("cloud_upgrade", &ConfigClient::cloud_upgrade, py::arg("robot"), py::arg("from_template"),
           py::arg("to_template"), py::call_guard<py::gil_scoped_release>());

  py::class_<ConfigSubscriber, std::shared_ptr<ConfigSubscriber>>(m, "ConfigSubscriber")
      .def(py::init<std::string>(), py::call_guard<py::gil_scoped_release>())
      .def("add_config_tree", &ConfigSubscriber::add_config_tree, py::arg("name"), py::arg("key"), py::arg("def_path"),
           py::arg("unique_key") = py::none(), py::call_guard<py::gil_scoped_release>())
      .def("start", &ConfigSubscriber::start, py::call_guard<py::gil_scoped_release>())
      .def("refill", &ConfigSubscriber::refill, py::call_guard<py::gil_scoped_release>())
      .def("get_config_node", &ConfigSubscriber::get_config_node, py::arg("name"), py::arg("key"),
           py::call_guard<py::gil_scoped_release>())
      .def("started", &ConfigSubscriber::started, py::call_guard<py::gil_scoped_release>())
      .def("wait_until_ready", &ConfigSubscriber::wait_until_ready, py::call_guard<py::gil_scoped_release>())
      .def("get_client", &ConfigSubscriber::get_client, py::call_guard<py::gil_scoped_release>());

  py::class_<AtomicFlagConfigScopedCallback, std::shared_ptr<AtomicFlagConfigScopedCallback>>(
      m, "AtomicFlagConfigScopedCallback")
      .def(py::init<std::shared_ptr<ConfigTree>, bool>(), py::call_guard<py::gil_scoped_release>())
      .def("reload_required", &AtomicFlagConfigScopedCallback::reload_required,
           py::call_guard<py::gil_scoped_release>())
      .def("tree", &AtomicFlagConfigScopedCallback::tree, py::call_guard<py::gil_scoped_release>());

  declare_atomic_accessor<float>(m, "Float");
  declare_atomic_accessor<bool>(m, "Bool");
  declare_atomic_accessor<uint32_t>(m, "UInt");
}

} // namespace config
} // namespace carbon
