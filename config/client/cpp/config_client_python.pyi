from typing import Callable, Generic, List, Optional, TypeVar

def make_robot_local_addr(port: int) -> str: ...
def get_computer_config_prefix() -> str: ...
def get_command_computer_config_prefix() -> str: ...
def get_global_config_subscriber() -> ConfigSubscriber: ...

DEFAULT_PORT: int

class ConfigClient:
    def __init__(self, addr: str) -> None: ...
    def ping(self) -> None: ...
    def reset(self) -> None: ...
    def fill_tree(self, key: str, config_tree: ConfigTree) -> None: ...
    def get_config_type(self, key: str) -> ConfigType: ...
    def set_int_value(self, key: str, val: int) -> None: ...
    def set_uint_value(self, key: str, val: int) -> None: ...
    def set_float_value(self, key: str, val: float) -> None: ...
    def set_bool_value(self, key: str, val: bool) -> None: ...
    def set_string_value(self, key: str, val: str) -> None: ...
    def get_list_dump(self, key: str) -> str: ...
    def get_keys(self, key: str) -> List[str]: ...
    def add_to_list(self, key: str, name: str) -> None: ...
    def remove_from_list(self, key: str, name: str) -> None: ...
    def copy(self, source: str, target: str) -> None: ...
    def cloud_upgrade(self, robot: str, from_template: str, to_template: str) -> None: ...

class ConfigClientError(RuntimeError): ...

class ConfigType:
    NODE: "ConfigType"
    LIST: "ConfigType"
    STRING: "ConfigType"
    INT: "ConfigType"
    UINT: "ConfigType"
    FLOAT: "ConfigType"
    BOOL: "ConfigType"

class ConfigDefTree:
    def __init__(self, name: str, definition_file_path: str) -> None: ...
    def get_type(self) -> ConfigType: ...
    def is_default_recommended(self) -> bool: ...
    def get_int_value(self) -> int: ...
    def get_uint_value(self) -> int: ...
    def get_float_value(self) -> float: ...
    def get_bool_value(self) -> bool: ...
    def get_string_value(self) -> str: ...

class ConfigTree:
    def __init__(self, name: str, def_tree: ConfigDefTree, file_path: Optional[str] = None) -> None: ...
    def get_node(self, key: str) -> ConfigTree: ...
    def get_children_nodes(self) -> List[ConfigTree]: ...
    def get_name(self) -> str: ...
    def get_timestamp_ms(self) -> int: ...
    def get_def(self) -> ConfigDefTree: ...
    def get_int_value(self) -> int: ...
    def get_uint_value(self) -> int: ...
    def get_float_value(self) -> float: ...
    def get_bool_value(self) -> bool: ...
    def get_string_value(self) -> str: ...
    def set_int_value(self, val: int, timestamp_ms: int, timestamp_priority: int) -> None: ...
    def set_uint_value(self, val: int, timestamp_ms: int, timestamp_priority: int) -> None: ...
    def set_float_value(self, val: float, timestamp_ms: int, timestamp_priority: int) -> None: ...
    def set_bool_value(self, val: bool, timestamp_ms: int, timestamp_priority: int) -> None: ...
    def set_string_value(self, val: str, timestamp_ms: int, timestamp_priority: int) -> None: ...
    def to_string(self) -> str: ...
    def get_path(self) -> str: ...
    def register_callback(self, callback: Callable[[], None]) -> int: ...

class ConfigSubscriber:
    def __init__(self, addr: str) -> None: ...
    def add_config_tree(self, name: str, key: str, def_path: str, unique_key: Optional[str] = None) -> None: ...
    def start(self) -> None: ...
    def started(self) -> bool: ...
    def refill(self) -> bool: ...
    def get_config_node(self, name: str, key: str) -> ConfigTree: ...
    def wait_until_ready(self) -> None: ...
    def get_client(self) -> ConfigClient: ...

class AtomicFlagConfigScopedCallback:
    def __init__(self, tree: ConfigTree, initial_sync_state: bool) -> None: ...
    def reload_required(self) -> bool: ...
    def tree(self) -> ConfigTree: ...

T = TypeVar("T")

class _ConfigAtomicAccessor(Generic[T]):
    def __init__(self, tree: ConfigTree, initializer: Optional[Callable[[T], T]] = None) -> None: ...
    def get_value(self) -> T: ...

ConfigAtomicAccessorFloat = _ConfigAtomicAccessor[float]
ConfigAtomicAccessorBool = _ConfigAtomicAccessor[bool]
ConfigAtomicAccessorUInt = _ConfigAtomicAccessor[int]
