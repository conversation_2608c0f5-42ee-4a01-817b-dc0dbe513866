type: "node"
children:
  aimbot:
    include: "services/aimbot.yaml"
  cv:
    include: "services/cv.yaml"
  host_check:
    include: "services/host_check.yaml"
  broadcaster:
    include: "services/broadcaster.yaml"
  row_synced:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Cloud sets to true once the row is synced with the cloud template. Set to false to force a resync. Used by reaper."
