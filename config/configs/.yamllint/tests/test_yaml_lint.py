import os
import subprocess
import pytest


def run_yamllint(file_path):
    """Run yamllint on a file and return the exit code and output."""
    result = subprocess.run(
        ["yamllint", file_path, "--config-file", ".yamllint/tests/test_config.yaml"],
        capture_output=True,
        text=True,
    )
    return result.returncode, result.stdout, result.stderr


def test_yaml_files():
    """Test that YAML files fail yamllint as expected."""
    test_dir = ".yamllint/tests/test_cases"
    for filename in os.listdir(test_dir):
        if filename.endswith(".yaml"):
            file_path = os.path.join(test_dir, filename)
            exit_code, stdout, stderr = run_yamllint(file_path)
            assert exit_code != 0, f"File {filename} passed yamllint when it should have failed"
            print(f"\nLinting errors in {filename}:")
            print(stdout)
