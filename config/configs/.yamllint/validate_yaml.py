#!/usr/bin/env python3

import os
import sys
import fnmatch
import yaml
import re
from pathlib import Path
from typing import Any

CONFIG_PATH = Path(".yamllint/rules.yaml")

IGNORED_PATHS = [
    "./.yamllint/tests/test_cases/*",
    "./.yamllint/rules.yaml",
    "./.github/*",
    "**/.venv/**",
]

ALLOWED_SPECIAL_KEYS = {
    "pointCategoriesLegacy",
    "segmentationCategories",
    "P2PCaptureReason_MISS",
    "P2PCaptureReason_SUCCESS",
    "P2PCaptureReason_JUMP",
    "P2PCaptureReason_First_P2P",
    "max_gpu_temp_C",
}


def should_ignore_path(file_path: str) -> bool:
    for pattern in IGNORED_PATHS:
        if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(os.path.relpath(file_path, "."), pattern):
            return True
    return False


def get_yaml_files() -> list[str]:
    yaml_files = []
    for root, _, files in os.walk("."):
        for file in files:
            if file.endswith((".yaml", ".yml")):
                file_path = os.path.join(root, file)
                if not should_ignore_path(file_path):
                    yaml_files.append(file_path)
    return yaml_files


def extract_keys(yaml_content: Any, current_path: str = "") -> set[str]:
    keys = set()
    if isinstance(yaml_content, dict):
        for key, value in yaml_content.items():
            if isinstance(key, str):
                full_key = f"{current_path}.{key}" if current_path else key
                keys.add(key)
                keys.update(extract_keys(value, full_key))
    elif isinstance(yaml_content, list):
        for item in yaml_content:
            keys.update(extract_keys(item, current_path))
    return keys


def validate_yaml_keys() -> bool:
    has_errors = False
    print("Validating YAML key names...")
    key_pattern = re.compile(r"^[a-z0-9_]+$")
    for file_path in get_yaml_files():
        print(f"Checking {file_path}...")
        try:
            with open(file_path, "r") as f:
                yaml_content = yaml.safe_load(f)
            if yaml_content is None:
                continue
            all_keys = extract_keys(yaml_content)
            invalid_keys = {key for key in all_keys if not key_pattern.match(key) and key not in ALLOWED_SPECIAL_KEYS}
            if invalid_keys:
                print(f"Error: Invalid keys found in {file_path}:")
                for key in invalid_keys:
                    print(f"  - '{key}' contains special or captialized characters")
                has_errors = True
        except Exception as e:
            print(f"Error processing {file_path}: {str(e)}")
            has_errors = True
    return not has_errors


def main():
    if validate_yaml_keys():
        print("All YAML files passed key validation!")
        sys.exit(0)
    else:
        print("YAML validation failed.")
        sys.exit(1)


if __name__ == "__main__":
    main()
