SHELL := /bin/bash

IMAGE_NAME = configs
VENV_DIR := .venv

.PHONY: \
	build lint-yaml test clean validate-yaml black

build:
	docker build -t $(IMAGE_NAME) .

lint: build $(VENV_DIR)/bin/activate
	echo "Linting YAML files..."
	docker run --rm \
		-v $(shell pwd):/configs \
		$(IMAGE_NAME) \
		yamllint /configs --config-file /configs/.yamllint/docker_rules.yaml
	echo "Validating YAML files..."
	. $(VENV_DIR)/bin/activate && \
	python -m pip install -r .yamllint/requirements.txt && \
	python .yamllint/validate_yaml.py

test: $(VENV_DIR)/bin/activate
	echo "Running YAML lint tests..."
	. $(VENV_DIR)/bin/activate && \
	python -m pip install -r .yamllint/requirements.txt && \
	python -m pytest .yamllint/tests/test_yaml_lint.py -v

$(VENV_DIR)/bin/activate:
	python3 -m venv $(VENV_DIR)

clean:
	rm -rf $(VENV_DIR)

black: $(VENV_DIR)/bin/activate
	echo "Formatting Python code with Black..."
	. $(VENV_DIR)/bin/activate && \
	python -m pip install -r .yamllint/requirements.txt && \
	black --config pyproject.toml .yamllint/
