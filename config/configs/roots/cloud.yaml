type: "node"
children:
  buds:
    type: "list"
    default_recommended: false
    item:
      include: "roots/bud.yaml"
  slayers:
    type: "list"
    default_recommended: false
    item:
      include: "roots/slayer.yaml"
  reapers:
    type: "list"
    default_recommended: false
    item:
      include: "roots/reaper.yaml"
  simulators:
    type: "list"
    default_recommended: false
    item:
      include: "roots/simulator.yaml"
  rtcs:
    type: "list"
    default_recommended: false
    item:
      include: "roots/tractor.yaml"
  default:
    type: "node"
    default_recommended: false
    children:
      slayer:
        include: "roots/slayer.yaml"
      bud:
        include: "roots/bud.yaml"
      simulator:
        include: "roots/simulator.yaml"
