type: "node"
children:
  use_static_kill_time:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Use the static kill time instead of the almanac"
  static_kill_time:
    type: "uint"
    default: 500
    complexity: "expert"
    hint: "When in static kill time mode, use this amount of milliseconds on every target"
  almanac_size_category_count:
    type: "uint"
    default: 3
    default_recommended: true
    complexity: "expert"
    hint: "This defines how many size categories to use in a type category. This should almost never change."
  base_time_multiplier:
    type: "float"
    default: 50
    complexity: "expert"
    hint: "Deprecated: [v1.16] Use almanac profiles"
  base_time:
    type: "float"
    default: 50
    complexity: "expert"
    hint: "Deprecated: [v1.16] Use almanac profiles"
  default_cat_importance:
    type: "float"
    default: 1.0
    max: 1.0
    complexity: "expert"
    hint: "Deprecated: [v1.16] Use almanac profiles"
  default_size_importance:
    type: "float"
    default: 1.0
    max: 1.0
    complexity: "expert"
    hint: "Deprecated: [v1.16] Use almanac profiles"
  default_multiplier:
    type: "float"
    default: 1.0
    complexity: "expert"
    hint: "Deprecated: [v1.16] Use almanac profiles"
  weed_categories:
    type: "list"
    hint: "Deprecated: [v1.16] Use almanac profiles"
    item:
      type: "node"
      children:
        multiplier:
          type: "float"
          default: 1.0
          complexity: "user"
          hint: "Deprecated: [v1.16] Use almanac profiles"
        threshold: # TODO Hint
          type: "float"
          default: 1.0
          complexity: "user"
        max_weed_size:
          type: "float"
          default: 100
          complexity: "expert"
        importance: # Anything less than 0 will NOT be shot
          type: "float"
          default: 1.0
          max: 1.0
          complexity: "user"
        use_in_velocity_estimator:
          type: "bool"
          default: true
          complexity: "user"
          hint: "Deprecated: [v1.16] Use almanac profiles"
  size_categories:
    type: "list"
    item:
      type: "node"
      children:
        multiplier:
          type: "float"
          default: 1.0
          complexity: "user"
        min:
          type: "float"
          default: 0.0
          complexity: "user"
        max:
          type: "float"
          default: 0.0
          complexity: "user"
        max_time:
          type: "uint"
          default: 0
          complexity: "user"
        importance: # Anything less than 0 will NOT be shot
          type: "float"
          default: 1.0
          max: 1.0
          complexity: "user"
        use_in_velocity_estimator:
          type: "bool"
          default: true
          complexity: "user"
          hint: "Deprecated: [v1.16] Use TVE profiles"
  point_categories:
    type: "list"
    item:
      type: "node"
      children:
        enabled:
          type: "bool"
          default: false
          complexity: "user"
