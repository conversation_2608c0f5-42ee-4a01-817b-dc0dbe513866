type: "node"
children:
  k:
    type: "float"
    default: 2.5
    complexity: "developer"
    default_recommended: true
    hint: "Gain term for cross track error"
  k_soft:
    type: "float"
    default: 1.5
    complexity: "developer"
    default_recommended: true
    hint: "Gain to soften cross track error at low speeds"
  k_d:
    type: "float"
    default: 0.1
    complexity: "developer"
    default_recommended: true
    hint: "Tuning parameter to account for time required for steering input to take effect"
  k_sigma:
    type: "float"
    default: 0.6
    complexity: "developer"
    default_recommended: true
    hint: "Gain for heading correction"
  k_vel:
    type: "float"
    default: 7
    complexity: "developer"
    default_recommended: true
    hint: "Gain for speed dampening"
