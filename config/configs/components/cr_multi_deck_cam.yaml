type: "node"
children:
  ip:
    type: "string"
    default: ""
    default_recommended: false
    complexity: "expert"
    hint: "The IP address of the multi sensor"
  width:
    type: "uint"
    default: 960
    default_recommended: true
    complexity: "expert"
    hint: "Width of the image output"
    units: "px"
  height:
    type: "uint"
    default: 600
    default_recommended: true
    complexity: "expert"
    hint: "Height of the image output"
    units: "px"
  frame_rate:
    type: "float"
    default: 30.0
    default_recommended: true
    complexity: "expert"
    hint: "The rate the sensor should run at"
    units: "fps"
  stereo_confidence_filter:
    type: "float"
    default: 0.5
    default_recommended: false
    min: 0
    max: 1.0
    complexity: "expert"
    hint: "Confidence threshold for disparity map"
  stereo_img_config:
    include: "components/cr_multi_image.yaml"
  aux_img_config:
    include: "components/cr_multi_image.yaml"
  aux_depth_lines:
    type: "node"
    children:
      edge_threshold:
        type: "float"
        default: 0.0
        complexity: "developer"
        hint: "Threshold to use for drawing lines at depth edged"
      start_dist:
        type: "uint"
        default: 1000
        complexity: "developer"
        units: "mm"
        hint: "Minimum distance line"
      delta_dist:
        type: "uint"
        default: 1000
        complexity: "developer"
        units: "mm"
        hint: "How far apart each line should be drawn"
      count:
        type: "uint"
        default: 10
        complexity: "developer"
        hint: "Max number of lines to draw"
  cameras:
    type: "node"
    children:
      depth:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Enable depth camera"
      edge:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Enable edge detection camera"
      edge_depth:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Enable depth with edge detection"
      aux:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Enable aux color cam"
      aux_depth:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Enable aux color cam with depth lines"
