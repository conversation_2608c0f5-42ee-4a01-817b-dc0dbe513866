type: "node"
children:
  type:
    type: "string"
    selected: "LucidCameraConfig"
    choices: ["LucidCameraConfig"]
    complexity: "expert"
    hint: "Type of camera"
  enabled:
    type: "bool"
    default: true
    complexity: "expert"
    hint: "Is this camera enabled to be used"
  serial:
    type: "string"
    default: ""
    complexity: "expert"
    hint: "Serial number for the camera"
  ip:
    type: "string"
    default: ""
    complexity: "expert"
    hint: "ip address for the camera"
  mirror:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Mirror the image horizontally, at the camera level, must be supported by the camera hardware"
  flip:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Flip the image vertically, at the camera level, must be supported by the camera hardware"
  ptp:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Use PTP for image timestamps"
  lidar:
    type: "node"
    children:
      phi_min:
        type: "float"
        default: -35
      phi_max:
        type: "float"
        default: 8
      phi_delta:
        type: "float"
        default: 2
      theta_min:
        type: "float"
        default: -45
      theta_max:
        type: "float"
        default: 45
      theta_delta:
        type: "float"
        default: 0.5
  hdr:
    type: "node"
    children:
      saturation:
        type: "float"
        default: -1.0
        complexity: "expert"
      contrast:
        type: "float"
        default: -1.0
        complexity: "expert"
      brightness:
        type: "float"
        default: -1.0
        complexity: "expert"
      detail:
        type: "float"
        default: -1.0
        complexity: "expert"
      average_num:
        type: "int"
        default: -1
        complexity: "expert"
      exposure_us:
        type: "float"
        default: 10000.0
        complexity: "expert"
      auto_brightness:
        type: "node"
        children:
          enabled:
            type: "bool"
            default: false
            complexity: "expert"
          min_hdr_brightness:
            type: "float"
            default: 100
            complexity: "expert"
          max_hdr_brightness:
            type: "float"
            default: 30000
            complexity: "expert"
          hdr_brightness_delta:
            type: "float"
            default: 100
            complexity: "expert"
          min_brightness:
            type: "float"
            default: 0.45
            complexity: "expert"
          max_brightness:
            type: "float"
            default: 0.55
            complexity: "expert"
          quantile:
            type: "float"
            default: 0.5
            complexity: "expert"

  depth_colorization:
    type: "node"
    children:
      min_value:
        type: "int"
        default: 3500
        complexity: "expert"
        hint: "All values below this in the depth map will be squashed to this value when colorizing"
      max_value:
        type: "int"
        default: 10000
        complexity: "expert"
        hint: "All values above this in the depth map will be squashed to this value when colorizing"
  capture:
    type: "node"
    children:
      persistence_rate:
        type: "float"
        default: 0
        complexity: "expert"
        hint: "probability that we'll save the output of this camera"
      use_deck_cv:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Switch to Deck CV for image capture"
  fisheye:
    type: "node"
    children:
      is_fisheye:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Is this camera a fisheye"
      k:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort K params"
      d:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort D params"
  binning:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "pixel binning square size"
    units: "px"
  width:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "output width of the camera"
    units: "px"
  height:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "output height of the camera"
    units: "px"
  buffer_count:
    type: "int"
    default: -1
    complexity: "developer"
    hint: "Number of buffers to reserve in arena SDK for image fetching. <= 0 means use default"
  extra_configs:
    type: "string"
    default: "" # base64 encoded json blob
    process: "base64"
    hint: "JSON object with key value pairs for nodemap config settings"
    complexity: "expert"
  zed_resolution:
    type: "string"
    selected: "HD1080"
    choices: ["HD1080", "HD2K", "HD1200", "HD720", "SVGA", "VGA"]
    complexity: "expert"
    hint: "Camera resolution settings"
  zed_depth_mode:
    type: "string"
    selected: "PERFORMANCE"
    choices: ["PERFORMANCE", "NEURAL_PLUS", "ULTRA", "NEURAL", "QUALITY"]
    complexity: "expert"
    hint: "Mode to compute depth"
  cv_required:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Deprecated Is this camera needed for CV, if not in current view we will still grab frames for capturing"
  cv:
    include: "components/deck_cam_cv.yaml"
  sensor_types:
    type: "list"
    item:
      type: "string"
      selected: "depth"
      choices: ["depth"]
      complexity: "expert"
      hint: "Type of sensors to use for this camera"
