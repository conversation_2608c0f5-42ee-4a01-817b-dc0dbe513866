type: "node"
children:
  skip_list:
    type: "list"
    item:
      type: "int"
      default: 0 # unused
      complexity: "expert"
  extra_params:
    type: "list"
    item:
      type: "string"
      default: "" # base64 encoded json blob
      hint: "Depreciated use extra_params_encoded"
      complexity: "expert"
  extra_params_encoded:
    type: "list"
    item:
      type: "string"
      default: "" # base64 encoded json blob
      process: "base64"
      hint: "JSON object with key value pairs to override kwargs in device"
      complexity: "expert"
  strobe_control_params:
    type: "node"
    children:
      exposure_us:
        type: "int"
        default: 800
        complexity: "expert"
      period_us:
        type: "int"
        default: 33333
        complexity: "expert"
      targets_per_predict_ratio:
        type: "int"
        default: 5
        complexity: "expert"
      slayer_strobe_recommendation_override:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "expert"
        hint: "Override the slayer strobe recommendation code, set directly from config"
  scanner_params:
    type: "list"
    item:
      type: "node"
      children:
        intensity:
          type: "float"
          default: 1.0
          complexity: "expert"
