type: "node"
children:
  tracking_service_port:
    type: "uint"
    default: 65432
    default_recommended: true
    complexity: "developer"
  missed_delete_ttl:
    type: "uint"
    default: 5
    default_recommended: false
    complexity: "developer"
    units: "seconds"
    hint: "Deprecated: [v1.12]"
  add_max_time:
    type: "int"
    default: 100
    default_recommended: false
    complexity: "developer"
    units: "ms"
    hint: "Deprecated: [v1.12]"
  add_max_age:
    type: "int"
    default: 2000
    default_recommended: false
    complexity: "developer"
    units: "ms"
    hint: "Deprecated: [v1.12]"
  next_weed_look_ahead:
    type: "int"
    units: "ms"
    default: 40
    default_recommended: true
    complexity: "developer"
    hint: "How far ahead to estimate position based on settle times"
  merge_radius:
    type: "float"
    default: 2.5
    default_recommended: false
    complexity: "developer"
    units: "mm"
    hint: "Deprecated: [2022-11-12]"
  multi_laser_enabled:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
  cancellable:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
    hint: "Allow weeds that are currently being shot to be cancelled in favor of others."
  ignore_weeds:
    type: "bool"
    default: false
    complexity: "user"
    hint: "Deprecated: [2022-12-02] see targeting_mode"
  not_moving_time:
    type: "uint"
    default: 10000
    complexity: "developer"
    units: "ms"
    hint: "How long velocity must be below threshold to be considered not moving."
    default_recommended: true
  scoring:
    type: "node"
    children:
      min_detections_over_opportunities:
        type: "float"
        default: 0
        complexity: "developer"
        hint: "The minimum value of deepweed detections divided by deepweed opportunities (0 through 1)
        in order to target the weed, where 0 means no detections required (will target everything) and
        1 means detections required for all images (likely will never shoot anything)."
      thread_pool_size:
        type: "uint"
        default: 0
        default_recommended: true
        complexity: "developer"
        hint: "0 means use num scanners"
      pan_offset:
        type: "int"
        default: 100
        units: "ticks"
        default_recommended: true
        complexity: "developer"
        hint: "Offset from min/max"
      tilt_offset_back:
        type: "int"
        default: 1000
        default_recommended: true
        units: "ticks"
        complexity: "developer"
        hint: "How far from back of travel"
      tilt_offset_back_killed:
        type: "int"
        default: 1000
        default_recommended: true
        units: "ticks"
        complexity: "developer"
        hint: "How far from back of travel when finished lasing"
      tilt_offset_front:
        type: "int"
        units: "ticks"
        default: 500
        default_recommended: true
        complexity: "developer"
        hint: "How far from top of travel"
      pan_inset_filter:
        type: "int"
        units: "ticks"
        default: 500
        default_recommended: true
        complexity: "developer"
      starting_weed_look_ahead:
        type: "int"
        units: "ms"
        default: 50
        default_recommended: true
        complexity: "developer"
      laser_overhead_time:
        type: "int"
        units: "ms"
        default: 200
        default_recommended: true
        complexity: "developer"
      ignore_weeds_out_of_bands: # if enabled and banding enabled weeds not in band WILL NEVER be shot
        type: "bool"
        default: false
        default_recommended: true
        complexity: "user"
        hint: "Deprecated: [v1.16] no longer can toggle false we will never shoot out of band now."
      prioritize_crops:
        type: "bool"
        default: true
        default_recommended: true
        complexity: "developer"
        hint: "Should we try to prioritize shooting crops over weeds when weeding and thinning? If going <= target velocity prioritization should not be needed."
      weights:
        type: "node"
        children:
          tilt:
            type: "float"
            default: 1000.0
            default_recommended: true
            complexity: "expert"
          pan:
            type: "float"
            default: 50.0
            default_recommended: true
            complexity: "expert"
          busy:
            type: "float"
            default: 500.0
            default_recommended: true
            complexity: "expert"
          being_shot: # boost the weed currently being shot so we don't jump around too much
            type: "float"
            default: 100.0
            default_recommended: true
            complexity: "expert"
          in_range:
            type: "float"
            default: 1000.0
            default_recommended: true
            complexity: "expert"
          category_importance: # how much we care about this type of weed
            type: "float"
            default: 500.0
            default_recommended: true
            complexity: "expert"
          size: # weighting smaller weeds more
            type: "float"
            default: 500.0
            default_recommended: true
            complexity: "expert"
      max_extermination_failures:
        type: "uint"
        default: 3
        default_recommended: true
        complexity: "developer"
  velocity_smoothing:
    type: "node"
    children:
      min_vel_mph:
        type: "float"
        units: "mph"
        default: 0.05
        default_recommended: true
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      max_vel_mph:
        type: "float"
        units: "mph"
        default: 1.5
        default_recommended: true
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      initial_vel_mph:
        type: "float"
        units: "mph"
        default: 0.5
        default_recommended: true
        complexity: "expert"
        hint: "Velocity to be recommended when we dont yet have an estimate"
      decrease_averaging:
        type: "float"
        default: .1
        default_recommended: true
        min: 0.0
        max: 1.0
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      increase_averaging:
        type: "float"
        default: .05
        default_recommended: true
        min: 0.0
        max: 1.0
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      max_weed_time_ms_for_vel:
        type: "uint"
        units: "ms"
        default: 2000
        default_recommended: true
        complexity: "expert"
      min_delta_vel_mph:
        type: "float"
        units: "mph"
        default: 0.1
        default_recommended: true
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      window_size_multiplier:
        type: "float"
        default: 1.0
        default_recommended: true
        min: 0.0
        max: 100.0
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      auto_overhead_speed_multiplier:
        type: "float"
        default: 1.0
        default_recommended: true
        min: 0.0
        complexity: "expert"
        hint: "Adjust the overall estimated velocity"
      dist_meters:
        type: "float"
        units: "m"
        default: 1.0
        default_recommended: true
        complexity: "expert"
        min: 1.0
        max: 100.0
      debug_logging:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Enable debug logs which will be verbose"
      velocity_offest:
        type: "float"
        units: "mph"
        default: 0.0
        default_recommended: true
        complexity: "developer"
        hint: "Deprecated: [v1.18] use TVE profiles"
      kill_percent_target:
        type: "float"
        default: 1
        default_recommended: true
        min: 0.0
        max: 1.0
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      forward_dist_offset:
        type: "float"
        units: "mm"
        default: 0.0
        default_recommended: true
        complexity: "developer"
        hint: "Offset from front of predict space to start counting targets. Larger means less flicker but older data."
      auto_calculate_usage:
        type: "bool"
        default: false
        complexity: "developer"
        hint: "Deprecated: [v1.18] use TVE profiles"
      max_error_rate:
        type: "float"
        default: 0.2
        min: 0.0
        max: 1.0
        default_recommended: true
        complexity: "developer"
        hint: "clamp avg error rate by this value to prevent error rate taking over estimation"
      sort_targets:
        type: "bool"
        default: true
        default_recommended: true
        complexity: "developer"
        hint: "Feature flag to sort targets by y pos to better represent reality."
      cache_crops:
        type: "bool"
        default: false
        default_recommended: true
        complexity: "developer"
        hint: "Feature flag to cache crops to help provide enough data for VE while thinning"
