type: "node"
complexity: "expert"
children:
  module:
    type: "node"
    children:
      predict_cam_temp:
        type: "node"
        children:
          min:
            type: "float"
            default: -20
            units: "C"
            hint: "Lower acceptable limit for predict camera temperature"
          max:
            type: "float"
            default: 60
            units: "C"
            hint: "Upper acceptable limit for predict camera temperature"
      scanner:
        type: "node"
        children:
          laser:
            type: "node"
            children:
              current:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: 0
                    units: "mA"
                    hint: "Lower acceptable limit for laser diode current"
                  max:
                    type: "float"
                    default: 12500
                    units: "mA"
                    hint: "Upper acceptable limit for laser diode current"
              humidity:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: 20
                    units: "%RH"
                    hint: "Lower acceptable limit for laser internal humidity"
                  max:
                    type: "float"
                    default: 55
                    units: "%RH"
                    hint: "Upper acceptable limit for laser internal humidity"
              temp:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: 15
                    units: "C"
                    hint: "Lower acceptable limit for laser internal temperature"
                  max:
                    type: "float"
                    default: 40
                    units: "C"
                    hint: "Upper acceptable limit for laser internal temperature"
          motor:
            type: "node"
            children:
              current:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: -1
                    units: "A"
                    hint: "Lower acceptable limit for motor controller drive current"
                  max:
                    type: "float"
                    default: 1
                    units: "A"
                    hint: "Upper acceptable limit for motor controller drive current"
              supply:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: 20
                    units: "V"
                    hint: "Lower acceptable limit for motor controller drive supply"
                  max:
                    type: "float"
                    default: 26
                    units: "V"
                    hint: "Upper acceptable limit for motor controller drive supply"
              output_temp:
                type: "node"
                children:
                  min:
                    type: "float"
                    default: 5
                    units: "C"
                    hint: "Lower acceptable limit for motor controller output stage temperature"
                  max:
                    type: "float"
                    default: 90
                    units: "C"
                    hint: "Upper acceptable limit for motor controller output stage temperature"
          power_current:
            type: "node"
            children:
              min:
                type: "float"
                default: 0.05
                units: "A"
                hint: "Lower acceptable limit for scanner current consumption"
              max:
                type: "float"
                default: 1.5
                units: "A"
                hint: "Upper acceptable limit for scanner current consumption"
          target_cam_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: -20
                units: "C"
                hint: "Lower acceptable limit for target camera temperature"
              max:
                type: "float"
                default: 60
                units: "C"
                hint: "Upper acceptable limit for target camera temperature"
          temp_collimator:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "C"
                hint: "Lower acceptable limit for laser collimator temperature"
              max:
                type: "float"
                default: 50
                units: "C"
                hint: "Upper acceptable limit for laser collimator temperature"
          temp_fiber:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "C"
                hint: "Lower acceptable limit for laser fiber temperature"
              max:
                type: "float"
                default: 50
                units: "C"
                hint: "Upper acceptable limit for laser fiber temperature"
          laser_power_w:
            type: "node"
            children:
              min:
                type: "float"
                default: 200
                units: "W"
              max:
                type: "float"
                default: 265
                units: "W"
      pc:
        type: "node"
        children:
          cpu_core_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 25
                units: "C"
                hint: "Lower acceptable limit for module PC core temperature"
              max:
                type: "float"
                default: 95
                units: "C"
                hint: "Upper acceptable limit for module PC core temperature"
          system_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 15
                units: "C"
                hint: "Lower acceptable limit for module PC motherboard temperature"
              max:
                type: "float"
                default: 55
                units: "C"
                hint: "Upper acceptable limit for module PC motherboard temperature"
          gpu_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 15
                units: "C"
                hint: "Lower acceptable limit for module GPU temperature"
              max:
                type: "float"
                default: 75
                units: "C"
                hint: "Upper acceptable limit for module GPU temperature"
          psu_12v:
            type: "node"
            children:
              min:
                type: "float"
                default: 11.4
                units: "V"
                hint: "Lower acceptable limit for module PC power supply 12V rail"
              max:
                type: "float"
                default: 12.6
                units: "V"
                hint: "Upper acceptable limit for module PC power supply 12V rail"
          psu_5v:
            type: "node"
            children:
              min:
                type: "float"
                default: 4.75
                units: "V"
                hint: "Lower acceptable limit for module PC power supply 5V rail"
              max:
                type: "float"
                default: 5.25
                units: "V"
                hint: "Upper acceptable limit for module PC power supply 5V rail"
          psu_3v3:
            type: "node"
            children:
              min:
                type: "float"
                default: 3.13
                units: "V"
                hint: "Lower acceptable limit for module PC power supply 3.3V rail"
              max:
                type: "float"
                default: 3.47
                units: "V"
                hint: "Upper acceptable limit for module PC power supply 3.3V rail"
          loadavg:
            type: "node"
            children:
              min:
                type: "float"
                default: 0.1
                hint: "Lower acceptable limit for module PC load average"
              max:
                type: "float"
                default: 16
                hint: "Upper acceptable limit for module PC load average"
          disk_usage:
            type: "node"
            children:
              min:
                type: "float"
                default: 1
                units: "%"
                hint: "Lower acceptable limit for module PC disk usage"
              max:
                type: "float"
                default: 75
                units: "%"
                hint: "Upper acceptable limit for module PC disk usage"
          ram_usage:
            type: "node"
            children:
              min:
                type: "float"
                default: 1
                units: "%"
                hint: "Lower acceptable limit for module PC RAM usage"
              max:
                type: "float"
                default: 90
                units: "%"
                hint: "Upper acceptable limit for module PC RAM usage"
      enviro:
        type: "node"
        children:
          pc_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "C"
                hint: "Lower acceptable limit for module PC zone temperature"
              max:
                type: "float"
                default: 60
                units: "C"
                hint: "Upper acceptable limit for module PC zone temperature"
          pc_humidity:
            type: "node"
            children:
              min:
                type: "float"
                default: 25
                units: "%RH"
                hint: "Lower acceptable limit for module PC zone humidity"
              max:
                type: "float"
                default: 60
                units: "%RH"
                hint: "Upper acceptable limit for module PC zone humidity"
          pc_pressure:
            type: "node"
            children:
              min:
                type: "float"
                default: 950
                units: "hPa"
                hint: "Lower acceptable limit for module PC zone air pressure"
              max:
                type: "float"
                default: 1050
                units: "hPa"
                hint: "Upper acceptable limit for module PC zone air pressure"
          enclosure_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "C"
                hint: "Lower acceptable limit for module ambient temperature"
              max:
                type: "float"
                default: 60
                units: "C"
                hint: "Upper acceptable limit for module ambient temperature"
          enclosure_humidity:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "%RH"
                hint: "Lower acceptable limit for module ambient humidity"
              max:
                type: "float"
                default: 50
                units: "%RH"
                hint: "Upper acceptable limit for module ambient humidity"
          enclosure_pressure:
            type: "node"
            children:
              min:
                type: "float"
                default: 950
                units: "hPa"
                hint: "Lower acceptable limit for module ambient air pressure"
              max:
                type: "float"
                default: 1050
                units: "hPa"
                hint: "Upper acceptable limit for module ambient air pressure"

      coolant:
        type: "node"
        children:
          inlet_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 15
                units: "C"
                hint: "Lower acceptable limit for coolant inlet temperature"
              max:
                type: "float"
                default: 25
                units: "C"
                hint: "Upper acceptable limit for coolant inlet temperature"
          inlet_pressure:
            type: "node"
            children:
              min:
                type: "float"
                default: 75
                units: "KPa"
                hint: "Lower acceptable limit for coolant inlet pressure"
              max:
                type: "float"
                default: 250
                units: "KPa"
                hint: "Upper acceptable limit for coolant inlet pressure"
          outlet_temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 15
                units: "C"
                hint: "Lower acceptable limit for coolant outlet temperature"
              max:
                type: "float"
                default: 35
                units: "C"
                hint: "Upper acceptable limit for coolant outlet temperature"
          outlet_pressure:
            type: "node"
            children:
              min:
                type: "float"
                default: 75
                units: "KPa"
                hint: "Lower acceptable limit for coolant outlet pressure"
              max:
                type: "float"
                default: 200
                units: "KPa"
                hint: "Upper acceptable limit for coolant inlet pressure"

      strobe:
        type: "node"
        children:
          temp:
            type: "node"
            children:
              min:
                type: "float"
                default: 5
                units: "C"
                hint: "Lower acceptable limit for strobe board temperature"
              max:
                type: "float"
                default: 85
                units: "C"
                hint: "Upper acceptable limit for strobe board temperature"
          voltage:
            type: "node"
            children:
              min:
                type: "float"
                default: 86
                units: "V"
                hint: "Lower acceptable limit for strobe capacitor bank voltage"
              max:
                type: "float"
                default: 96
                units: "V"
                hint: "Upper acceptable limit for strobe capacitor bank voltage"
          current:
            type: "node"
            children:
              min:
                type: "float"
                default: 2.0
                units: "A"
                hint: "Lower acceptable limit for strobe LED current"
              max:
                type: "float"
                default: 4.75
                units: "A"
                hint: "Upper acceptable limit for strobe LED current"

  chiller:
    type: "node"
    children:
      flow:
        type: node
        children:
          min:
            type: "float"
            default: 70
            units: "L/min"
          max:
            type: "float"
            default: 150
            units: "L/min"
      pressure:
        type: node
        children:
          min:
            type: "float"
            default: 75
            units: "Kpa"
          max:
            type: "float"
            default: 150
            units: "Kpa"
      conductivity:
        type: node
        children:
          min:
            type: "float"
            default: 0
            units: "µS/cm"
            hint: "Lower acceptable limit for chiller fluid conductivity"
          max:
            type: "float"
            default: 5
            units: "µS/cm"
            hint: "Upper acceptable limit for chiller fluid conductivity"
      set_temp:
        type: node
        children:
          min:
            type: "float"
            default: 15
            units: "C"
          max:
            type: "float"
            default: 20
            units: "C"
      heat_transfer:
        type: node
        children:
          min:
            type: "float"
            default: 0
            units: "kBTU"
          max:
            type: "float"
            default: 20
            units: "kBTU"
      delta_temp:
        type: node
        children:
          min:
            type: "float"
            default: 0
          max:
            type: "float"
            default: 20
  enclosure:
    type: "node"
    children:
      humidity:
        type: node
        children:
          min:
            type: "float"
            default: 5
            units: "%RH"
            hint: "Lower acceptable limit for center enclosure interior humidity"
          max:
            type: "float"
            default: 75
            units: "%RH"
            hint: "Upper acceptable limit for center enclosure interior humidity"
      temp:
        type: node
        children:
          min:
            type: "float"
            default: 5
            units: "C"
            hint: "Lower acceptable limit for center enclosure interior temperature"
          max:
            type: "float"
            default: 45
            units: "C"
            hint: "Upper acceptable limit for center enclosure interior temperature"
  power:
    type: "node"
    children:
      battery:
        type: "node"
        children:
          min:
            type: "float"
            default: 12
            units: "V"
            hint: "Lower acceptable limit for ISOBUS/tractor battery voltage"
          max:
            type: "float"
            default: 14.5
            units: "V"
            hint: "Upper acceptable limit for ISOBUS/tractor battery voltage"
      current:
        type: "node"
        children:
          min:
            type: "float"
            default: 1
            units: "A"
            hint: "Lower acceptable limit for AC power phase current"
          max:
            type: "float"
            default: 80
            units: "A"
            hint: "Upper acceptable limit for AC power phase current"
      phase_v:
        type: "node"
        children:
          min:
            type: "float"
            default: 200
            units: "V"
            hint: "Lower acceptable limit for AC power phase voltage"
          max:
            type: "float"
            default: 250
            units: "V"
            hint: "Upper acceptable limit for AC power phase voltage"
      frequency:
        type: "node"
        children:
          min:
            type: "float"
            default: 58
            units: "Hz"
            hint: "Lower acceptable limit for AC power frequency"
          max:
            type: "float"
            default: 62
            units: "Hz"
            hint: "Upper acceptable limit for AC power frequency"
      power_factor:
        type: "node"
        children:
          min:
            type: "float"
            default: 0.75
            hint: "Lower acceptable limit for AC power factor"
          max:
            type: "float"
            default: 1.0
            hint: "Upper acceptable limit for AC power factor"
