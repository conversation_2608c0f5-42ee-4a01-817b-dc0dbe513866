type: "node"
children:
  required:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Is this camera needed for CV, if not in current view we will still grab frames for capturing"
  furrows_enabled:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Should furrow detections be enabled for this camera?"
  reduction_ratio:
    type: "int"
    default: 5
    complexity: "expert"
    hint: "Reduction ratio 1/n on camera stream for images to send to Deck CV"
  furrows_reduction_ratio:
    type: "int"
    default: 1
    complexity: "expert"
    hint: "Reduction ratio 1/n on camera stream for images to process for furrows"
  brightness_adjuster_reduction_ratio:
    type: "int"
    default: 6
    complexity: "expert"
    hint: "Reduction ratio 1/n on camera stream for images to process in brightness adjuster"
  persistence_rate:
    type: "float"
    default: 0
    complexity: "expert"
    hint: "probability that we'll save the output of this camera"
