type: "node"
children:
  gamma:
    type: "float"
    default: 2.2
    default_recommended: true
    complexity: "expert"
    hint: "The gamma correction for image"
  auto_exposure:
    type: "node"
    children:
      max_exposure_time:
        type: "uint"
        default: 10000
        default_recommended: true
        complexity: "expert"
        hint: "The max exposure time"
        units: "us"
      decay:
        type: "uint"
        default: 7
        min: 0
        max: 20
        default_recommended: false
        complexity: "expert"
        hint: "The max decay rate for auto exposure"
      target_intensity:
        type: "float"
        default: 0.95
        default_recommended: false
        min: 0
        max: 1.0
        complexity: "expert"
        hint: "Auto exposure target intensity"
      target_threshold:
        type: "float"
        default: 0.75
        default_recommended: false
        min: 0
        max: 1.0
        complexity: "expert"
        hint: "Auto exposure threshold"
      max_gain:
        type: "float"
        default: 2.0
        default_recommended: false
        complexity: "expert"
        hint: "Auto exposure max gain"
      roi:
        type: "node"
        children:
          start_x:
            type: "uint"
            default: 0
            default_recommended: true
            complexity: "expert"
            hint: "Starting X position of the ROI"
            units: "px"
          start_y:
            type: "uint"
            default: 0
            default_recommended: true
            complexity: "expert"
            hint: "Starting Y position of the ROI"
            units: "px"
          width:
            type: "uint"
            default: 960
            default_recommended: true
            complexity: "expert"
            hint: "Width of the ROI"
            units: "px"
          height:
            type: "uint"
            default: 600
            default_recommended: true
            complexity: "expert"
            hint: "Height of the ROI"
            units: "px"
  auto_white_balance:
    type: "node"
    children:
      decay:
        type: "uint"
        default: 3
        min: 0
        max: 20
        default_recommended: false
        complexity: "expert"
        hint: "The max decay rate for auto white balance"
      threshold:
        type: "float"
        default: 0.5
        default_recommended: false
        min: 0
        max: 1.0
        complexity: "expert"
        hint: "Auto white balance threshold"
