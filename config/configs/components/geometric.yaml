type: "node"
children:
  predicts:
    type: "list"
    item:
      type: "node"
      children:
        offset:
          type: "node"
          children:
            x:
              type: "float"
              default: 0.0
              complexity: "user"
            "y":
              type: "float"
              default: 0.0
              complexity: "user"
            z:
              type: "float"
              default: 0.0
              complexity: "user"
  scanners:
    type: "list"
    item:
      type: "node"
      children:
        offset:
          type: "node"
          children:
            x:
              type: "float"
              default: 0.0
              complexity: "user"
            "y":
              type: "float"
              default: 0.0
              complexity: "user"
            z:
              type: "float"
              default: 0.0
              complexity: "user"
        pan_skew_factor:
          type: "float"
          default: 1.0
          complexity: "expert"
        tilt_skew_factor:
          type: "float"
          default: 1.0
          complexity: "expert"
        use_config_factor:
          type: "bool"
          default: false
          complexity: "expert"
        use_cached_skews:
          type: "bool"
          default: false
          complexity: "expert"
  calibrator:
    type: "node"
    children:
      num_pcam_override:
        type: "uint"
        default: 0
        default_recommended: true
        complexity: "expert"
        hint: "Use different number of pcams then default for generation. Only need to set if need more than default."
      max_usable_offset:
        type: "float"
        default: 40.0
        default_recommended: true
        complexity: "expert"
        units: "mm"
        hint: "If first p2p was further away than this value, we assume it was bad p2p and don't use in our averaging."
      max_dist_speculative_laser:
        type: "float"
        default: 3.5
        default_recommended: true
        complexity: "expert"
        units: "mm"
        hint: "If first p2p is within this L2 distance then we can use the speculative laser on time towards total shot time."
      speculative_laser_percentile:
        type: "float"
        default: .75
        default_recommended: true
        complexity: "expert"
        hint: "Percentage of previous moves that must be <= max_dist_speculative_laser in order to attempt speculative shooting"
      grid_size:
        type: "float"
        default: 10000
        default_recommended: true
        complexity: "expert"
        hint: "In order for more accurate offsets we break scanner space into a grid where each square is of this size."
        units: "mm"
      median_estimator:
        include: "utils/median_estimator.yaml"
