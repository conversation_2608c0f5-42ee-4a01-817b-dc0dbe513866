type: "node"
children:
  enabled:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Is this camera enabled to be used"
  serial:
    type: "string"
    default: ""
    complexity: "expert"
    hint: "Serial number for the camera"
  mirror:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Mirror the image horizontally, at the camera level, must be supported by the camera hardware"
  flip:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Flip the image vertically, at the camera level, must be supported by the camera hardware"
  ptp:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Use PTP for image timestamps"
  hdr:
    type: "node"
    children:
      saturation:
        type: "float"
        default: -1.0
        complexity: "expert"
      contrast:
        type: "float"
        default: -1.0
        complexity: "expert"
      brightness:
        type: "float"
        default: -1.0
        complexity: "expert"
      detail:
        type: "float"
        default: -1.0
        complexity: "expert"
      average_num:
        type: "int"
        default: -1
        complexity: "expert"
      exposure_us:
        type: "float"
        default: 10000.0
        complexity: "expert"
      auto_brightness:
        type: "node"
        children:
          enabled:
            type: "bool"
            default: false
            complexity: "expert"
          min_hdr_brightness:
            type: "float"
            default: 100
            complexity: "expert"
          max_hdr_brightness:
            type: "float"
            default: 30000
            complexity: "expert"
          hdr_brightness_delta:
            type: "float"
            default: 100
            complexity: "expert"
          min_brightness:
            type: "float"
            default: 0.45
            complexity: "expert"
          max_brightness:
            type: "float"
            default: 0.55
            complexity: "expert"
          quantile:
            type: "float"
            default: 0.5
            complexity: "expert"
  fisheye:
    type: "node"
    children:
      is_fisheye:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Is this camera a fisheye"
      k:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort K params"
      d:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort D params"
  binning:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "pixel binning square size"
    units: "px"
  width:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "output width of the camera"
    units: "px"
  height:
    type: "int"
    default: -1
    complexity: "expert"
    hint: "output height of the camera"
    units: "px"
  buffer_count:
    type: "int"
    default: -1
    complexity: "developer"
    hint: "Number of buffers to reserve in arena SDK for image fetching. <= 0 means use default"
  cv:
    include: "components/deck_cam_cv.yaml"
