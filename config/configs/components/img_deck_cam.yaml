type: "node"
children:
  enabled:
    type: "bool"
    default: false
    complexity: "expert"
    hint: "Is this camera enabled to be used"
  path:
    type: "string"
    default: ""
    complexity: "expert"
    hint: "Path to img dir"
  fps:
    type: "uint"
    default: 25
    complexity: "developer"
    hint: "Frame rate to push at"
  binning:
    type: "int"
    default: 1
    complexity: "expert"
    hint: "pixel binning square size"
    units: "px"
  fisheye:
    type: "node"
    children:
      is_fisheye:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Is this camera a fisheye"
      k:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort K params"
      d:
        type: "string"
        default: ""
        complexity: "expert"
        process: "base64"
        hint: "JSON array of arrays (matrix) for camera undistort D params"
