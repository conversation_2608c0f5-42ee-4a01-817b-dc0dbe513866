type: "node"
children:
  step_size:
    type: "float"
    default: 0.1
    default_recommended: true
    complexity: "expert"
    hint: "Step size to move the median"
  convergence_threshold:
    type: "float"
    default: 0.09
    default_recommended: true
    complexity: "expert"
    hint: "Threshold to trigger fast convergence, maxes out at step size"
  convergence_smoothing:
    type: "float"
    default: 0.9
    default_recommended: true
    complexity: "expert"
    hint: "Smoothing to accumulate convergence, based on step size"
  convergence_factor:
    type: "float"
    default: 10
    default_recommended: true
    complexity: "expert"
    hint: "Multiplier during fast convergence"
