---
name: <PERSON><PERSON>L Files

on:
  pull_request:
    branches:
      - "*"
    paths:
      - "**/*.yaml"
      - "**/*.yml"
      - ".yamllint/**"
      - "**/*.py"

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.x"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r .yamllint/requirements.txt

      - name: Run Black
        run: |
          make black

      - name: Run yamllint
        run: |
          yamllint . --config-file .yamllint/rules.yaml

      - name: Run YAML lint tests
        run: |
          pytest .yamllint/tests/test_yaml_lint.py -v

      - name: Validate YAML key names
        run: |
          make lint
