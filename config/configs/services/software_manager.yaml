type: "node"
children:
  target_version:
    type: "string"
    default: ""
    complexity: "expert"
  previous_version:
    type: "string"
    default: ""
    complexity: "developer"
    default_recommended: false
  version_poll_interval_s:
    type: "uint"
    default: 900
  enable_auto_restart:
    type: "bool"
    default: true
  enable_auto_restart_all:
    type: "bool"
    default: false
  version_metadata_service_url:
    type: "string"
    default: "versionmetadata.cloud.carbonrobotics.com:443"
    hint: "Deprecated [6-09-2025] from env now"
  use_local_registry:
    type: "bool"
    default: true
    hint: "Deprecated: [v2.1] default to true"
  verify_images_using_checksums:
    type: "bool"
    default: false
    default_recommended: false
    complexity: "developer"
    hint: "Verify docker images using checksums. Disable if causing repeated image deletion"
