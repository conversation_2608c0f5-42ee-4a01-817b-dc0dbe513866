type: "node"
children:
  local_signal_server:
    hint: "Optional signal server used for local streaming"
    include: "components/websocket_config.yaml"
  controls_server:
    hint: "Server for communicating with controls"
    include: "components/websocket_config.yaml"
  deck_server:
    hint: "Server for communicating with deck"
    include: "components/websocket_config.yaml"
  turn_servers:
    type: "list"
    hint: "Turn server urls to connect to format = stun:<URI>:<port>"
    item:
      type: "string"
  controlled_stream_name:
    type: "string"
    default: "Deck"
    default_recommended: true
    complexity: "developer"
    hint: "Name of the stream that we should attach tractor and deck controls capability to"
  verbose:
    type: "bool"
    default: false
    default_recommended: true
    complexity: "developer"
    hint: "Verbose logging"
  device_filtering:
    type: "node"
    children:
      enforce_loopback:
        type: "bool"
        default: true
        default_recommended: true
        hint: "Only broadcast loopback type devices"
      hide_dummy_devices:
        type: "bool"
        default: true
        default_recommended: true
        hint: "Do not broadcast dummy devices"
      white_list:
        type: "list"
        hint: "Regex style patterns to match device names against. Empty is allow all"
        item:
          type: "string"
  gps_interval:
    type: "int"
    default: 10
    default_recommended: true
    complexity: "developer"
    units: "seconds"
    hint: "How often to provide gps data"
  peer_cfg_interval:
    type: "int"
    default: 300
    default_recommended: true
    complexity: "developer"
    units: "seconds"
    hint: "How often to fetch peer config data from signal server"
  encoding:
    type: "node"
    children:
      codec:
        type: "string"
        default: "h264"
        choices: ["h264", "vp8", "av1"]
        default_recommended: true
        complexity: "developer"
        hint: "What codec to use for streaming video"
      bitrate:
        type: "int"
        default: 2621440
        default_recommended: true
        complexity: "developer"
        units: "bits per second"
        max: 2621440
        hint: "Bitrate to encode video at higher is better quality, but more bandwidth (max 2.5MB)"
      framerate:
        type: "string"
        default: "20/1"
        default_recommended: true
        complexity: "developer"
        units: "frames/second"
        hint: "Framerate to encode video at"
      keyframe_interval:
        type: "int"
        default: 20
        default_recommended: true
        complexity: "developer"
        units: "frames"
        hint: "How often to insert keyframe into encoding. generally 1 per second is good for streaming"
  interceptor_configs:
    type: "node"
    children:
      nack:
        type: "node"
        children:
          max_resend_count:
            type: "uint"
            default: 5
            default_recommended: true
            complexity: "developer"
            hint: "How many times a given packet can be resent if nacked"
          backoff_time:
            type: "int"
            default: 8
            default_recommended: true
            complexity: "developer"
            units: "ms"
            hint: "How long between consecutive resends of a nacked packet (should be less than expected RTT)"
  associated_controllables:
    type: "list"
    hint: "mapping what controllables should be used for which streams"
    item:
      type: "node"
      children:
        stream:
          type: "string"
          complexity: "developer"
          hint: "The name of the stream to associate"
        controllable:
          type: "string"
          complexity: "developer"
          hint: "The name of the controllable to associate"
        controllable_type:
          type: "int"
          complexity: "developer"
          hint: "The type of the controllable"
