type: "node"
children:
  device_overrides:
    include: "components/device_overrides.yaml"
  spatial_block_length:
    type: "float"
    default: 6.0
    complexity: "expert"
    units: "m"
    default_recommended: true
    hint: "Distance travelled in gps to make one spatial block for spatial metrics."
  spatial_block_debug:
    type: "bool"
    default: false
    complexity: "expert"
    default_recommended: true
    hint: "Store blocks in debug redis location for extended period"
  spatial_block_debug_ttl:
    type: "uint"
    default: 86400
    units: "seconds"
    complexity: "expert"
    default_recommended: true
    hint: "How long to store debug blocks in redis"
