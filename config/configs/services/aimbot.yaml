type: "node"
children:
  algorithm:
    type: "string"
    default: "RotaryAndP2P"
    complexity: "developer"
  use_low_latency_initial_move:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Use the low latency scanner move, currently under this config while A/B testing. Once validated remove this flag"
  use_fh_trajectory:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Use trajectory-based initial move for scanners with faulhaber controllers"
  positional_smoothing:
    type: "float"
    default: 0.0
    complexity: "developer"
    min: 0
    max: 1
    step: 0.1
  target_burst_capture_filter:
    type: "list"
    item:
      type: "int"
      default: 0
      complexity: "expert"
  banding:
    type: "node"
    default_recommended: false
    hint: "Deprecated: [v1.18] Use banding profiles"
    children:
      enabled:
        type: "bool"
        default: false
        complexity: "user"
        hint: "Deprecated: [v1.18] Use banding profiles"
      offset:
        type: "float"
        default: 0
        hint: "Deprecated: [v1.18] Use banding profiles"
      unit_multiplier:
        type: "float"
        default: 1
        hint: "Deprecated: [v1.18] Use banding profiles"
      bands:
        type: "list"
        hint: "Deprecated: [v1.18] Use banding profiles"
        item:
          include: "types/range_float.yaml"
  image_interval_sleep_ms:
    type: "uint"
    default: 5
    complexity: "developer"
  p2p_capture:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Deprecated: [v2.1] Use P2P capture config in common"
      rate:
        type: "float"
        default: 0.002
        min: 0
        max: 1
        complexity: "expert"
        hint: "Deprecated: [v2.1] Use P2P capture config in common"
      miss_rate:
        type: "float"
        default: 0.02
        complexity: "expert"
        hint: "Deprecated: [v2.1] Use P2P capture config in common"
      delay_ms:
        type: "uint"
        default: 10
        complexity: "expert"
      filter: # Empty list is all scanners
        hint: "Deprecated: [v2.1] Use P2P capture config in common"
        type: "list"
        item:
          type: "uint"
          default: 0
          complexity: "expert"
      probability_random_capture:
        hint: "Deprecated: [v2.1] Use P2P capture config in common"
        type: "float"
        default: 0.0
        complexity: "expert"
  target_burst_capture:
    type: "node"
    children:
      rate:
        type: "float"
        default: 0.0
        min: 0
        max: 1
        complexity: "expert"
      filter: # Empty list is all scanners
        type: "list"
        item:
          type: "uint"
          default: 0
          complexity: "expert"
  short_follow_time_ms:
    type: "uint"
    default: 1000
    units: "ms"
    complexity: "developer"
  go_to_interval_sleep_ms:
    type: "uint"
    default: 10
    units: "ms"
    complexity: "developer"
  max_p2p_match_fail:
    type: "uint"
    default: 6
    complexity: "developer"
  laser_test_time_ms:
    type: "uint"
    default: 5000
    units: "ms"
    complexity: "developer"
  emi_test_pulse_time_ms:
    type: "uint"
    default: 100
    units: "ms"
    complexity: "developer"
  tilt_mirror_height_mm:
    type: "float"
    default: 850.9
    units: "mm"
    complexity: "expert"
  load:
    type: "node"
    children:
      target:
        type: "float"
        default: 0.8
        complexity: "expert"
        min: 0
      percentile:
        type: "uint"
        default: 90
        complexity: "expert"
        min: 0
        max: 100
      lookback_ms:
        type: "uint"
        default: 2000
        complexity: "developer"
  target_safety_zone:
    type: "node"
    children:
      forward_px:
        type: "uint"
        default: 40
        complexity: "expert"
      backward_px:
        type: "uint"
        default: 10
        complexity: "expert"
      side_px:
        type: "uint"
        default: 10
        complexity: "expert"
  weed_accuracy_sampling:
    type: "node"
    children:
      record_enabled:
        type: "bool"
        default: false
        complexity: "expert"
      rate:
        type: "float"
        default: 0
        complexity: "expert"
        min: 0
        max: 1
  record_predictions:
    type: "bool"
    default: false
    description: "Shouldn't be long lived, just for local testing"
  weed_tracking:
    type: "node"
    children:
      distance_bucket_size:
        type: "float"
        default: 400.0
        complexity: "expert"
      error_bucket_size:
        type: "float"
        default: 25.0
        complexity: "expert"
      error_calculation:
        type: "node"
        children:
          crop_weight:
            type: "float"
            default: 0.0
            complexity: "developer"
      min_plant_score:
        type: "float"
        default: 0.2
        complexity: "developer"
        hint: "Min score for a plant to be added to the tracker"
      crop_safety_radius_mm:
        type: "float"
        default: 1.0
        complexity: "developer"
        units: "mm"
        hint: "Deprecated: [v1.18] see common/protector"
      reverse_crop_safety_radius_mm:
        type: "float"
        default: -1.0
        complexity: "developer"
        units: "mm"
        default_recommended: true
        hint: "Deprecated: [v1.18] see common/protector"
      crop_dedup_radius:
        type: "float"
        default: 200.0
        complexity: "developer"
        units: "px"
        default_recommended: true
        hint: "Deprecated: [v2.1], use plant_dedup_radius"
      weed_dedup_radius:
        type: "float"
        default: 100.0
        complexity: "developer"
        units: "px"
        default_recommended: true
        hint: "Deprecated: [v2.1], use plant_dedup_radius"
      plant_dedup_radius:
        type: "float"
        default: 200.0
        complexity: "developer"
        units: "px"
        default_recommended: true
      max_age_in_bounds:
        type: "uint"
        min: 1
        default: 1000
        default_recommended: true
        units: "ms"
        hint: "How quickly we age out tracks when they havent been deduplicated, and are still in the FOV of the predict cameras"
      max_age_out_of_bounds:
        type: "uint"
        default: 20000
        default_recommended: true
        units: "ms"
        hint: "How quickly we age out tracks when they havent been deduplicated, and are out of the FOV of the predict cameras, 0 to never timeout"
        complexity: "developer"
      position_range_offset:
        type: "uint"
        default: 100
        default_recommended: true
        units: "px"
        hint: "Offset from edge of predict px space that a target can be detected."
        complexity: "developer"
      roi:
        type: "list"
        hint: "Create an ROI to prevent obstructions in the camera FOV from creating tracks in the tracker."
        item:
          type: "node"
          children:
            tracker_id:
              type: uint
              hint: "ID of the tracker that this ROI is for"
            width:
              type: int
              units: "px"
            height:
              type: int
              units: "px"
            offset_x:
              type: int
              units: "px"
            offset_y:
              type: int
              units: "px"
      wheel_encoder_update_threshold:
        type: "uint"
        default: 0
        default_recommended: true
        units: "mm"
        complexity: "developer"
        hint: "How much the wheel encoders have to move before we create new centroids, 0 to disable"
  tilt_mirror_z_offset_mm:
    type: "float"
    default: 167.64
    complexity: "expert"
  follow_forever_pan_pos:
    type: "float"
    default: 0.5
    complexity: "developer"
    min: 0
    max: 1
  log_wheel_encoders:
    type: "bool"
    default: false
    complexity: "developer"
  height_estimation:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: true
        complexity: "expert"
      num_lanes:
        type: "uint"
        default: 5
        complexity: "developer"
      only_weeds_for_global:
        type: "bool"
        default: true
        complexity: "developer"
      trajectory_smoothing_factor:
        type: "float"
        default: 0
        complexity: "developer"
      weed_search_radius:
        type: "float"
        default: 200
        units: "mm"
        complexity: "developer"
        hint: "Deprecated: [v2.1], use plant_search_radius"
      crop_search_radius:
        type: "float"
        default: 600
        units: "mm"
        complexity: "developer"
        hint: "Deprecated: [v2.1], use plant_search_radius"
      plant_search_radius:
        type: "float"
        default: 400
        complexity: "developer"
        units: "mm"
      global_height_estimator_collection:
        type: "node"
        children:
          enable_size_buckets:
            type: "bool"
            default: false
            complexity: "developer"
            hint: "If enabled, height estimation will be bucketed by size rather than a single global estimator"
          bucket_size:
            type: "float"
            default: 15.0
            units: "mm"
            complexity: "developer"
          number_of_buckets:
            type: "uint"
            default: 3
            complexity: "developer"
            hint: "Buckets are [0, bucket_size), [bucket_size, 2*bucket_size), ..., [bucket_size*(num_buckets-1), inf)"
  max_pos_y_offset_mm:
    type: "float"
    default: 2000
    units: "mm"
    complexity: "expert"
  max_number_of_weeds:
    type: "uint"
    default: 1000
    complexity: "developer"
  enable_auto_focus:
    type: "bool"
    default: false
    complexity: "expert"
  dedup_radius_px:
    type: "uint"
    default: 100
    complexity: "developer"
    units: "px"
    hint: "Deprecated: [2023-03-01] use crop/weed dedup radius in weed_tracking tree"
  match_perspective_across_predicts:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "Deprecated: [2023-03-01]"
  scanners:
    include: "components/scanners.yaml"
  geometric:
    include: "components/geometric.yaml"
  device_overrides:
    include: "components/device_overrides.yaml"
  velocity_estimator:
    type: "node"
    children:
      kpt_override_enabled:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
      kill_percent_target:
        type: "float"
        default: 1
        min: 0.0
        max: 1.0
        complexity: "expert"
        hint: "Deprecated: [v1.18] use TVE profiles"
  debug_trajectory_monitor:
    type: "bool"
    default: false
    complexity: "developer"
  debug_arbiter_monitor:
    type: "bool"
    default: false
    complexity: "developer"
    hint: "If true, the abiter will log information about its current state"
  row_width_left_in:
    type: "float"
    default: 40
    units: "inches"
    hint: "For non-centered row placement, how much left of the center the row goes"
  row_width_right_in:
    type: "float"
    default: 40
    units: "inches"
    hint: "For non-centered row placement, how much right of the center the row goes"
  arbiter:
    type: "node"
    children:
      algorithm:
        type: "string"
        default: "embedding_classification"
        choices:
          [
            "modelinator",
            "highest_score",
            "embedding_classification",
            "embedding_with_fallback_classification",
          ]
        complexity: "developer"
        default_recommended: true
        hint: "The algorithm to use for deciding if a plant is a weed or crop when common/feature_flags/embedding_based_classification_feature is true"
      non_embedding_algorithm:
        type: "string"
        default: "modelinator"
        choices: ["modelinator", "highest_score"]
        complexity: "developer"
        default_recommended: true
        hint: "The algorithm to use for deciding if a plant is a weed or crop when common/feature_flags/embedding_based_classification_feature is false"
      decision_point:
        type: "float"
        default: 0.75
        complexity: "developer"
        default_recommended: true
        hint: "Deprecated: [v2.1] use common/decision_line/scanner_offset_in"
      combine_duplicate_information:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: false
        hint: "If true, duplicate trajectories will combine information and assign category based on both"
      algorithm_parameters:
        type: "node"
        children:
          embedding_with_fallback:
            type: "node"
            children:
              fallback:
                type: "string"
                default: "modelinator"
                choices: ["modelinator", "highest_score"]
                complexity: "developer"
                default_recommended: false
                hint: "Which algorithm should be used if embeddings aren't certain"
              ambiguity_threshold:
                type: "float"
                default: 0.1
                default_recommended: false
                hint: "If the smallest and second-smallest distances are within this value, we'll fallback"
              strength_threshold:
                type: "float"
                default: -0.5
                default_recommended: false
                hint: "If the smallest distance is above this value, we'll fallback"
              plant_mindoo:
                type: "float"
                default: 0.0
                default_recommended: false
                hint: "We need plant doo for a trajectory to be above this value for us to assign it weed or crop"
          embedding_classification:
            type: "node"
            children:
              plant_mindoo:
                type: "float"
                default: 0.0
                default_recommended: false
                hint: "We need plant doo for a trajectory to be above this value for us to assign it weed or crop"
