type: "node"
children:
  device_overrides:
    include: "components/device_overrides.yaml"
  temp_bypass:
    type: "bool"
    default: false
    complexity: "expert"
  humidity_bypass:
    type: "bool"
    default: false
    complexity: "expert"
  supervisory_plc_variant:
    type: "string"
    complexity: "expert"
  velocity_smoothing:
    type: "uint"
    default: 50
    min: 0
    max: 100
    complexity: "user"
  jimbox:
    include: "components/jimbox.yaml"
  peplink_kalman_filter:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        complexity: "expert"
      capture:
        type: "bool"
        default: false
        complexity: "expert"
      lat_long_r:
        type: "float"
        default: 2000.0
        complexity: "expert"
        hint: "Latitude/Longitude Measurement Noise"
      altitude_r:
        type: "float"
        default: 1
        complexity: "expert"
        hint: "Altitude Measurement Noise"
      lat_long_q:
        type: "float"
        default: 0.00001
        complexity: "expert"
        hint: "Latitude/Longitude Process Noise"
      lat_long_q_vel:
        type: "float"
        default: 0.00001
        complexity: "expert"
        hint: "Latitude/Longitude Velocity Process Noise"
      altitude_q:
        type: "float"
        default: 0.00001
        complexity: "expert"
        hint: "Altitude Process Noise"
      altitude_q_vel:
        type: "float"
        default: 0.00001
        complexity: "expert"
        hint: "Altitude Velocity Process Noise"
      lat_long_exp:
        type: "float"
        default: 1.2
        complexity: "expert"
        hint: "Exponent used to non-linearly amplify low motion detection"
      motion_n:
        type: "int"
        default: 300
        complexity: "expert"
        hint: "Last n number of samples to look at to determine low motion, by default 1 sample per second (300 = 5 minutes)"
  use_peplink_gps:
    type: "bool"
    default: false
    complexity: "expert"
  use_sierra_modem:
    type: "bool"
    default: true
    complexity: "expert"
  cruise_control_if:
    type: "string"
    selected: "jimbox"
    choices: ["jimbox", "atim", "tim", "tecu"]
    complexity: "expert"
    hint: "What HW to use for for cruise control interface"
  tim:
    type: "node"
    children:
      speed_control_scalar:
        type: "float"
        default: 1.0
        complexity: "user"
        hint: "Scalar applied to speed commands sent to the TIM/TECU interface"
  atim:
    type: "node"
    children:
      url_override:
        type: "string"
        default: ""
        complexity: "developer"
        hint: "Override the url for atim connection"
      enable_strobe_on_lift:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Automatically enable strobes on lift, when connecting to ATIM"
      speed_correction:
        type: "node"
        children:
          offset:
            type: "float"
            default: 0.0
            units: "mph"
            complexity: "expert"
            hint: "Reported Speed = (multiplier * atim_speed) + offset"
          multiplier:
            type: "float"
            default: 1.0
            units: "mph"
            complexity: "expert"
            hint: "Reported Speed = (multiplier * atim_speed) + offset"
  gps:
    type: "node"
    children:
      poll_interval:
        type: "int"
        default: 1000
        units: "ms"
        complexity: "developer"
        hint: "How quickly to poll gps data from gps board"
        default_recommended: false
      is_ptp_master:
        type: "bool"
        default: true
        default_recommended: true
        hint: >-
          Whether the GPS board serves as an IEEE1588 PTP grandmaster clock, providing precise time synchronization for the robot.
          If disabled, another device (such as command PC with the correct packages) must be configured as a master, otherwise robot no work.
        complexity: "developer"
      provide_controllable:
        type: "bool"
        default: false
        default_recommended: true
        hint: "Should we run server to act as RTC controllable for GPS"
      fixed_pos:
        type: "node"
        children:
          x:
            type: "float"
            default: 0.0
            complexity: "expert"
            units: "mm"
            default_recommended: false
            hint: "Position of the generation specific point relative to main GPS antenna top down XY coords"
          "y":
            type: "float"
            default: 0.0
            complexity: "expert"
            units: "mm"
            default_recommended: false
            hint: "Position of the generation specific point relative to main GPS antenna top down XY coords"
      rtk:
        type: "node"
        children:
          enabled:
            type: "bool"
            default: false
            complexity: "expert"
            hint: "Use GPS RTK correction service"
          usb_port:
            type: "string"
            default: "/dev/serial/by-id/usb-u-blox_AG_-_www.u-blox.com_u-blox_GNSS_receiver-if00"
            complexity: "expert"
            hint: "The usb port on computer that the f9p module is connected to"
          baudrate:
            type: "int"
            default: 115200
            complexity: "expert"
            hint: "serial baud rate"
          mount_point:
            type: "string"
            default: "/US"
            complexity: "expert"
            hint: "The mount point to use for correction data"
          uri:
            type: "string"
            default: "http://ppntrip.services.u-blox.com:2101"
            complexity: "expert"
            hint: "The url of the point perfect thing stream"
          username:
            type: "string"
            default: ""
            complexity: "expert"
            hint: "The username of the point perfect thing stream"
          password:
            type: "string"
            default: ""
            complexity: "expert"
            hint: "The password of the point perfect thing stream"
          nanopb_support:
            type: "bool"
            default: false
            complexity: "expert"
            hint: "Does the gps board support rtk over nanopb"
          dual_gps_heading_offset:
            type: "float"
            default: 0
            complexity: "expert"
            units: "degrees"
            default_recommended: true
            hint: "The offset to apply to heading to be inline with the direction of travel"
          correction_type:
            type: "string"
            default: "SPARTN"
            complexity: "developer"
            choices: ["SPARTN", "RTCM"]
            hint: "Which type of correction messages are being sent"
  gps_rtk:
    type: "node"
    children:
      enabled:
        type: "bool"
        default: false
        complexity: "expert"
        hint: "Use GPS RTK correction service (DEPRECATED USE gps/rtk/*)"
      usb_port:
        type: "string"
        default: "/dev/serial/by-id/usb-u-blox_AG_-_www.u-blox.com_u-blox_GNSS_receiver-if00"
        complexity: "expert"
        hint: "The usb port on computer that the f9p module is connected to (DEPRECATED USE gps/rtk/*)"
      baudrate:
        type: "int"
        default: 115200
        complexity: "expert"
        hint: "serial baud rate (DEPRECATED USE gps/rtk/*)"
      mount_point:
        type: "string"
        default: "/US"
        complexity: "expert"
        hint: "The mount point to use for correction data (DEPRECATED USE gps/rtk/*)"
      uri:
        type: "string"
        default: "http://ppntrip.services.u-blox.com:2101"
        complexity: "expert"
        hint: "The url of the point perfect thing stream (DEPRECATED USE gps/rtk/*)"
      username:
        type: "string"
        default: ""
        complexity: "expert"
        hint: "The username of the point perfect thing stream (DEPRECATED USE gps/rtk/*)"
      password:
        type: "string"
        default: ""
        complexity: "expert"
        hint: "The password of the point perfect thing stream (DEPRECATED USE gps/rtk/*)"
  allow_reaper_mcb_firmware_updates:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
    hint: "Allow the reaper to update the MCB firmware, keep false until we fix the firmware update bug, depricate this when fixed"
  validate_system_status_before_mcb_update:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
    hint: "MCB firmware update will wait for the software manger on the module to validate system version prior to performing update"
