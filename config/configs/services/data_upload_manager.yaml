type: "node"
children:
  retrieval_interval_seconds:
    type: "int"
    default: 180
    units: "seconds"
    complexity: "developer"
  upload_interval_seconds:
    type: "int"
    default: 300
    units: "seconds"
    complexity: "developer"
  transfer_interval_seconds:
    type: "int"
    default: 30
    units: "seconds"
    complexity: "developer"
  to_upload_expiration_hours:
    type: "int"
    default: 48
    units: "hours"
  emergency_image_expiration_hours:
    type: "int"
    default: 72
    units: "hours"
    complexity: "developer"
  completed_transfer_expiration_hours:
    type: "int"
    default: 48
    units: "hours"
    complexity: "developer"
  max_images_per_24_hours:
    type: "int"
    default: 300
    units: "hours"
    complexity: "developer"
    hint: "Deprecated: [2022-09-16] use max_images_per_hour"
  max_images_per_hour:
    type: "int"
    default: 10
    complexity: "developer"
    hint: "The max number of images to upload per hour"
  use_online_upload_flow:
    type: "bool"
    default: false
    complexity: "developer"
  use_offline_upload_flow:
    type: "bool"
    default: false
    complexity: "developer"
  min_weed_tracking_score:
    type: "float"
    default: 0.7
    complexity: "developer"
  weed_tracking_percentage:
    type: "float"
    default: 0.5
    complexity: "developer"
  min_random_score:
    type: "float"
    default: 0.9
    complexity: "developer"
  random_percentage:
    type: "float"
    default: 0.5
    complexity: "developer"
  min_margin_max_score:
    type: "float"
    default: 0.7
    complexity: "developer"
  min_recency_score:
    type: "float"
    default: 0
    complexity: "developer"
  min_ambiguous_weed_count_score:
    type: "float"
    default: 0
    complexity: "developer"
  min_ambiguous_crop_count_score:
    type: "float"
    default: 0
    complexity: "developer"
  upload_proportions:
    type: "node"
    children:
      recency:
        type: "float"
        default: 0.1
        complexity: "developer"
      weed_tracking:
        type: "float"
        default: 0.1
        complexity: "developer"
      weed_margin_max:
        type: "float"
        default: 0.1
        complexity: "developer"
      crop_margin_max:
        type: "float"
        default: 0.1
        complexity: "developer"
      ambiguous_weed_count:
        type: "float"
        default: 0.1
        complexity: "developer"
      ambiguous_crop_count:
        type: "float"
        default: 0.1
        complexity: "developer"
      driptape:
        type: "float"
        default: 0.1
        complexity: "developer"
      unknown_plant:
        type: "float"
        default: 0.1
        complexity: "developer"
  emergency_capture_proportions:
    type: "node"
    children:
      recency:
        type: "float"
        default: 0.1
        complexity: "developer"
      weed_tracking:
        type: "float"
        default: 0.1
        complexity: "developer"
      weed_margin_max:
        type: "float"
        default: 0.1
        complexity: "developer"
      crop_margin_max:
        type: "float"
        default: 0.1
        complexity: "developer"
      ambiguous_weed_count:
        type: "float"
        default: 0.1
        complexity: "developer"
      ambiguous_crop_count:
        type: "float"
        default: 0.1
        complexity: "developer"
      driptape:
        type: "float"
        default: 0.1
        complexity: "developer"
      unknown_plant:
        type: "float"
        default: 0.1
        complexity: "developer"
  ambiguous_min_score:
    type: "float"
    default: 0.4
    complexity: "developer"
  ambiguous_max_score:
    type: "float"
    default: 0.6
    complexity: "developer"
  min_weed_margin_max_score:
    type: "float"
    default: 0.9
    complexity: "developer"
  min_crop_margin_max_score:
    type: "float"
    default: 0.9
    complexity: "developer"
  min_driptape_score:
    type: "float"
    default: 0
    complexity: "developer"
  min_unknown_plant_score:
    type: "float"
    default: 0
    complexity: "developer"
  admin:
    type: "int"
    default: 1
    complexity: "developer"
  enable_emergency_priority:
    type: "bool"
    default: true
    complexity: "user"
    default_recommended: false
    hint: "If false, emergency captures will not be prioritized"
  emergency_capture_rate:
    default_recommended: false
    type: "float"
    default: 10.0
    complexity: "user"
  emergency_capture_target:
    type: "uint"
    default: 100
    complexity: "user"
  emergency_upload_target:
    type: "uint"
    default: 100
    complexity: "user"
  emergency_session_name:
    default_recommended: false
    type: "string"
    complexity: "user"
  lightweight_burst_records:
    type: "node"
    children:
      upload_interval:
        type: "int"
        default: 30
        complexity: "developer"
      max_uploads:
        type: "int"
        default: 100
        complexity: "developer"
  distance_accumulation:
    type: "node"
    children:
      retrieval_interval_milliseconds:
        type: "int"
        default: 10
        complexity: "developer"
      max_distances:
        type: "uint"
        default: 600
        complexity: "developer"
  predict_burst_capture:
    type: "node"
    children:
      num_images:
        type: "int"
        default: 7
        complexity: "developer"
      retrieval_interval_seconds:
        type: "int"
        default: 3600
        units: "seconds"
        complexity: "developer"
      enabled:
        type: "bool"
        default: false
        complexity: "developer"
      retrieval_timeout_seconds:
        type: "int"
        default: 120
        units: "seconds"
        complexity: "developer"
      upload_interval_seconds:
        type: "int"
        default: 120
        units: "seconds"
        complexity: "developer"
      max_images_per_hour:
        type: "int"
        default: 1
        complexity: "developer"
  chip_capture:
    type: "node"
    children:
      retrieval_interval_seconds:
        type: "int"
        default: 60
        complexity: "developer"
      upload_interval_seconds:
        type: "int"
        default: 60
        complexity: "developer"
      max_uploads_per_hour:
        type: "int"
        default: 20
        complexity: "developer"
      enabled:
        type: "bool"
        default: false
        complexity: "developer"
      expiration_hours:
        type: "int"
        default: 24
        complexity: "developer"
      flush_queue_after_retrieval:
        type: "bool"
        default: false
        complexity: "developer"
        hint: "In enabled, will flush all score queues on a row once an item has been retrieved"
      retrieval_row_selection:
        type: "string"
        default: "max"
        choices: ["max", "random"]
        complexity: "developer"
        default_recommended: true
        hint: "How to select which row to query an item from"
      only_retrieve_random:
        type: "bool"
        default: false
        complexity: "developer"
        default_recommended: true
        hint: "If true, we will only retrieve chips that were from the random queue. If none exist, we'll grab from a randomly selected queue"
      max_age_seconds:
        type: "int"
        units: "seconds"
        default: 120
        complexity: "developer"
        hint: "The max age a chip can be before it expires out of the score queue"
  p2p_capture:
    type: "node"
    children:
      retrieval_interval_seconds:
        type: "int"
        default: 240
        units: "seconds"
        complexity: "developer"
      upload_interval_seconds:
        type: "int"
        default: 240
        units: "seconds"
        complexity: "developer"
      max_uploads:
        type: "int"
        default: 100
        complexity: "developer"
      success_ratio:
        type: "float"
        default: 0.2
        hint: "Deprecated: [v1.18]"
      miss_ratio:
        type: "float"
        default: 0.8
        hint: "Deprecated: [v1.18]"
      aimbot_jump_ratio:
        type: "float"
        default: 0.5
        hint: "Deprecated: [v1.18]"
      reason_ratios:
        type: "node"
        hint: "Child names must match exactly to name in cv.proto reason enum"
        children:
          P2PCaptureReason_MISS:
            type: "float"
            default: 0.8
            complexity: "developer"
            default_recommended: true
            hint: "The ratio of p2p uploads that should be the result of p2p misses"
          P2PCaptureReason_SUCCESS:
            type: "float"
            default: 0.2
            complexity: "developer"
            default_recommended: true
            hint: "The ratio of p2p uploads that should be the result of p2p success"
          P2PCaptureReason_JUMP:
            type: "float"
            default: 0.5
            complexity: "developer"
            default_recommended: true
            hint: "The ratio of p2p uploads that should be the result of p2p jumping"
          P2PCaptureReason_First_P2P:
            type: "float"
            default: 0.5
            complexity: "developer"
            default_recommended: true
            hint: "The ratio of p2p uploads that should be just first p2ps"
  max_queue_size:
    type: "int"
    default: 300
    complexity: "developer"
  offline_agent_max_load:
    type: "float"
    default: 0.99
    complexity: "developer"
  single_predict_emergency_capture:
    type: "node"
    hint: "These parameters can be used to modify emergency capture to use the latest image under a single predict camera, rather than using interesting scores across all predict cameras."
    children:
      enable_single_predict_capture:
        type: "bool"
        default: false
        complexity: "developer"
        hint: "Whether to use the latest seen image for emergency capture vs the best scored image. Doing this will capture images only under one specific predict camera."
      row_ind:
        type: "uint"
        default_recommended: false
        default: 1
        complexity: "developer"
        hint: "Row index (1, 2, or 3)"
      predict_cam_id:
        type: "string"
        default_recommended: false
        default: "predict1"
        complexity: "developer"
        hint: "predict1, predict2, predict3, or predict4"
  predict_camera_aperture:
    type: "string"
    default: "f/5.6"
  target_camera_aperture:
    type: "string"
    default: "f/5"
  weeding_diagnostics:
    type: "node"
    children:
      max_retries:
        type: "uint"
        default: 20
        complexity: "developer"
        hint: "max number of retries to upload each part"
      max_timeout_sec:
        type: "uint"
        default: 3600
        units: "seconds"
        complexity: "developer"
        hint: "max timeout between retries in exponential backoff"
  plant_captcha:
    type: "node"
    children:
      max_retries:
        type: "uint"
        default: 20
        complexity: "developer"
        hint: "max number of retries to upload each part"
      max_timeout_sec:
        type: "uint"
        default: 3600
        units: "seconds"
        complexity: "developer"
        hint: "max timeout between retries in exponential backoff"
