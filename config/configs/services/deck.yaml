type: "node"
children:
  cameras:
    type: "list"
    item:
      include: "components/deck_cam.yaml" # Deprecated use typed cameras
  lucid_cameras:
    type: "list"
    item:
      include: "components/lucid_deck_cam.yaml"
  video_cameras:
    type: "list"
    item:
      include: "components/video_deck_cam.yaml"
  img_dir_cameras:
    type: "list"
    item:
      include: "components/img_deck_cam.yaml"
  cr_multi_sense_cameras:
    type: "list"
    item:
      include: "components/cr_multi_deck_cam.yaml"
  output:
    type: "node"
    children:
      width:
        type: "int"
        default: 1920
        default_recommended: true
        complexity: "developer"
        units: "px"
        hint: "width to set the v4l2 buffer"
      height:
        type: "int"
        default: 1080
        default_recommended: true
        complexity: "developer"
        units: "px"
        hint: "height to set the v4l2 buffer"
  furrows_model_path:
    type: "string"
    complexity: "developer"
    hint: "Path to .trt furrows model"
  min_capture_speed:
    type: "float"
    complexity: "developer"
    hint: "Minimum speed to capture images"
    units: "mph"
    default: 0.01
