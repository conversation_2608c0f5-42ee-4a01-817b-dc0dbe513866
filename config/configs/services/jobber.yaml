type: "node"
children:
  active_farm_id:
    type: "string"
    default: ""
    default_recommended: false
    complexity: "expert"
    hint: "ID of the farm to use for boundary checker, this is temporary until jobs is more integrated"
  hitch_down_val:
    type: "float"
    default: 40.0
    default_recommended: false
    units: "%"
    complexity: "expert"
    hint: "When hitch pos reads less than or equal to this value we consider the hitch to be in the down position value is 0-100"
  max_trusted_imp_state_age:
    type: "float"
    default: 20.0
    default_recommended: true
    units: "seconds"
    complexity: "developer"
    hint: "How old can the implement state be and still be trusted for setting active flag"
  use_imp_state:
    type: "bool"
    default: true
    default_recommended: true
    complexity: "expert"
    hint: "Use implement state, eventually this will be part of implement definition"
