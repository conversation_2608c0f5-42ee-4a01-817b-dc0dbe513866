type: "node"
children:
  environment:
    type: "string"
    default: "development"
  software_manager:
    include: "services/software_manager.yaml"
  furrow_model_category_id:
    type: "string"
    default_recommended: true
    default: "FURROW"
    hint: "Id of the relevant furrow model output category"
    complexity: "expert"
  feature_flags:
    type: "node"
    children:
      boundary_checker_feature:
        type: "bool"
        default: false
        default_recommended: true
        hint: "Enable boundary checker"
        complexity: "expert"
