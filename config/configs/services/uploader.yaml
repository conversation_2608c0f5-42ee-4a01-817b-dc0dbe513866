type: "node"
children:
  log_level:
    type: "string"
    default: "info"
    hint: "uploader log level"
  upload_interval:
    type: "string"
    default: "5m"
    hint: "interval on which to upload (go duration)"
  failed_upload_lifetime:
    type: "string"
    default: "72h"
    hint: "how long to keep failed uploads (go duration)"
  max_hourly_uploads:
    type: "int"
    default: 10
    hint: "maximum uploads per hour"
