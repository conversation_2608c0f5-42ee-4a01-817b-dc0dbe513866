type: "node"
children:
  log_level:
    type: "string"
    complexity: "expert"
    default: "info"
    hint: "Commander application log level"
    default_recommended: true
  support_phone:
    default_recommended: false
    type: "string"
    complexity: "expert"
    hint: "Support phone number displayed in operator app. Takes effect on operator app restart"
  support_slack:
    type: "string"
    complexity: "expert"
    default: "#support-null"
    hint: "Deprecated: [2024-11-01] comes from Portal now. (was: Slack channel to use for robot integrations like alarms and issue reports)"
    default_recommended: false
  crop_models: # pinned crop models
    type: "list"
    default_recommended: false
    item:
      type: "node"
      children:
        deepweed:
          type: "string"
  pinned_noncrop_models:
    type: "node"
    default_recommended: false
    children:
      p2p:
        type: "string"
  current_crop:
    type: "string"
    default_recommended: false
  current_crop_id:
    type: "string"
    default_recommended: false
  crop_ids:
    type: "list"
    default_recommended: false
    item:
      type: "node"
      children:
        crop_name_override:
          type: "string"
          hint: "override for crop display name"
        common_name:
          type: "string"
          hint: "Deprecated: [2023-12-06] comes from <PERSON><PERSON><PERSON><PERSON> now"
        carbon_name:
          type: "string"
          hint: "Deprecated: [2023-12-06] comes from <PERSON><PERSON><PERSON><PERSON> now"
        enabled:
          type: "bool"
        pinned_model:
          type: "string"
        recommended_model:
          type: "string"
        last_active_model:
          type: "string"
        maintained_models:
          type: "list"
          default_recommended: false
          item:
            type: "string"
        last_active_category_collection_id:
          type: "string"
  model_expiration_days:
    type: "uint"
    default: 30
  veselka_url:
    type: "string"
    hint: "Deprecated: [2023-09-13] use carbon_host_overrides in robot.json"
  recommendation_interval:
    type: "string"
    default: "6h0m"
    hint: "interval on which to refresh model recommendations (ex: 3h30m10s)"
  customer:
    type: "string"
    default_recommended: false
  preferred_crops:
    type: "list"
    default_recommended: false
    item:
      type: "string"
  weeding_update_db_interval_sec:
    type: "uint"
    default: 60
    units: "seconds"
    hint: "Deprecated: [2023-12-06] Use common/weeding_metrics"
  min_speed_for_weeding_indication_mph:
    type: "float"
    default: 0.01
    units: "mph"
  chiller_cooldown_time_seconds:
    type: "uint"
    default: 2400
    default_recommended: true
    complexity: "developer"
    units: "seconds"
  ac_cooldown_time_seconds:
    type: "uint"
    default: 600
    default_recommended: true
    complexity: "developer"
    units: "seconds"
  power_on_time_seconds:
    type: "uint"
    default: 600
    default_recommended: true
    complexity: "developer"
    units: "seconds"
  alarm:
    type: "node"
    children:
      scanner_reset_autofix:
        type: "bool"
        default: false
        hint: "Whether to use direct scanner reset autofix on servo failure (true) or full row power cycle like originally (false)"
      flicker_filter_s:
        type: "uint"
        default: 10
      ignore_list:
        type: "list"
        item:
          type: "node"
          children:
            enabled:
              type: "bool"
              default: true
      cv_runtime_latency_threshold_ms:
        type: "float"
        default: 3000
        units: "ms"
  dashboard:
    type: "node"
    children:
      metrics:
        type: "node"
        children:
          extra_conclusions:
            type: "list"
            item:
              type: "node"
              children:
                enabled:
                  type: "bool"
                  default: false
                title:
                  type: "string"
                  default: ""
                numerator:
                  include: "components/conclusion_selector.yaml"
                denominator:
                  include: "components/conclusion_selector.yaml"
                flip_thresholds:
                  type: "bool"
                  default: false
                  hint: "If toggled, thresholds use less than instead of greater than"
                good_threshold_pct:
                  type: "uint"
                  default: 90
                  hint: "Above/below this percentage, color is green meaning good"
                medium_threshold_pct:
                  type: "uint"
                  default: 75
                  hint: "Above/below this percentage threshold but below/above good, color is orange meaning not so good"
          efficiency:
            type: "node"
            children:
              enabled:
                type: "bool"
                default: false
                hint: "Enabled the Efficiency dashboard status"
              good_threshold_pct:
                type: "uint"
                default: 90
                hint: "Above this percentage of efficiency, color is green meaning good"
              medium_threshold_pct:
                type: "uint"
                default: 75
                hint: "Above this percentage threshold but below good of efficiency, color is orange meaning not so good"
          error_rate:
            type: "node"
            children:
              enabled:
                type: "bool"
                default: false
                hint: "Enabled the Error Rate dashboard status"
              good_threshold_pct:
                type: "uint"
                default: 10
                hint: "Below this percentage of error rate, color is green meaning good"
              medium_threshold_pct:
                type: "uint"
                default: 25
                hint: "below this percentage threshold but above good of error rate, color is orange meaning not so good"
  alarm_log_size:
    type: "uint"
    default: 1000
    hint: "Max size of alarm log"
  show_software_update_to_user:
    type: "bool"
    default: true
    hint: "when new version is available, show notification to user"
    default_recomended: true
  cruise_update_interval_msec:
    type: "uint"
    default: 1000
    units: "ms"
    hint: "Rate at which cruise control updates are sent to the UI"
    default_recommended: true
  gps_timezone_init_timeout:
    type: "uint"
    default: 300
    units: "seconds"
    hint: "How long to keep trying to get valid gps for timezone init, before giving up and using defaults."
    default_recommended: true
