#pragma once

#include "config/tree/cpp/config_tree.hpp"
#include "generated/config/api/proto/config_service.grpc.pb.h"
#include <atomic>
#include <boost/algorithm/string.hpp>
#include <condition_variable>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <queue>
#include <set>
#include <spdlog/spdlog.h>
#include <sstream>
#include <thread>
#include <unordered_map>

namespace carbon {
namespace config {

class ConfigSubscriptionManager;

class SubscriptionData {
public:
  SubscriptionData(proto::ConfigNotificationService::AsyncService *service, grpc::ServerCompletionQueue *cq,
                   ConfigSubscriptionManager *manager);

  void notify(std::string subscription_key, std::string key);
  void shutdown();
  void proceed(bool ok);

private:
  ConfigSubscriptionManager *manager_;
  proto::ConfigNotificationService::AsyncService *service_;
  grpc::ServerCompletionQueue *cq_;
  grpc::ServerContext ctx_;
  proto::SubscriptionRequest request_;
  proto::SubscriptionNotifyMessage message_;
  grpc::ServerAsyncWriter<proto::SubscriptionNotifyMessage> stream_;
  bool finished_;
  bool started_;
  bool writing_;

  void setup();
  void clean_up();
};

class ConfigSubscriptionNode {
private:
  std::string name_;
  std::set<SubscriptionData *> subscriptions_;
  std::unordered_map<std::string, std::shared_ptr<ConfigSubscriptionNode>> children_;

  void notify(std::stringstream &subscription_key, std::list<std::string> &key);
  void notify_all(std::stringstream &subscription_key);
  void add_subscription(SubscriptionData *subscription, std::list<std::string> &key);
  void remove_subscription(SubscriptionData *subscription, std::list<std::string> &key);

public:
  ConfigSubscriptionNode(std::string name);

  void notify(std::string key);
  void add_subscription(std::string key, SubscriptionData *subscription);
  void remove_subscription(std::string key, SubscriptionData *subscription);
};

class ConfigSubscriptionManager {
private:
  proto::ConfigNotificationService::AsyncService *service_;
  grpc::ServerCompletionQueue *cq_;
  std::shared_ptr<ConfigSubscriptionNode> node_;

  std::mutex manager_mutex_;
  std::mutex queue_mutex_;
  std::condition_variable queue_cv_;
  std::queue<std::string> notification_queue_;
  std::queue<std::tuple<uint64_t, std::string>> notification_wait_queue_;
  std::atomic<bool> destroyed_;
  std::thread subscription_thread_;
  std::thread notify_thread_;

  bool started_;

  void handle_subscriptions();
  void handle_notifications();

public:
  ConfigSubscriptionManager(proto::ConfigNotificationService::AsyncService *service, grpc::ServerCompletionQueue *cq);
  ~ConfigSubscriptionManager();
  void start();

  void notify(std::string key);
  void notify_internal_with_delay(std::string key);
  void add_subscription(std::string key, SubscriptionData *subscription);
  void remove_subscription(std::string key, SubscriptionData *subscription);
};

} // namespace config
} // namespace carbon
