#pragma once

#include "config/service/cpp/cloud_push.hpp"
#include "config/service/cpp/subscription_manager.hpp"
#include "config/tree/cpp/config_tree.hpp"
#include "generated/config/api/proto/config_service.grpc.pb.h"
#include <config/client/cpp/grpc_client.hpp>
#include <mutex>

#include <grpcpp/grpcpp.h>

namespace carbon {
namespace config {

class ConfigServiceImpl final : public proto::ConfigService::Service {
public:
  ConfigServiceImpl(std::shared_ptr<ConfigTree> config_tree,
                    std::shared_ptr<ConfigSubscriptionManager> subscription_manager, std::string save_file_path,
                    bool is_cloud_config_service);
  ~ConfigServiceImpl();

  grpc::Status Ping(grpc::ServerContext *, const proto::PingRequest *request, proto::PongResponse *);
  grpc::Status SetValue(grpc::ServerContext *, const proto::SetValueRequest *request, proto::SetValueResponse *);
  grpc::Status GetTree(grpc::ServerContext *, const proto::GetTreeRequest *request, proto::GetTreeResponse *);
  grpc::Status GetLeaves(grpc::ServerContext *, const proto::GetLeavesRequest *request,
                         proto::GetLeavesResponse *response);
  grpc::Status AddToList(grpc::ServerContext *, const proto::AddToListRequest *request,
                         proto::AddToListResponse *response);
  grpc::Status RemoveFromList(grpc::ServerContext *, const proto::RemoveFromListRequest *request,
                              proto::RemoveFromListResponse *response);
  grpc::Status SetTree(grpc::ServerContext *, const proto::SetTreeRequest *request, proto::SetTreeResponse *response);
  grpc::Status UpgradeCloudConfig(grpc::ServerContext *, const proto::UpgradeCloudConfigRequest *request,
                                  proto::UpgradeCloudConfigResponse *response);

  void add_cloud_client(std::string prefix, std::shared_ptr<ConfigClient> client);
  void set_remote_tree(std::string key);
  std::shared_ptr<ConfigTree> get_node(std::string key);
  void save_tree_to_file();

private:
  std::string save_file_path_;
  std::shared_ptr<ConfigTree> config_tree_;
  std::shared_ptr<ConfigSubscriptionManager> subscription_manager_;
  std::shared_ptr<ConfigCloudPush> cloud_push;
  std::mutex file_save_mutex_;
  bool is_cloud_config_service_;
};

} // namespace config
} // namespace carbon
