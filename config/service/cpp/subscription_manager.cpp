#include "config/service/cpp/subscription_manager.hpp"
#include <lib/common/cpp/time.h>

namespace carbon {
namespace config {

// **** Data ****

SubscriptionData::SubscriptionData(proto::ConfigNotificationService::AsyncService *service,
                                   grpc::ServerCompletionQueue *cq, ConfigSubscriptionManager *manager)
    : manager_(manager), service_(service), cq_(cq), stream_(&ctx_), finished_(false), started_(false),
      writing_(false) {
  service_->RequestSubscribe(&ctx_, &request_, &stream_, cq_, cq_, this);
  ctx_.AsyncNotifyWhenDone(this);
}

void SubscriptionData::notify(std::string subscription_key, std::string key) {
  if (this->writing_) {
    this->manager_->notify_internal_with_delay(subscription_key + "/" + key);
    return;
  }
  message_.set_subscription_key(subscription_key);
  message_.set_notify_key(key);
  if (!this->finished_) {
    this->writing_ = true;
    this->stream_.Write(message_, this);
  }
}

void SubscriptionData::shutdown() {
  if (!this->finished_) {
    this->stream_.Finish(grpc::Status::OK, this);
    this->finished_ = true;
    if (this->started_) {
      for (auto &key : this->request_.keys()) {
        this->manager_->remove_subscription(key, this);
      }
    }
  }
}

void SubscriptionData::setup() {
  // Release next async handler
  // Note: This is a messed up memory pattern but somehow it's how
  // the GRPC official async examples do it...
  new SubscriptionData(service_, cq_, manager_);

  // Setup Notifications
  for (auto &key : this->request_.keys()) {
    spdlog::info("Creating Stream for: {}", key);
    this->manager_->add_subscription(key, this);
  }
  this->started_ = true;
}

void SubscriptionData::clean_up() {
  // Note: This is a messed up memory pattern but somehow it's how
  // the GRPC official async examples do it...
  delete this;
}

void SubscriptionData::proceed(bool ok) {
  bool cancelled = ctx_.IsCancelled();
  if (ok) {
    ok = !cancelled;
  }
  if (!this->started_) {
    if (ok) {
      this->setup();
    } else {
      this->shutdown();
    }
  } else if (this->finished_) {
    this->clean_up();
  } else {
    if (!ok) {
      this->shutdown();
    } else {
      this->writing_ = false;
    }
  }
}

// **** Node ****

void ConfigSubscriptionNode::notify_all(std::stringstream &subscription_key) {
  // Notify This Node
  for (auto &sub : this->subscriptions_) {
    sub->notify(subscription_key.str(), "");
  }

  for (auto &child : this->children_) {
    std::string head = child.first;
    std::stringstream sub_key;
    sub_key << subscription_key.str();

    if (sub_key.str().size() > 0) {
      sub_key << "/";
    }

    sub_key << head;
    this->children_[head]->notify_all(sub_key);
  }
}

void ConfigSubscriptionNode::notify(std::stringstream &subscription_key, std::list<std::string> &key) {
  // Notify This Node
  for (auto &sub : this->subscriptions_) {
    std::stringstream sub_key;
    for (auto &item : key) {
      if (sub_key.str().size() > 0) {
        sub_key << "/";
      }
      sub_key << item;
    }
    sub->notify(subscription_key.str(), sub_key.str());
  }

  // Notify Children
  if (key.size() == 0) {
    return;
  }

  std::string head = key.front();
  key.pop_front();

  if (!this->children_.count(head)) {
    return;
  }

  if (subscription_key.str().size() > 0) {
    subscription_key << "/";
  }
  subscription_key << head;
  if (key.size() == 0) {
    return this->children_[head]->notify_all(subscription_key);
  } else {
    return this->children_[head]->notify(subscription_key, key);
  }
}

ConfigSubscriptionNode::ConfigSubscriptionNode(std::string name) : name_(name) {}

void ConfigSubscriptionNode::notify(std::string key) {
  std::list<std::string> result;
  boost::split(result, key, boost::is_any_of("/"));
  std::stringstream subscription_key;
  spdlog::info("Notifying: {}", key);
  this->notify(subscription_key, result);
}

void ConfigSubscriptionNode::add_subscription(SubscriptionData *subscription, std::list<std::string> &key) {
  // This is the node
  if (key.size() == 0) {
    this->subscriptions_.insert(subscription);
    return;
  }

  std::string head = key.front();
  key.pop_front();

  if (!this->children_.count(head)) {
    this->children_[head] = std::make_shared<ConfigSubscriptionNode>(head);
  }

  this->children_[head]->add_subscription(subscription, key);
}

void ConfigSubscriptionNode::remove_subscription(SubscriptionData *subscription, std::list<std::string> &key) {
  // This is the node
  if (key.size() == 0) {
    this->subscriptions_.erase(subscription);
    return;
  }

  std::string head = key.front();
  key.pop_front();

  if (!this->children_.count(head)) {
    // TODO This would be a strange case, maybe log smthg
    return;
  }

  this->children_[head]->remove_subscription(subscription, key);
}

void ConfigSubscriptionNode::add_subscription(std::string key, SubscriptionData *subscription) {
  std::list<std::string> result;
  boost::split(result, key, boost::is_any_of("/"));
  this->add_subscription(subscription, result);
}

void ConfigSubscriptionNode::remove_subscription(std::string key, SubscriptionData *subscription) {
  std::list<std::string> result;
  boost::split(result, key, boost::is_any_of("/"));
  this->remove_subscription(subscription, result);
}

// **** Manager ****

ConfigSubscriptionManager::ConfigSubscriptionManager(proto::ConfigNotificationService::AsyncService *service,
                                                     grpc::ServerCompletionQueue *cq)
    : service_(service), cq_(cq), node_(std::make_shared<ConfigSubscriptionNode>("root")), destroyed_(false),
      started_(false) {}

ConfigSubscriptionManager::~ConfigSubscriptionManager() {
  this->destroyed_ = true;
  this->queue_cv_.notify_all();
  if (this->started_) {
    this->notify_thread_.join();
    this->subscription_thread_.join();
  }
}

void ConfigSubscriptionManager::start() {
  this->notify_thread_ = std::thread(&ConfigSubscriptionManager::handle_notifications, this);
  this->subscription_thread_ = std::thread(&ConfigSubscriptionManager::handle_subscriptions, this);
  this->started_ = true;
}

void ConfigSubscriptionManager::notify_internal_with_delay(std::string key) {
  // This should be called under lock
  this->notification_wait_queue_.push({maka_control_timestamp_ms(), key});
}

void ConfigSubscriptionManager::notify(std::string key) {
  std::lock_guard<std::mutex> lock(this->manager_mutex_);
  this->notification_queue_.push(key);
  this->queue_cv_.notify_all();
}

void ConfigSubscriptionManager::add_subscription(std::string key, SubscriptionData *subscription) {
  // TODO this should be called by friends only.
  this->node_->add_subscription(key, subscription);
}

void ConfigSubscriptionManager::remove_subscription(std::string key, SubscriptionData *subscription) {
  // TODO this should be called by friends only, no locking because somehow already in lock?
  this->node_->remove_subscription(key, subscription);
}

void ConfigSubscriptionManager::handle_notifications() {
  std::string key;
  while (!this->destroyed_) {
    {
      std::unique_lock<std::mutex> lock(this->manager_mutex_);
      while (!this->destroyed_ && this->notification_queue_.size() == 0 && this->notification_wait_queue_.size() == 0) {
        this->queue_cv_.wait_for(lock, std::chrono::milliseconds(1000));
      }
      if (this->destroyed_) {
        return;
      }

      if (this->notification_wait_queue_.size() > 0) {
        std::set<std::string> duplicates;

        // Process wait queue
        uint64_t current_time = maka_control_timestamp_ms();
        while (this->notification_wait_queue_.size() > 0 &&
               std::get<0>(this->notification_wait_queue_.front()) + 1000 < current_time) {
          key = std::get<1>(this->notification_wait_queue_.front());
          this->notification_wait_queue_.pop();

          if (duplicates.count(key) != 0) {
            continue;
          }
          duplicates.insert(key);

          this->notification_queue_.push(key);
        }
      }

      // Process 1 Item From Notification Queue
      if (this->notification_queue_.size() > 0) {
        key = this->notification_queue_.front();
        this->notification_queue_.pop();
        this->node_->notify(key);
      }
    }
  }
}

void ConfigSubscriptionManager::handle_subscriptions() {
  // Setup Async Handling
  // Note: This is a messed up memory pattern but somehow it's how
  // the GRPC official async examples do it...
  new SubscriptionData(service_, cq_, this);
  void *tag; // uniquely identifies a request.
  bool ok;
  while (!this->destroyed_) {
    if (!this->cq_->Next(&tag, &ok)) {
      spdlog::error("Queue Shutdown for some reason...");
      break;
    }

    {
      const std::lock_guard<std::mutex> lock(this->manager_mutex_);
      static_cast<SubscriptionData *>(tag)->proceed(ok);
    }
  }
}

} // namespace config
} // namespace carbon
