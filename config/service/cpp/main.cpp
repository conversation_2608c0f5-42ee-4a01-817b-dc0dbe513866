#include "lib/common/cpp/logging_service.hpp"
#include <config/client/cpp/config_subscriber.hpp>
#include <config/service/cpp/grpc_service.hpp>
#include <config/service/cpp/subscription_manager.hpp>
#include <config/service/cpp/utils.hpp>
#include <csignal>
#include <filesystem>
#include <fmt/format.h>
#include <fstream>
#include <future>
#include <grpcpp/grpcpp.h>
#include <iostream>
#include <lib/common/cpp/utils/generation.hpp>
#include <memory>
#include <prometheus/exposer.h>
#include <prometheus/registry.h>
#include <signal.h>
#include <spdlog/spdlog.h>
#include <string>
#include <thread>

#include <grpcpp/grpcpp.h>

#ifdef CARBON_CONFIG_SERVICE_CLOUD
#define CARBON_SAVE_FILE_PATH_YAML "cloud_config.yaml"
#define CARBON_SAVE_FILE_PATH_JSON "cloud_config.json"
#define CARBON_CONFIG_PORT 61001
#define CARBON_METRICS_PORT 62001
#else
#define CARBON_SAVE_FILE_PATH_YAML "robot_config.yaml"
#define CARBON_SAVE_FILE_PATH_JSON "robot_config.json"
#define CARBON_CONFIG_PORT 61001
#define CARBON_METRICS_PORT 62001
#endif

using namespace carbon::config;
using namespace prometheus;

// The gRPC server is defined globally so that SIGTERM handler can shut it
// down when Kubernetes or bot stops the process.
std::unique_ptr<grpc::Server> server;
std::unique_ptr<grpc::ServerCompletionQueue> cq;

std::string get_config_def_path() {
#ifdef CARBON_CONFIG_SERVICE_CLOUD
  return "roots/cloud.yaml";
#else
  char *role_ptr = std::getenv("MAKA_ROLE");
  std::string role(carbon::common::GEN_BUD);
  if (role_ptr != NULL) {
    role = role_ptr;
  }
  if (role.rfind("simulator", 0) == 0) {
    if (carbon::common::is_reaper()) {
      return "roots/simulator_reaper.yaml";
    }
    return "roots/simulator.yaml";
  }

  if (carbon::common::is_bud()) {
    return "roots/bud.yaml";
  } else if (carbon::common::is_slayer()) {
    return "roots/slayer.yaml";
  } else if (carbon::common::is_reaper()) {
    return "roots/reaper.yaml";
  } else if (carbon::common::is_rtc()) {
    return "roots/tractor.yaml";
  } else {
    return "roots/bud.yaml";
  }
#endif
}

std::string get_cloud_config_prefix() {
  char *role_ptr = std::getenv("MAKA_ROLE");
  std::string role(carbon::common::GEN_BUD);
  if (role_ptr != NULL) {
    role = role_ptr;
  }
  if (role.rfind("simulator", 0) == 0) {
    return "simulators";
  }

  if (carbon::common::is_bud()) {
    return "buds";
  } else if (carbon::common::is_slayer()) {
    return "slayers";
  } else if (carbon::common::is_reaper()) {
    return "reapers";
  } else if (carbon::common::is_rtc()) {
    return "rtcs";
  } else {
    return "buds";
  }
}

std::string get_cloud_addr() {
  char *cloud_host_ptr = std::getenv("CARBON_CONFIG_CLOUD_HOST");
  std::string host = "rosy-grpc.cloud.carbonrobotics.com";
  if (cloud_host_ptr != NULL && strlen(cloud_host_ptr) > 0) {
    host = cloud_host_ptr;
  }

  char *cloud_port_ptr = std::getenv("CARBON_CONFIG_CLOUD_PORT");
  std::string port = "443";
  if (cloud_port_ptr != NULL && strlen(cloud_port_ptr) > 0) {
    port = cloud_port_ptr;
  }

  return fmt::format("{}:{}", host, port);
}

std::string get_save_path() {
  char *data_dir_env = std::getenv("MAKA_DATA_DIR");
  std::filesystem::path data_dir_path = "/data";
  if (data_dir_env != NULL) {
    data_dir_path = data_dir_env;
  }

  std::filesystem::path dir_path = data_dir_path / "config_service";
  std::filesystem::create_directories(dir_path);
  std::filesystem::path full_path = dir_path / CARBON_SAVE_FILE_PATH_JSON;

  auto path = full_path.string();
  if (!std::ifstream(path)) {
    spdlog::info(fmt::format("Couldn't find config file {}", path));
    auto yaml_path = (dir_path / CARBON_SAVE_FILE_PATH_YAML).string();
    std::ifstream source(yaml_path, std::ios::binary);
    if (source) {
      spdlog::info(fmt::format("Found YAML config file at {}, will copy into {}", yaml_path, path));
      std::ofstream target(path, std::ios::binary);
      target << source.rdbuf();
      target.close();
      source.close();
    }
  }
  return path;
}

int main() {
  // Create metrics server and registry
  Exposer exposer{fmt::format("0.0.0.0:{}", CARBON_METRICS_PORT)};
  auto registry = std::make_shared<Registry>();

  exposer.RegisterCollectable(registry);
  spdlog::info("Starting Config Service Server...");
  grpc::EnableDefaultHealthCheckService(true);
  std::string server_address = fmt::format("0.0.0.0:{}", CARBON_CONFIG_PORT);
  std::string save_file_path = get_save_path();

  grpc::ServerBuilder builder;
  builder.AddListeningPort(server_address, grpc::InsecureServerCredentials());
  builder.SetResourceQuota(grpc::ResourceQuota().SetMaxThreads(64));
  cq = builder.AddCompletionQueue();

  proto::ConfigNotificationService::AsyncService notif_service;
  auto subscription_manager = std::make_shared<ConfigSubscriptionManager>(&notif_service, cq.get());

  // TODO Pick Robot based on env var etc.
  std::string robot_name = carbon::config::utils::get_config_name();
  auto schema_tree = std::make_shared<ConfigDefTree>(robot_name, get_config_def_path());
  auto config_tree = std::make_shared<ConfigTree>(robot_name, schema_tree, save_file_path);
#ifdef CARBON_CONFIG_SERVICE_CLOUD
  bool is_cloud_config_service = true;
#else
  bool is_cloud_config_service = false;
#endif
  auto config_service = ConfigServiceImpl(config_tree, subscription_manager, save_file_path, is_cloud_config_service);

#ifndef CARBON_CONFIG_SERVICE_CLOUD
  auto subscriber = std::make_shared<ConfigSubscriber>(get_cloud_addr());
  std::string cloud_path = fmt::format("{}/{}", get_cloud_config_prefix(), robot_name);
  subscriber->add_existing_config_tree(robot_name, cloud_path, config_tree);
  config_service.add_cloud_client(cloud_path, subscriber->get_client());
  subscriber->set_notification_callback([&](const std::string key) {
    subscription_manager->notify(key);
    config_service.save_tree_to_file();
  });
  subscriber->start();
#endif

  auto log_service = lib::common::logging::LoggingServiceImpl("config_service.log");
  std::vector<::grpc::Service *> services;
  services.push_back(&config_service);
  services.push_back(&notif_service);
  services.push_back(&log_service);

  for (auto &service : services) {
    builder.RegisterService(service);
  }

  server = builder.BuildAndStart();

  subscription_manager->start();

  std::signal(SIGTERM, [](int) {
    // When SIGTERM is received, shutdown the gRPC server.
    server->Shutdown();
    cq->Shutdown();
  });

  spdlog::info("Started Config Service GRPC Server at: {}", server_address);

  // Wait Forever
  server->Wait();
}
