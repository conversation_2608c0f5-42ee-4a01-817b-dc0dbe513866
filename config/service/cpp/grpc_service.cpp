#include "config/service/cpp/grpc_service.hpp"
#include <config/client/cpp/exceptions.hpp>
#include <config/convert/cpp/convert.hpp>
#include <lib/common/cpp/time.h>

#include <filesystem>
#include <spdlog/spdlog.h>

#include <config/convert/cpp/convert.hpp>
#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon {
namespace config {

ConfigServiceImpl::ConfigServiceImpl(std::shared_ptr<ConfigTree> config_tree,
                                     std::shared_ptr<ConfigSubscriptionManager> subscription_manager,
                                     std::string save_file_path, bool is_cloud_config_service)
    : save_file_path_(save_file_path), config_tree_(config_tree), subscription_manager_(subscription_manager),
      is_cloud_config_service_(is_cloud_config_service) {
  this->cloud_push = std::make_shared<ConfigCloudPush>(this);
  this->cloud_push->start();
}

ConfigServiceImpl::~ConfigServiceImpl() {}

void ConfigServiceImpl::add_cloud_client(std::string prefix, std::shared_ptr<ConfigClient> client) {
  // Not Thread Safe, should only be called before the server is started
  this->cloud_push->add_cloud_client(prefix, client);
}

void ConfigServiceImpl::set_remote_tree(std::string key) { this->cloud_push->push_key(key); }

grpc::Status ConfigServiceImpl::Ping(grpc::ServerContext *, const proto::PingRequest *request,
                                     proto::PongResponse *response) {
  try {
    response->set_x(request->x());
    spdlog::info("Ping Request Value: {}", request->x());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

std::shared_ptr<ConfigTree> ConfigServiceImpl::get_node(std::string key) {
  auto node = key == "" ? this->config_tree_ : this->config_tree_->get_node(key);
  return node;
}

grpc::Status ConfigServiceImpl::SetValue(grpc::ServerContext *, const proto::SetValueRequest *request,
                                         proto::SetValueResponse *response) {
  try {
    spdlog::info("Set Value: {}", request->key());
    auto node = this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: ,{},", request->key()));
    }

    if (!request->has_value()) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "Value Not Specified");
    }

    auto timestamp_ms = maka_control_timestamp_ms();

    switch (request->value().value_case()) {
    case proto::ConfigValue::ValueCase::kInt64Val:
      node->set_value<int64_t>(request->value().int64_val(), timestamp_ms, false);
      break;
    case proto::ConfigValue::ValueCase::kUint64Val:
      node->set_value<uint64_t>(request->value().uint64_val(), timestamp_ms, false);
      break;
    case proto::ConfigValue::ValueCase::kFloatVal:
      node->set_value<double>(request->value().float_val(), timestamp_ms, false);
      break;
    case proto::ConfigValue::ValueCase::kBoolVal:
      node->set_value<bool>(request->value().bool_val(), timestamp_ms, false);
      break;
    case proto::ConfigValue::ValueCase::kStringVal:
      node->set_value<std::string>(request->value().string_val(), timestamp_ms, false);
      break;
    default:
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, "Invalid Value Type");
    }

    this->config_tree_->touch(request->key(), timestamp_ms);
    save_tree_to_file();
    (void)response;
    this->subscription_manager_->notify(request->key());
    this->set_remote_tree(request->key());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

void ConfigServiceImpl::save_tree_to_file() {
  std::lock_guard<std::mutex> lock(this->file_save_mutex_);
  this->config_tree_->save_to_file(this->save_file_path_);
}

grpc::Status ConfigServiceImpl::GetTree(grpc::ServerContext *, const proto::GetTreeRequest *request,
                                        proto::GetTreeResponse *response) {
  try {
    spdlog::info("Get Tree: {}", request->key());
    auto node = request->key() == "" ? this->config_tree_ : this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: {},", request->key()));
    }

    fill_proto_node(response->mutable_node(), node);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status ConfigServiceImpl::SetTree(grpc::ServerContext *, const proto::SetTreeRequest *request,
                                        proto::SetTreeResponse *response) {
  try {
    spdlog::info("Set Tree: {}", request->key());
    auto node = request->key() == "" ? this->config_tree_ : this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: {},", request->key()));
    }

    fill_tree_node(node, request->node(), false);
    save_tree_to_file();
    (void)response;
    this->subscription_manager_->notify(request->key());
    this->set_remote_tree(request->key());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

void push_leaves(proto::GetLeavesResponse *response, std::string prefix, std::shared_ptr<ConfigTree> node, bool first) {
  auto type = node->get_def()->get_type();
  std::filesystem::path key = prefix;
  if (!first) {
    key = key / node->get_name();
  }

  if (type != ConfigType::NODE) {
    auto leaf = response->add_leaves();
    auto value = leaf->mutable_value();
    leaf->set_key(key.string());
    value->set_timestamp_ms(node->get_timestamp_ms());
    switch (type) {
    case ConfigType::BOOL:
      value->set_bool_val(node->get_value<bool>());
      break;
    case ConfigType::INT:
      value->set_int64_val(node->get_value<int64_t>());
      break;
    case ConfigType::UINT:
      value->set_uint64_val(node->get_value<uint64_t>());
      break;
    case ConfigType::FLOAT:
      value->set_float_val(node->get_value<double>());
      break;
    case ConfigType::STRING:
      value->set_string_val(node->get_value<std::string>());
      break;
    case ConfigType::LIST:
      value->set_string_val("...");
      break;
    default:
      break;
    }
  }

  for (auto child : node->get_children_nodes()) {
    push_leaves(response, key.string(), child, false);
  }
}

grpc::Status ConfigServiceImpl::GetLeaves(grpc::ServerContext *, const proto::GetLeavesRequest *request,
                                          proto::GetLeavesResponse *response) {
  try {
    spdlog::info("Get Leaves: {}", request->key());
    auto node = request->key() == "" ? this->config_tree_ : this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: {},", request->key()));
    }

    push_leaves(response, request->key(), node, true);
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status ConfigServiceImpl::AddToList(grpc::ServerContext *, const proto::AddToListRequest *request,
                                          proto::AddToListResponse *response) {
  try {
    spdlog::info("Add To List: {}", request->key());
    auto node = request->key() == "" ? this->config_tree_ : this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: {},", request->key()));
    }

    if (node->get_def()->get_type() != ConfigType::LIST) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not A List: {},", request->key()));
    }

    auto config_def = node->get_def()->get_child("item");
    if (config_def == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Invalid List Def: {}", request->key()));
    }

    std::string name = request->name();
    if (node->is_protected(name)) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                          fmt::format("{} is a key word, please use a different name", request->name()));
    }
    for (size_t i = 0; i < name.length(); i++) {
      char c = name[i];
      if ((c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') || c == '_' || c == '-' ||
          c == '.' || c == '/' || c == ':') {
        continue;
      }
      return grpc::Status(
          grpc::StatusCode::INVALID_ARGUMENT,
          fmt::format("Item name should contain only letters, digits or underscore", request->name(), request->key()));
    }
    int count = 0;
    if (name == "") {
      while (name == "" || node->get_child(name) != nullptr) {
        name = fmt::format("{}_{}", node->get_name(), node->get_children_nodes().size() + count);
        count++;
      }
    } else if (node->get_child(name) != nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                          fmt::format("Child already exists with name {} at key {}", request->name(), request->key()));
    }

    node->add_child(std::make_shared<ConfigTree>(name, config_def));

    // touch the new child node
    auto child = node->get_child(name);
    child->touch_subtree();

    this->config_tree_->touch(fmt::format("{}/{}", request->key(), name), maka_control_timestamp_ms());
    save_tree_to_file();
    (void)response;
    this->subscription_manager_->notify(request->key());
    this->set_remote_tree(request->key());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status ConfigServiceImpl::RemoveFromList(grpc::ServerContext *, const proto::RemoveFromListRequest *request,
                                               proto::RemoveFromListResponse *response) {
  try {
    spdlog::info("Remove From List: {}", request->key());
    auto node = request->key() == "" ? this->config_tree_ : this->config_tree_->get_node(request->key());

    if (node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not Found: {},", request->key()));
    }

    if (node->get_def()->get_type() != ConfigType::LIST) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT, fmt::format("Key Not A List: {},", request->key()));
    }

    node->remove_child(request->name());

    this->config_tree_->touch(request->key(), maka_control_timestamp_ms());
    save_tree_to_file();
    (void)response;
    this->subscription_manager_->notify(request->key());
    this->set_remote_tree(request->key());
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

grpc::Status ConfigServiceImpl::UpgradeCloudConfig(grpc::ServerContext *,
                                                   const proto::UpgradeCloudConfigRequest *request,
                                                   proto::UpgradeCloudConfigResponse *) {
  try {
    if (!is_cloud_config_service_) {
      return grpc::Status(grpc::StatusCode::UNIMPLEMENTED, "UpgradeCloudConfig should be run at cloud config service");
    }

    spdlog::info("UpgradeCloudConfig robot={} from={} to={}", request->robot(), request->from_template(),
                 request->to_template());

    auto robot_node = this->config_tree_->get_node(request->robot());
    if (robot_node == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                          fmt::format("Couldn't find robot node {},", request->robot()));
    }
    auto from_template = this->config_tree_->get_node(request->from_template());
    if (from_template == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                          fmt::format("Couldn't find from_template node {},", request->from_template()));
    }
    auto to_template = this->config_tree_->get_node(request->to_template());
    if (to_template == nullptr) {
      return grpc::Status(grpc::StatusCode::INVALID_ARGUMENT,
                          fmt::format("Couldn't find to_template node {},", request->to_template()));
    }
    std::vector<std::string> updated_keys;
    robot_node->upgrade(from_template, to_template, &updated_keys);
    save_tree_to_file();
    for (auto key : updated_keys) {
      this->subscription_manager_->notify(key);
    }
  } catch (const std::exception &ex) {
    return grpc::Status(grpc::StatusCode::UNKNOWN, ex.what());
  }
  return grpc::Status::OK;
}

} // namespace config
} // namespace carbon
