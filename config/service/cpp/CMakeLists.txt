file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
list(REMOVE_ITEM SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp)

add_library(config_service_lib SHARED ${SOURCES})
target_compile_definitions(config_service_lib PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(config_service_lib PUBLIC m yaml-cpp spdlog fmt pthread gRPC::grpc++ config_service_proto config_tree_lib config_client_lib config_convert_lib prometheus-cpp-core prometheus-cpp-pull logging_service utils)

add_executable(config_service main.cpp)
target_compile_definitions(config_service PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(config_service PUBLIC config_service_lib)

add_executable(cloud_config_service main.cpp)
target_compile_definitions(cloud_config_service PUBLIC -DSPDLOG_FMT_EXTERNAL=1 -DCARBON_CONFIG_SERVICE_CLOUD)
target_link_libraries(cloud_config_service PUBLIC config_service_lib)