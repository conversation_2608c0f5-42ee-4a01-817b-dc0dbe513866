#include "config/service/cpp/cloud_push.hpp"
#include "config/service/cpp/grpc_service.hpp"
#include <config/client/cpp/exceptions.hpp>

namespace carbon {
namespace config {

ConfigCloudPush::ConfigCloudPush(ConfigServiceImpl *service) : service_(service), destroyed_(false), started_(false) {}

ConfigCloudPush::~ConfigCloudPush() {
  {
    std::unique_lock lck(this->cond_mutex_);
    this->destroyed_ = true;
    this->cond_.notify_all();
  }

  if (this->started_) {
    this->push_thread_.join();
  }
  this->started_ = false;
}

void ConfigCloudPush::start() {
  this->started_ = true;
  this->push_thread_ = std::thread(&ConfigCloudPush::handle_push, this);
}

void ConfigCloudPush::add_cloud_client(std::string prefix, std::shared_ptr<ConfigClient> client) {
  this->cloud_clients_[prefix] = client;
}

void ConfigCloudPush::push_key(std::string key) {
  std::unique_lock lck(this->cond_mutex_);
  this->requests_.push(key);
  this->cond_.notify_all();
}

void ConfigCloudPush::handle_one_push(std::string key) {
  auto node = this->service_->get_node(key);

  for (auto &client : this->cloud_clients_) {
    try {
      std::string path = fmt::format("{}/{}", client.first, key);
      client.second->set_tree(path, node);
    } catch (config_client_error &ex) {
      spdlog::warn("Failed to update remote tree at {}, {}", client.second->get_addr(), ex.what());
    }
  }
}

void ConfigCloudPush::handle_push() {
  while (1) {
    std::string v;
    {
      std::unique_lock lck(this->cond_mutex_);
      while (!this->destroyed_ && this->requests_.empty()) {
        this->cond_.wait(lck);
      }

      if (this->destroyed_) {
        return;
      }

      v = this->requests_.front();
      this->requests_.pop();
    }
    this->handle_one_push(v);
  }
}

} // namespace config
} // namespace carbon
