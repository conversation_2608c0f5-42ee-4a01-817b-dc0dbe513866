#pragma once

#include "config/client/cpp/grpc_client.hpp"
#include "config/tree/cpp/config_tree.hpp"
#include "generated/config/api/proto/config_service.grpc.pb.h"

#include <condition_variable>
#include <mutex>
#include <queue>
#include <thread>

/*
#include <atomic>
#include <boost/algorithm/string.hpp>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <set>
#include <spdlog/spdlog.h>
#include <sstream>
#include <unordered_map>
*/

namespace carbon {
namespace config {

class ConfigServiceImpl;

class ConfigCloudPush {
private:
  void handle_push();
  void handle_one_push(std::string key);

  std::unordered_map<std::string, std::shared_ptr<ConfigClient>> cloud_clients_;
  std::thread push_thread_;
  ConfigServiceImpl *service_;

  std::mutex cond_mutex_;
  std::condition_variable cond_;
  std::queue<std::string> requests_;

  bool destroyed_;
  bool started_;

public:
  ConfigCloudPush(ConfigServiceImpl *service);
  ~ConfigCloudPush();

  void start();
  void add_cloud_client(std::string prefix, std::shared_ptr<ConfigClient> client);
  void push_key(std::string key);
};

} // namespace config
} // namespace carbon
