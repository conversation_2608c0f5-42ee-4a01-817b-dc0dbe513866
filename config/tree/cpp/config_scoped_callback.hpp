#pragma once

#include <atomic>
#include <config/tree/cpp/config_tree.hpp>
#include <memory>

namespace carbon::config {
class ConfigScopedCallback {
public:
  ConfigScopedCallback(std::shared_ptr<ConfigTree> tree, ConfigTreeCallback cb, bool call_now = false);
  ConfigScopedCallback(ConfigScopedCallback &&rhs);
  virtual ~ConfigScopedCallback();
  inline std::shared_ptr<ConfigTree> tree() { return tree_; }
  ConfigTree *operator->() const { return tree_.get(); }

private:
  std::shared_ptr<ConfigTree> tree_;
  uint32_t cb_id_;
};
/*
 * Note: for performance reasons we use relaxed memory order.
 * This still guarantees that if the callback gets called then the
 * flag will be set, but it does not guarantee when. So we will trigger
 * a reload required, but it may take an aditional cycle to see that.
 * since this is just to keep config data in sync this should be fine, as
 * we only need eventual consistency.
 */
class AtomicFlagConfigScopedCallback : public ConfigScopedCallback {
public:
  AtomicFlagConfigScopedCallback(std::shared_ptr<ConfigTree> tree, bool initial_sync_state = true);
  AtomicFlagConfigScopedCallback(AtomicFlagConfigScopedCallback &&rhs) = delete;
  inline bool reload_required() { return !is_in_sync_.test_and_set(std::memory_order_relaxed); }

private:
  std::atomic_flag is_in_sync_;
};
} // namespace carbon::config