#include <config/tree/cpp/config_def_tree.hpp>
#include <cstdlib>
#include <filesystem>
#include <iostream>
#include <limits>
#include <spdlog/spdlog.h>
#include <sstream>

namespace carbon {
namespace config {

std::string get_def_path(std::string definition_file_path) {
  char *robot_dir_env = std::getenv("MAKA_ROBOT_DIR");
  std::filesystem::path robot_dir_path = "/robot";
  if (robot_dir_env != NULL) {
    robot_dir_path = robot_dir_env;
  }

  std::filesystem::path full_path = robot_dir_path / "config" / "configs" / definition_file_path;
  return full_path.string();
}

std::string configComplexityToString(ConfigComplexity complexity) {
  switch (complexity) {
  case ConfigComplexity::USER:
    return "user";
  case ConfigComplexity::ADVANCED:
    return "advanced";
  case ConfigComplexity::EXPERT:
    return "expert";
  case ConfigComplexity::DEVELOPER:
    return "developer";
  default:
    spdlog::error("Invalid complexity: {}", complexity);
    return "developer";
  }
}

std::string configTypeToString(ConfigType type) {
  switch (type) {
  case ConfigType::NODE:
    return "node";
  case ConfigType::LIST:
    return "list";
  case ConfigType::STRING:
    return "string";
  case ConfigType::INT:
    return "int";
  case ConfigType::UINT:
    return "uint";
  case ConfigType::FLOAT:
    return "float";
  case ConfigType::BOOL:
    return "bool";
  default:
    spdlog::error("Invalid type: {}", type);
    return "node";
  }
}

ConfigComplexity stringToConfigComplexity(std::string complexity) {
  std::string lcomplexity = complexity;
  transform(lcomplexity.begin(), lcomplexity.end(), lcomplexity.begin(), ::tolower);
  if (lcomplexity == "user") {
    return ConfigComplexity::USER;
  } else if (lcomplexity == "advanced") {
    return ConfigComplexity::ADVANCED;
  } else if (lcomplexity == "expert") {
    return ConfigComplexity::EXPERT;
  } else if (lcomplexity == "developer") {
    return ConfigComplexity::DEVELOPER;
  } else {
    spdlog::error("Invalid Complexity: {}", complexity);
    return ConfigComplexity::DEVELOPER;
  }
}

ConfigType stringToConfigType(std::string type) {
  std::string ltype = type;
  transform(ltype.begin(), ltype.end(), ltype.begin(), ::tolower);
  if (ltype == "node") {
    return ConfigType::NODE;
  } else if (ltype == "list") {
    return ConfigType::LIST;
  } else if (ltype == "string") {
    return ConfigType::STRING;
  } else if (ltype == "int") {
    return ConfigType::INT;
  } else if (ltype == "uint") {
    return ConfigType::UINT;
  } else if (ltype == "float") {
    return ConfigType::FLOAT;
  } else if (ltype == "bool") {
    return ConfigType::BOOL;
  } else {
    spdlog::error("Invalid type: {}", type);
    return ConfigType::NODE;
  }
}
StoreProcess stringToStoreProcess(std::string process) {
  std::string lprocess = process;
  transform(lprocess.begin(), lprocess.end(), lprocess.begin(), ::tolower);
  if (lprocess == "none") {
    return PROCESS_NONE;
  } else if (lprocess == "base64") {
    return PROCESS_BASE64;
  } else {
    spdlog::error("Invalid process: {}", process);
    return PROCESS_NONE;
  }
}

void ConfigDefTree::processDef(std::string name, const YAML::Node *def_node_ptr) {
  this->name_ = name;

  if (!def_node_ptr->IsMap()) {
    spdlog::error("Config Def is not a Map: {}", name);
  }

  YAML::Node include_def_node;

  if ((*def_node_ptr)["include"]) {
    include_def_node = YAML::LoadFile(get_def_path((*def_node_ptr)["include"].as<std::string>()));
    def_node_ptr = &include_def_node;
  }

  auto &def_node = *def_node_ptr;

  if (def_node["process"]) {
    this->store_process_ = stringToStoreProcess(def_node["process"].as<std::string>());
  }
  if (def_node["hint"]) {
    this->hint_ = def_node["hint"].as<std::string>();
  }
  if (def_node["units"]) {
    this->units_ = def_node["units"].as<std::string>();
  }
  if (def_node["default_recommended"]) {
    this->default_recommended_ = def_node["default_recommended"].as<bool>();
  } else {
    this->default_recommended_ = true;
  }
  if (!def_node["type"]) {
    spdlog::error("Config Def requires a type: {}", name);
  }

  this->type_ = stringToConfigType(def_node["type"].as<std::string>());
  switch (this->type_) {
  case ConfigType::LIST: {
    if (def_node["item"] && def_node["item"].IsMap()) {
      this->children_["item"] = std::make_shared<ConfigDefTree>("item", def_node["item"]);
    }
    break;
  }
  case ConfigType::NODE: {
    if (def_node["children"] && def_node["children"].IsMap()) {
      for (YAML::const_iterator it = def_node["children"].begin(); it != def_node["children"].end(); ++it) {
        this->children_[it->first.as<std::string>()] =
            (std::make_shared<ConfigDefTree>(it->first.as<std::string>(), it->second));
      }
    }
    break;
  }
  case ConfigType::BOOL:
    this->default_value_ = def_node["default"] ? def_node["default"].as<bool>() : false;
    break;
  case ConfigType::INT:
    this->default_value_ = def_node["default"] ? def_node["default"].as<int64_t>() : 0;
    this->min_ = def_node["min"] ? def_node["min"].as<int64_t>() : std::numeric_limits<int64_t>::min();
    this->max_ = def_node["max"] ? def_node["max"].as<int64_t>() : std::numeric_limits<int64_t>::max();
    this->step_ = def_node["step"] ? def_node["step"].as<int64_t>() : 1;
    break;
  case ConfigType::UINT:
    this->default_value_ = def_node["default"] ? def_node["default"].as<uint64_t>() : 0;
    this->min_ = def_node["min"] ? def_node["min"].as<uint64_t>() : std::numeric_limits<uint64_t>::min();
    this->max_ = def_node["max"] ? def_node["max"].as<uint64_t>() : std::numeric_limits<uint64_t>::max();
    this->step_ = def_node["step"] ? def_node["step"].as<uint64_t>() : 1;
    break;
  case ConfigType::FLOAT:
    this->default_value_ = def_node["default"] ? def_node["default"].as<double>() : 0;
    this->min_ = def_node["min"] ? def_node["min"].as<double>() : std::numeric_limits<double>::min();
    this->max_ = def_node["max"] ? def_node["max"].as<double>() : std::numeric_limits<double>::max();
    this->step_ = def_node["step"] ? def_node["step"].as<double>() : 1.0;
    break;
  case ConfigType::STRING: {
    if (def_node["choices"] && def_node["choices"].IsSequence()) {
      for (YAML::const_iterator it = def_node["choices"].begin(); it != def_node["choices"].end(); ++it) {
        this->choices_.push_back(it->as<std::string>());
      }
    }

    if (def_node["default"]) {
      this->default_value_ = def_node["default"].as<std::string>();
    } else {
      this->default_value_ = this->choices_.size() == 0 ? "" : this->choices_[0];
    }
    this->string_size_limit_ =
        def_node["size_limit"] ? def_node["size_limit"].as<uint32_t>() : std::numeric_limits<uint32_t>::max();
    break;
  }
  default:

    break;
  }
}

ConfigDefTree::ConfigDefTree(std::string name, std::string definition_file_path)
    : ConfigDefTree(name, YAML::LoadFile(get_def_path(definition_file_path))) {}

ConfigDefTree::ConfigDefTree(std::string name, const YAML::Node &def_node)
    : store_process_(PROCESS_NONE), hint_(""), units_("") {
  this->processDef(name, &def_node);
}

void ConfigDefTree::intoString(std::stringstream &ss, std::string prefix) {
  ss << prefix << this->name_ << ":" << std::endl;
  ss << prefix << "  "
     << "type: " << configTypeToString(this->type_) << std::endl;
  if (this->children_.size() > 0) {
    ss << prefix << "  "
       << "children:" << std::endl;
  }

  for (auto const &child : this->children_) {
    child.second->intoString(ss, prefix + "    ");
  }
}

std::string ConfigDefTree::to_string() {
  std::stringstream ss;
  this->intoString(ss, "");
  return ss.str();
}

ConfigType ConfigDefTree::get_type() const { return this->type_; }

std::shared_ptr<ConfigDefTree> ConfigDefTree::get_child(std::string name) {
  if (this->children_.count(name) == 0) {
    return nullptr;
  }
  return this->children_[name];
}

ConfigComplexity ConfigDefTree::get_complexity() const { return this->complexity_; }

template <>
int64_t ConfigDefTree::get_default_value() const {
  assert(this->type_ == ConfigType::INT);
  return boost::get<int64_t>(this->default_value_);
}

template <>
uint64_t ConfigDefTree::get_default_value() const {
  assert(this->type_ == ConfigType::UINT);
  return boost::get<uint64_t>(this->default_value_);
}

template <>
double ConfigDefTree::get_default_value() const {
  assert(this->type_ == ConfigType::FLOAT);
  return boost::get<double>(this->default_value_);
}

template <>
bool ConfigDefTree::get_default_value() const {
  assert(this->type_ == ConfigType::BOOL);
  return boost::get<bool>(this->default_value_);
}

template <>
std::string ConfigDefTree::get_default_value() const {
  assert(this->type_ == ConfigType::STRING);
  return boost::get<std::string>(this->default_value_);
}

template <>
int64_t ConfigDefTree::get_min() const {
  assert(this->type_ == ConfigType::INT);
  return boost::get<int64_t>(this->min_);
}

template <>
uint64_t ConfigDefTree::get_min() const {
  assert(this->type_ == ConfigType::UINT);
  return boost::get<uint64_t>(this->min_);
}

template <>
double ConfigDefTree::get_min() const {
  assert(this->type_ == ConfigType::FLOAT);
  return boost::get<double>(this->min_);
}

template <>
int64_t ConfigDefTree::get_max() const {
  assert(this->type_ == ConfigType::INT);
  return boost::get<int64_t>(this->max_);
}

template <>
uint64_t ConfigDefTree::get_max() const {
  assert(this->type_ == ConfigType::UINT);
  return boost::get<uint64_t>(this->max_);
}

template <>
double ConfigDefTree::get_max() const {
  assert(this->type_ == ConfigType::FLOAT);
  return boost::get<double>(this->max_);
}

template <>
int64_t ConfigDefTree::get_step() const {
  assert(this->type_ == ConfigType::INT);
  return boost::get<int64_t>(this->step_);
}

template <>
uint64_t ConfigDefTree::get_step() const {
  assert(this->type_ == ConfigType::UINT);
  return boost::get<uint64_t>(this->step_);
}

template <>
double ConfigDefTree::get_step() const {
  assert(this->type_ == ConfigType::FLOAT);
  return boost::get<double>(this->step_);
}

boost::variant<bool, int64_t, uint64_t, double, std::string> ConfigDefTree::get_default_value() const {
  return this->default_value_;
}

const std::unordered_map<std::string, std::shared_ptr<ConfigDefTree>> ConfigDefTree::get_children() const {
  return this->children_;
}

const std::vector<std::string> &ConfigDefTree::get_choices() const { return this->choices_; }

uint32_t ConfigDefTree::get_string_size_limit() const { return this->string_size_limit_; }

ConfigDefTree::~ConfigDefTree() {}

} // namespace config
} // namespace carbon