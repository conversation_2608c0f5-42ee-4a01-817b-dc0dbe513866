#pragma once

#include <atomic>
#include <boost/variant.hpp>
#include <boost/variant/get.hpp>
#include <condition_variable>
#include <config/tree/cpp/config_def_tree.hpp>
#include <functional>
#include <list>
#include <memory>
#include <nlohmann/json.hpp>
#include <shared_mutex>
#include <sstream>
#include <string>
#include <unordered_map>
#include <yaml-cpp/yaml.h>

namespace carbon {
namespace config {

// TODO constraints aren't really enforced on the backend yet, but at some point we should sanitize inputs

using ConfigTreeCallback = std::function<void(void)>;
class ConfigTree {
private:
  static std::set<std::string> protected_keys;
  std::string name_;
  bool default_val_;
  boost::variant<bool, int64_t, uint64_t, double, std::string> value_;
  uint64_t timestamp_ms_;
  std::shared_ptr<ConfigDefTree> def_;
  std::unordered_map<std::string, std::shared_ptr<ConfigTree>> children_;

  std::shared_ptr<std::shared_mutex> mutex_;
  uint32_t callback_id_;
  std::map<uint32_t, ConfigTreeCallback> callbacks_;
  ConfigTree *parent_;
  std::unordered_map<std::string, uint64_t> unsynced_removed_children_;

  void fill_json_node(nlohmann::json &node);
  void processValues(std::string name, std::shared_ptr<ConfigDefTree> def_tree, const YAML::Node *value_node);
  void intoString(std::stringstream &ss, std::string prefix = "");
  void touch(std::list<std::string> &key, uint64_t timestamp_ms);
  std::shared_ptr<ConfigTree> get_node(std::list<std::string> &key);
  void notify();
  void _get_path(std::vector<std::string> &path_parts);
  uint64_t get_sane_timestamp(uint64_t timestamp_ms);
  bool _is_equal(std::shared_ptr<ConfigTree> rhs);

public:
  ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, ConfigTree *parent = nullptr);
  ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, std::string value_file_path,
             ConfigTree *parent = nullptr);
  ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, const YAML::Node &value_node,
             std::shared_ptr<std::shared_mutex> mutex_, ConfigTree *parent = nullptr);
  ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, std::shared_ptr<std::shared_mutex> mutex_,
             ConfigTree *parent = nullptr);
  ~ConfigTree();

  template <typename T>
  bool set_value(T val, uint64_t timestamp_ms, bool timestamp_priority);
  void add_child(std::shared_ptr<ConfigTree> child);
  void remove_child(std::string name);
  void save_to_file(std::string path);
  uint32_t register_callback(ConfigTreeCallback callback);
  void unregister_callback(uint32_t id);

  template <typename T>
  T get_value() const;
  std::shared_ptr<ConfigTree> get_node(std::string key);
  bool has_node(std::string key);

  std::vector<std::shared_ptr<ConfigTree>> get_children_nodes();
  std::vector<std::string> get_children_names();
  void touch(std::string key, uint64_t timestamp_ms);

  std::string get_name() const;
  std::shared_ptr<ConfigDefTree> get_def() const;
  uint64_t get_timestamp_ms() const;
  std::shared_ptr<ConfigTree> get_child(std::string name);
  void touch_subtree();

  std::string to_string();
  std::string get_path();
  bool child_has_been_removed_after(const std::string &child_name, uint64_t timestamp_ms);
  void syncronized();
  void upgrade(std::shared_ptr<ConfigTree> from_template, std::shared_ptr<ConfigTree> to_template,
               std::vector<std::string> *updated_keys);

  static bool is_protected(const std::string &key);
};

// Because Golang has great compatibility
std::string get_string_value(std::shared_ptr<ConfigTree> tree);
int64_t get_int_value(std::shared_ptr<ConfigTree> tree);
uint64_t get_uint_value(std::shared_ptr<ConfigTree> tree);
bool get_bool_value(std::shared_ptr<ConfigTree> tree);
double get_double_value(std::shared_ptr<ConfigTree> tree);

uint32_t register_callback(std::shared_ptr<ConfigTree> tree, ConfigTreeCallback callback);
void unregister_callback(std::shared_ptr<ConfigTree> tree, uint32_t id);

std::string get_name(std::shared_ptr<ConfigTree> tree);
std::vector<std::string> get_children_names(std::shared_ptr<ConfigTree> tree);
std::shared_ptr<ConfigTree> get_child(std::shared_ptr<ConfigTree> tree, std::string name);
std::shared_ptr<ConfigTree> get_node(std::shared_ptr<ConfigTree> tree, std::string key);
bool has_node(std::shared_ptr<ConfigTree> tree, std::string key);

void delete_config_tree_shared_ptr(std::shared_ptr<ConfigTree> *tree);

} // namespace config
} // namespace carbon
