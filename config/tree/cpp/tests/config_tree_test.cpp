#include "config/tree/cpp/config_tree.hpp"
#include "gtest/gtest.h"
#include <boost/algorithm/string.hpp>
#include <iostream>
#include <vector>
#include <yaml-cpp/yaml.h>

using namespace carbon::config;

TEST(CONFIG_TREE, BasicUsage) {
  auto tree = std::make_shared<ConfigDefTree>("test", "/robot/config/tree/cpp/tests/schema/parent.yaml");
  std::cout << tree->to_string() << std::endl;
  auto conf = ConfigTree("test", tree, "/robot/config/tree/cpp/tests/data/data.yaml");
  std::cout << conf.to_string() << std::endl;
  std::cout << "Node: " << conf.get_node("cameras/predict2/ppi")->get_value<int64_t>() << std::endl;
}
