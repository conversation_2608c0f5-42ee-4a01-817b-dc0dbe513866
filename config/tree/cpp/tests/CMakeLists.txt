set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR})

add_executable(config_tree_test config_tree_test.cpp)
get_target_property(CONFIG_TREE_INCLUDES config_tree_lib INCLUDE_DIRECTORIES)
target_link_libraries(config_tree_test gtest_main config_tree_lib)
target_include_directories(config_tree_test SYSTEM PUBLIC ${CONFIG_TREE_INCLUDES})

gtest_discover_tests(config_tree_test DISCOVERY_TIMEOUT 10)
