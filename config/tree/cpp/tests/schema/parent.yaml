type: "node"
children:
  model:
    type: "string"
    default: ""
    complexity: 1
  serial_number:
    type: "string"
    default: ""
  ppi: &anchor
    type: "int"
    default: 204
  settings:
    include: "/robot/config/tree/cpp/tests/schema/child.yaml"
  cameras:
    type: "list"
    item:
      type: "node"
      children:
        ppi: *anchor
        settings:
          include: "/robot/config/tree/cpp/tests/schema/child.yaml"

    