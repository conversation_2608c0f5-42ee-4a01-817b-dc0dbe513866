#pragma once

#include <boost/variant.hpp>
#include <boost/variant/get.hpp>
#include <memory>
#include <sstream>
#include <string>
#include <unordered_map>
#include <yaml-cpp/yaml.h>

namespace carbon {
namespace config {

enum ConfigType {
  NODE,
  LIST,
  STRING,
  INT,
  UINT,
  FLOAT,
  BOOL,
};

enum ConfigComplexity {
  USER,      // Normal User Modifiable
  ADVANCED,  // Advanced User Modifiable
  EXPERT,    // Carbon Tech Modifiable
  DEVELOPER, // Carbon Engineer Modifiable
};

enum StoreProcess {
  PROCESS_NONE,   // Do nothing to data on read/write
  PROCESS_BASE64, // base64 encode on write and decode on read
};

class ConfigDefTree {
private:
  std::string name_;
  ConfigType type_;
  std::unordered_map<std::string, std::shared_ptr<ConfigDefTree>> children_;
  boost::variant<bool, int64_t, uint64_t, double, std::string> default_value_;
  ConfigComplexity complexity_;
  std::vector<std::string> choices_;
  boost::variant<int64_t, uint64_t, double> min_;
  boost::variant<int64_t, uint64_t, double> max_;
  boost::variant<int64_t, uint64_t, double> step_;
  uint32_t string_size_limit_;
  StoreProcess store_process_;
  std::string hint_;
  bool default_recommended_;
  std::string units_;

  void processDef(std::string name, const YAML::Node *definition_node);
  void intoString(std::stringstream &ss, std::string prefix = "");

public:
  ConfigDefTree(std::string name, std::string definition_file_path);
  ConfigDefTree(std::string name, const YAML::Node &definition_node);
  ~ConfigDefTree();

  ConfigType get_type() const;
  ConfigComplexity get_complexity() const;
  std::shared_ptr<ConfigDefTree> get_child(std::string name);
  const std::unordered_map<std::string, std::shared_ptr<ConfigDefTree>> get_children() const;
  boost::variant<bool, int64_t, uint64_t, double, std::string> get_default_value() const;
  inline StoreProcess get_store_process() const { return store_process_; }
  inline std::string get_hint() const { return hint_; }
  inline std::string get_units() const { return units_; }
  inline bool is_default_recommended() const { return default_recommended_; }
  template <typename T>
  T get_default_value() const;
  template <typename T>
  T get_min() const;
  template <typename T>
  T get_max() const;
  template <typename T>
  T get_step() const;
  const std::vector<std::string> &get_choices() const;
  uint32_t get_string_size_limit() const;

  std::string to_string();
};

} // namespace config
} // namespace carbon