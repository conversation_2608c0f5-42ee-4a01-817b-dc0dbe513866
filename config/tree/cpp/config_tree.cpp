#include <boost/algorithm/string.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <lib/common/cpp/base64.hpp>
#include <lib/common/cpp/time.h>
#include <spdlog/spdlog.h>
#include <sstream>

namespace carbon {
namespace config {
constexpr char unsynced_key[] = "__unsync_erased__";
constexpr uint64_t unsynced_max_age_ms = 24 * 60 * 60 * 1000 * 10; // 10 days in ms
constexpr std::string_view config_prefix_cloud = "/cloud/";

std::set<std::string> ConfigTree::protected_keys = {"ts", unsynced_key};
bool ConfigTree::is_protected(const std::string &key) { return protected_keys.count(key) > 0; }

template <>
std::string ConfigTree::get_value() const {
  assert(this->def_->get_type() == ConfigType::STRING);
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return boost::get<std::string>(this->value_);
}

template <>
int64_t ConfigTree::get_value() const {
  assert(this->def_->get_type() == ConfigType::INT);
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return boost::get<int64_t>(this->value_);
}
template <>
int32_t ConfigTree::get_value() const {
  return (int32_t)this->get_value<int64_t>();
}

template <>
uint64_t ConfigTree::get_value() const {
  assert(this->def_->get_type() == ConfigType::UINT);
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return boost::get<uint64_t>(this->value_);
}
template <>
uint32_t ConfigTree::get_value() const {
  return (uint32_t)this->get_value<uint64_t>();
}

template <>
bool ConfigTree::get_value() const {
  assert(this->def_->get_type() == ConfigType::BOOL);
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return boost::get<bool>(this->value_);
}

template <>
double ConfigTree::get_value() const {
  assert(this->def_->get_type() == ConfigType::FLOAT);
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return boost::get<double>(this->value_);
}
template <>
float ConfigTree::get_value() const {
  return (float)this->get_value<double>();
}

void ConfigTree::save_to_file(std::string path) {
  auto node = nlohmann::json();
  {
    std::shared_lock<std::shared_mutex> lock(*this->mutex_);
    this->fill_json_node(node);
  }
  std::string temp_path = fmt::format("{}.temp", path);
  std::ofstream fout(temp_path);
  fout << node.dump(2);
  fout.close();
  std::filesystem::rename(temp_path, path);
}

void ConfigTree::fill_json_node(nlohmann::json &node) {
  // Not Thread Safe, caller must ensure thread safety
  switch (this->def_->get_type()) {
  case ConfigType::BOOL:
    node["value"] = this->get_value<bool>();
    node["ts"] = this->timestamp_ms_;
    break;
  case ConfigType::INT:
    node["value"] = this->get_value<int64_t>();
    node["ts"] = this->timestamp_ms_;
    break;
  case ConfigType::UINT:
    node["value"] = this->get_value<uint64_t>();
    node["ts"] = this->timestamp_ms_;
    break;
  case ConfigType::FLOAT:
    node["value"] = this->get_value<double>();
    node["ts"] = this->timestamp_ms_;
    break;
  case ConfigType::STRING:
    if (this->def_->get_store_process() == StoreProcess::PROCESS_BASE64) {
      node["value"] = carbon::base64::encode(this->get_value<std::string>());
    } else {
      node["value"] = this->get_value<std::string>();
    }
    node["ts"] = this->timestamp_ms_;
    break;
  case ConfigType::LIST: {
    node["ts"] = this->timestamp_ms_;
    node[unsynced_key] = {};
    uint64_t now = maka_control_timestamp_ms();
    for (auto &it : unsynced_removed_children_) {
      if (it.second > now || now > (it.second + unsynced_max_age_ms)) {
        continue;
      } else {
        node[unsynced_key][it.first] = it.second;
      }
    }
    [[fallthrough]];
  }
  default: {
    for (auto child : this->children_) {
      node[child.second->get_name()] = {};
      child.second->fill_json_node(node[child.second->get_name()]);
    }
    break;
  }
  }
}

void ConfigTree::processValues(std::string name, std::shared_ptr<ConfigDefTree> def_tree,
                               const YAML::Node *value_node_ptr) {
  // Thread safety guaranteed by only being called in the constructor
  this->name_ = name;
  this->def_ = def_tree;
  this->default_val_ = value_node_ptr == nullptr;

  if (!this->default_val_ && !value_node_ptr->IsMap()) {
    spdlog::error("Value Node is not a Map: {}", name);
  }

  YAML::Node include_value_node;

  if (!this->default_val_ && (*value_node_ptr)["include"]) {
    include_value_node = YAML::LoadFile((*value_node_ptr)["include"].as<std::string>());
    include_value_node.SetTag(name);
    value_node_ptr = &include_value_node;
  }

  if (this->default_val_) {
    this->value_ = this->def_->get_default_value();
  } else if ((*value_node_ptr)["value"]) {
    switch (this->def_->get_type()) {
    case ConfigType::BOOL:
      this->value_ = (*value_node_ptr)["value"].as<bool>();
      break;
    case ConfigType::INT:
      this->value_ = (*value_node_ptr)["value"].as<int64_t>();
      break;
    case ConfigType::UINT:
      this->value_ = (*value_node_ptr)["value"].as<uint64_t>();
      break;
    case ConfigType::FLOAT:
      this->value_ = (*value_node_ptr)["value"].as<double>();
      break;
    case ConfigType::STRING:
      if (this->def_->get_store_process() == StoreProcess::PROCESS_BASE64) {
        try {
          this->value_ = carbon::base64::decode((*value_node_ptr)["value"].as<std::string>());
        } catch (const std::exception &ex) {
          spdlog::warn(ex.what());
          this->value_ = (*value_node_ptr)["value"].as<std::string>();
        }
      } else {
        this->value_ = (*value_node_ptr)["value"].as<std::string>();
      }
      break;
    default:
      break;
    }
  } else if (this->def_->get_type() != ConfigType::NODE && this->def_->get_type() != ConfigType::LIST) {
    spdlog::error("Value Node Leaf requires a value: {}", name);
  }

  // TODO at some point we should add validation that no node is ever called ts, otherwise it will conflict :)
  // For non-val, poll children
  if (this->default_val_) {
    this->timestamp_ms_ = 0;
  } else if ((*value_node_ptr)["ts"]) {

    this->timestamp_ms_ = get_sane_timestamp((*value_node_ptr)["ts"].as<uint64_t>());
  } else {
    this->timestamp_ms_ = 0;
  }

  if (this->default_val_ && this->def_->get_type() == ConfigType::LIST) {
    return; // No Default Children for lists
  }

  if (this->def_->get_type() == ConfigType::LIST) {
    if ((*value_node_ptr)[unsynced_key]) {
      auto &unsynced = (*value_node_ptr)[unsynced_key];
      for (YAML::const_iterator it = unsynced.begin(); it != unsynced.end(); ++it) {
        this->unsynced_removed_children_.emplace(it->first.as<std::string>(), it->second.as<uint64_t>());
      }
    }
    for (YAML::const_iterator it = value_node_ptr->begin(); it != value_node_ptr->end(); ++it) {
      if (is_protected(it->first.as<std::string>())) {
        continue;
      }
      auto resp = this->children_.emplace(it->first.as<std::string>(),
                                          std::make_shared<ConfigTree>(it->first.as<std::string>(),
                                                                       this->def_->get_child("item"), it->second,
                                                                       this->mutex_, this));
      if (resp.second && resp.first->second->get_timestamp_ms() > this->timestamp_ms_) {
        this->timestamp_ms_ =
            resp.first->second
                ->get_timestamp_ms(); // no need to check if sane as that was done when the child value was set
      }
    }
    return;
  } else if (this->def_->get_type() == ConfigType::NODE) {
    // Parse Children Along Schema
    for (auto &child_pair : this->def_->get_children()) {
      if (!this->default_val_ && (*value_node_ptr)[child_pair.first]) {
        this->children_[child_pair.first] = std::make_shared<ConfigTree>(
            child_pair.first, child_pair.second, (*value_node_ptr)[child_pair.first], this->mutex_, this);
      } else {
        this->children_[child_pair.first] =
            std::make_shared<ConfigTree>(child_pair.first, child_pair.second, this->mutex_, this);
      }
    }
  }
}

ConfigTree::ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, ConfigTree *parent)
    : mutex_(std::make_shared<std::shared_mutex>()), callback_id_(0), parent_(parent) {
  this->processValues(name, def_tree, nullptr);
}

ConfigTree::ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, std::string value_file_path,
                       ConfigTree *parent)
    : mutex_(std::make_shared<std::shared_mutex>()), callback_id_(0), parent_(parent) {
  if (std::filesystem::exists(value_file_path)) {
    YAML::Node value_node = YAML::LoadFile(value_file_path);
    this->processValues(name, def_tree, &value_node);
  } else {
    this->processValues(name, def_tree, nullptr);
  }
}

ConfigTree::ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree, const YAML::Node &value_node,
                       std::shared_ptr<std::shared_mutex> mutex, ConfigTree *parent)
    : mutex_(mutex), callback_id_(0), parent_(parent) {
  this->processValues(name, def_tree, &value_node);
}

ConfigTree::ConfigTree(std::string name, std::shared_ptr<ConfigDefTree> def_tree,
                       std::shared_ptr<std::shared_mutex> mutex, ConfigTree *parent)
    : mutex_(mutex), callback_id_(0), parent_(parent) {
  this->processValues(name, def_tree, nullptr);
}

uint64_t ConfigTree::get_sane_timestamp(uint64_t timestamp_ms) {
  uint64_t now = (uint64_t)maka_control_timestamp_ms();
  if (timestamp_ms > now + 60000) {
    spdlog::warn("Time travel is real protect john connor");
    return now;
  }
  return timestamp_ms;
}
template <>
bool ConfigTree::set_value(std::string val, uint64_t timestamp_ms, bool timestamp_priority) {
  assert(this->def_->get_type() == ConfigType::STRING);
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  if (timestamp_priority && this->timestamp_ms_ > timestamp_ms) {
    return false;
  }
  bool changed = boost::get<std::string>(value_) != val || timestamp_ms_ != timestamp_ms;
  this->value_ = val;
  this->timestamp_ms_ = timestamp_ms;
  lock.unlock(); // want to switch to reader lock not writter
  if (changed) {
    notify();
  }
  return true;
}

template <>
bool ConfigTree::set_value(int64_t val, uint64_t timestamp_ms, bool timestamp_priority) {
  assert(this->def_->get_type() == ConfigType::INT);
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  if (timestamp_priority && this->timestamp_ms_ > timestamp_ms) {
    return false;
  }
  bool changed = boost::get<int64_t>(value_) != val || timestamp_ms_ != timestamp_ms;
  this->value_ = val;
  this->timestamp_ms_ = timestamp_ms;
  lock.unlock(); // want to switch to reader lock not writter
  if (changed) {
    notify();
  }
  return true;
}

template <>
bool ConfigTree::set_value(uint64_t val, uint64_t timestamp_ms, bool timestamp_priority) {
  assert(this->def_->get_type() == ConfigType::UINT);
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  if (timestamp_priority && this->timestamp_ms_ > timestamp_ms) {
    return false;
  }
  bool changed = boost::get<uint64_t>(value_) != val || timestamp_ms_ != timestamp_ms;
  this->value_ = val;
  this->timestamp_ms_ = timestamp_ms;
  lock.unlock(); // want to switch to reader lock not writter
  if (changed) {
    notify();
  }
  return true;
}

template <>
bool ConfigTree::set_value(double val, uint64_t timestamp_ms, bool timestamp_priority) {
  assert(this->def_->get_type() == ConfigType::FLOAT);
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  if (timestamp_priority && this->timestamp_ms_ > timestamp_ms) {
    return false;
  }
  bool changed = boost::get<double>(value_) != val || timestamp_ms_ != timestamp_ms;
  this->value_ = val;
  this->timestamp_ms_ = timestamp_ms;
  lock.unlock(); // want to switch to reader lock not writter
  if (changed) {
    notify();
  }
  return true;
}

template <>
bool ConfigTree::set_value(bool val, uint64_t timestamp_ms, bool timestamp_priority) {
  assert(this->def_->get_type() == ConfigType::BOOL);
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  if (timestamp_priority && this->timestamp_ms_ > timestamp_ms) {
    return false;
  }
  bool changed = boost::get<bool>(value_) != val || timestamp_ms_ != timestamp_ms;
  this->value_ = val;
  this->timestamp_ms_ = timestamp_ms;
  lock.unlock(); // want to switch to reader lock not writter
  if (changed) {
    notify();
  }
  return true;
}

void ConfigTree::touch(std::string key, uint64_t timestamp_ms) {
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  std::list<std::string> result;
  boost::split(result, key, boost::is_any_of("/"));
  return this->touch(result, timestamp_ms);
}

void ConfigTree::touch(std::list<std::string> &key, uint64_t timestamp_ms) {
  // Not threadsafe, private and caller must guarantee thread safety
  timestamp_ms = get_sane_timestamp(timestamp_ms);
  this->timestamp_ms_ = timestamp_ms;

  if (key.size() == 0) {
    return;
  }
  std::string head = key.front();
  key.pop_front();

  if (!this->children_.count(head)) {
    return;
  }

  this->children_[head]->touch(key, timestamp_ms);
}

std::shared_ptr<ConfigTree> ConfigTree::get_node(std::list<std::string> &key) {
  if (key.size() == 0) {
    return nullptr;
  }
  std::string head = key.front();
  key.pop_front();

  {
    std::shared_lock<std::shared_mutex> lock(*this->mutex_);

    if (!this->children_.count(head)) {
      return nullptr;
    }

    if (key.size() == 0) {
      return this->children_[head];
    }

    return this->children_[head]->get_node(key);
  }
}

std::shared_ptr<ConfigTree> ConfigTree::get_node(std::string key) {
  std::list<std::string> result;
  boost::split(result, key, boost::is_any_of("/"));
  return this->get_node(result);
}

bool ConfigTree::has_node(std::string key) { return this->get_node(key) != nullptr; }

void ConfigTree::add_child(std::shared_ptr<ConfigTree> child) {
  {
    std::shared_lock<std::shared_mutex> lock(*this->mutex_);
    auto it = children_.find(child->get_name());
    if (it != children_.end()) {
      if (it->second->_is_equal(child)) {
        return;
      }
    }
  }
  {
    std::unique_lock<std::shared_mutex> lock(*this->mutex_);
    this->children_[child->get_name()] = child;
    child->parent_ = this;
    this->unsynced_removed_children_.erase(child->get_name()); // handle case where we remove and re-add before sync
  }
  notify();
}

// recursively update the timestamp for this node, unless it is type NODE, and
// update the timestamps of all children of all lists and nodes
void ConfigTree::touch_subtree() {
  switch (this->def_->get_type()) {
  case ConfigType::LIST:
    for (auto const &[name, child] : this->children_) {
      child->touch_subtree();
    }
    break;
  case ConfigType::NODE:
    for (auto const &[name, child] : this->children_) {
      child->touch_subtree();
    }
    // return without updating node timestamp
    return;
  default:
    break;
  }

  {
    std::unique_lock<std::shared_mutex> lock(*this->mutex_);
    this->timestamp_ms_ = maka_control_timestamp_ms();
  }
  notify();
}

void ConfigTree::remove_child(std::string name) {
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  auto it = this->children_.find(name);
  if (it == this->children_.end()) {
    return;
  }
  it->second->parent_ = nullptr;
  this->children_.erase(it);
  if (this->def_->get_type() == ConfigType::LIST) {
    this->unsynced_removed_children_.emplace(name, maka_control_timestamp_ms());
  }
  lock.unlock(); // want to switch to reader lock not writter
  notify();
}
bool ConfigTree::child_has_been_removed_after(const std::string &child_name, uint64_t timestamp_ms) {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  auto it = this->unsynced_removed_children_.find(child_name);
  if (it == this->unsynced_removed_children_.end()) {
    return false;
  }
  return it->second > timestamp_ms;
}
void ConfigTree::syncronized() {
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  uint64_t now = maka_control_timestamp_ms();
  for (auto it = unsynced_removed_children_.begin(); it != unsynced_removed_children_.end();) {
    if (it->second > now || now > (it->second + unsynced_max_age_ms)) {
      it = unsynced_removed_children_.erase(it);
    } else {
      ++it;
    }
  }
}

std::vector<std::shared_ptr<ConfigTree>> ConfigTree::get_children_nodes() {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  std::vector<std::shared_ptr<ConfigTree>> nodes;
  for (auto &pair : this->children_) {
    nodes.push_back(pair.second);
  }
  return nodes;
}

std::vector<std::string> ConfigTree::get_children_names() {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  std::vector<std::string> nodes;
  for (auto &pair : this->children_) {
    nodes.push_back(pair.first);
  }
  return nodes;
}

void ConfigTree::intoString(std::stringstream &ss, std::string prefix) {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  ss << prefix << this->name_ << ":" << std::endl;
  if (this->def_->get_type() != ConfigType::NODE && this->def_->get_type() != ConfigType::LIST) {
    ss << prefix << "  "
       << "value: " << this->value_ << std::endl
       << prefix << "  "
       << "ts: " << this->timestamp_ms_ << std::endl;
  }

  for (auto const &child : this->children_) {
    child.second->intoString(ss, prefix + "  ");
  }
}

std::string ConfigTree::get_name() const { return this->name_; }

std::shared_ptr<ConfigDefTree> ConfigTree::get_def() const { return this->def_; }

uint64_t ConfigTree::get_timestamp_ms() const {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  return this->timestamp_ms_;
}

std::string ConfigTree::to_string() {
  std::stringstream ss;
  this->intoString(ss, "");
  return ss.str();
}

std::shared_ptr<ConfigTree> ConfigTree::get_child(std::string name) {
  std::shared_lock<std::shared_mutex> lock(*this->mutex_);
  if (this->children_.count(name) == 0) {
    return nullptr;
  }
  return this->children_[name];
}

ConfigTree::~ConfigTree() {}

uint32_t ConfigTree::register_callback(ConfigTreeCallback callback) {
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  callbacks_.emplace(++callback_id_, callback);
  return callback_id_;
}
void ConfigTree::unregister_callback(uint32_t id) {
  std::unique_lock<std::shared_mutex> lock(*this->mutex_);
  callbacks_.erase(id);
}
void ConfigTree::notify() {
  std::map<uint32_t, ConfigTreeCallback> callbacks;
  {
    std::shared_lock<std::shared_mutex> lock(*this->mutex_);
    callbacks = callbacks_;
  }
  for (auto &cb : callbacks) {
    cb.second();
  }
  if (parent_) {
    parent_->notify();
  }
}

void ConfigTree::_get_path(std::vector<std::string> &path_parts) {
  path_parts.push_back(name_);
  if (parent_) {
    parent_->_get_path(path_parts);
  }
}

std::string ConfigTree::get_path() {
  std::vector<std::string> path_parts;
  _get_path(path_parts);
  std::stringstream ss;
  for (auto it = path_parts.rbegin(); it != path_parts.rend(); ++it) {
    ss << "/";
    ss << *it;
  }
  return ss.str();
}

bool ConfigTree::_is_equal(std::shared_ptr<ConfigTree> rhs) {
  if (name_ != rhs->name_ || def_->get_type() != rhs->def_->get_type()) {
    return false;
  }
  if (value_ != rhs->value_ || timestamp_ms_ != rhs->timestamp_ms_) {
    return false;
  }
  if (def_->get_type() == ConfigType::LIST || def_->get_type() == ConfigType::NODE) {
    if (children_.size() != rhs->children_.size()) {
      return false;
    }
    for (auto &pair : children_) {
      auto it = rhs->children_.find(pair.first);
      if (it == rhs->children_.end()) {
        return false;
      }
      if (!pair.second->_is_equal(it->second)) {
        return false;
      }
    }
  }
  return true;
}

std::string cut_root(std::string path) {
  if (path.rfind(config_prefix_cloud, 0) == 0) {
    return path.substr(config_prefix_cloud.length());
  }
  return path;
}

void ConfigTree::upgrade(std::shared_ptr<ConfigTree> from_template, std::shared_ptr<ConfigTree> to_template,
                         std::vector<std::string> *updated_keys) {
  if (!def_->is_default_recommended()) {
    return;
  }
  if (def_->get_type() == ConfigType::LIST) {
    for (auto &pair : children_) {
      auto from_it = from_template->children_.find(pair.first);
      if (from_it == from_template->children_.end()) {
        continue;
      }
      auto to_it = to_template->children_.find(pair.first);
      if (to_it == to_template->children_.end()) {
        continue;
      }
      pair.second->upgrade(from_it->second, to_it->second, updated_keys);
    }

    // checking if new template has list nodes that are new, adding them
    for (auto &pair : to_template->children_) {
      // check if node in from_template
      auto from_it = from_template->children_.find(pair.first);
      if (from_it != from_template->children_.end()) {
        continue; // list node found in from template, do nothing
      }
      // check if node was already added to current, just in case
      auto current_it = children_.find(pair.first);
      if (current_it != children_.end()) {
        continue;
      }
      // at this point list node is in new template, but not in from template or current list, add it
      auto def = to_template->get_def()->get_child("item");
      auto new_node = std::make_shared<ConfigTree>(pair.first, def);
      new_node->timestamp_ms_ = maka_control_timestamp_ms();
      add_child(new_node);
      spdlog::info("Added list item {}", new_node->get_path());
      updated_keys->push_back(cut_root(get_path()));
      auto new_from_template = std::make_shared<ConfigTree>(pair.first, def);
      new_node->upgrade(new_from_template, pair.second, updated_keys);
    }

    // checking if new template has nodes that were removed, removing them
    for (auto &pair : from_template->children_) {
      auto to_it = to_template->children_.find(pair.first);
      if (to_it != to_template->children_.end()) {
        continue; // list node found in to_template, do nothing
      }
      // check if node exists in current, if yes, remove
      auto current_it = children_.find(pair.first);
      if (current_it == children_.end()) {
        continue; // node already not in current config
      }
      spdlog::info("Remove list item {}", current_it->second->get_path());
      remove_child(pair.first);
      {
        std::unique_lock<std::shared_mutex> lock(*this->mutex_);
        this->timestamp_ms_ = maka_control_timestamp_ms();
      }
      updated_keys->push_back(cut_root(get_path()));
    }
    return;
  }

  if (def_->get_type() == ConfigType::NODE) {
    for (auto &pair : children_) {
      auto from_it = from_template->children_.find(pair.first);
      if (from_it == from_template->children_.end()) {
        spdlog::error("Couldn't find {}/{} in from_template", get_path(), pair.first);
        continue;
      }
      auto to_it = to_template->children_.find(pair.first);
      if (to_it == to_template->children_.end()) {
        spdlog::error("Couldn't find {}/{} in to_template", get_path(), pair.first);
        continue;
      }
      pair.second->upgrade(from_it->second, to_it->second, updated_keys);
    }
    return;
  }

  if (value_ != to_template->value_ && value_ == from_template->value_) {
    std::unique_lock<std::shared_mutex> lock(*this->mutex_);
    value_ = to_template->value_;
    timestamp_ms_ = maka_control_timestamp_ms();
    spdlog::info("Upgrading value for {}", get_path());
    updated_keys->push_back(cut_root(get_path()));
  }
}

// Because Golang has great compatibility
std::string get_string_value(std::shared_ptr<ConfigTree> tree) { return tree->get_value<std::string>(); }

int64_t get_int_value(std::shared_ptr<ConfigTree> tree) { return tree->get_value<int64_t>(); }

uint64_t get_uint_value(std::shared_ptr<ConfigTree> tree) { return tree->get_value<uint64_t>(); }

bool get_bool_value(std::shared_ptr<ConfigTree> tree) { return tree->get_value<bool>(); }

double get_double_value(std::shared_ptr<ConfigTree> tree) { return tree->get_value<double>(); }

uint32_t register_callback(std::shared_ptr<ConfigTree> tree, ConfigTreeCallback callback) {
  return tree->register_callback(callback);
}

void unregister_callback(std::shared_ptr<ConfigTree> tree, uint32_t id) { tree->unregister_callback(id); }

std::string get_name(std::shared_ptr<ConfigTree> tree) { return tree->get_name(); }

std::vector<std::string> get_children_names(std::shared_ptr<ConfigTree> tree) { return tree->get_children_names(); }

std::shared_ptr<ConfigTree> get_child(std::shared_ptr<ConfigTree> tree, std::string name) {
  return tree->get_child(name);
}

std::shared_ptr<ConfigTree> get_node(std::shared_ptr<ConfigTree> tree, std::string key) { return tree->get_node(key); }

bool has_node(std::shared_ptr<ConfigTree> tree, std::string key) { return tree->has_node(key); }

void delete_config_tree_shared_ptr(std::shared_ptr<ConfigTree> *tree) { delete tree; }

} // namespace config
} // namespace carbon
