#pragma once

#include <atomic>
#include <config/tree/cpp/config_tree.hpp>
#include <functional>
#include <memory>
#include <optional>

#include <spdlog/spdlog.h>

namespace carbon::config {
template <typename T>
class ConfigAtomicAccessor {
public:
  ConfigAtomicAccessor() = delete;
  ConfigAtomicAccessor(std::shared_ptr<ConfigTree> tree, std::optional<std::function<T(T)>> initializer = {})
      : tree_(tree), initializer_(initializer) {
    callback();
    callback_id_ = tree_->register_callback(std::bind(&ConfigAtomicAccessor::callback, this));
  }
  ConfigAtomicAccessor(std::shared_ptr<ConfigTree> tree, const T &default_val,
                       std::optional<std::function<T(T)>> initializer = {})
      : tree_(tree), initializer_(initializer) {
    if (tree_) {
      callback();
      callback_id_ = tree_->register_callback(std::bind(&ConfigAtomicAccessor::callback, this));
    } else {
      spdlog::warn("Config tree missing, using default value");
      value_ = default_val;
    }
  }
  ConfigAtomicAccessor(const ConfigAtomicAccessor &&rhs) : tree_(rhs.tree_), value_(tree_->get_value<T>()) {
    callback_id_ = tree_->register_callback(std::bind(&ConfigAtomicAccessor::callback, this));
  }
  ConfigAtomicAccessor operator=(const ConfigAtomicAccessor &rhs) = delete;

  ~ConfigAtomicAccessor() {
    if (tree_) {
      tree_->unregister_callback(callback_id_);
    }
  }

  T get_value() const { return value_; }

private:
  std::shared_ptr<ConfigTree> tree_;
  std::atomic<T> value_;
  uint32_t callback_id_;
  std::optional<std::function<T(T)>> initializer_;

  void callback() {
    T val = tree_->get_value<T>();
    if (initializer_) {
      val = initializer_.value()(val);
    }
    value_ = val;
  }
};
} // namespace carbon::config