add_compile_options(-fvisibility=default)
file(GLOB SOURCES CONFIGURE_DEPENDS *.cpp)
list(REMOVE_ITEM SOURCES ${CMAKE_CURRENT_SOURCE_DIR}/config_tree_python.cpp)

add_library(config_tree_lib SHARED ${SOURCES})
target_compile_definitions(config_tree_lib PUBLIC -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(config_tree_lib PUBLIC exceptions m yaml-cpp spdlog fmt pthread)

pybind11_add_module(config_tree_lib_python SHARED config_tree_python.cpp)
target_link_libraries(config_tree_lib_python PUBLIC config_tree_lib)
target_compile_definitions(config_tree_lib_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
set_target_properties(config_tree_lib_python PROPERTIES OUTPUT_NAME config_tree_python.so PREFIX "" SUFFIX "" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})

add_subdirectory(tests)