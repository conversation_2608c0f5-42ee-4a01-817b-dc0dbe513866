#include "config/tree/cpp/config_scoped_callback.hpp"

namespace carbon::config {
ConfigScopedCallback::ConfigScopedCallback(std::shared_ptr<ConfigTree> tree, ConfigTreeCallback cb, bool call_now)
    : tree_(tree), cb_id_(0) {
  if (tree_) {
    cb_id_ = tree_->register_callback(cb);
    if (call_now) {
      cb();
    }
  }
}
ConfigScopedCallback::ConfigScopedCallback(ConfigScopedCallback &&rhs) : tree_(rhs.tree_), cb_id_(rhs.cb_id_) {
  rhs.cb_id_ = 0; // remove callback from rhs
}
ConfigScopedCallback::~ConfigScopedCallback() {
  if (cb_id_ > 0) {
    tree_->unregister_callback(cb_id_);
  }
}
AtomicFlagConfigScopedCallback::AtomicFlagConfigScopedCallback(std::shared_ptr<ConfigTree> tree,
                                                               bool initial_sync_state)
    : ConfigScopedCallback(tree, [&]() { this->is_in_sync_.clear(std::memory_order_relaxed); }) {
  if (initial_sync_state) {
    reload_required(); // sets in_in_sync_ to true
  } else {
    is_in_sync_.clear(); // sets to false
  }
}
} // namespace carbon::config