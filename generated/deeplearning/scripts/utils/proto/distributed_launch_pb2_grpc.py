# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.deeplearning.scripts.utils.proto import distributed_launch_pb2 as deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2


class DistributedLaunchServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCommand = channel.unary_unary(
                '/deeplearning.script.utils.proto.DistributedLaunchService/GetCommand',
                request_serializer=deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandRequest.SerializeToString,
                response_deserializer=deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandResponse.FromString,
                )


class DistributedLaunchServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetCommand(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DistributedLaunchServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCommand': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCommand,
                    request_deserializer=deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandRequest.FromString,
                    response_serializer=deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'deeplearning.script.utils.proto.DistributedLaunchService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DistributedLaunchService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetCommand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/deeplearning.script.utils.proto.DistributedLaunchService/GetCommand',
            deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandRequest.SerializeToString,
            deeplearning_dot_scripts_dot_utils_dot_proto_dot_distributed__launch__pb2.GetCommandResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
