"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetCommandRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetCommandRequest = GetCommandRequest

class GetCommandResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    sequence_number: builtin___int = ...
    pickled_environment_vars: builtin___bytes = ...
    pickled_function: builtin___bytes = ...
    pickled_failure_function: builtin___bytes = ...
    pickled_arguments: builtin___bytes = ...

    def __init__(self,
        *,
        sequence_number : typing___Optional[builtin___int] = None,
        pickled_environment_vars : typing___Optional[builtin___bytes] = None,
        pickled_function : typing___Optional[builtin___bytes] = None,
        pickled_failure_function : typing___Optional[builtin___bytes] = None,
        pickled_arguments : typing___Optional[builtin___bytes] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pickled_arguments",b"pickled_arguments",u"pickled_environment_vars",b"pickled_environment_vars",u"pickled_failure_function",b"pickled_failure_function",u"pickled_function",b"pickled_function",u"sequence_number",b"sequence_number"]) -> None: ...
type___GetCommandResponse = GetCommandResponse
