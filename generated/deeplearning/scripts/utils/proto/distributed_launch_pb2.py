# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/scripts/utils/proto/distributed_launch.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/scripts/utils/proto/distributed_launch.proto',
  package='deeplearning.script.utils.proto',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n9deeplearning/scripts/utils/proto/distributed_launch.proto\x12\x1f\x64\x65\x65plearning.script.utils.proto\"\x13\n\x11GetCommandRequest\"\xa6\x01\n\x12GetCommandResponse\x12\x17\n\x0fsequence_number\x18\x01 \x01(\x03\x12 \n\x18pickled_environment_vars\x18\x02 \x01(\x0c\x12\x18\n\x10pickled_function\x18\x03 \x01(\x0c\x12 \n\x18pickled_failure_function\x18\x04 \x01(\x0c\x12\x19\n\x11pickled_arguments\x18\x05 \x01(\x0c\x32\x93\x01\n\x18\x44istributedLaunchService\x12w\n\nGetCommand\x12\x32.deeplearning.script.utils.proto.GetCommandRequest\x1a\x33.deeplearning.script.utils.proto.GetCommandResponse\"\x00\x62\x06proto3'
)




_GETCOMMANDREQUEST = _descriptor.Descriptor(
  name='GetCommandRequest',
  full_name='deeplearning.script.utils.proto.GetCommandRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=94,
  serialized_end=113,
)


_GETCOMMANDRESPONSE = _descriptor.Descriptor(
  name='GetCommandResponse',
  full_name='deeplearning.script.utils.proto.GetCommandResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sequence_number', full_name='deeplearning.script.utils.proto.GetCommandResponse.sequence_number', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pickled_environment_vars', full_name='deeplearning.script.utils.proto.GetCommandResponse.pickled_environment_vars', index=1,
      number=2, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pickled_function', full_name='deeplearning.script.utils.proto.GetCommandResponse.pickled_function', index=2,
      number=3, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pickled_failure_function', full_name='deeplearning.script.utils.proto.GetCommandResponse.pickled_failure_function', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pickled_arguments', full_name='deeplearning.script.utils.proto.GetCommandResponse.pickled_arguments', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=116,
  serialized_end=282,
)

DESCRIPTOR.message_types_by_name['GetCommandRequest'] = _GETCOMMANDREQUEST
DESCRIPTOR.message_types_by_name['GetCommandResponse'] = _GETCOMMANDRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetCommandRequest = _reflection.GeneratedProtocolMessageType('GetCommandRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCOMMANDREQUEST,
  '__module__' : 'deeplearning.scripts.utils.proto.distributed_launch_pb2'
  # @@protoc_insertion_point(class_scope:deeplearning.script.utils.proto.GetCommandRequest)
  })
_sym_db.RegisterMessage(GetCommandRequest)

GetCommandResponse = _reflection.GeneratedProtocolMessageType('GetCommandResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCOMMANDRESPONSE,
  '__module__' : 'deeplearning.scripts.utils.proto.distributed_launch_pb2'
  # @@protoc_insertion_point(class_scope:deeplearning.script.utils.proto.GetCommandResponse)
  })
_sym_db.RegisterMessage(GetCommandResponse)



_DISTRIBUTEDLAUNCHSERVICE = _descriptor.ServiceDescriptor(
  name='DistributedLaunchService',
  full_name='deeplearning.script.utils.proto.DistributedLaunchService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=285,
  serialized_end=432,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetCommand',
    full_name='deeplearning.script.utils.proto.DistributedLaunchService.GetCommand',
    index=0,
    containing_service=None,
    input_type=_GETCOMMANDREQUEST,
    output_type=_GETCOMMANDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DISTRIBUTEDLAUNCHSERVICE)

DESCRIPTOR.services_by_name['DistributedLaunchService'] = _DISTRIBUTEDLAUNCHSERVICE

# @@protoc_insertion_point(module_scope)
