# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.proto.software_manager import software_manager_pb2 as proto_dot_software__manager_dot_software__manager__pb2


class SoftwareManagerServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetSoftwareVersionMetadata = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.FromString,
                )
        self.GetVersionsSummary = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryReply.FromString,
                )
        self.GetIdentity = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/GetIdentity',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.GetIdentityRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.IdentityInfo.FromString,
                )
        self.ClearPackagesCache = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.FromString,
                )
        self.PrepareUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateResponse.FromString,
                )
        self.AbortUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateResponse.FromString,
                )
        self.TriggerUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateRequest.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateReply.FromString,
                )
        self.Reboot = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/Reboot',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                )
        self.PushRobotDefinitionUpdate = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/PushRobotDefinitionUpdate',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                )
        self.RestartDependentServices = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/RestartDependentServices',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                )
        self.Ping = channel.unary_unary(
                '/carbon.software_manager.SoftwareManagerService/Ping',
                request_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
                response_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                )


class SoftwareManagerServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetSoftwareVersionMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVersionsSummary(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIdentity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearPackagesCache(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PrepareUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AbortUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TriggerUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Reboot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PushRobotDefinitionUpdate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestartDependentServices(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_SoftwareManagerServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetSoftwareVersionMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSoftwareVersionMetadata,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.SerializeToString,
            ),
            'GetVersionsSummary': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVersionsSummary,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryReply.SerializeToString,
            ),
            'GetIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIdentity,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.GetIdentityRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.IdentityInfo.SerializeToString,
            ),
            'ClearPackagesCache': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearPackagesCache,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.SerializeToString,
            ),
            'PrepareUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.PrepareUpdate,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateResponse.SerializeToString,
            ),
            'AbortUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.AbortUpdate,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateResponse.SerializeToString,
            ),
            'TriggerUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.TriggerUpdate,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateRequest.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateReply.SerializeToString,
            ),
            'Reboot': grpc.unary_unary_rpc_method_handler(
                    servicer.Reboot,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            ),
            'PushRobotDefinitionUpdate': grpc.unary_unary_rpc_method_handler(
                    servicer.PushRobotDefinitionUpdate,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            ),
            'RestartDependentServices': grpc.unary_unary_rpc_method_handler(
                    servicer.RestartDependentServices,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            ),
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
                    response_serializer=proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.software_manager.SoftwareManagerService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class SoftwareManagerService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetSoftwareVersionMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/GetSoftwareVersionMetadata',
            proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadataRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.SoftwareVersionMetadata.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetVersionsSummary(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/GetVersionsSummary',
            proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.VersionSummaryReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/GetIdentity',
            proto_dot_software__manager_dot_software__manager__pb2.GetIdentityRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.IdentityInfo.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearPackagesCache(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/ClearPackagesCache',
            proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.ClearPackagesCacheResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PrepareUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/PrepareUpdate',
            proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.PrepareUpdateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AbortUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/AbortUpdate',
            proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.AbortUpdateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TriggerUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/TriggerUpdate',
            proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateRequest.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.TriggerUpdateReply.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Reboot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/Reboot',
            proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PushRobotDefinitionUpdate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/PushRobotDefinitionUpdate',
            proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RestartDependentServices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/RestartDependentServices',
            proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.software_manager.SoftwareManagerService/Ping',
            proto_dot_software__manager_dot_software__manager__pb2.empty.SerializeToString,
            proto_dot_software__manager_dot_software__manager__pb2.empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
