# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: proto/software_manager/software_manager.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='proto/software_manager/software_manager.proto',
  package='carbon.software_manager',
  syntax='proto3',
  serialized_options=b'Z\026proto/software_manager',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n-proto/software_manager/software_manager.proto\x12\x17\x63\x61rbon.software_manager\"l\n\x17SoftwareVersionMetadata\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x12\n\ncontainers\x18\x02 \x03(\t\x12\x0e\n\x06system\x18\x03 \x01(\t\x12\r\n\x05ready\x18\x04 \x01(\x08\x12\x11\n\tavailable\x18\x05 \x01(\x08\"-\n\x1eSoftwareVersionMetadataRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\"\x17\n\x15VersionSummaryRequest\"\xf0\x01\n\x13VersionSummaryReply\x12\x41\n\x07\x63urrent\x18\x01 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\x12@\n\x06target\x18\x02 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\x12\x42\n\x08previous\x18\x03 \x01(\x0b\x32\x30.carbon.software_manager.SoftwareVersionMetadata\x12\x10\n\x08updating\x18\x04 \x01(\x08\"\x14\n\x12GetIdentityRequest\">\n\x0c\x43omputerRole\x12\x0c\n\x04role\x18\x01 \x01(\t\x12\x0b\n\x03row\x18\x02 \x01(\t\x12\x13\n\x0b\x65xtra_roles\x18\x03 \x03(\t\"\x81\x02\n\x0cIdentityInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\ngeneration\x18\x02 \x01(\t\x12\x33\n\x04role\x18\x03 \x01(\x0b\x32%.carbon.software_manager.ComputerRole\x12\x16\n\x0e\x61uth_client_id\x18\x04 \x01(\t\x12\x1a\n\x12\x61uth_client_secret\x18\x05 \x01(\t\x12\x13\n\x0b\x61uth_domain\x18\x06 \x01(\t\x12\x1d\n\x15\x63\x61rbon_robot_username\x18\x07 \x01(\t\x12\x1d\n\x15\x63\x61rbon_robot_password\x18\x08 \x01(\t\x12\x13\n\x0b\x65nvironment\x18\t \x01(\t\"\x1b\n\x19\x43learPackagesCacheRequest\"\x1c\n\x1a\x43learPackagesCacheResponse\"3\n\x14PrepareUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06req_id\x18\x02 \x01(\t\"\x17\n\x15PrepareUpdateResponse\"1\n\x12\x41\x62ortUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06req_id\x18\x02 \x01(\t\"\x15\n\x13\x41\x62ortUpdateResponse\"3\n\x14TriggerUpdateRequest\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06req_id\x18\x02 \x01(\t\"\x14\n\x12TriggerUpdateReply\",\n\x17SystemVersionStateReply\x12\x11\n\tconfirmed\x18\x01 \x01(\x08\"\x07\n\x05\x65mpty2\x8a\t\n\x16SoftwareManagerService\x12\x87\x01\n\x1aGetSoftwareVersionMetadata\x12\x37.carbon.software_manager.SoftwareVersionMetadataRequest\x1a\x30.carbon.software_manager.SoftwareVersionMetadata\x12r\n\x12GetVersionsSummary\x12..carbon.software_manager.VersionSummaryRequest\x1a,.carbon.software_manager.VersionSummaryReply\x12\x61\n\x0bGetIdentity\x12+.carbon.software_manager.GetIdentityRequest\x1a%.carbon.software_manager.IdentityInfo\x12}\n\x12\x43learPackagesCache\x12\x32.carbon.software_manager.ClearPackagesCacheRequest\x1a\x33.carbon.software_manager.ClearPackagesCacheResponse\x12n\n\rPrepareUpdate\x12-.carbon.software_manager.PrepareUpdateRequest\x1a..carbon.software_manager.PrepareUpdateResponse\x12h\n\x0b\x41\x62ortUpdate\x12+.carbon.software_manager.AbortUpdateRequest\x1a,.carbon.software_manager.AbortUpdateResponse\x12k\n\rTriggerUpdate\x12-.carbon.software_manager.TriggerUpdateRequest\x1a+.carbon.software_manager.TriggerUpdateReply\x12H\n\x06Reboot\x12\x1e.carbon.software_manager.empty\x1a\x1e.carbon.software_manager.empty\x12[\n\x19PushRobotDefinitionUpdate\x12\x1e.carbon.software_manager.empty\x1a\x1e.carbon.software_manager.empty\x12Z\n\x18RestartDependentServices\x12\x1e.carbon.software_manager.empty\x1a\x1e.carbon.software_manager.empty\x12\x46\n\x04Ping\x12\x1e.carbon.software_manager.empty\x1a\x1e.carbon.software_manager.emptyB\x18Z\x16proto/software_managerb\x06proto3'
)




_SOFTWAREVERSIONMETADATA = _descriptor.Descriptor(
  name='SoftwareVersionMetadata',
  full_name='carbon.software_manager.SoftwareVersionMetadata',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.software_manager.SoftwareVersionMetadata.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='containers', full_name='carbon.software_manager.SoftwareVersionMetadata.containers', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='system', full_name='carbon.software_manager.SoftwareVersionMetadata.system', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='carbon.software_manager.SoftwareVersionMetadata.ready', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='carbon.software_manager.SoftwareVersionMetadata.available', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=74,
  serialized_end=182,
)


_SOFTWAREVERSIONMETADATAREQUEST = _descriptor.Descriptor(
  name='SoftwareVersionMetadataRequest',
  full_name='carbon.software_manager.SoftwareVersionMetadataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.software_manager.SoftwareVersionMetadataRequest.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=184,
  serialized_end=229,
)


_VERSIONSUMMARYREQUEST = _descriptor.Descriptor(
  name='VersionSummaryRequest',
  full_name='carbon.software_manager.VersionSummaryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=231,
  serialized_end=254,
)


_VERSIONSUMMARYREPLY = _descriptor.Descriptor(
  name='VersionSummaryReply',
  full_name='carbon.software_manager.VersionSummaryReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.software_manager.VersionSummaryReply.current', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target', full_name='carbon.software_manager.VersionSummaryReply.target', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='previous', full_name='carbon.software_manager.VersionSummaryReply.previous', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updating', full_name='carbon.software_manager.VersionSummaryReply.updating', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=257,
  serialized_end=497,
)


_GETIDENTITYREQUEST = _descriptor.Descriptor(
  name='GetIdentityRequest',
  full_name='carbon.software_manager.GetIdentityRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=499,
  serialized_end=519,
)


_COMPUTERROLE = _descriptor.Descriptor(
  name='ComputerRole',
  full_name='carbon.software_manager.ComputerRole',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='role', full_name='carbon.software_manager.ComputerRole.role', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row', full_name='carbon.software_manager.ComputerRole.row', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_roles', full_name='carbon.software_manager.ComputerRole.extra_roles', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=521,
  serialized_end=583,
)


_IDENTITYINFO = _descriptor.Descriptor(
  name='IdentityInfo',
  full_name='carbon.software_manager.IdentityInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.software_manager.IdentityInfo.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='generation', full_name='carbon.software_manager.IdentityInfo.generation', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='role', full_name='carbon.software_manager.IdentityInfo.role', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auth_client_id', full_name='carbon.software_manager.IdentityInfo.auth_client_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auth_client_secret', full_name='carbon.software_manager.IdentityInfo.auth_client_secret', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auth_domain', full_name='carbon.software_manager.IdentityInfo.auth_domain', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carbon_robot_username', full_name='carbon.software_manager.IdentityInfo.carbon_robot_username', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carbon_robot_password', full_name='carbon.software_manager.IdentityInfo.carbon_robot_password', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='environment', full_name='carbon.software_manager.IdentityInfo.environment', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=586,
  serialized_end=843,
)


_CLEARPACKAGESCACHEREQUEST = _descriptor.Descriptor(
  name='ClearPackagesCacheRequest',
  full_name='carbon.software_manager.ClearPackagesCacheRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=845,
  serialized_end=872,
)


_CLEARPACKAGESCACHERESPONSE = _descriptor.Descriptor(
  name='ClearPackagesCacheResponse',
  full_name='carbon.software_manager.ClearPackagesCacheResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=874,
  serialized_end=902,
)


_PREPAREUPDATEREQUEST = _descriptor.Descriptor(
  name='PrepareUpdateRequest',
  full_name='carbon.software_manager.PrepareUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.software_manager.PrepareUpdateRequest.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='req_id', full_name='carbon.software_manager.PrepareUpdateRequest.req_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=904,
  serialized_end=955,
)


_PREPAREUPDATERESPONSE = _descriptor.Descriptor(
  name='PrepareUpdateResponse',
  full_name='carbon.software_manager.PrepareUpdateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=957,
  serialized_end=980,
)


_ABORTUPDATEREQUEST = _descriptor.Descriptor(
  name='AbortUpdateRequest',
  full_name='carbon.software_manager.AbortUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.software_manager.AbortUpdateRequest.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='req_id', full_name='carbon.software_manager.AbortUpdateRequest.req_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=982,
  serialized_end=1031,
)


_ABORTUPDATERESPONSE = _descriptor.Descriptor(
  name='AbortUpdateResponse',
  full_name='carbon.software_manager.AbortUpdateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1033,
  serialized_end=1054,
)


_TRIGGERUPDATEREQUEST = _descriptor.Descriptor(
  name='TriggerUpdateRequest',
  full_name='carbon.software_manager.TriggerUpdateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.software_manager.TriggerUpdateRequest.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='req_id', full_name='carbon.software_manager.TriggerUpdateRequest.req_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1056,
  serialized_end=1107,
)


_TRIGGERUPDATEREPLY = _descriptor.Descriptor(
  name='TriggerUpdateReply',
  full_name='carbon.software_manager.TriggerUpdateReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1109,
  serialized_end=1129,
)


_SYSTEMVERSIONSTATEREPLY = _descriptor.Descriptor(
  name='SystemVersionStateReply',
  full_name='carbon.software_manager.SystemVersionStateReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='confirmed', full_name='carbon.software_manager.SystemVersionStateReply.confirmed', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1131,
  serialized_end=1175,
)


_EMPTY = _descriptor.Descriptor(
  name='empty',
  full_name='carbon.software_manager.empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1177,
  serialized_end=1184,
)

_VERSIONSUMMARYREPLY.fields_by_name['current'].message_type = _SOFTWAREVERSIONMETADATA
_VERSIONSUMMARYREPLY.fields_by_name['target'].message_type = _SOFTWAREVERSIONMETADATA
_VERSIONSUMMARYREPLY.fields_by_name['previous'].message_type = _SOFTWAREVERSIONMETADATA
_IDENTITYINFO.fields_by_name['role'].message_type = _COMPUTERROLE
DESCRIPTOR.message_types_by_name['SoftwareVersionMetadata'] = _SOFTWAREVERSIONMETADATA
DESCRIPTOR.message_types_by_name['SoftwareVersionMetadataRequest'] = _SOFTWAREVERSIONMETADATAREQUEST
DESCRIPTOR.message_types_by_name['VersionSummaryRequest'] = _VERSIONSUMMARYREQUEST
DESCRIPTOR.message_types_by_name['VersionSummaryReply'] = _VERSIONSUMMARYREPLY
DESCRIPTOR.message_types_by_name['GetIdentityRequest'] = _GETIDENTITYREQUEST
DESCRIPTOR.message_types_by_name['ComputerRole'] = _COMPUTERROLE
DESCRIPTOR.message_types_by_name['IdentityInfo'] = _IDENTITYINFO
DESCRIPTOR.message_types_by_name['ClearPackagesCacheRequest'] = _CLEARPACKAGESCACHEREQUEST
DESCRIPTOR.message_types_by_name['ClearPackagesCacheResponse'] = _CLEARPACKAGESCACHERESPONSE
DESCRIPTOR.message_types_by_name['PrepareUpdateRequest'] = _PREPAREUPDATEREQUEST
DESCRIPTOR.message_types_by_name['PrepareUpdateResponse'] = _PREPAREUPDATERESPONSE
DESCRIPTOR.message_types_by_name['AbortUpdateRequest'] = _ABORTUPDATEREQUEST
DESCRIPTOR.message_types_by_name['AbortUpdateResponse'] = _ABORTUPDATERESPONSE
DESCRIPTOR.message_types_by_name['TriggerUpdateRequest'] = _TRIGGERUPDATEREQUEST
DESCRIPTOR.message_types_by_name['TriggerUpdateReply'] = _TRIGGERUPDATEREPLY
DESCRIPTOR.message_types_by_name['SystemVersionStateReply'] = _SYSTEMVERSIONSTATEREPLY
DESCRIPTOR.message_types_by_name['empty'] = _EMPTY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SoftwareVersionMetadata = _reflection.GeneratedProtocolMessageType('SoftwareVersionMetadata', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSIONMETADATA,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.SoftwareVersionMetadata)
  })
_sym_db.RegisterMessage(SoftwareVersionMetadata)

SoftwareVersionMetadataRequest = _reflection.GeneratedProtocolMessageType('SoftwareVersionMetadataRequest', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSIONMETADATAREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.SoftwareVersionMetadataRequest)
  })
_sym_db.RegisterMessage(SoftwareVersionMetadataRequest)

VersionSummaryRequest = _reflection.GeneratedProtocolMessageType('VersionSummaryRequest', (_message.Message,), {
  'DESCRIPTOR' : _VERSIONSUMMARYREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.VersionSummaryRequest)
  })
_sym_db.RegisterMessage(VersionSummaryRequest)

VersionSummaryReply = _reflection.GeneratedProtocolMessageType('VersionSummaryReply', (_message.Message,), {
  'DESCRIPTOR' : _VERSIONSUMMARYREPLY,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.VersionSummaryReply)
  })
_sym_db.RegisterMessage(VersionSummaryReply)

GetIdentityRequest = _reflection.GeneratedProtocolMessageType('GetIdentityRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETIDENTITYREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.GetIdentityRequest)
  })
_sym_db.RegisterMessage(GetIdentityRequest)

ComputerRole = _reflection.GeneratedProtocolMessageType('ComputerRole', (_message.Message,), {
  'DESCRIPTOR' : _COMPUTERROLE,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.ComputerRole)
  })
_sym_db.RegisterMessage(ComputerRole)

IdentityInfo = _reflection.GeneratedProtocolMessageType('IdentityInfo', (_message.Message,), {
  'DESCRIPTOR' : _IDENTITYINFO,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.IdentityInfo)
  })
_sym_db.RegisterMessage(IdentityInfo)

ClearPackagesCacheRequest = _reflection.GeneratedProtocolMessageType('ClearPackagesCacheRequest', (_message.Message,), {
  'DESCRIPTOR' : _CLEARPACKAGESCACHEREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.ClearPackagesCacheRequest)
  })
_sym_db.RegisterMessage(ClearPackagesCacheRequest)

ClearPackagesCacheResponse = _reflection.GeneratedProtocolMessageType('ClearPackagesCacheResponse', (_message.Message,), {
  'DESCRIPTOR' : _CLEARPACKAGESCACHERESPONSE,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.ClearPackagesCacheResponse)
  })
_sym_db.RegisterMessage(ClearPackagesCacheResponse)

PrepareUpdateRequest = _reflection.GeneratedProtocolMessageType('PrepareUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _PREPAREUPDATEREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.PrepareUpdateRequest)
  })
_sym_db.RegisterMessage(PrepareUpdateRequest)

PrepareUpdateResponse = _reflection.GeneratedProtocolMessageType('PrepareUpdateResponse', (_message.Message,), {
  'DESCRIPTOR' : _PREPAREUPDATERESPONSE,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.PrepareUpdateResponse)
  })
_sym_db.RegisterMessage(PrepareUpdateResponse)

AbortUpdateRequest = _reflection.GeneratedProtocolMessageType('AbortUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _ABORTUPDATEREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.AbortUpdateRequest)
  })
_sym_db.RegisterMessage(AbortUpdateRequest)

AbortUpdateResponse = _reflection.GeneratedProtocolMessageType('AbortUpdateResponse', (_message.Message,), {
  'DESCRIPTOR' : _ABORTUPDATERESPONSE,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.AbortUpdateResponse)
  })
_sym_db.RegisterMessage(AbortUpdateResponse)

TriggerUpdateRequest = _reflection.GeneratedProtocolMessageType('TriggerUpdateRequest', (_message.Message,), {
  'DESCRIPTOR' : _TRIGGERUPDATEREQUEST,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.TriggerUpdateRequest)
  })
_sym_db.RegisterMessage(TriggerUpdateRequest)

TriggerUpdateReply = _reflection.GeneratedProtocolMessageType('TriggerUpdateReply', (_message.Message,), {
  'DESCRIPTOR' : _TRIGGERUPDATEREPLY,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.TriggerUpdateReply)
  })
_sym_db.RegisterMessage(TriggerUpdateReply)

SystemVersionStateReply = _reflection.GeneratedProtocolMessageType('SystemVersionStateReply', (_message.Message,), {
  'DESCRIPTOR' : _SYSTEMVERSIONSTATEREPLY,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.SystemVersionStateReply)
  })
_sym_db.RegisterMessage(SystemVersionStateReply)

empty = _reflection.GeneratedProtocolMessageType('empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'proto.software_manager.software_manager_pb2'
  # @@protoc_insertion_point(class_scope:carbon.software_manager.empty)
  })
_sym_db.RegisterMessage(empty)


DESCRIPTOR._options = None

_SOFTWAREMANAGERSERVICE = _descriptor.ServiceDescriptor(
  name='SoftwareManagerService',
  full_name='carbon.software_manager.SoftwareManagerService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1187,
  serialized_end=2349,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetSoftwareVersionMetadata',
    full_name='carbon.software_manager.SoftwareManagerService.GetSoftwareVersionMetadata',
    index=0,
    containing_service=None,
    input_type=_SOFTWAREVERSIONMETADATAREQUEST,
    output_type=_SOFTWAREVERSIONMETADATA,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetVersionsSummary',
    full_name='carbon.software_manager.SoftwareManagerService.GetVersionsSummary',
    index=1,
    containing_service=None,
    input_type=_VERSIONSUMMARYREQUEST,
    output_type=_VERSIONSUMMARYREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetIdentity',
    full_name='carbon.software_manager.SoftwareManagerService.GetIdentity',
    index=2,
    containing_service=None,
    input_type=_GETIDENTITYREQUEST,
    output_type=_IDENTITYINFO,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ClearPackagesCache',
    full_name='carbon.software_manager.SoftwareManagerService.ClearPackagesCache',
    index=3,
    containing_service=None,
    input_type=_CLEARPACKAGESCACHEREQUEST,
    output_type=_CLEARPACKAGESCACHERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PrepareUpdate',
    full_name='carbon.software_manager.SoftwareManagerService.PrepareUpdate',
    index=4,
    containing_service=None,
    input_type=_PREPAREUPDATEREQUEST,
    output_type=_PREPAREUPDATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AbortUpdate',
    full_name='carbon.software_manager.SoftwareManagerService.AbortUpdate',
    index=5,
    containing_service=None,
    input_type=_ABORTUPDATEREQUEST,
    output_type=_ABORTUPDATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TriggerUpdate',
    full_name='carbon.software_manager.SoftwareManagerService.TriggerUpdate',
    index=6,
    containing_service=None,
    input_type=_TRIGGERUPDATEREQUEST,
    output_type=_TRIGGERUPDATEREPLY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Reboot',
    full_name='carbon.software_manager.SoftwareManagerService.Reboot',
    index=7,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PushRobotDefinitionUpdate',
    full_name='carbon.software_manager.SoftwareManagerService.PushRobotDefinitionUpdate',
    index=8,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RestartDependentServices',
    full_name='carbon.software_manager.SoftwareManagerService.RestartDependentServices',
    index=9,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='carbon.software_manager.SoftwareManagerService.Ping',
    index=10,
    containing_service=None,
    input_type=_EMPTY,
    output_type=_EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_SOFTWAREMANAGERSERVICE)

DESCRIPTOR.services_by_name['SoftwareManagerService'] = _SOFTWAREMANAGERSERVICE

# @@protoc_insertion_point(module_scope)
