"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class SoftwareVersionMetadata(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...
    containers: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    system: typing___Text = ...
    ready: builtin___bool = ...
    available: builtin___bool = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        containers : typing___Optional[typing___Iterable[typing___Text]] = None,
        system : typing___Optional[typing___Text] = None,
        ready : typing___Optional[builtin___bool] = None,
        available : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"available",b"available",u"containers",b"containers",u"ready",b"ready",u"system",b"system",u"tag",b"tag"]) -> None: ...
type___SoftwareVersionMetadata = SoftwareVersionMetadata

class SoftwareVersionMetadataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"tag",b"tag"]) -> None: ...
type___SoftwareVersionMetadataRequest = SoftwareVersionMetadataRequest

class VersionSummaryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___VersionSummaryRequest = VersionSummaryRequest

class VersionSummaryReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    updating: builtin___bool = ...

    @property
    def current(self) -> type___SoftwareVersionMetadata: ...

    @property
    def target(self) -> type___SoftwareVersionMetadata: ...

    @property
    def previous(self) -> type___SoftwareVersionMetadata: ...

    def __init__(self,
        *,
        current : typing___Optional[type___SoftwareVersionMetadata] = None,
        target : typing___Optional[type___SoftwareVersionMetadata] = None,
        previous : typing___Optional[type___SoftwareVersionMetadata] = None,
        updating : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"target",b"target"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"target",b"target",u"updating",b"updating"]) -> None: ...
type___VersionSummaryReply = VersionSummaryReply

class GetIdentityRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___GetIdentityRequest = GetIdentityRequest

class ComputerRole(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    role: typing___Text = ...
    row: typing___Text = ...
    extra_roles: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        role : typing___Optional[typing___Text] = None,
        row : typing___Optional[typing___Text] = None,
        extra_roles : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"extra_roles",b"extra_roles",u"role",b"role",u"row",b"row"]) -> None: ...
type___ComputerRole = ComputerRole

class IdentityInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    generation: typing___Text = ...
    auth_client_id: typing___Text = ...
    auth_client_secret: typing___Text = ...
    auth_domain: typing___Text = ...
    carbon_robot_username: typing___Text = ...
    carbon_robot_password: typing___Text = ...
    environment: typing___Text = ...

    @property
    def role(self) -> type___ComputerRole: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        generation : typing___Optional[typing___Text] = None,
        role : typing___Optional[type___ComputerRole] = None,
        auth_client_id : typing___Optional[typing___Text] = None,
        auth_client_secret : typing___Optional[typing___Text] = None,
        auth_domain : typing___Optional[typing___Text] = None,
        carbon_robot_username : typing___Optional[typing___Text] = None,
        carbon_robot_password : typing___Optional[typing___Text] = None,
        environment : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"role",b"role"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"auth_client_id",b"auth_client_id",u"auth_client_secret",b"auth_client_secret",u"auth_domain",b"auth_domain",u"carbon_robot_password",b"carbon_robot_password",u"carbon_robot_username",b"carbon_robot_username",u"environment",b"environment",u"generation",b"generation",u"name",b"name",u"role",b"role"]) -> None: ...
type___IdentityInfo = IdentityInfo

class ClearPackagesCacheRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ClearPackagesCacheRequest = ClearPackagesCacheRequest

class ClearPackagesCacheResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ClearPackagesCacheResponse = ClearPackagesCacheResponse

class PrepareUpdateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...
    req_id: typing___Text = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        req_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"req_id",b"req_id",u"tag",b"tag"]) -> None: ...
type___PrepareUpdateRequest = PrepareUpdateRequest

class PrepareUpdateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___PrepareUpdateResponse = PrepareUpdateResponse

class AbortUpdateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...
    req_id: typing___Text = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        req_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"req_id",b"req_id",u"tag",b"tag"]) -> None: ...
type___AbortUpdateRequest = AbortUpdateRequest

class AbortUpdateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___AbortUpdateResponse = AbortUpdateResponse

class TriggerUpdateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...
    req_id: typing___Text = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        req_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"req_id",b"req_id",u"tag",b"tag"]) -> None: ...
type___TriggerUpdateRequest = TriggerUpdateRequest

class TriggerUpdateReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___TriggerUpdateReply = TriggerUpdateReply

class SystemVersionStateReply(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    confirmed: builtin___bool = ...

    def __init__(self,
        *,
        confirmed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"confirmed",b"confirmed"]) -> None: ...
type___SystemVersionStateReply = SystemVersionStateReply

class empty(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___empty = empty
