// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/cruise_control.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcruise_5fcontrol_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcruise_5fcontrol_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fcruise_5fcontrol_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fcruise_5fcontrol_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto;
namespace carbon {
namespace frontend {
namespace cruise_control {
class CruiseState;
struct CruiseStateDefaultTypeInternal;
extern CruiseStateDefaultTypeInternal _CruiseState_default_instance_;
class GetNextCruiseStateResponse;
struct GetNextCruiseStateResponseDefaultTypeInternal;
extern GetNextCruiseStateResponseDefaultTypeInternal _GetNextCruiseStateResponse_default_instance_;
}  // namespace cruise_control
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::cruise_control::CruiseState* Arena::CreateMaybeMessage<::carbon::frontend::cruise_control::CruiseState>(Arena*);
template<> ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::cruise_control::GetNextCruiseStateResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace cruise_control {

enum CruiseBlocker : int {
  NONE = 0,
  ACTIVE_ALARM = 1,
  NOT_WEEDING = 2,
  TRACTOR_NOT_SAFE = 3,
  CruiseBlocker_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CruiseBlocker_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CruiseBlocker_IsValid(int value);
constexpr CruiseBlocker CruiseBlocker_MIN = NONE;
constexpr CruiseBlocker CruiseBlocker_MAX = TRACTOR_NOT_SAFE;
constexpr int CruiseBlocker_ARRAYSIZE = CruiseBlocker_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CruiseBlocker_descriptor();
template<typename T>
inline const std::string& CruiseBlocker_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CruiseBlocker>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CruiseBlocker_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CruiseBlocker_descriptor(), enum_t_value);
}
inline bool CruiseBlocker_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CruiseBlocker* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CruiseBlocker>(
    CruiseBlocker_descriptor(), name, value);
}
// ===================================================================

class CruiseState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.cruise_control.CruiseState) */ {
 public:
  inline CruiseState() : CruiseState(nullptr) {}
  ~CruiseState() override;
  explicit constexpr CruiseState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CruiseState(const CruiseState& from);
  CruiseState(CruiseState&& from) noexcept
    : CruiseState() {
    *this = ::std::move(from);
  }

  inline CruiseState& operator=(const CruiseState& from) {
    CopyFrom(from);
    return *this;
  }
  inline CruiseState& operator=(CruiseState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CruiseState& default_instance() {
    return *internal_default_instance();
  }
  static inline const CruiseState* internal_default_instance() {
    return reinterpret_cast<const CruiseState*>(
               &_CruiseState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CruiseState& a, CruiseState& b) {
    a.Swap(&b);
  }
  inline void Swap(CruiseState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CruiseState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CruiseState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CruiseState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CruiseState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CruiseState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CruiseState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.cruise_control.CruiseState";
  }
  protected:
  explicit CruiseState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCommandedVelocityFieldNumber = 1,
    kBlockerFieldNumber = 2,
  };
  // double commanded_velocity = 1;
  void clear_commanded_velocity();
  double commanded_velocity() const;
  void set_commanded_velocity(double value);
  private:
  double _internal_commanded_velocity() const;
  void _internal_set_commanded_velocity(double value);
  public:

  // .carbon.frontend.cruise_control.CruiseBlocker blocker = 2;
  void clear_blocker();
  ::carbon::frontend::cruise_control::CruiseBlocker blocker() const;
  void set_blocker(::carbon::frontend::cruise_control::CruiseBlocker value);
  private:
  ::carbon::frontend::cruise_control::CruiseBlocker _internal_blocker() const;
  void _internal_set_blocker(::carbon::frontend::cruise_control::CruiseBlocker value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.cruise_control.CruiseState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double commanded_velocity_;
  int blocker_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcruise_5fcontrol_2eproto;
};
// -------------------------------------------------------------------

class GetNextCruiseStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.cruise_control.GetNextCruiseStateResponse) */ {
 public:
  inline GetNextCruiseStateResponse() : GetNextCruiseStateResponse(nullptr) {}
  ~GetNextCruiseStateResponse() override;
  explicit constexpr GetNextCruiseStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextCruiseStateResponse(const GetNextCruiseStateResponse& from);
  GetNextCruiseStateResponse(GetNextCruiseStateResponse&& from) noexcept
    : GetNextCruiseStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextCruiseStateResponse& operator=(const GetNextCruiseStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextCruiseStateResponse& operator=(GetNextCruiseStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextCruiseStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextCruiseStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextCruiseStateResponse*>(
               &_GetNextCruiseStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextCruiseStateResponse& a, GetNextCruiseStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextCruiseStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextCruiseStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextCruiseStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextCruiseStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextCruiseStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextCruiseStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextCruiseStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.cruise_control.GetNextCruiseStateResponse";
  }
  protected:
  explicit GetNextCruiseStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kStateFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.cruise_control.CruiseState state = 2;
  bool has_state() const;
  private:
  bool _internal_has_state() const;
  public:
  void clear_state();
  const ::carbon::frontend::cruise_control::CruiseState& state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::cruise_control::CruiseState* release_state();
  ::carbon::frontend::cruise_control::CruiseState* mutable_state();
  void set_allocated_state(::carbon::frontend::cruise_control::CruiseState* state);
  private:
  const ::carbon::frontend::cruise_control::CruiseState& _internal_state() const;
  ::carbon::frontend::cruise_control::CruiseState* _internal_mutable_state();
  public:
  void unsafe_arena_set_allocated_state(
      ::carbon::frontend::cruise_control::CruiseState* state);
  ::carbon::frontend::cruise_control::CruiseState* unsafe_arena_release_state();

  // @@protoc_insertion_point(class_scope:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::cruise_control::CruiseState* state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcruise_5fcontrol_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CruiseState

// double commanded_velocity = 1;
inline void CruiseState::clear_commanded_velocity() {
  commanded_velocity_ = 0;
}
inline double CruiseState::_internal_commanded_velocity() const {
  return commanded_velocity_;
}
inline double CruiseState::commanded_velocity() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.cruise_control.CruiseState.commanded_velocity)
  return _internal_commanded_velocity();
}
inline void CruiseState::_internal_set_commanded_velocity(double value) {
  
  commanded_velocity_ = value;
}
inline void CruiseState::set_commanded_velocity(double value) {
  _internal_set_commanded_velocity(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.cruise_control.CruiseState.commanded_velocity)
}

// .carbon.frontend.cruise_control.CruiseBlocker blocker = 2;
inline void CruiseState::clear_blocker() {
  blocker_ = 0;
}
inline ::carbon::frontend::cruise_control::CruiseBlocker CruiseState::_internal_blocker() const {
  return static_cast< ::carbon::frontend::cruise_control::CruiseBlocker >(blocker_);
}
inline ::carbon::frontend::cruise_control::CruiseBlocker CruiseState::blocker() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.cruise_control.CruiseState.blocker)
  return _internal_blocker();
}
inline void CruiseState::_internal_set_blocker(::carbon::frontend::cruise_control::CruiseBlocker value) {
  
  blocker_ = value;
}
inline void CruiseState::set_blocker(::carbon::frontend::cruise_control::CruiseBlocker value) {
  _internal_set_blocker(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.cruise_control.CruiseState.blocker)
}

// -------------------------------------------------------------------

// GetNextCruiseStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextCruiseStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextCruiseStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextCruiseStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextCruiseStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts)
  return _internal_ts();
}
inline void GetNextCruiseStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextCruiseStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCruiseStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCruiseStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextCruiseStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts)
  return _msg;
}
inline void GetNextCruiseStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts)
}

// .carbon.frontend.cruise_control.CruiseState state = 2;
inline bool GetNextCruiseStateResponse::_internal_has_state() const {
  return this != internal_default_instance() && state_ != nullptr;
}
inline bool GetNextCruiseStateResponse::has_state() const {
  return _internal_has_state();
}
inline void GetNextCruiseStateResponse::clear_state() {
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
}
inline const ::carbon::frontend::cruise_control::CruiseState& GetNextCruiseStateResponse::_internal_state() const {
  const ::carbon::frontend::cruise_control::CruiseState* p = state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::cruise_control::CruiseState&>(
      ::carbon::frontend::cruise_control::_CruiseState_default_instance_);
}
inline const ::carbon::frontend::cruise_control::CruiseState& GetNextCruiseStateResponse::state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.cruise_control.GetNextCruiseStateResponse.state)
  return _internal_state();
}
inline void GetNextCruiseStateResponse::unsafe_arena_set_allocated_state(
    ::carbon::frontend::cruise_control::CruiseState* state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(state_);
  }
  state_ = state;
  if (state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.cruise_control.GetNextCruiseStateResponse.state)
}
inline ::carbon::frontend::cruise_control::CruiseState* GetNextCruiseStateResponse::release_state() {
  
  ::carbon::frontend::cruise_control::CruiseState* temp = state_;
  state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::cruise_control::CruiseState* GetNextCruiseStateResponse::unsafe_arena_release_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.cruise_control.GetNextCruiseStateResponse.state)
  
  ::carbon::frontend::cruise_control::CruiseState* temp = state_;
  state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::cruise_control::CruiseState* GetNextCruiseStateResponse::_internal_mutable_state() {
  
  if (state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::cruise_control::CruiseState>(GetArenaForAllocation());
    state_ = p;
  }
  return state_;
}
inline ::carbon::frontend::cruise_control::CruiseState* GetNextCruiseStateResponse::mutable_state() {
  ::carbon::frontend::cruise_control::CruiseState* _msg = _internal_mutable_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.cruise_control.GetNextCruiseStateResponse.state)
  return _msg;
}
inline void GetNextCruiseStateResponse::set_allocated_state(::carbon::frontend::cruise_control::CruiseState* state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete state_;
  }
  if (state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::cruise_control::CruiseState>::GetOwningArena(state);
    if (message_arena != submessage_arena) {
      state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, state, submessage_arena);
    }
    
  } else {
    
  }
  state_ = state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.cruise_control.GetNextCruiseStateResponse.state)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace cruise_control
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::cruise_control::CruiseBlocker> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::cruise_control::CruiseBlocker>() {
  return ::carbon::frontend::cruise_control::CruiseBlocker_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcruise_5fcontrol_2eproto
