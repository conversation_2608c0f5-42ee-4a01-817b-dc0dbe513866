// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/cruise_control.proto

#include "frontend/proto/cruise_control.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace cruise_control {
constexpr CruiseState::CruiseState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : commanded_velocity_(0)
  , blocker_(0)
{}
struct CruiseStateDefaultTypeInternal {
  constexpr CruiseStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CruiseStateDefaultTypeInternal() {}
  union {
    CruiseState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CruiseStateDefaultTypeInternal _CruiseState_default_instance_;
constexpr GetNextCruiseStateResponse::GetNextCruiseStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , state_(nullptr){}
struct GetNextCruiseStateResponseDefaultTypeInternal {
  constexpr GetNextCruiseStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCruiseStateResponseDefaultTypeInternal() {}
  union {
    GetNextCruiseStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCruiseStateResponseDefaultTypeInternal _GetNextCruiseStateResponse_default_instance_;
}  // namespace cruise_control
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcruise_5fcontrol_2eproto[2];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fcruise_5fcontrol_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcruise_5fcontrol_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcruise_5fcontrol_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::CruiseState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::CruiseState, commanded_velocity_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::CruiseState, blocker_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::GetNextCruiseStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::GetNextCruiseStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::cruise_control::GetNextCruiseStateResponse, state_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::cruise_control::CruiseState)},
  { 8, -1, -1, sizeof(::carbon::frontend::cruise_control::GetNextCruiseStateResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::cruise_control::_CruiseState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::cruise_control::_GetNextCruiseStateResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcruise_5fcontrol_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n#frontend/proto/cruise_control.proto\022\036c"
  "arbon.frontend.cruise_control\032\031frontend/"
  "proto/util.proto\"i\n\013CruiseState\022\032\n\022comma"
  "nded_velocity\030\001 \001(\001\022>\n\007blocker\030\002 \001(\0162-.c"
  "arbon.frontend.cruise_control.CruiseBloc"
  "ker\"\205\001\n\032GetNextCruiseStateResponse\022+\n\002ts"
  "\030\001 \001(\0132\037.carbon.frontend.util.Timestamp\022"
  ":\n\005state\030\002 \001(\0132+.carbon.frontend.cruise_"
  "control.CruiseState*R\n\rCruiseBlocker\022\010\n\004"
  "NONE\020\000\022\020\n\014ACTIVE_ALARM\020\001\022\017\n\013NOT_WEEDING\020"
  "\002\022\024\n\020TRACTOR_NOT_SAFE\020\0032\211\001\n\024CruiseContro"
  "lService\022q\n\022GetNextCruiseState\022\037.carbon."
  "frontend.util.Timestamp\032:.carbon.fronten"
  "d.cruise_control.GetNextCruiseStateRespo"
  "nseB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto = {
  false, false, 589, descriptor_table_protodef_frontend_2fproto_2fcruise_5fcontrol_2eproto, "frontend/proto/cruise_control.proto", 
  &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_once, descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_deps, 1, 2,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcruise_5fcontrol_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcruise_5fcontrol_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcruise_5fcontrol_2eproto, file_level_service_descriptors_frontend_2fproto_2fcruise_5fcontrol_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcruise_5fcontrol_2eproto(&descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto);
namespace carbon {
namespace frontend {
namespace cruise_control {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CruiseBlocker_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fcruise_5fcontrol_2eproto[0];
}
bool CruiseBlocker_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class CruiseState::_Internal {
 public:
};

CruiseState::CruiseState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.cruise_control.CruiseState)
}
CruiseState::CruiseState(const CruiseState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&commanded_velocity_, &from.commanded_velocity_,
    static_cast<size_t>(reinterpret_cast<char*>(&blocker_) -
    reinterpret_cast<char*>(&commanded_velocity_)) + sizeof(blocker_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.cruise_control.CruiseState)
}

inline void CruiseState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&commanded_velocity_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&blocker_) -
    reinterpret_cast<char*>(&commanded_velocity_)) + sizeof(blocker_));
}

CruiseState::~CruiseState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.cruise_control.CruiseState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CruiseState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CruiseState::ArenaDtor(void* object) {
  CruiseState* _this = reinterpret_cast< CruiseState* >(object);
  (void)_this;
}
void CruiseState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CruiseState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CruiseState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.cruise_control.CruiseState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&commanded_velocity_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&blocker_) -
      reinterpret_cast<char*>(&commanded_velocity_)) + sizeof(blocker_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CruiseState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double commanded_velocity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          commanded_velocity_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.cruise_control.CruiseBlocker blocker = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_blocker(static_cast<::carbon::frontend::cruise_control::CruiseBlocker>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CruiseState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.cruise_control.CruiseState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double commanded_velocity = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_commanded_velocity = this->_internal_commanded_velocity();
  uint64_t raw_commanded_velocity;
  memcpy(&raw_commanded_velocity, &tmp_commanded_velocity, sizeof(tmp_commanded_velocity));
  if (raw_commanded_velocity != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_commanded_velocity(), target);
  }

  // .carbon.frontend.cruise_control.CruiseBlocker blocker = 2;
  if (this->_internal_blocker() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_blocker(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.cruise_control.CruiseState)
  return target;
}

size_t CruiseState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.cruise_control.CruiseState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double commanded_velocity = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_commanded_velocity = this->_internal_commanded_velocity();
  uint64_t raw_commanded_velocity;
  memcpy(&raw_commanded_velocity, &tmp_commanded_velocity, sizeof(tmp_commanded_velocity));
  if (raw_commanded_velocity != 0) {
    total_size += 1 + 8;
  }

  // .carbon.frontend.cruise_control.CruiseBlocker blocker = 2;
  if (this->_internal_blocker() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_blocker());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CruiseState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CruiseState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CruiseState::GetClassData() const { return &_class_data_; }

void CruiseState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CruiseState *>(to)->MergeFrom(
      static_cast<const CruiseState &>(from));
}


void CruiseState::MergeFrom(const CruiseState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.cruise_control.CruiseState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_commanded_velocity = from._internal_commanded_velocity();
  uint64_t raw_commanded_velocity;
  memcpy(&raw_commanded_velocity, &tmp_commanded_velocity, sizeof(tmp_commanded_velocity));
  if (raw_commanded_velocity != 0) {
    _internal_set_commanded_velocity(from._internal_commanded_velocity());
  }
  if (from._internal_blocker() != 0) {
    _internal_set_blocker(from._internal_blocker());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CruiseState::CopyFrom(const CruiseState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.cruise_control.CruiseState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CruiseState::IsInitialized() const {
  return true;
}

void CruiseState::InternalSwap(CruiseState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CruiseState, blocker_)
      + sizeof(CruiseState::blocker_)
      - PROTOBUF_FIELD_OFFSET(CruiseState, commanded_velocity_)>(
          reinterpret_cast<char*>(&commanded_velocity_),
          reinterpret_cast<char*>(&other->commanded_velocity_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CruiseState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_getter, &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcruise_5fcontrol_2eproto[0]);
}

// ===================================================================

class GetNextCruiseStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCruiseStateResponse* msg);
  static const ::carbon::frontend::cruise_control::CruiseState& state(const GetNextCruiseStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCruiseStateResponse::_Internal::ts(const GetNextCruiseStateResponse* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::cruise_control::CruiseState&
GetNextCruiseStateResponse::_Internal::state(const GetNextCruiseStateResponse* msg) {
  return *msg->state_;
}
void GetNextCruiseStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextCruiseStateResponse::GetNextCruiseStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
}
GetNextCruiseStateResponse::GetNextCruiseStateResponse(const GetNextCruiseStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_state()) {
    state_ = new ::carbon::frontend::cruise_control::CruiseState(*from.state_);
  } else {
    state_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
}

inline void GetNextCruiseStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(state_));
}

GetNextCruiseStateResponse::~GetNextCruiseStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCruiseStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete state_;
}

void GetNextCruiseStateResponse::ArenaDtor(void* object) {
  GetNextCruiseStateResponse* _this = reinterpret_cast< GetNextCruiseStateResponse* >(object);
  (void)_this;
}
void GetNextCruiseStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCruiseStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCruiseStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCruiseStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.cruise_control.CruiseState state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCruiseStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.cruise_control.CruiseState state = 2;
  if (this->_internal_has_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::state(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  return target;
}

size_t GetNextCruiseStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.cruise_control.CruiseState state = 2;
  if (this->_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *state_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCruiseStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCruiseStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCruiseStateResponse::GetClassData() const { return &_class_data_; }

void GetNextCruiseStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCruiseStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextCruiseStateResponse &>(from));
}


void GetNextCruiseStateResponse::MergeFrom(const GetNextCruiseStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_state()) {
    _internal_mutable_state()->::carbon::frontend::cruise_control::CruiseState::MergeFrom(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCruiseStateResponse::CopyFrom(const GetNextCruiseStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCruiseStateResponse::IsInitialized() const {
  return true;
}

void GetNextCruiseStateResponse::InternalSwap(GetNextCruiseStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextCruiseStateResponse, state_)
      + sizeof(GetNextCruiseStateResponse::state_)
      - PROTOBUF_FIELD_OFFSET(GetNextCruiseStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCruiseStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_getter, &descriptor_table_frontend_2fproto_2fcruise_5fcontrol_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcruise_5fcontrol_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace cruise_control
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::cruise_control::CruiseState* Arena::CreateMaybeMessage< ::carbon::frontend::cruise_control::CruiseState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::cruise_control::CruiseState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::cruise_control::GetNextCruiseStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::cruise_control::GetNextCruiseStateResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
