"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

CruiseBlockerValue = typing___NewType('CruiseBlockerValue', builtin___int)
type___CruiseBlockerValue = CruiseBlockerValue
CruiseBlocker: _CruiseBlocker
class _CruiseBlocker(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[CruiseBlockerValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(CruiseBlockerValue, 0)
    ACTIVE_ALARM = typing___cast(CruiseBlockerValue, 1)
    NOT_WEEDING = typing___cast(CruiseBlockerValue, 2)
    TRACTOR_NOT_SAFE = typing___cast(CruiseBlockerValue, 3)
NONE = typing___cast(CruiseBlockerValue, 0)
ACTIVE_ALARM = typing___cast(CruiseBlockerValue, 1)
NOT_WEEDING = typing___cast(CruiseBlockerValue, 2)
TRACTOR_NOT_SAFE = typing___cast(CruiseBlockerValue, 3)

class CruiseState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    commanded_velocity: builtin___float = ...
    blocker: type___CruiseBlockerValue = ...

    def __init__(self,
        *,
        commanded_velocity : typing___Optional[builtin___float] = None,
        blocker : typing___Optional[type___CruiseBlockerValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"blocker",b"blocker",u"commanded_velocity",b"commanded_velocity"]) -> None: ...
type___CruiseState = CruiseState

class GetNextCruiseStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def state(self) -> type___CruiseState: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        state : typing___Optional[type___CruiseState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> None: ...
type___GetNextCruiseStateResponse = GetNextCruiseStateResponse
