# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/cruise_control.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/cruise_control.proto',
  package='carbon.frontend.cruise_control',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n#frontend/proto/cruise_control.proto\x12\x1e\x63\x61rbon.frontend.cruise_control\x1a\x19\x66rontend/proto/util.proto\"i\n\x0b\x43ruiseState\x12\x1a\n\x12\x63ommanded_velocity\x18\x01 \x01(\x01\x12>\n\x07\x62locker\x18\x02 \x01(\x0e\x32-.carbon.frontend.cruise_control.CruiseBlocker\"\x85\x01\n\x1aGetNextCruiseStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12:\n\x05state\x18\x02 \x01(\x0b\x32+.carbon.frontend.cruise_control.CruiseState*R\n\rCruiseBlocker\x12\x08\n\x04NONE\x10\x00\x12\x10\n\x0c\x41\x43TIVE_ALARM\x10\x01\x12\x0f\n\x0bNOT_WEEDING\x10\x02\x12\x14\n\x10TRACTOR_NOT_SAFE\x10\x03\x32\x89\x01\n\x14\x43ruiseControlService\x12q\n\x12GetNextCruiseState\x12\x1f.carbon.frontend.util.Timestamp\x1a:.carbon.frontend.cruise_control.GetNextCruiseStateResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_CRUISEBLOCKER = _descriptor.EnumDescriptor(
  name='CruiseBlocker',
  full_name='carbon.frontend.cruise_control.CruiseBlocker',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ACTIVE_ALARM', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NOT_WEEDING', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TRACTOR_NOT_SAFE', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=341,
  serialized_end=423,
)
_sym_db.RegisterEnumDescriptor(_CRUISEBLOCKER)

CruiseBlocker = enum_type_wrapper.EnumTypeWrapper(_CRUISEBLOCKER)
NONE = 0
ACTIVE_ALARM = 1
NOT_WEEDING = 2
TRACTOR_NOT_SAFE = 3



_CRUISESTATE = _descriptor.Descriptor(
  name='CruiseState',
  full_name='carbon.frontend.cruise_control.CruiseState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='commanded_velocity', full_name='carbon.frontend.cruise_control.CruiseState.commanded_velocity', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='blocker', full_name='carbon.frontend.cruise_control.CruiseState.blocker', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=98,
  serialized_end=203,
)


_GETNEXTCRUISESTATERESPONSE = _descriptor.Descriptor(
  name='GetNextCruiseStateResponse',
  full_name='carbon.frontend.cruise_control.GetNextCruiseStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.frontend.cruise_control.GetNextCruiseStateResponse.state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=206,
  serialized_end=339,
)

_CRUISESTATE.fields_by_name['blocker'].enum_type = _CRUISEBLOCKER
_GETNEXTCRUISESTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCRUISESTATERESPONSE.fields_by_name['state'].message_type = _CRUISESTATE
DESCRIPTOR.message_types_by_name['CruiseState'] = _CRUISESTATE
DESCRIPTOR.message_types_by_name['GetNextCruiseStateResponse'] = _GETNEXTCRUISESTATERESPONSE
DESCRIPTOR.enum_types_by_name['CruiseBlocker'] = _CRUISEBLOCKER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CruiseState = _reflection.GeneratedProtocolMessageType('CruiseState', (_message.Message,), {
  'DESCRIPTOR' : _CRUISESTATE,
  '__module__' : 'frontend.proto.cruise_control_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.cruise_control.CruiseState)
  })
_sym_db.RegisterMessage(CruiseState)

GetNextCruiseStateResponse = _reflection.GeneratedProtocolMessageType('GetNextCruiseStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCRUISESTATERESPONSE,
  '__module__' : 'frontend.proto.cruise_control_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.cruise_control.GetNextCruiseStateResponse)
  })
_sym_db.RegisterMessage(GetNextCruiseStateResponse)


DESCRIPTOR._options = None

_CRUISECONTROLSERVICE = _descriptor.ServiceDescriptor(
  name='CruiseControlService',
  full_name='carbon.frontend.cruise_control.CruiseControlService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=426,
  serialized_end=563,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextCruiseState',
    full_name='carbon.frontend.cruise_control.CruiseControlService.GetNextCruiseState',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTCRUISESTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CRUISECONTROLSERVICE)

DESCRIPTOR.services_by_name['CruiseControlService'] = _CRUISECONTROLSERVICE

# @@protoc_insertion_point(module_scope)
