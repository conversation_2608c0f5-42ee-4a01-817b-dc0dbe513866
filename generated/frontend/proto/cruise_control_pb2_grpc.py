# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import cruise_control_pb2 as frontend_dot_proto_dot_cruise__control__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class CruiseControlServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextCruiseState = channel.unary_unary(
                '/carbon.frontend.cruise_control.CruiseControlService/GetNextCruiseState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_cruise__control__pb2.GetNextCruiseStateResponse.FromString,
                )


class CruiseControlServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextCruiseState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CruiseControlServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextCruiseState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCruiseState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_cruise__control__pb2.GetNextCruiseStateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.cruise_control.CruiseControlService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CruiseControlService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextCruiseState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.cruise_control.CruiseControlService/GetNextCruiseState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_cruise__control__pb2.GetNextCruiseStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
