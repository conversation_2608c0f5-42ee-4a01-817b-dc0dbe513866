// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/cruise_control.proto

#include "frontend/proto/cruise_control.pb.h"
#include "frontend/proto/cruise_control.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace cruise_control {

static const char* CruiseControlService_method_names[] = {
  "/carbon.frontend.cruise_control.CruiseControlService/GetNextCruiseState",
};

std::unique_ptr< CruiseControlService::Stub> CruiseControlService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CruiseControlService::Stub> stub(new CruiseControlService::Stub(channel, options));
  return stub;
}

CruiseControlService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextCruiseState_(CruiseControlService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CruiseControlService::Stub::GetNextCruiseState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCruiseState_, context, request, response);
}

void CruiseControlService::Stub::async::GetNextCruiseState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCruiseState_, context, request, response, std::move(f));
}

void CruiseControlService::Stub::async::GetNextCruiseState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCruiseState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::cruise_control::GetNextCruiseStateResponse>* CruiseControlService::Stub::PrepareAsyncGetNextCruiseStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::cruise_control::GetNextCruiseStateResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCruiseState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::cruise_control::GetNextCruiseStateResponse>* CruiseControlService::Stub::AsyncGetNextCruiseStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCruiseStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

CruiseControlService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CruiseControlService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CruiseControlService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CruiseControlService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* resp) {
               return service->GetNextCruiseState(ctx, req, resp);
             }, this)));
}

CruiseControlService::Service::~Service() {
}

::grpc::Status CruiseControlService::Service::GetNextCruiseState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::cruise_control::GetNextCruiseStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace cruise_control

