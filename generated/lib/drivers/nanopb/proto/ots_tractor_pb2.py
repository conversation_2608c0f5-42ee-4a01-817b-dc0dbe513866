# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: generated/lib/drivers/nanopb/proto/ots_tractor.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='generated/lib/drivers/nanopb/proto/ots_tractor.proto',
  package='ots_tractor',
  syntax='proto3',
  serialized_options=b'Z\022nanopb/ots_tractor',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n4generated/lib/drivers/nanopb/proto/ots_tractor.proto\x12\x0bots_tractor\",\n\tGearState\x12\x1f\n\x04gear\x18\x01 \x01(\x0e\x32\x11.ots_tractor.Gear\"2\n\x0bLightsState\x12#\n\x06lights\x18\x01 \x01(\x0e\x32\x13.ots_tractor.Lights\"\"\n\x11SpeedControlState\x12\r\n\x05speed\x18\x01 \x01(\x02\"\x1c\n\x0cRpmDialState\x12\x0c\n\x04rpms\x18\x01 \x01(\x02\"1\n\x0bPTOFailType\x12\x10\n\x08\x62\x61\x64_gear\x18\x01 \x01(\x08\x12\x10\n\x08\x62\x61\x64_rpms\x18\x02 \x01(\x08\"\"\n\x0fPTOCommandState\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"r\n\x08PtoState\x12-\n\x05state\x18\x01 \x01(\x0b\x32\x1c.ots_tractor.PTOCommandStateH\x00\x12+\n\x07\x66\x61ilure\x18\x02 \x01(\x0b\x32\x18.ots_tractor.PTOFailTypeH\x00\x42\n\n\x08\x64\x65\x63ision\"U\n\x0eHitchV2Request\x12$\n\x05hitch\x18\x01 \x01(\x0e\x32\x15.ots_tractor.HitchCmd\x12\x1d\n\x15precise_hitch_percent\x18\x02 \x01(\x02\"(\n\x0cHitchRequest\x12\x18\n\x10hitch_lift_force\x18\x01 \x01(\x05\"\x0c\n\nHitchReply\"@\n\nScvRequest\x12\x0e\n\x06scv_id\x18\x01 \x01(\x05\x12\r\n\x05\x66orce\x18\x02 \x01(\x05\x12\x13\n\x0b\x63md_time_ms\x18\x03 \x01(\x05\"\n\n\x08ScvReply\"G\n\x13TractorVariantState\x12\x30\n\x07variant\x18\x01 \x01(\x0e\x32\x1f.ots_tractor.TractorVariantType\"\xb9\x01\n\x12WheelAngleCalState\x12\x1a\n\x12sensor_deg_per_bit\x18\x01 \x01(\x02\x12\x19\n\x11sensor_center_val\x18\x02 \x01(\r\x12\x17\n\x0f\x63\x65nter_trim_deg\x18\x03 \x01(\x02\x12\x16\n\x0eright_lock_deg\x18\x04 \x01(\x02\x12\x1c\n\x14sensor_full_left_val\x18\x05 \x01(\r\x12\x1d\n\x15sensor_full_right_val\x18\x06 \x01(\r\"\x1f\n\tFuelLevel\x12\x12\n\nfuel_level\x18\x01 \x01(\x02\"!\n\nEngineTemp\x12\x13\n\x0b\x65ngine_temp\x18\x01 \x01(\x02\"I\n\x12IgnitionErrorCodes\x12\x10\n\x08\x62\x61\x64_gear\x18\x01 \x01(\x08\x12\x10\n\x08\x62\x61\x64_rpms\x18\x02 \x01(\x08\x12\x0f\n\x07\x62\x61\x64_pto\x18\x03 \x01(\x08\"v\n\x13IgnitionOffResponse\x12\x1f\n\x15target_ignition_state\x18\x01 \x01(\x08H\x00\x12\x32\n\x07\x66\x61ilure\x18\x02 \x01(\x0b\x32\x1f.ots_tractor.IgnitionErrorCodesH\x00\x42\n\n\x08\x64\x65\x63ision\"\x07\n\x05\x45mpty\"\xf1\x04\n\nSetRequest\x12&\n\x04gear\x18\x01 \x01(\x0b\x32\x16.ots_tractor.GearStateH\x00\x12*\n\x06lights\x18\x02 \x01(\x0b\x32\x18.ots_tractor.LightsStateH\x00\x12\x37\n\rspeed_control\x18\x03 \x01(\x0b\x32\x1e.ots_tractor.SpeedControlStateH\x00\x12*\n\x05hitch\x18\x04 \x01(\x0b\x32\x19.ots_tractor.HitchRequestH\x00\x12&\n\x03scv\x18\x05 \x01(\x0b\x32\x17.ots_tractor.ScvRequestH\x00\x12)\n\x04rpms\x18\x06 \x01(\x0b\x32\x19.ots_tractor.RpmDialStateH\x00\x12\x31\n\tfront_pto\x18\x07 \x01(\x0b\x32\x1c.ots_tractor.PTOCommandStateH\x00\x12*\n\x08rear_pto\x18\x08 \x01(\x0b\x32\x12.ots_tractor.EmptyB\x02\x18\x01H\x00\x12\x33\n\x07variant\x18\t \x01(\x0b\x32 .ots_tractor.TractorVariantStateH\x00\x12\x34\n\twheel_cal\x18\n \x01(\x0b\x32\x1f.ots_tractor.WheelAngleCalStateH\x00\x12*\n\x0cignition_off\x18\x0b \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12/\n\x08hitch_V2\x18\x0c \x01(\x0b\x32\x1b.ots_tractor.HitchV2RequestH\x00\x12)\n\x0bpower_cycle\x18\r \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x42\x05\n\x03set\"\xfc\x04\n\x08SetReply\x12&\n\x04gear\x18\x01 \x01(\x0b\x32\x16.ots_tractor.GearStateH\x00\x12*\n\x06lights\x18\x02 \x01(\x0b\x32\x18.ots_tractor.LightsStateH\x00\x12\x37\n\rspeed_control\x18\x03 \x01(\x0b\x32\x1e.ots_tractor.SpeedControlStateH\x00\x12(\n\x05hitch\x18\x04 \x01(\x0b\x32\x17.ots_tractor.HitchReplyH\x00\x12$\n\x03scv\x18\x05 \x01(\x0b\x32\x15.ots_tractor.ScvReplyH\x00\x12)\n\x04rpms\x18\x06 \x01(\x0b\x32\x19.ots_tractor.RpmDialStateH\x00\x12*\n\tfront_pto\x18\x07 \x01(\x0b\x32\x15.ots_tractor.PtoStateH\x00\x12*\n\x08rear_pto\x18\x08 \x01(\x0b\x32\x12.ots_tractor.EmptyB\x02\x18\x01H\x00\x12\x33\n\x07variant\x18\t \x01(\x0b\x32 .ots_tractor.TractorVariantStateH\x00\x12\x34\n\twheel_cal\x18\n \x01(\x0b\x32\x1f.ots_tractor.WheelAngleCalStateH\x00\x12\x38\n\x0cignition_off\x18\x0b \x01(\x0b\x32 .ots_tractor.IgnitionOffResponseH\x00\x12+\n\x08hitch_V2\x18\x0c \x01(\x0b\x32\x17.ots_tractor.HitchReplyH\x00\x12\x37\n\x0bpower_cycle\x18\r \x01(\x0b\x32 .ots_tractor.IgnitionOffResponseH\x00\x42\x05\n\x03set\"\xa8\x03\n\nGetRequest\x12\"\n\x04gear\x18\x01 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12$\n\x06lights\x18\x02 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12+\n\rspeed_control\x18\x03 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12\"\n\x04rpms\x18\x04 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12\'\n\tfront_pto\x18\x05 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12*\n\x08rear_pto\x18\x06 \x01(\x0b\x32\x12.ots_tractor.EmptyB\x02\x18\x01H\x00\x12%\n\x07variant\x18\x07 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12\'\n\twheel_cal\x18\x08 \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12(\n\nfuel_level\x18\t \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x12)\n\x0b\x65ngine_temp\x18\n \x01(\x0b\x32\x12.ots_tractor.EmptyH\x00\x42\x05\n\x03get\"\xd3\x03\n\x08GetReply\x12&\n\x04gear\x18\x01 \x01(\x0b\x32\x16.ots_tractor.GearStateH\x00\x12*\n\x06lights\x18\x02 \x01(\x0b\x32\x18.ots_tractor.LightsStateH\x00\x12\x37\n\rspeed_control\x18\x03 \x01(\x0b\x32\x1e.ots_tractor.SpeedControlStateH\x00\x12)\n\x04rpms\x18\x04 \x01(\x0b\x32\x19.ots_tractor.RpmDialStateH\x00\x12\x13\n\tfront_pto\x18\x05 \x01(\x08H\x00\x12*\n\x08rear_pto\x18\x06 \x01(\x0b\x32\x12.ots_tractor.EmptyB\x02\x18\x01H\x00\x12\x33\n\x07variant\x18\x07 \x01(\x0b\x32 .ots_tractor.TractorVariantStateH\x00\x12\x34\n\twheel_cal\x18\x08 \x01(\x0b\x32\x1f.ots_tractor.WheelAngleCalStateH\x00\x12,\n\nfuel_level\x18\t \x01(\x0b\x32\x16.ots_tractor.FuelLevelH\x00\x12.\n\x0b\x65ngine_temp\x18\n \x01(\x0b\x32\x17.ots_tractor.EngineTempH\x00\x42\x05\n\x03get\"d\n\x07Request\x12&\n\x03set\x18\x01 \x01(\x0b\x32\x17.ots_tractor.SetRequestH\x00\x12&\n\x03get\x18\x02 \x01(\x0b\x32\x17.ots_tractor.GetRequestH\x00\x42\t\n\x07request\"\\\n\x05Reply\x12$\n\x03set\x18\x01 \x01(\x0b\x32\x15.ots_tractor.SetReplyH\x00\x12$\n\x03get\x18\x02 \x01(\x0b\x32\x15.ots_tractor.GetReplyH\x00\x42\x07\n\x05reply*\x81\x01\n\x12TractorVariantType\x12\x0e\n\nTV_UNKNOWN\x10\x00\x12\r\n\tTV_JD_6LH\x10\x01\x12\x0e\n\nTV_JD_6LHM\x10\x02\x12\x0e\n\nTV_JD_6PRO\x10\x03\x12\r\n\tTV_JD_7LH\x10\x04\x12\x0e\n\nTV_JD_7PRO\x10\x05\x12\r\n\tTV_JD_8RH\x10\x06*_\n\x04Gear\x12\r\n\tGEAR_PARK\x10\x00\x12\x10\n\x0cGEAR_REVERSE\x10\x01\x12\x10\n\x0cGEAR_NEUTRAL\x10\x02\x12\x10\n\x0cGEAR_FORWARD\x10\x03\x12\x12\n\x0eGEAR_POWERZERO\x10\x04*9\n\x06Lights\x12\x0e\n\nLIGHTS_OFF\x10\x00\x12\x0e\n\nLIGHTS_LOW\x10\x01\x12\x0f\n\x0bLIGHTS_HIGH\x10\x02*,\n\x08HitchCmd\x12\x08\n\x04LIFT\x10\x00\x12\t\n\x05LOWER\x10\x01\x12\x0b\n\x07PRECISE\x10\x02\x42\x14Z\x12nanopb/ots_tractorb\x06proto3'
)

_TRACTORVARIANTTYPE = _descriptor.EnumDescriptor(
  name='TractorVariantType',
  full_name='ots_tractor.TractorVariantType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='TV_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6LH', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6LHM', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_6PRO', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_7LH', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_7PRO', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TV_JD_8RH', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3551,
  serialized_end=3680,
)
_sym_db.RegisterEnumDescriptor(_TRACTORVARIANTTYPE)

TractorVariantType = enum_type_wrapper.EnumTypeWrapper(_TRACTORVARIANTTYPE)
_GEAR = _descriptor.EnumDescriptor(
  name='Gear',
  full_name='ots_tractor.Gear',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='GEAR_PARK', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_REVERSE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_NEUTRAL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_FORWARD', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='GEAR_POWERZERO', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3682,
  serialized_end=3777,
)
_sym_db.RegisterEnumDescriptor(_GEAR)

Gear = enum_type_wrapper.EnumTypeWrapper(_GEAR)
_LIGHTS = _descriptor.EnumDescriptor(
  name='Lights',
  full_name='ots_tractor.Lights',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_OFF', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_LOW', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LIGHTS_HIGH', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3779,
  serialized_end=3836,
)
_sym_db.RegisterEnumDescriptor(_LIGHTS)

Lights = enum_type_wrapper.EnumTypeWrapper(_LIGHTS)
_HITCHCMD = _descriptor.EnumDescriptor(
  name='HitchCmd',
  full_name='ots_tractor.HitchCmd',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='LIFT', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LOWER', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PRECISE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3838,
  serialized_end=3882,
)
_sym_db.RegisterEnumDescriptor(_HITCHCMD)

HitchCmd = enum_type_wrapper.EnumTypeWrapper(_HITCHCMD)
TV_UNKNOWN = 0
TV_JD_6LH = 1
TV_JD_6LHM = 2
TV_JD_6PRO = 3
TV_JD_7LH = 4
TV_JD_7PRO = 5
TV_JD_8RH = 6
GEAR_PARK = 0
GEAR_REVERSE = 1
GEAR_NEUTRAL = 2
GEAR_FORWARD = 3
GEAR_POWERZERO = 4
LIGHTS_OFF = 0
LIGHTS_LOW = 1
LIGHTS_HIGH = 2
LIFT = 0
LOWER = 1
PRECISE = 2



_GEARSTATE = _descriptor.Descriptor(
  name='GearState',
  full_name='ots_tractor.GearState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='ots_tractor.GearState.gear', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=69,
  serialized_end=113,
)


_LIGHTSSTATE = _descriptor.Descriptor(
  name='LightsState',
  full_name='ots_tractor.LightsState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lights', full_name='ots_tractor.LightsState.lights', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=115,
  serialized_end=165,
)


_SPEEDCONTROLSTATE = _descriptor.Descriptor(
  name='SpeedControlState',
  full_name='ots_tractor.SpeedControlState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='speed', full_name='ots_tractor.SpeedControlState.speed', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=167,
  serialized_end=201,
)


_RPMDIALSTATE = _descriptor.Descriptor(
  name='RpmDialState',
  full_name='ots_tractor.RpmDialState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rpms', full_name='ots_tractor.RpmDialState.rpms', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=231,
)


_PTOFAILTYPE = _descriptor.Descriptor(
  name='PTOFailType',
  full_name='ots_tractor.PTOFailType',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bad_gear', full_name='ots_tractor.PTOFailType.bad_gear', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bad_rpms', full_name='ots_tractor.PTOFailType.bad_rpms', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=233,
  serialized_end=282,
)


_PTOCOMMANDSTATE = _descriptor.Descriptor(
  name='PTOCommandState',
  full_name='ots_tractor.PTOCommandState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='ots_tractor.PTOCommandState.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=284,
  serialized_end=318,
)


_PTOSTATE = _descriptor.Descriptor(
  name='PtoState',
  full_name='ots_tractor.PtoState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='state', full_name='ots_tractor.PtoState.state', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='failure', full_name='ots_tractor.PtoState.failure', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='decision', full_name='ots_tractor.PtoState.decision',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=320,
  serialized_end=434,
)


_HITCHV2REQUEST = _descriptor.Descriptor(
  name='HitchV2Request',
  full_name='ots_tractor.HitchV2Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hitch', full_name='ots_tractor.HitchV2Request.hitch', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='precise_hitch_percent', full_name='ots_tractor.HitchV2Request.precise_hitch_percent', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=436,
  serialized_end=521,
)


_HITCHREQUEST = _descriptor.Descriptor(
  name='HitchRequest',
  full_name='ots_tractor.HitchRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hitch_lift_force', full_name='ots_tractor.HitchRequest.hitch_lift_force', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=523,
  serialized_end=563,
)


_HITCHREPLY = _descriptor.Descriptor(
  name='HitchReply',
  full_name='ots_tractor.HitchReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=565,
  serialized_end=577,
)


_SCVREQUEST = _descriptor.Descriptor(
  name='ScvRequest',
  full_name='ots_tractor.ScvRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scv_id', full_name='ots_tractor.ScvRequest.scv_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='force', full_name='ots_tractor.ScvRequest.force', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cmd_time_ms', full_name='ots_tractor.ScvRequest.cmd_time_ms', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=579,
  serialized_end=643,
)


_SCVREPLY = _descriptor.Descriptor(
  name='ScvReply',
  full_name='ots_tractor.ScvReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=645,
  serialized_end=655,
)


_TRACTORVARIANTSTATE = _descriptor.Descriptor(
  name='TractorVariantState',
  full_name='ots_tractor.TractorVariantState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='variant', full_name='ots_tractor.TractorVariantState.variant', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=657,
  serialized_end=728,
)


_WHEELANGLECALSTATE = _descriptor.Descriptor(
  name='WheelAngleCalState',
  full_name='ots_tractor.WheelAngleCalState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='sensor_deg_per_bit', full_name='ots_tractor.WheelAngleCalState.sensor_deg_per_bit', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_center_val', full_name='ots_tractor.WheelAngleCalState.sensor_center_val', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_trim_deg', full_name='ots_tractor.WheelAngleCalState.center_trim_deg', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right_lock_deg', full_name='ots_tractor.WheelAngleCalState.right_lock_deg', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_full_left_val', full_name='ots_tractor.WheelAngleCalState.sensor_full_left_val', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_full_right_val', full_name='ots_tractor.WheelAngleCalState.sensor_full_right_val', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=731,
  serialized_end=916,
)


_FUELLEVEL = _descriptor.Descriptor(
  name='FuelLevel',
  full_name='ots_tractor.FuelLevel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='fuel_level', full_name='ots_tractor.FuelLevel.fuel_level', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=918,
  serialized_end=949,
)


_ENGINETEMP = _descriptor.Descriptor(
  name='EngineTemp',
  full_name='ots_tractor.EngineTemp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='engine_temp', full_name='ots_tractor.EngineTemp.engine_temp', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=951,
  serialized_end=984,
)


_IGNITIONERRORCODES = _descriptor.Descriptor(
  name='IgnitionErrorCodes',
  full_name='ots_tractor.IgnitionErrorCodes',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bad_gear', full_name='ots_tractor.IgnitionErrorCodes.bad_gear', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bad_rpms', full_name='ots_tractor.IgnitionErrorCodes.bad_rpms', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bad_pto', full_name='ots_tractor.IgnitionErrorCodes.bad_pto', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=986,
  serialized_end=1059,
)


_IGNITIONOFFRESPONSE = _descriptor.Descriptor(
  name='IgnitionOffResponse',
  full_name='ots_tractor.IgnitionOffResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='target_ignition_state', full_name='ots_tractor.IgnitionOffResponse.target_ignition_state', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='failure', full_name='ots_tractor.IgnitionOffResponse.failure', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='decision', full_name='ots_tractor.IgnitionOffResponse.decision',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1061,
  serialized_end=1179,
)


_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='ots_tractor.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1181,
  serialized_end=1188,
)


_SETREQUEST = _descriptor.Descriptor(
  name='SetRequest',
  full_name='ots_tractor.SetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='ots_tractor.SetRequest.gear', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lights', full_name='ots_tractor.SetRequest.lights', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_control', full_name='ots_tractor.SetRequest.speed_control', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch', full_name='ots_tractor.SetRequest.hitch', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scv', full_name='ots_tractor.SetRequest.scv', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rpms', full_name='ots_tractor.SetRequest.rpms', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_pto', full_name='ots_tractor.SetRequest.front_pto', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rear_pto', full_name='ots_tractor.SetRequest.rear_pto', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='ots_tractor.SetRequest.variant', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_cal', full_name='ots_tractor.SetRequest.wheel_cal', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ignition_off', full_name='ots_tractor.SetRequest.ignition_off', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch_V2', full_name='ots_tractor.SetRequest.hitch_V2', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_cycle', full_name='ots_tractor.SetRequest.power_cycle', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='set', full_name='ots_tractor.SetRequest.set',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1191,
  serialized_end=1816,
)


_SETREPLY = _descriptor.Descriptor(
  name='SetReply',
  full_name='ots_tractor.SetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='ots_tractor.SetReply.gear', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lights', full_name='ots_tractor.SetReply.lights', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_control', full_name='ots_tractor.SetReply.speed_control', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch', full_name='ots_tractor.SetReply.hitch', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scv', full_name='ots_tractor.SetReply.scv', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rpms', full_name='ots_tractor.SetReply.rpms', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_pto', full_name='ots_tractor.SetReply.front_pto', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rear_pto', full_name='ots_tractor.SetReply.rear_pto', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='ots_tractor.SetReply.variant', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_cal', full_name='ots_tractor.SetReply.wheel_cal', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ignition_off', full_name='ots_tractor.SetReply.ignition_off', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hitch_V2', full_name='ots_tractor.SetReply.hitch_V2', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_cycle', full_name='ots_tractor.SetReply.power_cycle', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='set', full_name='ots_tractor.SetReply.set',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1819,
  serialized_end=2455,
)


_GETREQUEST = _descriptor.Descriptor(
  name='GetRequest',
  full_name='ots_tractor.GetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='ots_tractor.GetRequest.gear', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lights', full_name='ots_tractor.GetRequest.lights', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_control', full_name='ots_tractor.GetRequest.speed_control', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rpms', full_name='ots_tractor.GetRequest.rpms', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_pto', full_name='ots_tractor.GetRequest.front_pto', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rear_pto', full_name='ots_tractor.GetRequest.rear_pto', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='ots_tractor.GetRequest.variant', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_cal', full_name='ots_tractor.GetRequest.wheel_cal', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuel_level', full_name='ots_tractor.GetRequest.fuel_level', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='engine_temp', full_name='ots_tractor.GetRequest.engine_temp', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='get', full_name='ots_tractor.GetRequest.get',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2458,
  serialized_end=2882,
)


_GETREPLY = _descriptor.Descriptor(
  name='GetReply',
  full_name='ots_tractor.GetReply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='gear', full_name='ots_tractor.GetReply.gear', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lights', full_name='ots_tractor.GetReply.lights', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_control', full_name='ots_tractor.GetReply.speed_control', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rpms', full_name='ots_tractor.GetReply.rpms', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_pto', full_name='ots_tractor.GetReply.front_pto', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rear_pto', full_name='ots_tractor.GetReply.rear_pto', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='variant', full_name='ots_tractor.GetReply.variant', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_cal', full_name='ots_tractor.GetReply.wheel_cal', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuel_level', full_name='ots_tractor.GetReply.fuel_level', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='engine_temp', full_name='ots_tractor.GetReply.engine_temp', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='get', full_name='ots_tractor.GetReply.get',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2885,
  serialized_end=3352,
)


_REQUEST = _descriptor.Descriptor(
  name='Request',
  full_name='ots_tractor.Request',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='ots_tractor.Request.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='ots_tractor.Request.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='ots_tractor.Request.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3354,
  serialized_end=3454,
)


_REPLY = _descriptor.Descriptor(
  name='Reply',
  full_name='ots_tractor.Reply',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='set', full_name='ots_tractor.Reply.set', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get', full_name='ots_tractor.Reply.get', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='reply', full_name='ots_tractor.Reply.reply',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=3456,
  serialized_end=3548,
)

_GEARSTATE.fields_by_name['gear'].enum_type = _GEAR
_LIGHTSSTATE.fields_by_name['lights'].enum_type = _LIGHTS
_PTOSTATE.fields_by_name['state'].message_type = _PTOCOMMANDSTATE
_PTOSTATE.fields_by_name['failure'].message_type = _PTOFAILTYPE
_PTOSTATE.oneofs_by_name['decision'].fields.append(
  _PTOSTATE.fields_by_name['state'])
_PTOSTATE.fields_by_name['state'].containing_oneof = _PTOSTATE.oneofs_by_name['decision']
_PTOSTATE.oneofs_by_name['decision'].fields.append(
  _PTOSTATE.fields_by_name['failure'])
_PTOSTATE.fields_by_name['failure'].containing_oneof = _PTOSTATE.oneofs_by_name['decision']
_HITCHV2REQUEST.fields_by_name['hitch'].enum_type = _HITCHCMD
_TRACTORVARIANTSTATE.fields_by_name['variant'].enum_type = _TRACTORVARIANTTYPE
_IGNITIONOFFRESPONSE.fields_by_name['failure'].message_type = _IGNITIONERRORCODES
_IGNITIONOFFRESPONSE.oneofs_by_name['decision'].fields.append(
  _IGNITIONOFFRESPONSE.fields_by_name['target_ignition_state'])
_IGNITIONOFFRESPONSE.fields_by_name['target_ignition_state'].containing_oneof = _IGNITIONOFFRESPONSE.oneofs_by_name['decision']
_IGNITIONOFFRESPONSE.oneofs_by_name['decision'].fields.append(
  _IGNITIONOFFRESPONSE.fields_by_name['failure'])
_IGNITIONOFFRESPONSE.fields_by_name['failure'].containing_oneof = _IGNITIONOFFRESPONSE.oneofs_by_name['decision']
_SETREQUEST.fields_by_name['gear'].message_type = _GEARSTATE
_SETREQUEST.fields_by_name['lights'].message_type = _LIGHTSSTATE
_SETREQUEST.fields_by_name['speed_control'].message_type = _SPEEDCONTROLSTATE
_SETREQUEST.fields_by_name['hitch'].message_type = _HITCHREQUEST
_SETREQUEST.fields_by_name['scv'].message_type = _SCVREQUEST
_SETREQUEST.fields_by_name['rpms'].message_type = _RPMDIALSTATE
_SETREQUEST.fields_by_name['front_pto'].message_type = _PTOCOMMANDSTATE
_SETREQUEST.fields_by_name['rear_pto'].message_type = _EMPTY
_SETREQUEST.fields_by_name['variant'].message_type = _TRACTORVARIANTSTATE
_SETREQUEST.fields_by_name['wheel_cal'].message_type = _WHEELANGLECALSTATE
_SETREQUEST.fields_by_name['ignition_off'].message_type = _EMPTY
_SETREQUEST.fields_by_name['hitch_V2'].message_type = _HITCHV2REQUEST
_SETREQUEST.fields_by_name['power_cycle'].message_type = _EMPTY
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['gear'])
_SETREQUEST.fields_by_name['gear'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['lights'])
_SETREQUEST.fields_by_name['lights'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['speed_control'])
_SETREQUEST.fields_by_name['speed_control'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['hitch'])
_SETREQUEST.fields_by_name['hitch'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['scv'])
_SETREQUEST.fields_by_name['scv'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['rpms'])
_SETREQUEST.fields_by_name['rpms'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['front_pto'])
_SETREQUEST.fields_by_name['front_pto'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['rear_pto'])
_SETREQUEST.fields_by_name['rear_pto'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['variant'])
_SETREQUEST.fields_by_name['variant'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['wheel_cal'])
_SETREQUEST.fields_by_name['wheel_cal'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['ignition_off'])
_SETREQUEST.fields_by_name['ignition_off'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['hitch_V2'])
_SETREQUEST.fields_by_name['hitch_V2'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREQUEST.oneofs_by_name['set'].fields.append(
  _SETREQUEST.fields_by_name['power_cycle'])
_SETREQUEST.fields_by_name['power_cycle'].containing_oneof = _SETREQUEST.oneofs_by_name['set']
_SETREPLY.fields_by_name['gear'].message_type = _GEARSTATE
_SETREPLY.fields_by_name['lights'].message_type = _LIGHTSSTATE
_SETREPLY.fields_by_name['speed_control'].message_type = _SPEEDCONTROLSTATE
_SETREPLY.fields_by_name['hitch'].message_type = _HITCHREPLY
_SETREPLY.fields_by_name['scv'].message_type = _SCVREPLY
_SETREPLY.fields_by_name['rpms'].message_type = _RPMDIALSTATE
_SETREPLY.fields_by_name['front_pto'].message_type = _PTOSTATE
_SETREPLY.fields_by_name['rear_pto'].message_type = _EMPTY
_SETREPLY.fields_by_name['variant'].message_type = _TRACTORVARIANTSTATE
_SETREPLY.fields_by_name['wheel_cal'].message_type = _WHEELANGLECALSTATE
_SETREPLY.fields_by_name['ignition_off'].message_type = _IGNITIONOFFRESPONSE
_SETREPLY.fields_by_name['hitch_V2'].message_type = _HITCHREPLY
_SETREPLY.fields_by_name['power_cycle'].message_type = _IGNITIONOFFRESPONSE
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['gear'])
_SETREPLY.fields_by_name['gear'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['lights'])
_SETREPLY.fields_by_name['lights'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['speed_control'])
_SETREPLY.fields_by_name['speed_control'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['hitch'])
_SETREPLY.fields_by_name['hitch'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['scv'])
_SETREPLY.fields_by_name['scv'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['rpms'])
_SETREPLY.fields_by_name['rpms'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['front_pto'])
_SETREPLY.fields_by_name['front_pto'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['rear_pto'])
_SETREPLY.fields_by_name['rear_pto'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['variant'])
_SETREPLY.fields_by_name['variant'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['wheel_cal'])
_SETREPLY.fields_by_name['wheel_cal'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['ignition_off'])
_SETREPLY.fields_by_name['ignition_off'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['hitch_V2'])
_SETREPLY.fields_by_name['hitch_V2'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_SETREPLY.oneofs_by_name['set'].fields.append(
  _SETREPLY.fields_by_name['power_cycle'])
_SETREPLY.fields_by_name['power_cycle'].containing_oneof = _SETREPLY.oneofs_by_name['set']
_GETREQUEST.fields_by_name['gear'].message_type = _EMPTY
_GETREQUEST.fields_by_name['lights'].message_type = _EMPTY
_GETREQUEST.fields_by_name['speed_control'].message_type = _EMPTY
_GETREQUEST.fields_by_name['rpms'].message_type = _EMPTY
_GETREQUEST.fields_by_name['front_pto'].message_type = _EMPTY
_GETREQUEST.fields_by_name['rear_pto'].message_type = _EMPTY
_GETREQUEST.fields_by_name['variant'].message_type = _EMPTY
_GETREQUEST.fields_by_name['wheel_cal'].message_type = _EMPTY
_GETREQUEST.fields_by_name['fuel_level'].message_type = _EMPTY
_GETREQUEST.fields_by_name['engine_temp'].message_type = _EMPTY
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['gear'])
_GETREQUEST.fields_by_name['gear'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['lights'])
_GETREQUEST.fields_by_name['lights'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['speed_control'])
_GETREQUEST.fields_by_name['speed_control'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['rpms'])
_GETREQUEST.fields_by_name['rpms'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['front_pto'])
_GETREQUEST.fields_by_name['front_pto'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['rear_pto'])
_GETREQUEST.fields_by_name['rear_pto'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['variant'])
_GETREQUEST.fields_by_name['variant'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['wheel_cal'])
_GETREQUEST.fields_by_name['wheel_cal'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['fuel_level'])
_GETREQUEST.fields_by_name['fuel_level'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREQUEST.oneofs_by_name['get'].fields.append(
  _GETREQUEST.fields_by_name['engine_temp'])
_GETREQUEST.fields_by_name['engine_temp'].containing_oneof = _GETREQUEST.oneofs_by_name['get']
_GETREPLY.fields_by_name['gear'].message_type = _GEARSTATE
_GETREPLY.fields_by_name['lights'].message_type = _LIGHTSSTATE
_GETREPLY.fields_by_name['speed_control'].message_type = _SPEEDCONTROLSTATE
_GETREPLY.fields_by_name['rpms'].message_type = _RPMDIALSTATE
_GETREPLY.fields_by_name['rear_pto'].message_type = _EMPTY
_GETREPLY.fields_by_name['variant'].message_type = _TRACTORVARIANTSTATE
_GETREPLY.fields_by_name['wheel_cal'].message_type = _WHEELANGLECALSTATE
_GETREPLY.fields_by_name['fuel_level'].message_type = _FUELLEVEL
_GETREPLY.fields_by_name['engine_temp'].message_type = _ENGINETEMP
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['gear'])
_GETREPLY.fields_by_name['gear'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['lights'])
_GETREPLY.fields_by_name['lights'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['speed_control'])
_GETREPLY.fields_by_name['speed_control'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['rpms'])
_GETREPLY.fields_by_name['rpms'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['front_pto'])
_GETREPLY.fields_by_name['front_pto'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['rear_pto'])
_GETREPLY.fields_by_name['rear_pto'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['variant'])
_GETREPLY.fields_by_name['variant'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['wheel_cal'])
_GETREPLY.fields_by_name['wheel_cal'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['fuel_level'])
_GETREPLY.fields_by_name['fuel_level'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_GETREPLY.oneofs_by_name['get'].fields.append(
  _GETREPLY.fields_by_name['engine_temp'])
_GETREPLY.fields_by_name['engine_temp'].containing_oneof = _GETREPLY.oneofs_by_name['get']
_REQUEST.fields_by_name['set'].message_type = _SETREQUEST
_REQUEST.fields_by_name['get'].message_type = _GETREQUEST
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['set'])
_REQUEST.fields_by_name['set'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REQUEST.oneofs_by_name['request'].fields.append(
  _REQUEST.fields_by_name['get'])
_REQUEST.fields_by_name['get'].containing_oneof = _REQUEST.oneofs_by_name['request']
_REPLY.fields_by_name['set'].message_type = _SETREPLY
_REPLY.fields_by_name['get'].message_type = _GETREPLY
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['set'])
_REPLY.fields_by_name['set'].containing_oneof = _REPLY.oneofs_by_name['reply']
_REPLY.oneofs_by_name['reply'].fields.append(
  _REPLY.fields_by_name['get'])
_REPLY.fields_by_name['get'].containing_oneof = _REPLY.oneofs_by_name['reply']
DESCRIPTOR.message_types_by_name['GearState'] = _GEARSTATE
DESCRIPTOR.message_types_by_name['LightsState'] = _LIGHTSSTATE
DESCRIPTOR.message_types_by_name['SpeedControlState'] = _SPEEDCONTROLSTATE
DESCRIPTOR.message_types_by_name['RpmDialState'] = _RPMDIALSTATE
DESCRIPTOR.message_types_by_name['PTOFailType'] = _PTOFAILTYPE
DESCRIPTOR.message_types_by_name['PTOCommandState'] = _PTOCOMMANDSTATE
DESCRIPTOR.message_types_by_name['PtoState'] = _PTOSTATE
DESCRIPTOR.message_types_by_name['HitchV2Request'] = _HITCHV2REQUEST
DESCRIPTOR.message_types_by_name['HitchRequest'] = _HITCHREQUEST
DESCRIPTOR.message_types_by_name['HitchReply'] = _HITCHREPLY
DESCRIPTOR.message_types_by_name['ScvRequest'] = _SCVREQUEST
DESCRIPTOR.message_types_by_name['ScvReply'] = _SCVREPLY
DESCRIPTOR.message_types_by_name['TractorVariantState'] = _TRACTORVARIANTSTATE
DESCRIPTOR.message_types_by_name['WheelAngleCalState'] = _WHEELANGLECALSTATE
DESCRIPTOR.message_types_by_name['FuelLevel'] = _FUELLEVEL
DESCRIPTOR.message_types_by_name['EngineTemp'] = _ENGINETEMP
DESCRIPTOR.message_types_by_name['IgnitionErrorCodes'] = _IGNITIONERRORCODES
DESCRIPTOR.message_types_by_name['IgnitionOffResponse'] = _IGNITIONOFFRESPONSE
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['SetRequest'] = _SETREQUEST
DESCRIPTOR.message_types_by_name['SetReply'] = _SETREPLY
DESCRIPTOR.message_types_by_name['GetRequest'] = _GETREQUEST
DESCRIPTOR.message_types_by_name['GetReply'] = _GETREPLY
DESCRIPTOR.message_types_by_name['Request'] = _REQUEST
DESCRIPTOR.message_types_by_name['Reply'] = _REPLY
DESCRIPTOR.enum_types_by_name['TractorVariantType'] = _TRACTORVARIANTTYPE
DESCRIPTOR.enum_types_by_name['Gear'] = _GEAR
DESCRIPTOR.enum_types_by_name['Lights'] = _LIGHTS
DESCRIPTOR.enum_types_by_name['HitchCmd'] = _HITCHCMD
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GearState = _reflection.GeneratedProtocolMessageType('GearState', (_message.Message,), {
  'DESCRIPTOR' : _GEARSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.GearState)
  })
_sym_db.RegisterMessage(GearState)

LightsState = _reflection.GeneratedProtocolMessageType('LightsState', (_message.Message,), {
  'DESCRIPTOR' : _LIGHTSSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.LightsState)
  })
_sym_db.RegisterMessage(LightsState)

SpeedControlState = _reflection.GeneratedProtocolMessageType('SpeedControlState', (_message.Message,), {
  'DESCRIPTOR' : _SPEEDCONTROLSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.SpeedControlState)
  })
_sym_db.RegisterMessage(SpeedControlState)

RpmDialState = _reflection.GeneratedProtocolMessageType('RpmDialState', (_message.Message,), {
  'DESCRIPTOR' : _RPMDIALSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.RpmDialState)
  })
_sym_db.RegisterMessage(RpmDialState)

PTOFailType = _reflection.GeneratedProtocolMessageType('PTOFailType', (_message.Message,), {
  'DESCRIPTOR' : _PTOFAILTYPE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.PTOFailType)
  })
_sym_db.RegisterMessage(PTOFailType)

PTOCommandState = _reflection.GeneratedProtocolMessageType('PTOCommandState', (_message.Message,), {
  'DESCRIPTOR' : _PTOCOMMANDSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.PTOCommandState)
  })
_sym_db.RegisterMessage(PTOCommandState)

PtoState = _reflection.GeneratedProtocolMessageType('PtoState', (_message.Message,), {
  'DESCRIPTOR' : _PTOSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.PtoState)
  })
_sym_db.RegisterMessage(PtoState)

HitchV2Request = _reflection.GeneratedProtocolMessageType('HitchV2Request', (_message.Message,), {
  'DESCRIPTOR' : _HITCHV2REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.HitchV2Request)
  })
_sym_db.RegisterMessage(HitchV2Request)

HitchRequest = _reflection.GeneratedProtocolMessageType('HitchRequest', (_message.Message,), {
  'DESCRIPTOR' : _HITCHREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.HitchRequest)
  })
_sym_db.RegisterMessage(HitchRequest)

HitchReply = _reflection.GeneratedProtocolMessageType('HitchReply', (_message.Message,), {
  'DESCRIPTOR' : _HITCHREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.HitchReply)
  })
_sym_db.RegisterMessage(HitchReply)

ScvRequest = _reflection.GeneratedProtocolMessageType('ScvRequest', (_message.Message,), {
  'DESCRIPTOR' : _SCVREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.ScvRequest)
  })
_sym_db.RegisterMessage(ScvRequest)

ScvReply = _reflection.GeneratedProtocolMessageType('ScvReply', (_message.Message,), {
  'DESCRIPTOR' : _SCVREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.ScvReply)
  })
_sym_db.RegisterMessage(ScvReply)

TractorVariantState = _reflection.GeneratedProtocolMessageType('TractorVariantState', (_message.Message,), {
  'DESCRIPTOR' : _TRACTORVARIANTSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.TractorVariantState)
  })
_sym_db.RegisterMessage(TractorVariantState)

WheelAngleCalState = _reflection.GeneratedProtocolMessageType('WheelAngleCalState', (_message.Message,), {
  'DESCRIPTOR' : _WHEELANGLECALSTATE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.WheelAngleCalState)
  })
_sym_db.RegisterMessage(WheelAngleCalState)

FuelLevel = _reflection.GeneratedProtocolMessageType('FuelLevel', (_message.Message,), {
  'DESCRIPTOR' : _FUELLEVEL,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.FuelLevel)
  })
_sym_db.RegisterMessage(FuelLevel)

EngineTemp = _reflection.GeneratedProtocolMessageType('EngineTemp', (_message.Message,), {
  'DESCRIPTOR' : _ENGINETEMP,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.EngineTemp)
  })
_sym_db.RegisterMessage(EngineTemp)

IgnitionErrorCodes = _reflection.GeneratedProtocolMessageType('IgnitionErrorCodes', (_message.Message,), {
  'DESCRIPTOR' : _IGNITIONERRORCODES,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.IgnitionErrorCodes)
  })
_sym_db.RegisterMessage(IgnitionErrorCodes)

IgnitionOffResponse = _reflection.GeneratedProtocolMessageType('IgnitionOffResponse', (_message.Message,), {
  'DESCRIPTOR' : _IGNITIONOFFRESPONSE,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.IgnitionOffResponse)
  })
_sym_db.RegisterMessage(IgnitionOffResponse)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.Empty)
  })
_sym_db.RegisterMessage(Empty)

SetRequest = _reflection.GeneratedProtocolMessageType('SetRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.SetRequest)
  })
_sym_db.RegisterMessage(SetRequest)

SetReply = _reflection.GeneratedProtocolMessageType('SetReply', (_message.Message,), {
  'DESCRIPTOR' : _SETREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.SetReply)
  })
_sym_db.RegisterMessage(SetReply)

GetRequest = _reflection.GeneratedProtocolMessageType('GetRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETREQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.GetRequest)
  })
_sym_db.RegisterMessage(GetRequest)

GetReply = _reflection.GeneratedProtocolMessageType('GetReply', (_message.Message,), {
  'DESCRIPTOR' : _GETREPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.GetReply)
  })
_sym_db.RegisterMessage(GetReply)

Request = _reflection.GeneratedProtocolMessageType('Request', (_message.Message,), {
  'DESCRIPTOR' : _REQUEST,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.Request)
  })
_sym_db.RegisterMessage(Request)

Reply = _reflection.GeneratedProtocolMessageType('Reply', (_message.Message,), {
  'DESCRIPTOR' : _REPLY,
  '__module__' : 'generated.lib.drivers.nanopb.proto.ots_tractor_pb2'
  # @@protoc_insertion_point(class_scope:ots_tractor.Reply)
  })
_sym_db.RegisterMessage(Reply)


DESCRIPTOR._options = None
_SETREQUEST.fields_by_name['rear_pto']._options = None
_SETREPLY.fields_by_name['rear_pto']._options = None
_GETREQUEST.fields_by_name['rear_pto']._options = None
_GETREPLY.fields_by_name['rear_pto']._options = None
# @@protoc_insertion_point(module_scope)
