from enum import Enum
from typing import List, Optional

import generated.proto.rtc.jobs_pb2 as jobs_pb


class TaskType(str, Enum):
    # This must match the string literals defined in proto
    sequence = "sequence"
    manual = "manual"
    go_to_and_face = "go_to_and_face"
    follow_path = "follow_path"
    tractor_state = "tractor_state"
    laser_weed = "laser_weed"
    stop_autonomy = "stop_autonomy"
    go_to_reversible_path = "go_to_reversible_path"


class TaskWrapper:
    def __init__(self, task: jobs_pb.Task) -> None:
        self._task = task
        self._complete = False
        self.__children: List["TaskWrapper"] = []
        if self._task.WhichOneof("task_details") == TaskType.sequence:
            for task in self._task.sequence.items:
                tw = TaskWrapper(task)
                self.__children.append(tw)
            assert len(self.__children) > 0, "Invalid task sequence"

    def get_active(self) -> Optional[jobs_pb.Task]:
        if self.__children:
            return self.__children[0].get_active()
        elif not self._complete:
            return self._task
        else:
            return None

    def get_active_id_chain(self) -> List[int]:
        ids = [self._task.id]
        if self.__children:
            ids.extend(self.__children[0].get_active_id_chain())
        return ids

    def complete(self) -> None:
        if self.__complete():
            self._complete = True

    def __complete(self) -> bool:
        if self.__children:
            done = self.__children[0].__complete()
            if done:
                self.__children.pop(0)
            return False
        else:
            return True
