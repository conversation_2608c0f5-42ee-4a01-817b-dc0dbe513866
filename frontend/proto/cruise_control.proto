syntax = "proto3";

package carbon.frontend.cruise_control;
option go_package = "proto/frontend";
import "frontend/proto/util.proto";


enum CruiseBlocker {
  NONE = 0;
  ACTIVE_ALARM = 1;
  NOT_WEEDING = 2;
  TRACTOR_NOT_SAFE = 3;
}
message CruiseState{
  double commanded_velocity = 1;
  CruiseBlocker blocker = 2;
}
message GetNextCruiseStateResponse {
  carbon.frontend.util.Timestamp ts = 1;
  CruiseState state = 2;
}

service CruiseControlService {
    rpc GetNextCruiseState(carbon.frontend.util.Timestamp) returns (GetNextCruiseStateResponse);
}