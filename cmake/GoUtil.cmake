function(add_go_executable OUT_NAME SRC_FILE)
  get_filename_component(SRC_FILE_ABS ${SRC_FILE} ABSOLUTE)
  add_custom_target(${OUT_NAME})
  add_custom_command(TARGET ${OUT_NAME}
                    POST_BUILD
                    COMMAND env GOPATH=${GOPATH} go build
                    -o "${CMAKE_SOURCE_DIR}/bin/${OUT_NAME}"
                    ${CMAKE_GO_FLAGS} ${SRC_FILE_ABS}
                    WORKING_DIRECTORY ${CMAKE_CURRENT_LIST_DIR})
  foreach(DEP ${ARGN})
    add_dependencies(${OUT_NAME} ${DEP})
  endforeach()
  add_custom_target(${OUT_NAME}_all ALL DEPENDS ${OUT_NAME})
endfunction(add_go_executable)

function(add_go_swig_file SRC_FILE)
  get_filename_component(SRC_FILE_ABS ${SRC_FILE} ABSOLUTE)
  get_filename_component(SOURCE_FILE_PATH ${SRC_FILE_ABS} DIRECTORY)
  get_filename_component(SOURCE_FILE_NAME ${SRC_FILE_ABS} NAME_WE)
  set(GO_FILE ${CMAKE_CURRENT_LIST_DIR}/${SOURCE_FILE_NAME}.go)
  set(SWIG_TARGET go_swig_${SOURCE_FILE_NAME})
  set(GENERATED_FILES ${CMAKE_CURRENT_LIST_DIR}/${SOURCE_FILE_NAME}_wrap.cxx ${CMAKE_CURRENT_LIST_DIR}/${SOURCE_FILE_NAME}.go)
  foreach(DEP ${ARGN})
    list(APPEND DEPS "${CMAKE_SOURCE_DIR}/${DEP}")
  endforeach()
  add_custom_command(OUTPUT ${GENERATED_FILES}
                    COMMAND env GOPATH=${GOPATH} swig -go -cgo -c++ -intgosize 64
                    ${SRC_FILE_ABS} && cd golang && go fmt ${GO_FILE}
                    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
                    DEPENDS ${SRC_FILE_ABS} ${DEPS})
  set_source_files_properties(${GENERATED_FILES} PROPERTIES GENERATED TRUE)
  add_custom_target(${SWIG_TARGET} ALL DEPENDS ${GENERATED_FILES})
endfunction(add_go_swig_file)