execute_process(COMMAND which grpc_cpp_plugin
                OUTPUT_VARIABLE GRPC_PLUGIN_PATH OUTPUT_STRIP_TRAILING_WHITESPACE)

include(CMakePrintHelpers)

function(CompileProto SOURCE_FILE BUILT_PATH_OUT)
    get_filename_component(SOURCE_FILE ${SOURCE_FILE} ABSOLUTE)
    get_filename_component(SOURCE_FILE_PATH ${SOURCE_FILE} DIRECTORY)
    get_filename_component(SOURCE_FILE_NAME ${SOURCE_FILE} NAME_WE)
    file(RELATIVE_PATH REL_PATH ${CMAKE_SOURCE_DIR} ${SOURCE_FILE_PATH})
    set(GENERATED_DIR ${CMAKE_SOURCE_DIR}/generated)
    set(GENERATED_PATH ${CMAKE_SOURCE_DIR}/generated/${REL_PATH})
    set(GOLANG_GENERATED_DIR ${CMAKE_SOURCE_DIR}/golang/generated)
    cmake_parse_arguments(PARSE_ARGV 2 ARGS "" "GOPKG" "LANGS")

    if (DEFINED ARGS_GOPKG)
        string(REPLACE "/" "_" ARGS_GOPKG_RE ${ARGS_GOPKG})
        set(GOPKG_TARGET gopkg_proto_${ARGS_GOPKG_RE})
        if (NOT TARGET ${GOPKG_TARGET})
            add_custom_target(${GOPKG_TARGET})
        endif()
    endif()

    string(REPLACE "/" "_" TARGET_PATH ${REL_PATH})
    # Set value for output parameter
    set(${BUILT_PATH_OUT} ${GENERATED_PATH} PARENT_SCOPE)
    set(GENERATED_TARGET generate_${TARGET_PATH}_${SOURCE_FILE_NAME})
    add_custom_target(${GENERATED_TARGET})
    # cmake_print_variables(GENERATED_TARGET)
    foreach(arg IN LISTS ARGS_LANGS)
        if (arg STREQUAL "python")
            set(EXTENSION "_pb2.py")
        elseif(arg STREQUAL "mypy")
            set(EXTENSION "_pb2.pyi")
        elseif(arg STREQUAL "grpc_python")
            set(EXTENSION "_pb2_grpc.py")
        elseif(arg STREQUAL "cpp")
            set(EXTENSION ".pb")
        elseif(arg STREQUAL "grpc")
            set(EXTENSION ".grpc.pb")
        elseif(arg STREQUAL "go")
            set(EXTENSION ".pb.go")
        elseif(arg STREQUAL "go-grpc")
            set(EXTENSION "_grpc.pb.go")
        else()
            message(FATAL_ERROR "Unsupported proto output type ${arg}")
        endif()

        if (arg STREQUAL "mypy" OR arg STREQUAL "python" OR arg STREQUAL "grpc_python")
            set(GENERATED_FILES ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION})
            add_custom_command(OUTPUT ${GENERATED_FILES}
                DEPENDS ${SOURCE_FILE}
                COMMAND mkdir -p ${GENERATED_DIR}
                COMMAND python ARGS -m grpc_tools.protoc --experimental_allow_proto3_optional=True ${SOURCE_FILE}
                    --proto_path=${CMAKE_SOURCE_DIR} --${arg}_out=${GENERATED_DIR}
                # Fix protobuf imports
                COMMAND sed -i -e 's/from lib./from generated.lib./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from deeplearning./from generated.deeplearning./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from cv./from generated.cv./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from core./from generated.core./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from frontend./from generated.frontend./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from portal./from generated.portal./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from veselka./from generated.veselka./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from weed_tracking./from generated.weed_tracking./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from hardware_manager./from generated.hardware_manager./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from geo_service./from generated.geo_service./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from metrics./from generated.metrics./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from calibration./from generated.calibration./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from golang\\.sim\\./from generated.golang.sim./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from proto.weed_tracking/from generated.proto.weed_tracking/' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from proto.calibration/from generated.proto.calibration/' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from recorder.proto/from generated.recorder.proto/' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from golang\\.simulator\\./from generated.golang.simulator./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from proto./from generated.proto./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from tractor_ctl./from generated.tractor_ctl./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from deck./from generated.deck./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}
                COMMAND sed -i -e 's/from category./from generated.category./' ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION})
        elseif(arg STREQUAL "go" OR arg STREQUAL "go-grpc")
            set(GENERATED_FILES ${GOLANG_GENERATED_DIR}/${ARGS_GOPKG}/${SOURCE_FILE_NAME}${EXTENSION})
            set(ERR_REDIRECT "2>/dev/null")
            add_custom_command(OUTPUT ${GENERATED_FILES}
                DEPENDS ${SOURCE_FILE}
                COMMAND mkdir -p ${GOLANG_GENERATED_DIR}
                COMMAND protoc ${SOURCE_FILE}
                    --plugin=protoc-gen-grpc=${GRPC_PLUGIN_PATH}
                    --proto_path=${CMAKE_SOURCE_DIR} --${arg}_out=${GOLANG_GENERATED_DIR} --experimental_allow_proto3_optional=True
                # Fix protobuf imports
                COMMAND sed -i -s -e 's|portal \"proto|portal \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|frontend \"proto|frontend \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|logging \"proto|logging \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|aimbot \"proto|aimbot \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|cv \"proto|cv \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|weed_tracking \"proto|weed_tracking \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|startup_task \"proto|startup_task \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|recorder \"proto|recorder \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|thinning \"proto|thinning \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|metrics_aggregator \"proto|metrics_aggregator \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|metrics \"proto|metrics \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|almanac \"proto|almanac \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|target_velocity_estimator \"proto|target_velocity_estimator \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|types \"proto|types \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|geo \"proto|geo \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|rtc \"proto|rtc \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|hardware_manager \"proto|hardware_manager \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|geo_service \"proto|geo_service \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|category \"proto|category \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
                COMMAND sed -i -s -e 's|robot_syncer \"proto|robot_syncer \"github.com\\/carbonrobotics\\/robot\\/golang\\/generated\\/proto|' ${GENERATED_FILES} "${ERR_REDIRECT}" || true
            )
        else()
            if(arg STREQUAL "cpp" OR arg STREQUAL "grpc")
                set(GENERATED_FILES ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}.h ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION}.cc)
            else()
                set(GENERATED_FILES ${GENERATED_PATH}/${SOURCE_FILE_NAME}${EXTENSION})
            endif()
            add_custom_command(OUTPUT ${GENERATED_FILES}
                DEPENDS ${SOURCE_FILE}
                COMMAND mkdir -p ${GENERATED_DIR}
                COMMAND protoc ${SOURCE_FILE}
                    --plugin=protoc-gen-grpc=${GRPC_PLUGIN_PATH}
                    --proto_path=${CMAKE_SOURCE_DIR} --${arg}_out=${GENERATED_DIR} --experimental_allow_proto3_optional=True)
        endif()
        set_source_files_properties(${GENERATED_FILES} PROPERTIES GENERATED TRUE)
        add_custom_target(generate_${TARGET_PATH}_${SOURCE_FILE_NAME}_${arg} ALL DEPENDS ${GENERATED_FILES})
        add_dependencies(generated generate_${TARGET_PATH}_${SOURCE_FILE_NAME}_${arg})
        add_dependencies(${GENERATED_TARGET} generate_${TARGET_PATH}_${SOURCE_FILE_NAME}_${arg})
        if (arg STREQUAL "go" OR arg STREQUAL "go-grpc" AND DEFINED ARGS_GOPKG)
            add_dependencies(${GOPKG_TARGET} generate_${TARGET_PATH}_${SOURCE_FILE_NAME}_${arg})
        endif()
    endforeach()
endfunction(CompileProto)
