import asyncio
import logging

from calibration.server import CalibrationServer
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name

LOG = get_logger(__name__)


async def main() -> None:
    LOG.info("Calibration Service Starting...")
    calibration_service = CalibrationServer(61009)
    await calibration_service.run()


if __name__ == "__main__":
    logging.basicConfig(level="INFO")
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result()
