from typing import Optional, cast

import grpc

from generated.calibration.proto.color_calibration_pb2 import Empty, Request, Response
from generated.calibration.proto.color_calibration_pb2_grpc import ColorCalibrationServiceStub
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class CalibrationClient:
    def __init__(self, hostname: str = "localhost", port: int = 61009):
        self._addr = f"{hostname}:{port}"
        self._channel = None
        self._color_calibration_stub: Optional[ColorCalibrationServiceStub] = None

    def _get_color_calibration_stub(self) -> ColorCalibrationServiceStub:
        if self._color_calibration_stub is None:
            if self._channel is None:
                self._channel = grpc.insecure_channel(self._addr)
            self._color_calibration_stub = ColorCalibrationServiceStub(self._channel)
        return self._color_calibration_stub

    def reset(self) -> None:
        self._color_calibration_stub = None
        self._channel = None

    def reset_color(self) -> bool:
        try:
            req = Empty()
            resp: Response = self._get_color_calibration_stub().Reset(req)
            assert resp.success
            LOG.info("Reset succeeded")
            return cast(bool, resp.success)
        except Exception as e:
            LOG.error(f"Reset failed {e}")
            return False

    def initialize(self, row_id: int, row_type: str) -> bool:
        try:
            req = Request(row_id=row_id, row_type=row_type)
            resp: Response = self._get_color_calibration_stub().InitializeColorCalibration(req)
            assert resp.success
            LOG.info("Initialization succeeded")
            return cast(bool, resp.success)
        except Exception as e:
            LOG.error(f"Initialization failed {e}")
            return False

    def center(self, row_id: int, row_type: str) -> bool:
        try:
            req = Request(row_id=row_id, row_type=row_type)
            resp: Response = self._get_color_calibration_stub().CenterTargetCameras(req)
            assert resp.success
            LOG.info("Center succeeded")
            return resp.success
        except Exception as e:
            LOG.error(f"Center failed {e}")
            return False

    def whitebalance(self, row_id: int, row_type: str) -> bool:
        try:
            req = Request(row_id=row_id, row_type=row_type)
            resp: Response = self._get_color_calibration_stub().SetAutoWhitebalance(req)
            assert resp.success
            LOG.info("Whitebalance succeeded")
            return resp.success
        except Exception as e:
            LOG.error(f"Whitebalance failed {e}")
            return False

    def save(self, row_id: int, row_type: str) -> bool:
        try:
            req = Request(row_id=row_id, row_type=row_type)
            resp: Response = self._get_color_calibration_stub().SaveToConfig(req)
            assert resp.success
            LOG.info("Save succeeded")
            return resp.success
        except Exception as e:
            LOG.error(f"Save failed {e}")
            return False
