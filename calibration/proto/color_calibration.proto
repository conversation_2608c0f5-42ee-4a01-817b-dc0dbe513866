syntax = "proto3";

package calibration.proto;
option go_package = "proto/calibration";

message Empty {}

message Request {
    int64 row_id = 1;
    string row_type = 2;
}

message Response {
    bool success = 1; 
}

service ColorCalibrationService {
    rpc InitializeColorCalibration(Request) returns (Response) {}
    rpc CenterTargetCameras(Request) returns (Response) {}
    rpc SetAutoWhitebalance(Request) returns (Response) {}
    rpc SaveToConfig(Request) returns (Response) {}
    rpc Reset(Empty) returns (Response) {}
}