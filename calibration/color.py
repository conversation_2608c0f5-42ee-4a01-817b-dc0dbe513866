from enum import Enum
from typing import Optional

import grpc

import generated.calibration.proto.color_calibration_pb2 as color_calibration_pb
import generated.calibration.proto.color_calibration_pb2_grpc as color_calibration_grpc
from lib.common.generation import GENERATION
from lib.common.logging import get_logger
from tools.calibration.color_calibration import ColorCalibrator

LOG = get_logger(__name__)


class Step(Enum):
    INITIALIZE = 1
    CENTER = 2
    WHITEBALANCE = 3
    SAVE = 4


class ColorCalibrationServicer(color_calibration_grpc.ColorCalibrationServiceServicer):
    def __init__(self) -> None:
        self._reset()

    def _validate_row_id_and_type(self, row_id: int, row_type: str, next_step: Step) -> bool:
        assert row_id >= 1 and row_id <= 3, "Row id should be 1 through 3"
        if row_type == GENERATION.BUD.to_str():
            assert row_id == 1
        assert (
            row_type in GENERATION.generation_strs()
        ), f"Row type needs to be one of [{', '.join(GENERATION.generation_strs())}]"
        assert next_step == self._next_step
        return True

    def _reset(self) -> None:
        self._current_row_id: int = -1
        self._current_row_type: str = ""
        self._color_calibrator: Optional[ColorCalibrator] = None
        self._next_step: Step = Step.INITIALIZE

    async def Reset(
        self, request: color_calibration_pb.Empty, context: grpc.ServicerContext
    ) -> color_calibration_pb.Response:
        try:
            self._reset()
            context.set_code(grpc.StatusCode.OK)
            return color_calibration_pb.Response(success=True)
        except Exception as e:
            LOG.error(f"Error reseting color calibration: {e}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            return color_calibration_pb.Response(success=False)

    async def InitializeColorCalibration(
        self, request: color_calibration_pb.Request, context: grpc.ServicerContext
    ) -> color_calibration_pb.Response:
        assert self._current_row_id == -1 and self._color_calibrator is None
        row_id = request.row_id
        row_type = request.row_type
        self._validate_row_id_and_type(row_id, row_type, Step.INITIALIZE)
        self._current_row_id = row_id
        self._current_row_type = row_type
        try:
            self._color_calibrator = ColorCalibrator(row_id, GENERATION.from_str_with_default(row_type, GENERATION.BUD))
            self._next_step = Step.CENTER
            context.set_code(grpc.StatusCode.OK)
            return color_calibration_pb.Response(success=True)
        except Exception as e:
            LOG.error(f"Error initializing color calibration: {e}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            return color_calibration_pb.Response(success=False)

    async def CenterTargetCameras(
        self, request: color_calibration_pb.Request, context: grpc.ServicerContext
    ) -> color_calibration_pb.Response:
        row_id = request.row_id
        row_type = request.row_type
        self._validate_row_id_and_type(row_id, row_type, Step.CENTER)
        assert self._current_row_id == row_id and self._current_row_type == row_type
        assert self._color_calibrator is not None
        try:
            await self._color_calibrator.center_target_cameras()
            self._next_step = Step.WHITEBALANCE
            context.set_code(grpc.StatusCode.OK)
            return color_calibration_pb.Response(success=True)
        except Exception as e:
            LOG.error(f"Error centering target cameras: {e}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            return color_calibration_pb.Response(success=False)

    async def SetAutoWhitebalance(
        self, request: color_calibration_pb.Request, context: grpc.ServicerContext
    ) -> color_calibration_pb.Response:
        row_id = request.row_id
        row_type = request.row_type
        self._validate_row_id_and_type(row_id, row_type, Step.WHITEBALANCE)
        assert self._current_row_id == row_id and self._current_row_type == row_type
        assert self._color_calibrator is not None
        try:
            await self._color_calibrator.set_auto_whitebalance()
            self._next_step = Step.SAVE
            context.set_code(grpc.StatusCode.OK)
            return color_calibration_pb.Response(success=True)
        except Exception as e:
            LOG.error(f"Error setting auto-whitebalance: {e}")
            self._reset()
            context.set_code(grpc.StatusCode.UNKNOWN)
            return color_calibration_pb.Response(success=False)

    async def SaveToConfig(
        self, request: color_calibration_pb.Request, context: grpc.ServicerContext
    ) -> color_calibration_pb.Response:
        row_id = request.row_id
        row_type = request.row_type
        self._validate_row_id_and_type(row_id, row_type, Step.SAVE)
        assert self._current_row_id == row_id and self._current_row_type == row_type
        assert self._color_calibrator is not None
        try:
            await self._color_calibrator.save_to_config()
            self._next_step = Step.INITIALIZE
            context.set_code(grpc.StatusCode.OK)
            return color_calibration_pb.Response(success=True)
        except Exception as e:
            LOG.error(f"Error saving to config: {e}")
            context.set_code(grpc.StatusCode.UNKNOWN)
            return color_calibration_pb.Response(success=False)
