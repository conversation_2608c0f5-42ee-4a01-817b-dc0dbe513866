import grpc

import generated.calibration.proto.color_calibration_pb2_grpc as color_calibration_pb2_grpc
import lib.common.logging
from calibration.color import ColorCalibrationServicer


class CalibrationServer:
    def __init__(self, port: int) -> None:
        self._port = port
        self._server = grpc.aio.server()
        self._color_calibartion_servicer = ColorCalibrationServicer()
        color_calibration_pb2_grpc.add_ColorCalibrationServiceServicer_to_server(
            self._color_calibartion_servicer, self._server
        )
        self._loggingServicer = lib.common.logging.LoggingGRPCServicer(self._server, "calibration.log")
        self._server.add_insecure_port(f"[::]:{self._port}")

    async def run(self) -> None:
        await self._server.start()
        await self._server.wait_for_termination()
