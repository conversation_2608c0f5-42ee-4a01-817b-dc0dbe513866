add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(almanac SHARED ${SOURCES})
target_compile_definitions(almanac PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(almanac PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib utils redis_client almanac_proto)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(almanac_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(almanac_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(almanac_python PUBLIC almanac)
set_target_properties(almanac_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)