#pragma once

#include <lib/common/redis/redis_client.hpp>

#include <atomic>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>
namespace carbon {
namespace aimbot::almanac {
class DiscriminatorTypeCategory;
} // namespace aimbot::almanac
namespace almanac {
class Discriminator {
public:
  Discriminator(const Discriminator &) = delete;
  Discriminator &operator=(const Discriminator &) = delete;
  static Discriminator &get();
  static constexpr std::string_view DiscriminatorCfgs = "/almanac/discriminator/config";
  static constexpr std::string_view DiscriminatorActive = "/almanac/discriminator/active";
  struct Data {
    bool ignorable;
    bool avoid;
    Data() : ignorable(false), avoid(false) {}
    Data(bool _ignorable, bool _avoid) : ignorable(_ignorable), avoid(_avoid) {}
  };
  Data get_data(const std::string &cls, float size);
  inline void reload_required() {
    std::unique_lock<std::shared_mutex> lock(mut_);
    up_to_date_ = false;
  }

private:
  class TypeCategory {
  public:
    TypeCategory(int expected_size, const std::string &name, const aimbot::almanac::DiscriminatorTypeCategory &dtc);
    Data operator()(float size) const;

  private:
    const std::string name_;
    std::vector<Data> trusts_;
  };

  bool up_to_date_;

  std::unordered_map<std::string, TypeCategory> types_;
  mutable std::shared_mutex mut_;

  Discriminator();
  void reload();
};
} // namespace almanac
} // namespace carbon