#pragma once

#include <lib/common/redis/redis_client.hpp>

#include <atomic>
#include <shared_mutex>
#include <string>
#include <unordered_map>
#include <vector>
namespace carbon {
namespace aimbot::almanac {
class ModelinatorTypeCategory;
} // namespace aimbot::almanac
namespace almanac {
class Modelinator {
public:
  Modelinator(const Modelinator &) = delete;
  Modelinator &operator=(const Modelinator &) = delete;
  static Modelinator &get();
  static constexpr std::string_view ModelinatorCfgs = "/almanac/modelinator/config";
  static constexpr std::string_view ActiveModelId = "/almanac/active_model_id";
  struct Data {
    float min_doo;
    float weeding_threshold;
    float thinning_threshold;
    float banding_threshold;
    bool usable;
    Data() : min_doo(0.0f), weeding_threshold(0.0f), thinning_threshold(0.0f), banding_threshold(0.0f), usable(true) {}
    Data(float _min_doo, float _weeding_threshold, float _thinning_threshold, float _banding_threshold)
        : min_doo(_min_doo), weeding_threshold(_weeding_threshold), thinning_threshold(_thinning_threshold),
          banding_threshold(_banding_threshold), usable(true) {}
    Data &operator+=(const Data &rhs) {
      min_doo += rhs.min_doo;
      weeding_threshold += rhs.weeding_threshold;
      thinning_threshold += rhs.thinning_threshold;
      banding_threshold += rhs.banding_threshold;
      // Do not care about usable here
      return *this;
    }
    Data &operator/=(size_t count) {
      float cnt = static_cast<float>(count);
      min_doo /= cnt;
      weeding_threshold /= cnt;
      thinning_threshold /= cnt;
      banding_threshold /= cnt;
      // Do not care about usable here
      return *this;
    }
  };
  Data get_data(const std::string &cls, float size);
  Data get_data(const std::string &cls, float size, bool is_weed, float confidence);
  inline void reload_required() {
    std::unique_lock<std::shared_mutex> lock(mut_);
    up_to_date_ = false;
  }
  static std::string get_id(std::string_view model_id, std::string_view crop_id);

private:
  class TypeCategory {
  public:
    TypeCategory(int expected_size, const std::string &name, const aimbot::almanac::ModelinatorTypeCategory &mtc);
    Data operator()(float size) const;

  private:
    const std::string name_;
    std::vector<Data> trusts_;
  };

  bool up_to_date_;

  std::unordered_map<std::string, TypeCategory> types_;
  mutable std::shared_mutex mut_;

  Modelinator();
  void reload();
};
} // namespace almanac
} // namespace carbon