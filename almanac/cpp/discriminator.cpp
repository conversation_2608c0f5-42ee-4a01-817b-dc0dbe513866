#include "almanac/cpp/discriminator.hpp"
#include <almanac/cpp/almanac.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <generated/proto/almanac/almanac.pb.h>
#include <lib/common/cpp/utils/environment.hpp>

#include <algorithm>
#include <cmath>
#include <limits>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>

namespace carbon::almanac {
Discriminator::TypeCategory::TypeCategory(int expected_size, const std::string &name,
                                          const aimbot::almanac::DiscriminatorTypeCategory &dtc)
    : name_(name) {
  if (dtc.trusts_size() != expected_size) {
    throw AlmanacException("Invalid Discriminator.");
  }
  for (auto i = dtc.trusts_size() - 1; i >= 0; --i) {
    const auto &trust = dtc.trusts(i);
    trusts_.emplace_back(trust.ignorable(), trust.avoid());
  }
}
Discriminator::Data Discriminator::TypeCategory::operator()(float size) const {
  auto index = Almanac::get().get_index(name_, size);
  return trusts_[index];
}

Discriminator &Discriminator::get() {
  static Discriminator discriminator;
  return discriminator;
}
Discriminator::Discriminator() : up_to_date_(false) {}
Discriminator::Data Discriminator::get_data(const std::string &cls, float size) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  auto it = types_.find(cls);
  if (it == types_.end()) {
    return Data();
  }
  Data data = it->second(size);
  return data;
}
void Discriminator::reload() {
  {
    std::shared_lock<std::shared_mutex> lock(mut_);
    if (up_to_date_) {
      // Nothing to do so return
      return;
    }
  }
  std::unique_lock<std::shared_mutex> lock(mut_);
  if (up_to_date_) {
    // Double check with unique lock aquired now.
    return;
  }
  up_to_date_ = true;
  types_.clear();
  if (!carbon::common::is_aimbot()) {
    // No discrimination
    return;
  }
  lib::common::RedisClient redis;
  int expected_size = static_cast<int>(Almanac::get().expected_size_count());
  std::unordered_map<std::string, std::string> point_cats;
  redis->hgetall(Almanac::PointCategories, std::inserter(point_cats, point_cats.begin()));
  auto active_crop = redis.get(Almanac::ActiveCropId);
  if (!active_crop || active_crop.value() == "") {
    throw AlmanacException("No active crop set, cannot load discriminator.");
  }
  auto active_id = redis.hget(DiscriminatorActive, active_crop.value());
  if (!active_id || active_id.value() == "") {
    spdlog::info("No discriminator defined for crop {}", active_crop.value());
    return;
  }
  auto encoded_disc = redis.hget(DiscriminatorCfgs, active_id.value());
  if (!encoded_disc || encoded_disc.value() == "") {
    spdlog::warn("Invalid discriminator found, cannot use this");
    return;
  }
  aimbot::almanac::DiscriminatorConfig proto_disc;
  proto_disc.ParseFromString(encoded_disc.value());
  if (proto_disc.id() != active_id.value()) {
    throw AlmanacException(fmt::format("Fetched discriminator does not match id expected = '{}', actual = '{}'",
                                       active_id.value(), proto_disc.id()));
  }

  for (auto i = 0; i < proto_disc.categories_size(); ++i) {
    const auto &dtc = proto_disc.categories(i);
    std::string name = "";
    if (dtc.type().classification() == aimbot::almanac::CategoryClassification::CATEGORY_WEED) {
      auto it = point_cats.find(dtc.type().category());
      if (it == point_cats.end()) {
        spdlog::info("Unknown weed category with id {}", dtc.type().category());
        continue;
      }
      name = it->second;
      // point categories now produces lowercase/mixed case names
      std::transform(name.begin(), name.end(), name.begin(), ::toupper);
    } else if (dtc.type().category() == active_crop.value()) {
      name = "CROP"; // This is currently hardcoded in our models
    } else {
      // other crop in almanac.
      continue;
    }
    types_.try_emplace(name, expected_size, name, dtc);
  }
}

} // namespace carbon::almanac