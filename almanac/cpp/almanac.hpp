#pragma once

#include <config/tree/cpp/config_scoped_callback.hpp>
#include <lib/common/cpp/exceptions.h>
#include <lib/common/redis/redis_client.hpp>

#include <atomic>
#include <memory>
#include <shared_mutex>
#include <unordered_map>
#include <vector>

namespace carbon {
namespace aimbot::almanac {
class Formula;
class AlmanacTypeCategory;
} // namespace aimbot::almanac
namespace almanac {
class AlmanacException : public maka_error {
public:
  explicit AlmanacException(const std::string &message) : maka_error(message, false, 1) {}
};
class Almanac {
public:
  static constexpr std::string_view ActiveCropId = "/almanac/active_crop_id";
  static constexpr std::string_view PointCategories = "/point_categories/ids";
  static constexpr std::string_view AlmanacCfgs = "/almanac/almanac/config";
  static constexpr std::string_view AlmanacActive = "/almanac/almanac/active";
  Almanac(const Almanac &almanac) = delete;
  Almanac &operator=(const Almanac &) = delete;
  static Almanac &get();
  uint32_t get_shoot_time(const std::string &cls, float size);
  size_t get_index(const std::string &cls, float size);
  size_t get_protobuf_size_category_index(const std::string &cls, float size);
  inline void reload_required() {
    std::unique_lock<std::shared_mutex> lock(mut_);
    up_to_date_ = false;
  }
  inline size_t expected_size_count() const { return size_counts_; }

private:
  class Formula {
  public:
    Formula(const aimbot::almanac::Formula &formula);
    uint32_t operator()(float x) const;

  private:
    const float A_;
    const float b_;
    const float e_;
    const float M_;
    const uint32_t max_time_;
  };
  class SizeCategory {
  public:
    SizeCategory(float min_size, const aimbot::almanac::Formula &formula);
    inline uint32_t operator()(float size) const { return formula_(size); }
    inline float min_size() const { return min_size_; }

  private:
    float min_size_;
    Formula formula_;
  };
  class TypeCategory {
  public:
    TypeCategory(int expected_size, const aimbot::almanac::AlmanacTypeCategory &atc);
    uint32_t operator()(float size) const;
    size_t get_index(float size) const;
    size_t get_protobuf_size_category_index(float size) const;

  private:
    std::vector<SizeCategory> formulas_;
  };
  using TypeMap = std::unordered_map<std::string, TypeCategory>;

  const size_t size_counts_;
  bool up_to_date_;
  bool use_static_kill_time_;
  uint32_t static_kill_time_;
  TypeMap types_;
  TypeMap::iterator default_iterator_;
  config::ConfigScopedCallback scoped_tree_;
  mutable std::shared_mutex mut_;

  Almanac();
  void reload();
  TypeMap::iterator _find_category(const std::string &cls);
};
} // namespace almanac
} // namespace carbon