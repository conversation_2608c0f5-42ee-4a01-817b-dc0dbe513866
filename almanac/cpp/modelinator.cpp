#include "almanac/cpp/modelinator.hpp"
#include <almanac/cpp/almanac.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <generated/proto/almanac/almanac.pb.h>
#include <lib/common/cpp/utils/environment.hpp>

#include <algorithm>
#include <cmath>
#include <limits>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>

namespace carbon::almanac {
Modelinator::TypeCategory::TypeCategory(int expected_size, const std::string &name,
                                        const aimbot::almanac::ModelinatorTypeCategory &mtc)
    : name_(name) {
  if (mtc.trusts_size() != expected_size) {
    throw AlmanacException("Invalid Modelinator.");
  }
  for (auto i = mtc.trusts_size() - 1; i >= 0; --i) {
    const auto &trust = mtc.trusts(i);
    trusts_.emplace_back(trust.min_doo(), trust.weeding_threshold(), trust.thinning_threshold(),
                         trust.banding_threshold());
  }
}
Modelinator::Data Modelinator::TypeCategory::operator()(float size) const {
  auto index = Almanac::get().get_index(name_, size);
  return trusts_[index];
}

Modelinator &Modelinator::get() {
  static Modelinator modelinator;
  return modelinator;
}
Modelinator::Modelinator() : up_to_date_(false) {}
Modelinator::Data Modelinator::get_data(const std::string &cls, float size) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  auto it = types_.find(cls);
  if (it == types_.end()) {
    return Data();
  }
  return it->second(size);
}
Modelinator::Data Modelinator::get_data(const std::string &cls, float size, bool is_weed, float confidence) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  auto it = types_.find(cls);
  if (it == types_.end()) {
    return Data();
  }
  Data data = it->second(size);
  float min_threshold = data.weeding_threshold;
  if (data.thinning_threshold < min_threshold) {
    min_threshold = data.thinning_threshold;
  }
  if (!is_weed) {
    if (data.banding_threshold < min_threshold) {
      min_threshold = data.banding_threshold;
    }
  }
  data.usable = min_threshold < confidence;
  return data;
}
void Modelinator::reload() {
  {
    std::shared_lock<std::shared_mutex> lock(mut_);
    if (up_to_date_) {
      // Nothing to do so return
      return;
    }
  }
  std::unique_lock<std::shared_mutex> lock(mut_);
  if (up_to_date_) {
    // Double check with unique lock aquired now.
    return;
  }
  up_to_date_ = true;
  types_.clear();
  if (!carbon::common::is_aimbot()) {
    // do nothing
    return;
  }
  lib::common::RedisClient redis;
  int expected_size = static_cast<int>(Almanac::get().expected_size_count());
  std::unordered_map<std::string, std::string> point_cats;
  redis->hgetall(Almanac::PointCategories, std::inserter(point_cats, point_cats.begin()));
  auto active_crop = redis.get(Almanac::ActiveCropId);
  auto active_model = redis.get(ActiveModelId);
  if (!active_crop || active_crop.value() == "") {
    throw AlmanacException("No active crop set, cannot load modelinator.");
  }
  if (!active_model || active_model.value() == "") {
    throw AlmanacException("No active model set, cannot load modelinator.");
  }
  auto active_id = Modelinator::get_id(active_model.value(), active_crop.value());
  auto encoded_mod = redis.hget(ModelinatorCfgs, active_id);
  if (!encoded_mod) {
    spdlog::debug("No modelinator, using default.");
    return;
  }
  if (encoded_mod.value() == "") {
    spdlog::warn("Invalid modelinator, using default.");
    return;
  }
  aimbot::almanac::ModelinatorConfig proto_mod;
  proto_mod.ParseFromString(encoded_mod.value());
  auto actual_id = Modelinator::get_id(proto_mod.model_id(), proto_mod.crop_id());
  if (actual_id != active_id) {
    throw AlmanacException(
        fmt::format("Fetched modelinator does not match expected id = '{}', actual = '{}'", active_id, actual_id));
  }

  for (auto i = 0; i < proto_mod.categories_size(); ++i) {
    const auto &mtc = proto_mod.categories(i);
    std::string name = "";
    if (mtc.type().classification() == aimbot::almanac::CategoryClassification::CATEGORY_WEED) {
      auto it = point_cats.find(mtc.type().category());
      if (it == point_cats.end()) {
        spdlog::info("Unknown weed category with id {}", mtc.type().category());
        continue;
      }
      name = it->second;
      // point categories now produces lowercase/mixed case names
      std::transform(name.begin(), name.end(), name.begin(), ::toupper);
    } else if (mtc.type().category() == active_crop.value()) {
      name = "CROP"; // This is currently hardcoded in our models
    } else {
      // other crop in almanac.
      continue;
    }
    types_.try_emplace(name, expected_size, name, mtc);
  }
}
std::string Modelinator::get_id(std::string_view model_id, std::string_view crop_id) {
  return fmt::format("{}/{}", model_id, crop_id);
}

} // namespace carbon::almanac