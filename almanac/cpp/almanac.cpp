#include "almanac/cpp/almanac.hpp"

#include <algorithm>
#include <cmath>
#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <generated/proto/almanac/almanac.pb.h>
#include <lib/common/cpp/utils/environment.hpp>
#include <limits>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <string>

std::string_view constexpr DEFAULT_KEY = "DEFAULT";
constexpr float min_mult = 0.1f;
constexpr float center_val = 5;
namespace carbon {
namespace almanac {
float get_with_min(float ftm_val, uint32_t key) {
  float val = 1.0f + (static_cast<float>(key) - center_val) * ftm_val;
  return std::max(val, min_mult);
}
Almanac::Formula::Formula(const aimbot::almanac::Formula &formula)
    : A_(formula.multiplier()), b_(formula.offset()), e_(formula.exponent()),
      M_(get_with_min(formula.fine_tune_multiplier_val(), formula.fine_tune_multiplier())),
      max_time_(formula.max_time()) {}
uint32_t Almanac::Formula::operator()(float x) const {
  uint32_t shoot_time = static_cast<uint32_t>(std::round(M_ * ((A_ * std::pow(x, e_)) + b_)));
  if (shoot_time > max_time_) {
    return max_time_;
  }
  return shoot_time;
}
Almanac::SizeCategory::SizeCategory(float min_size, const aimbot::almanac::Formula &formula)
    : min_size_(min_size), formula_(formula) {}
Almanac::TypeCategory::TypeCategory(int expected_size, const aimbot::almanac::AlmanacTypeCategory &atc) {
  if (atc.formulas_size() != expected_size || atc.formulas_size() != (atc.sizes_size() + 1)) {
    throw AlmanacException("Invalid almanac.");
  }
  for (auto i = atc.formulas_size() - 1; i > 0; --i) {
    formulas_.emplace_back(atc.sizes(i - 1), atc.formulas(i));
  }
  formulas_.emplace_back(0.0f, atc.formulas(0)); // always start at 0
}
uint32_t Almanac::TypeCategory::operator()(float size) const {
  for (const auto &formula : formulas_) {
    if (formula.min_size() < size) {
      return formula(size);
    }
  }
  if (!std::isnan(size)) {
    spdlog::warn("Failed to find valid size category for target of size {}", size);
  }
  return 0;
}
size_t Almanac::TypeCategory::get_index(float size) const {
  size_t i = 0;
  for (const auto &formula : formulas_) {
    if (formula.min_size() < size) {
      return i;
    }
    ++i;
  }
  if (!std::isnan(size)) {
    spdlog::warn("Failed to find valid size category for target of size {}", size);
  }
  return i;
}

size_t Almanac::TypeCategory::get_protobuf_size_category_index(float size) const {
  // formulas are stored in reverse order from protobuf, otherwise we could just use get_index()
  return formulas_.size() - get_index(size) - 1;
}

Almanac &Almanac::get() {
  static Almanac almanac;
  return almanac;
}
Almanac::Almanac()
    : size_counts_(carbon::config::get_global_config_subscriber()
                       ->get_config_node("common", "almanac/almanac_size_category_count")
                       ->get_value<uint32_t>()),
      up_to_date_(false), use_static_kill_time_(false), static_kill_time_(0), default_iterator_(types_.end()),
      scoped_tree_(config::get_global_config_subscriber()->get_config_node("common", "almanac"),
                   std::bind(&Almanac::reload_required, this)) {}

Almanac::TypeMap::iterator Almanac::_find_category(const std::string &cls) {
  auto it = types_.find(cls);
  if (it == types_.end()) {
    if (default_iterator_ == types_.end()) {
      // Saw a segfault occur here, there mus be a condition we don't check properly
      throw AlmanacException(fmt::format("The comment below is actually a lie '{}'", cls));
    }
    return default_iterator_; // guaranteed to exist as checked in loading
  }
  return it;
}
uint32_t Almanac::get_shoot_time(const std::string &cls, float size) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  if (use_static_kill_time_) {
    return static_kill_time_;
  }
  return _find_category(cls)->second(size);
}

size_t Almanac::get_index(const std::string &cls, float size) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  return _find_category(cls)->second.get_index(size);
}

size_t Almanac::get_protobuf_size_category_index(const std::string &cls, float size) {
  reload();
  std::shared_lock<std::shared_mutex> lock(mut_);
  return _find_category(cls)->second.get_protobuf_size_category_index(size);
}

void Almanac::reload() {
  {
    std::shared_lock<std::shared_mutex> lock(mut_);
    if (up_to_date_) {
      // Nothing to do so return
      return;
    }
  }
  std::unique_lock<std::shared_mutex> lock(mut_);
  if (up_to_date_) {
    // Double check with unique lock aquired now.
    return;
  }
  up_to_date_ = true;
  if (!carbon::common::is_aimbot()) {
    use_static_kill_time_ = true;
    static_kill_time_ = scoped_tree_->get_node("static_kill_time")->get_value<uint32_t>();
    return;
  }
  use_static_kill_time_ = scoped_tree_->get_node("use_static_kill_time")->get_value<bool>();
  static_kill_time_ = scoped_tree_->get_node("static_kill_time")->get_value<uint32_t>();

  lib::common::RedisClient redis;
  auto active_id = redis.get(AlmanacActive);
  if (!active_id || active_id.value() == "") {
    throw AlmanacException("Active Almanac required, but no active id found.");
  }
  auto encoded_almanac = redis.hget(AlmanacCfgs, active_id.value());
  if (!encoded_almanac) {
    throw AlmanacException(fmt::format("Active almanac {} was not found", active_id.value()));
  }
  aimbot::almanac::AlmanacConfig proto_almanac;
  proto_almanac.ParseFromString(encoded_almanac.value());
  if (proto_almanac.id() != active_id.value()) {
    throw AlmanacException(fmt::format("Fetched almanac does not match id expected = '{}', actual = '{}'",
                                       active_id.value(), proto_almanac.id()));
  }
  std::unordered_map<std::string, std::string> point_cats;
  redis->hgetall(PointCategories, std::inserter(point_cats, point_cats.begin()));
  auto active_crop = redis.get(ActiveCropId);
  if (!active_crop || active_crop.value() == "") {
    throw AlmanacException("No active crop set, cannot load almanac.");
  }
  types_.clear();
  size_t default_count = 0;
  for (auto i = 0; i < proto_almanac.categories_size(); ++i) {
    const auto &atc = proto_almanac.categories(i);
    std::string name = "";
    if (atc.type().category() == DEFAULT_KEY) {
      name = DEFAULT_KEY;
      ++default_count;
    } else if (atc.type().classification() == aimbot::almanac::CategoryClassification::CATEGORY_WEED) {
      auto it = point_cats.find(atc.type().category());
      if (it == point_cats.end()) {
        spdlog::info("Unknown weed category with id {}", atc.type().category());
        continue;
      }
      name = it->second;
      // point categories now produces lowercase/mixed case names
      std::transform(name.begin(), name.end(), name.begin(), ::toupper);
    } else if (atc.type().category() == active_crop.value()) {
      name = "CROP"; // This is currently hardcoded in our models
    } else {
      // other crop in almanac.
      continue;
    }
    types_.try_emplace(name, static_cast<int>(size_counts_), atc);
  }
  if (default_count != 1) {
    throw AlmanacException(fmt::format("almanac {} is invalid.", active_id.value()));
  }
  default_iterator_ =
      types_.find(std::string(DEFAULT_KEY)); // find cannot take a string_view directly so need to convert to string
}
} // namespace almanac
} // namespace carbon