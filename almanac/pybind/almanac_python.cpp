#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include <almanac/cpp/almanac.hpp>
#include <almanac/cpp/discriminator.hpp>
#include <almanac/cpp/modelinator.hpp>
namespace py = pybind11;

namespace carbon {
namespace almanac {

PYBIND11_MODULE(almanac_python, m) {
  py::class_<Almanac, std::unique_ptr<Almanac, py::nodelete>>(m, "Almanac")
      .def(py::init([]() { return std::unique_ptr<Almanac, py::nodelete>(&Almanac::get()); }))
      .def("reload_required", &Almanac::reload_required, py::call_guard<py::gil_scoped_release>());

  py::class_<Discriminator, std::unique_ptr<Discriminator, py::nodelete>>(m, "Discriminator")
      .def(py::init([]() { return std::unique_ptr<Discriminator, py::nodelete>(&Discriminator::get()); }))
      .def("reload_required", &Discriminator::reload_required, py::call_guard<py::gil_scoped_release>());

  py::class_<Modelinator, std::unique_ptr<Modelinator, py::nodelete>>(m, "Modelinator")
      .def(py::init([]() { return std::unique_ptr<Modelinator, py::nodelete>(&Modelinator::get()); }))
      .def("reload_required", &Modelinator::reload_required, py::call_guard<py::gil_scoped_release>());
}

} // namespace almanac
} // namespace carbon