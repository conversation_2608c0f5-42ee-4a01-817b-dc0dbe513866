#pragma once
#include <optional>

namespace carbon::gps_distributor {
struct ValueWithAccuracy {
  double value;
  double accuracy;
};
struct DualGpsData {
  bool gnss_valid;
  bool diff_corrections;
  bool is_moving_base;
  uint64_t timestamp_ms; // Timestamp of this fix (if valid)
  ValueWithAccuracy heading_deg;
};
enum FixType {
  NO_FIX = 0,
  DEAD_RECKONING_ONLY = 1,
  FIX_2D = 2,
  FIX_3D = 3,
  GNSS_DR = 4, // GNSS + dead reckoning combined
  TIME_ONLY = 5,
};
enum CarrierSolution {
  NO_SOLUTION = 0,
  FLOATING_SOLUTION = 1,
  FIXED_SOLUTION = 2,
};
struct FixFlags {
  bool gnss_fix_ok;
  bool diff_soln;
  CarrierSolution carr_soln;
};

struct RawGpsData {
  bool have_fix;
  double latitude;
  double longitude;
  int32_t num_sats;
  float hdop;
  uint64_t timestamp_ms; // Only valid if fix is present
  int32_t height_mm;
  bool have_approx_fix;
  FixType fix_type;
  FixFlags fix_flags;
  std::optional<DualGpsData> dual;
};
} // namespace carbon::gps_distributor