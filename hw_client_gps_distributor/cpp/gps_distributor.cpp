#include "hw_client_gps_distributor/cpp/gps_distributor.hpp"

#include <cmath>

#include "config/client/cpp/config_subscriber.hpp"
#include "hardware_manager/cpp/grpc_client.h"
#include "lib/common/bot/cpp/stop_handler/stop_handler.hpp"

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon::gps_distributor {
std::shared_ptr<RawGpsData> from_grpc(const hardware_manager::GetNextRawGPSDataResponse *input) {
  auto output = std::make_shared<RawGpsData>();
  output->have_fix = input->have_fix();
  output->have_approx_fix = input->have_approx_fix();
  output->latitude = input->latitude();
  output->longitude = input->longitude();
  output->height_mm = input->height_mm();
  output->timestamp_ms = input->timestamp_ms();
  output->num_sats = input->num_sats();
  output->hdop = input->hdop();
  output->fix_type = FixType(input->fix_type());
  output->fix_flags.gnss_fix_ok = input->gnss_valid();
  output->fix_flags.diff_soln = input->diff_corrections();
  output->fix_flags.carr_soln = CarrierSolution(input->carrier_phase());
  if (input->has_dual()) {
    output->dual = DualGpsData();
    output->dual->gnss_valid = input->dual().gnss_valid();
    output->dual->diff_corrections = input->dual().diff_corrections();
    output->dual->is_moving_base = input->dual().is_moving_base();
    output->dual->timestamp_ms = input->dual().timestamp_ms();
    output->dual->heading_deg.accuracy = input->dual().heading().accuracy();
    output->dual->heading_deg.value = input->dual().heading().value();
  }
  return output;
}
GpsDistributor::GpsDistributor()
    : curr_(nullptr), stopped_(false), gps_update_(&GpsDistributor::gps_update_loop, this) {}
const GpsDistributor &GpsDistributor::get() {
  static GpsDistributor inst;
  return inst;
}
std::shared_ptr<RawGpsData> GpsDistributor::get_next(const uint64_t &prev_ts_ms, std::chrono::milliseconds timeout) {
  return get()._get_next(prev_ts_ms, timeout);
}
std::shared_ptr<RawGpsData> GpsDistributor::_get_next(const uint64_t &prev_ts_ms,
                                                      std::chrono::milliseconds timeout) const {
  auto test = [&]() { return stopped_ || (curr_ && curr_->timestamp_ms > prev_ts_ms); };
  std::unique_lock<std::mutex> lg(mut_);
  // Check before waiting
  if (test()) {
    return curr_;
  }
  if (timeout.count() == 0) {
    cv_.wait(lg, test);
  } else {
    if (!cv_.wait_for(lg, timeout, test)) {
      return nullptr;
    }
  }
  return curr_;
}

GpsDistributor::~GpsDistributor() { gps_update_.join(); }

void GpsDistributor::gps_update_loop() {
  hardware_manager::HardwareManagerClient hc(
      config::make_robot_local_addr(hardware_manager::HardwareManagerClient::DEFAULT_PORT));
  auto bse = lib::common::bot::BotStopHandler::get().create_scoped_event("gps_distributor", [&] { cv_.notify_all(); });
  uint64_t prev_ts = 0;
  while (!bse.is_stopped()) {
    auto resp = hc.next_raw_gps(prev_ts);
    if (!resp) {
      std::this_thread::sleep_for(std::chrono::milliseconds(200));
      continue;
    }
    if (resp->timestamp_ms() == 0 || std::abs(resp->latitude()) < 1 || std::abs(resp->longitude()) < 1 ||
        (resp->has_dual() && !resp->dual().has_heading())) {
      spdlog::warn("got Invalid GPS data");
      continue;
    }
    prev_ts = resp->timestamp_ms();
    auto curr = from_grpc(resp.get());
    {
      std::lock_guard lg(mut_);
      curr_ = curr;
      cv_.notify_all();
    }
  }
}

} // namespace carbon::gps_distributor
