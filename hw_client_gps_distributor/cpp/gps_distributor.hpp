#pragma once

#include <atomic>
#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <thread>

#include "hw_client_gps_distributor/cpp/gps_record.hpp"

namespace carbon::gps_distributor {
class GpsDistributor {
public:
  static std::shared_ptr<RawGpsData> get_next(const uint64_t &prev_ts_ms, std::chrono::milliseconds timeout);
  ~GpsDistributor();

private:
  mutable std::mutex mut_;
  mutable std::condition_variable cv_;
  std::shared_ptr<RawGpsData> curr_;
  std::atomic<bool> stopped_;

  static const GpsDistributor &get();
  std::shared_ptr<RawGpsData> _get_next(const uint64_t &prev_ts_ms, std::chrono::milliseconds timeout) const;
  GpsDistributor();
  GpsDistributor(const GpsDistributor &) = delete;
  GpsDistributor &operator=(const GpsDistributor &) = delete;

  void gps_update_loop();

  // Threads at the end
  std::thread gps_update_;
};
} // namespace carbon::gps_distributor