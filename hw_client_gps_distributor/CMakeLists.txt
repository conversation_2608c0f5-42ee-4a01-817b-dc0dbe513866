add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(hw_client_gps_distributor SHARED ${SOURCES})
target_compile_definitions(hw_client_gps_distributor PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1)
target_link_libraries(hw_client_gps_distributor PRIVATE m stdc++fs pthread rt fmt config_tree_lib config_client_lib utils bot_stop proj_utils hardware_manager_client)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(hw_client_gps_distributor_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(hw_client_gps_distributor_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 -DUSE_UNSTABLE_GEOS_CPP_API=1 PYBIND)
target_link_libraries(hw_client_gps_distributor_python PUBLIC hw_client_gps_distributor)
target_link_libraries(hw_client_gps_distributor_python PRIVATE utils)
set_target_properties(hw_client_gps_distributor_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)
target_compile_options(hw_client_gps_distributor_python PRIVATE -fvisibility=hidden)