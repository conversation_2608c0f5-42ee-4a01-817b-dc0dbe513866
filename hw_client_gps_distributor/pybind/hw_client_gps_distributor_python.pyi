from typing import Optional

class FixType:
    NO_FIX: "FixType"
    DEAD_RECKONING_ONLY: "FixType"
    FIX_2D: "FixType"
    FIX_3D: "FixType"
    GNSS_DR: "FixType"
    TIME_ONLY: "FixType"

class CarrierSolution:
    NO_SOLUTION: "CarrierSolution"
    FLOATING_SOLUTION: "CarrierSolution"
    FIXED_SOLUTION: "CarrierSolution"

class ValueWithAccuracy:
    @property
    def value(self) -> float: ...
    @property
    def accuracy(self) -> float: ...

class DualGpsData:
    @property
    def gnss_valid(self) -> bool: ...
    @property
    def diff_corrections(self) -> bool: ...
    @property
    def is_moving_base(self) -> bool: ...
    @property
    def timestamp_ms(self) -> int: ...
    @property
    def heading_deg(self) -> ValueWithAccuracy: ...

class FixFlags:
    @property
    def gnss_fix_ok(self) -> bool: ...
    @property
    def diff_soln(self) -> bool: ...
    @property
    def carr_soln(self) -> CarrierSolution: ...

class RawGpsData:
    @property
    def have_fix(self) -> bool: ...
    @property
    def latitude(self) -> float: ...
    @property
    def longitude(self) -> float: ...
    @property
    def num_sats(self) -> int: ...
    @property
    def hdop(self) -> float: ...
    @property
    def timestamp_ms(self) -> int: ...
    @property
    def height_mm(self) -> float: ...
    @property
    def have_approx_fix(self) -> bool: ...
    @property
    def fix_type(self) -> FixType: ...
    @property
    def fix_flags(self) -> FixFlags: ...
    @property
    def dual(self) -> Optional[DualGpsData]: ...

def get_next(prev_ts_ms: int, timeout_ms: int) -> Optional[RawGpsData]: ...
