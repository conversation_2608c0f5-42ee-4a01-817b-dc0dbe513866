#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "hw_client_gps_distributor/cpp/gps_distributor.hpp"
#include "hw_client_gps_distributor/cpp/gps_record.hpp"

namespace py = pybind11;

namespace carbon::gps_distributor {
std::shared_ptr<RawGpsData> get_next(int64_t prev_ts_ms, int64_t timeout_ms) {
  return GpsDistributor::get_next(prev_ts_ms, std::chrono::milliseconds(timeout_ms));
}
PYBIND11_MODULE(hw_client_gps_distributor_python, m) {

  m.def("get_next", &get_next, py::arg("prev_ts_ms"), py::arg("timeout_ms"), py::call_guard<py::gil_scoped_release>());
  py::enum_<FixType>(m, "FixType")
      .value("NO_FIX", FixType::NO_FIX)
      .value("DEAD_RECKONING_ONLY", FixType::DEAD_RECKONING_ONLY)
      .value("FIX_2D", FixType::FIX_2D)
      .value("FIX_3D", FixType::FIX_3D)
      .value("GNSS_DR", FixType::GNSS_DR)
      .value("TIME_ONLY", FixType::TIME_ONLY);
  py::enum_<CarrierSolution>(m, "CarrierSolution")
      .value("NO_SOLUTION", CarrierSolution::NO_SOLUTION)
      .value("FLOATING_SOLUTION", CarrierSolution::FLOATING_SOLUTION)
      .value("FIXED_SOLUTION", CarrierSolution::FIXED_SOLUTION);

  py::class_<ValueWithAccuracy, std::unique_ptr<ValueWithAccuracy>>(m, "ValueWithAccuracy")
      .def_readonly("value", &ValueWithAccuracy::value, py::call_guard<py::gil_scoped_release>())
      .def_readonly("accuracy", &ValueWithAccuracy::accuracy, py::call_guard<py::gil_scoped_release>());
  py::class_<DualGpsData, std::unique_ptr<DualGpsData>>(m, "DualGpsData")
      .def_readonly("gnss_valid", &DualGpsData::gnss_valid, py::call_guard<py::gil_scoped_release>())
      .def_readonly("diff_corrections", &DualGpsData::diff_corrections, py::call_guard<py::gil_scoped_release>())
      .def_readonly("is_moving_base", &DualGpsData::is_moving_base, py::call_guard<py::gil_scoped_release>())
      .def_readonly("timestamp_ms", &DualGpsData::timestamp_ms, py::call_guard<py::gil_scoped_release>())
      .def_readonly("heading_deg", &DualGpsData::heading_deg, py::call_guard<py::gil_scoped_release>(),
                    py::return_value_policy::reference_internal);
  py::class_<FixFlags, std::unique_ptr<FixFlags>>(m, "FixFlags")
      .def_readonly("gnss_fix_ok", &FixFlags::gnss_fix_ok, py::call_guard<py::gil_scoped_release>())
      .def_readonly("diff_soln", &FixFlags::diff_soln, py::call_guard<py::gil_scoped_release>())
      .def_readonly("carr_soln", &FixFlags::carr_soln, py::call_guard<py::gil_scoped_release>());
  py::class_<RawGpsData, std::shared_ptr<RawGpsData>>(m, "RawGpsData")
      .def_readonly("have_fix", &RawGpsData::have_fix, py::call_guard<py::gil_scoped_release>())
      .def_readonly("latitude", &RawGpsData::latitude, py::call_guard<py::gil_scoped_release>())
      .def_readonly("longitude", &RawGpsData::longitude, py::call_guard<py::gil_scoped_release>())
      .def_readonly("num_sats", &RawGpsData::num_sats, py::call_guard<py::gil_scoped_release>())
      .def_readonly("hdop", &RawGpsData::hdop, py::call_guard<py::gil_scoped_release>())
      .def_readonly("timestamp_ms", &RawGpsData::timestamp_ms, py::call_guard<py::gil_scoped_release>())
      .def_readonly("height_mm", &RawGpsData::height_mm, py::call_guard<py::gil_scoped_release>())
      .def_readonly("have_approx_fix", &RawGpsData::have_approx_fix, py::call_guard<py::gil_scoped_release>())
      .def_readonly("fix_type", &RawGpsData::fix_type, py::call_guard<py::gil_scoped_release>())
      .def_readonly("fix_flags", &RawGpsData::fix_flags, py::call_guard<py::gil_scoped_release>(),
                    py::return_value_policy::reference_internal)
      .def_readonly("dual", &RawGpsData::dual, py::call_guard<py::gil_scoped_release>(),
                    py::return_value_policy::reference_internal);
};
} // namespace carbon::gps_distributor