import asyncio
from typing import Optional

from hw_client_gps_distributor.pybind.hw_client_gps_distributor_python import RawGpsData
from hw_client_gps_distributor.pybind.hw_client_gps_distributor_python import get_next as get_next_sync


async def get_next(prev_ts_ms: int, timeout_ms: int) -> Optional[RawGpsData]:
    return await asyncio.get_event_loop().run_in_executor(None, lambda: get_next_sync(prev_ts_ms, timeout_ms))


async def get_next_non_opt(prev_ts_ms: int) -> RawGpsData:
    pos = await asyncio.get_event_loop().run_in_executor(None, lambda: get_next_sync(prev_ts_ms, 0))
    assert pos is not None
    return pos
