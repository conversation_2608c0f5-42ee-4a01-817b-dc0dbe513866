name: daily_release_dl_arm

on:
  pull_request:
    branches:
      - master
    paths:
      - ".github/workflows/daily_release_dl_arm.yml"
  schedule:
   - cron: "0 10 * * *" # 3 am Pacific
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  GITHUB_TOKEN: ${{ secrets.CR_PAT }}

jobs:
  daily_release_dl_arm:
    runs-on: [self-hosted, arc-robot-ci-arm]
    timeout-minutes: 200
    permissions:
      actions: write
      contents: write
      packages: write
    env:
      GIT_BRANCH: ${{github.head_ref}}
      MAKA_ROBOT_DIR:  ${{github.workspace}}
      MAKA_LOG_DIR: ${{github.workspace}}/tmp
      MAKA_ROLE: all
      CARBON_HOST_BASE_DIR: ${{github.workspace}}/..
      AUTH0_TOKEN_CACHE_FILE: /data/release_auth_token.json
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      TARGET_LATEST_TAG: ""
      RELEASE_TAG_NAME: c4.4.0
      S3_CACHE_PROXY_SERVICE_HOST: frankenmini01.dc.carbonrobotics.com
    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Load .env file
        uses: xom9ikk/dotenv@v2
        with:
          path: ${{github.workspace}}/../../..

      - name: Set workspace permissions
        run: |
          chown -R $USER:$USER ${{ github.workspace }}

      - name: Checkout branch
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          lfs: true
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Set Daily Version
        run: |
          echo "DAILY_RELEASE_VERSION=$RELEASE_TAG_NAME-nightly-$(date '+%Y%m%d')" >> $GITHUB_ENV

      - name: Setup environment
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
          else
            # If release tag name is c1.3.4, BRANCH_TAG will be shasum of c1.3
            BRANCH_TAG=`echo -n $DAILY_RELEASE_VERSION | sed 's/\.[0-9][0-9]*$//' | shasum | awk '{print $1}'`
          fi
          echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV

      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci --repo ${{github.workspace}} --role rtc

      - name: Build gobot
        uses: docker/build-push-action@v6
        with:
          build-args: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          context: .
          file: services/containers/gobot/Dockerfile
          cache-from: |
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.BRANCH_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.MAKA_DOCKER_TAG}}
          push: false
          load: true
          tags: |
            ghcr.io/carbonrobotics/robot/gobot:latest

      - name: Bot Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull || true
          ${{github.workspace}}/bot/bot pull deeplearning || true

      - name: Build and push containers
        run: |
          echo "Releasing version: $DAILY_RELEASE_VERSION"
          ${{github.workspace}}/bot/bot release --release $DAILY_RELEASE_VERSION --tag $BRANCH_TAG --tag dl-nightly --id ${{ github.sha }} --version $DAILY_RELEASE_VERSION -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
