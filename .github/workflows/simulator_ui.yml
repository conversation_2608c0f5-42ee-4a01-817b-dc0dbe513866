name: Simulator Quality Check

on:
  pull_request:
    branches:
      - master

jobs:
  quality_checks:
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./simulator_ui

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18.17

      - name: Install dependencies
        run: yarn install

      - name: Compile TypeScript
        run: yarn tsc

      - name: Run ESLint
        run: yarn lint