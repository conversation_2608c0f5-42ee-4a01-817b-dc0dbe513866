name: Release Commit Diff

on:
  push:
    branches:
      - 'release/*'

jobs:
  list-missing-commits:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for git log comparison

      - name: Extract release version
        id: extract-version
        run: |
          BRANCH_NAME="${{ github.ref_name }}"
          VERSION=${BRANCH_NAME#release/}
          echo "version=$VERSION" >> "$GITHUB_OUTPUT"
          echo "branch_name=$BRANCH_NAME" >> "$GITHUB_OUTPUT"

      - name: List commits in master but not in release branch
        id: commit-diff
        run: |
          BRANCH_NAME="${{ steps.extract-version.outputs.branch_name }}"
          echo "Comparing origin/$BRANCH_NAME with origin/master..."
          echo "Commits in origin/master but not in origin/$BRANCH_NAME:"

          # Create output file
          OUTPUT_FILE="missing-commits-${{ steps.extract-version.outputs.version }}.txt"

          # Add header to file
          echo "Commits in origin/master but not in origin/$<PERSON>ANCH_NAME" > "$OUTPUT_FILE"
          echo "Generated on: $(date)" >> "$OUTPUT_FILE"
          echo "Comparison: origin/$BRANCH_NAME...origin/master" >> "$OUTPUT_FILE"
          echo "----------------------------------------" >> "$OUTPUT_FILE"

          # Get the commit diff with committer name and save to file
          git log --cherry-pick --right-only --oneline --format="%h %an: %s" "origin/$BRANCH_NAME...origin/master" >> "$OUTPUT_FILE"

          # Also output to console for workflow logs
          echo "Contents of $OUTPUT_FILE:"
          cat "$OUTPUT_FILE"

          # Set output for artifact upload
          echo "output_file=$OUTPUT_FILE" >> "$GITHUB_OUTPUT"

      - name: Upload commit diff as artifact
        uses: actions/upload-artifact@v4
        with:
          name: missing-commits-${{ steps.extract-version.outputs.version }}
          path: ${{ steps.commit-diff.outputs.output_file }}
          retention-days: 30

      - name: Comment on latest commit (if available)
        if: github.event_name == 'push'
        run: |
          echo "Commit diff analysis completed for ${{ steps.extract-version.outputs.branch_name }}"
          echo "Check the workflow artifacts for the detailed commit list"
          echo "File generated: ${{ steps.commit-diff.outputs.output_file }}"
