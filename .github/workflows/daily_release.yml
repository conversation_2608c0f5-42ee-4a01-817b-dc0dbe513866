name: daily_release

on:
  pull_request:
    branches:
      - master
    paths:
      - ".github/workflows/daily_release.yml"
      - ".github/workflows/version_increment.py"
  schedule:
   - cron: "0 10 * * *" # 3 am Pacific
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  GITHUB_TOKEN: ${{ secrets.CR_PAT }}
  DAILY_RELEASE_VERSION: ${{ vars.DAILY_RELEASE_VERSION}}
  SLACK_WEBHOOK_URL: ${{ secrets.SLACK_QA_RELEASE_ANNOUNCER_URL }}

jobs:
  daily_release:
    runs-on: [self-hosted, linux, dc_gpu]
    timeout-minutes: 200
    permissions:
      actions: write
      contents: write
      packages: write
    env:
      GIT_BRANCH: ${{github.head_ref}}
      MAKA_ROBOT_DIR:  ${{github.workspace}}
      MAKA_LOG_DIR: ${{github.workspace}}/tmp
      MAKA_ROLE: all
      CARBON_HOST_BASE_DIR: ${{github.workspace}}/..
      AUTH0_TOKEN_CACHE_FILE: /data/release_auth_token.json
      AUTH0_DOMAIN: ${{ secrets.AUTH0_DOMAIN }}
      AUTH0_CLIENT_ID: ${{ secrets.AUTH0_CLIENT_ID }}
      AUTH0_CLIENT_SECRET: ${{ secrets.AUTH0_CLIENT_SECRET }}
      TARGET_LATEST_TAG: ""
      RELEASE_TAG_NAME: ${{ github.event.release.tag_name }}
      S3_CACHE_PROXY_SERVICE_HOST: frankenmini01.dc.carbonrobotics.com
    steps:

      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: Load .env file
        uses: xom9ikk/dotenv@v2
        with:
          path: ${{github.workspace}}/../../..
      - name: Set env
        run: |
          echo "MAKA_DATA_DIR=${{env.DATA_DIR}}" >> $GITHUB_ENV
          echo "CUDA_VISIBLE_DEVICES=${{ env.CUDA_VISIBLE_DEVICES }}" >> $GITHUB_ENV
          echo "DEVICES_STR=${{ env.DEVICES_STR }}" >> $GITHUB_ENV
      - name: Set workspace permissions
        run: |
          chown -R $USER:$USER ${{ github.workspace }}

      - name: Checkout branch
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          lfs: true
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Checkout LFS objects
        run: |
          git lfs pull
          find ${{ github.workspace }} -name calibration.json | xargs ls -al

      - name: Increment Daily Version
        run: |
          export DAILY_RELEASE_VERSION=$(.github/workflows/version_increment.py)
          echo $DAILY_RELEASE_VERSION
          curl -L \
          -X PATCH \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${GITHUB_TOKEN}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/carbonrobotics/robot/actions/variables/DAILY_RELEASE_VERSION \
          -d "{\"name\":\"DAILY_RELEASE_VERSION\",\"value\":\"$DAILY_RELEASE_VERSION\"}"
          echo "DAILY_RELEASE_VERSION=$DAILY_RELEASE_VERSION" >> $GITHUB_ENV

      - name: Update Version in Repo
        run: |
          echo $DAILY_RELEASE_VERSION
          curl -L \
          -X PATCH \
          -H "Accept: application/vnd.github+json" \
          -H "Authorization: Bearer ${GITHUB_TOKEN}" \
          -H "X-GitHub-Api-Version: 2022-11-28" \
          https://api.github.com/repos/carbonrobotics/robot/actions/variables/DAILY_RELEASE_VERSION \
          -d "{\"name\":\"DAILY_RELEASE_VERSION\",\"value\":\"$DAILY_RELEASE_VERSION\"}"

      - name: Setup environment
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          else
            # If release tag name is c1.3.4, BRANCH_TAG will be shasum of c1.3
            BRANCH_TAG=`echo -n $DAILY_RELEASE_VERSION | sed 's/\.[0-9][0-9]*$//' | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            if [[ $DAILY_RELEASE_VERSION = "v*" ]]; then
              RELEASE_TAG_WITHOUT_PREFIX=${DAILY_RELEASE_VERSION#b}
              echo "TARGET_LATEST_TAG=${RELEASE_TAG_WITHOUT_PREFIX%.*}" >> $GITHUB_ENV  # vX.Y.Z -> X.Y
            fi
          fi
          docker images --no-trunc | grep -E "(REPOSITORY|${DOCKER_TAG})"

      - name: Echo Release
        run: |
          echo "Releasing version: $DAILY_RELEASE_VERSION"

      - name: Echo System Version
        run: |
          python -m tools.ci.read_system_version
          export CARBON_SYSTEM_VERSION=$(python -m tools.ci.read_system_version)
          echo "Releasing for System Version: ${CARBON_SYSTEM_VERSION}"

      - name: Setup Bot Context
        run: |
          export CI_ROBOT_USERNAME=${{ secrets.CI_ROBOT_USERNAME }}
          export CI_ROBOT_PASSWORD=${{ secrets.CI_ROBOT_PASSWORD }}
          python3 ${{github.workspace}}/bot/initialize.py ci --repo ${{github.workspace}}

      - name: Build Gobot Container
        run: |
          docker buildx build -t ghcr.io/carbonrobotics/robot/gobot:latest --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-amd64-cache --cache-from ghcr.io/carbonrobotics/robot/gobot:latest-arm64-cache  -f services/containers/gobot/Dockerfile .

      - name: Build and push containers
        run: |
          if [[ -n "$TARGET_LATEST_TAG" ]]; then 
            ${{github.workspace}}/bot/bot release --release $DAILY_RELEASE_VERSION --tag $BRANCH_TAG --tag $TARGET_LATEST_TAG --id ${{ github.sha }} --version $DAILY_RELEASE_VERSION -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
          else
            ${{github.workspace}}/bot/bot release --release $DAILY_RELEASE_VERSION --tag $BRANCH_TAG --id ${{ github.sha }} --version $DAILY_RELEASE_VERSION -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
          fi

      - name: Announce Release to QA
        run: |
          curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"$DAILY_RELEASE_VERSION Now Available for QA! <!subteam^S072UUNUG9M|Robot QA Masters>\"}" $SLACK_WEBHOOK_URL
