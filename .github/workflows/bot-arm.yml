name: bot-arm

on:
  pull_request:
    branches:
      - master
      - release/*
  push:
    branches:
      - master
      - release/*
    paths:
      - services/containers/common/Dockerfile
      - services/containers/common/Dockerfile.production

env:
  REGISTRY: ghcr.io

jobs:
  bot:
    runs-on: [self-hosted, arc-robot-ci-arm]
    timeout-minutes: 150
    permissions:
      actions: write
      contents: read
      packages: write
    env:
      CACHE_DIR: /cache
      CARBON_HOST_BASE_DIR: ${{ github.workspace }}/.data
      MAKA_ROBOT_DIR: ${{ github.workspace }}
      MAKA_BOT_MODE: develop
      GIT_BRANCH: ${{github.head_ref}}
      TARGET_LATEST_TAG: latest
      GITHUB_TOKEN: ${{ secrets.CR_PAT }}

    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.CR_PAT }}

      - name: Checkout github repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Cancel past CI runs
        uses: ./.github/actions/cancel_workflows

      - name: Set docker tag
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
            if [ "${{github.base_ref}}" != "master" ]; then # pr -> release branch
              echo "TARGET_LATEST_TAG=${${{github.base_ref}}##*/}" >> $GITHUB_ENV  # (release/X.Y -> X.Y)
            fi
          else
            echo "BRANCH_TAG=${{env.TARGET_LATEST_TAG}}" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=${{env.TARGET_LATEST_TAG}}-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          fi

      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci \
            --repo ${{github.workspace}} \
            --calibration_dir ${{env.CACHE_DIR}} \
            --role rtc

      - name: Build gobot
        uses: docker/build-push-action@v6
        with:
          build-args: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          context: .
          file: services/containers/gobot/Dockerfile
          cache-from: |
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.BRANCH_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.MAKA_DOCKER_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.TARGET_LATEST_TAG}}-arm64
          push: false
          load: true
          tags: |
            ghcr.io/carbonrobotics/robot/gobot:${{env.TARGET_LATEST_TAG}}
            ghcr.io/carbonrobotics/robot/gobot:latest

      - name: Bot Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull || true

      - name: Bot Build Push Branch Containers
        run: |
          if [ -n "${{ github.head_ref }}" ]; then
            # skip building unless docker files changed in pr
            ${{github.workspace}}/bot/bot build --tag ${{env.BRANCH_TAG}} --tag ${{env.MAKA_DOCKER_TAG}} --cache-from ${{env.BRANCH_TAG}} --cache-from ${{env.TARGET_LATEST_TAG}} -p -ba GITHUB_TOKEN=${{ secrets.CR_PAT }} --ci-base-revision "origin/${{github.base_ref}}" -srt ${{env.TARGET_LATEST_TAG}} --container-build-retries 3
          else
            # on master merge, this should build and push latest because there is no head ref
            ${{github.workspace}}/bot/bot build --tag ${{env.BRANCH_TAG}} --tag ${{env.MAKA_DOCKER_TAG}} --cache-from ${{env.BRANCH_TAG}} --cache-from ${{env.TARGET_LATEST_TAG}} -p -ba GITHUB_TOKEN=${{ secrets.CR_PAT }} --container-build-retries 3
          fi
