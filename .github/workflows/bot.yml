name: bot

on:
  pull_request:
    branches:
      - master
      - release/*
  push:
    branches:
      - master
      - release/*
  workflow_dispatch:

env:
  REGISTRY: ghcr.io

jobs:
  bot:
    runs-on: [self-hosted, arc-robot-ci]
    timeout-minutes: 150
    permissions:
      actions: write
      contents: read
      packages: write
    env:
      CACHE_DIR: /cache
      CARBON_HOST_BASE_DIR: ${{ github.workspace }}/.data
      MAKA_ROBOT_DIR: ${{ github.workspace }}
      MAKA_BOT_MODE: develop
      GIT_BRANCH: ${{github.head_ref}}
      TARGET_LATEST_TAG: latest
      GITHUB_TOKEN: ${{ secrets.CR_PAT }}

    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.CR_PAT }}

      - name: Checkout github repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Cancel past CI runs
        uses: ./.github/actions/cancel_workflows

      - name: Set docker tag
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            if [ "${{github.base_ref}}" != "master" ]; then # pr -> release branch
              echo "TARGET_LATEST_TAG=${GITHUB_BASE_REF##*/}" >> $GITHUB_ENV  # (release/X.Y -> X.Y)
            fi
          else
            if [ "$GITHUB_REF_NAME" = "master" ]; then # only push 'latest' for master
              BRANCH_TAG=${{env.TARGET_LATEST_TAG}}
              echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            else
              BRANCH_TAG=${GITHUB_REF_NAME##*/} # (release/X.Y -> X.Y)
              echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
              echo "TARGET_LATEST_TAG=$BRANCH_TAG" >> $GITHUB_ENV  
            fi
          fi
          echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV

      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci \
            --repo ${{github.workspace}} \
            --calibration_dir ${{env.CACHE_DIR}}

      - name: Bot Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull -a || true

      - name: Build gobot
        uses: docker/build-push-action@v6
        with:
          build-args: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          context: .
          file: services/containers/gobot/Dockerfile
          cache-from: |
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.BRANCH_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.MAKA_DOCKER_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.TARGET_LATEST_TAG}}
          push: false
          load: true
          tags: ghcr.io/carbonrobotics/robot/gobot:${{env.TARGET_LATEST_TAG}}

      - name: Remove Generated Files
        run: |
          ${{github.workspace}}/bot/bot run common make clean

      - name: Bot Build Push Branch Containers
        run: |
          ${{github.workspace}}/bot/bot run common ccache -s
          if [ -n "${{ github.head_ref }}" ]; then
            # skip building unless docker files changed in pr
            ${{github.workspace}}/bot/bot build --tag ${{env.BRANCH_TAG}} --tag ${{env.MAKA_DOCKER_TAG}} --cache-from ${{env.BRANCH_TAG}} --cache-from ${{env.TARGET_LATEST_TAG}} -p -a -ba GITHUB_TOKEN=${{ secrets.CR_PAT }} --ci-base-revision "origin/${{github.base_ref}}" -srt ${{env.TARGET_LATEST_TAG}} --container-build-retries 3
          else
            # on master merge, this should build and push latest because there is no head ref
            ${{github.workspace}}/bot/bot build --tag ${{env.BRANCH_TAG}} --tag ${{env.MAKA_DOCKER_TAG}} --cache-from ${{env.TARGET_LATEST_TAG}} -p -a -ba GITHUB_TOKEN=${{ secrets.CR_PAT }} --container-build-retries 3
          fi

      - name: Static Analysis
        run: |
          ${{github.workspace}}/bot/bot run --carbon-release-tag "${MAKA_DOCKER_TAG:-latest}" common make static_analysis

      - name: CPP Lint
        run: |
          make lint_cpp_ci

      - name: GO Lint
        run: |
          make lint_go

      - name: Grep for bad imports in lib/common/
        run: |
          ./tools/lint/no_bad_lib_common_imports

      - name: Unit Tests
        timeout-minutes: 15
        run: ${{github.workspace}}/bot/bot run --carbon-release-tag "${MAKA_DOCKER_TAG:-latest}" common make unit_tests

      - name: Ensure no diffs
        run: |
          # Cleanup temp files
          sudo rm -r data || true
          test/test_no_git_diffs
