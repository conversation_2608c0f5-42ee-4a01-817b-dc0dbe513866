name: release_arm

on:
  release:
    types: [published]

env:
  REGISTRY: ghcr.io

jobs:
  release_arm:
    runs-on: [self-hosted, arc-robot-ci-arm]
    timeout-minutes: 150
    permissions:
      actions: write
      contents: read
      packages: write
    env:
      CACHE_DIR: /cache
      CARBON_HOST_BASE_DIR: ${{ github.workspace }}/.data
      MAKA_ROBOT_DIR: ${{ github.workspace }}
      MAKA_BOT_MODE: develop
      GIT_BRANCH: ${{github.head_ref}}
      TARGET_LATEST_TAG: ""
      GITHUB_TOKEN: ${{ secrets.CR_PAT }}
      RELEASE_TAG_NAME: ${{ github.event.release.tag_name }}

    steps:
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.CR_PAT }}

      - name: Checkout github repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          lfs: true
          submodules: recursive
          token: ${{ secrets.CR_PAT }}

      - name: Cancel past CI runs
        uses: ./.github/actions/cancel_workflows

      - name: Set docker tag
        run: |
          if [ -n "${{ github.head_ref }}" ]; then # a PR
            BRANCH_TAG=`echo -n ${GIT_BRANCH} | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            echo "MAKA_DOCKER_TAG=$BRANCH_TAG-${{ github.run_id }}-${{ github.run_number }}" >> $GITHUB_ENV
          else
            # If release tag name is c1.3.4, BRANCH_TAG will be shasum of c1.3
            BRANCH_TAG=`echo -n $RELEASE_TAG_NAME | sed 's/\.[0-9][0-9]*$//' | shasum | awk '{print $1}'`
            echo "BRANCH_TAG=$BRANCH_TAG" >> $GITHUB_ENV
            if [[ $RELEASE_TAG_NAME = "v*" ]]; then
              RELEASE_TAG_WITHOUT_PREFIX=${RELEASE_TAG_NAME#b}
              echo "TARGET_LATEST_TAG=${RELEASE_TAG_WITHOUT_PREFIX%.*}" >> $GITHUB_ENV  # vX.Y.Z -> X.Y
            fi
          fi

      - name: Setup Bot Context
        run: |
          python3 ${{github.workspace}}/bot/initialize.py ci \
            --repo ${{github.workspace}} \
            --calibration_dir ${{env.CACHE_DIR}} \
            --role rtc

      - name: Build gobot
        uses: docker/build-push-action@v6
        with:
          build-args: |
            GITHUB_TOKEN=${{ secrets.CR_PAT }}
          context: .
          file: services/containers/gobot/Dockerfile
          cache-from: |
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.BRANCH_TAG}}
            type=registry,ref=ghcr.io/carbonrobotics/robot/gobot:${{env.MAKA_DOCKER_TAG}}
          push: false
          load: true
          tags: |
            ghcr.io/carbonrobotics/robot/gobot:latest

      - name: Bot Pull Latest Containers
        run: |
          ${{github.workspace}}/bot/bot pull || true
          ${{github.workspace}}/bot/bot pull deeplearning || true

      - name: Build and push containers
        run: |
          if [[ -n "$TARGET_LATEST_TAG" ]]; then 
            ${{github.workspace}}/bot/bot release --release $RELEASE_TAG_NAME --tag $BRANCH_TAG --tag $TARGET_LATEST_TAG --id ${{ github.sha }} --version $RELEASE_TAG_NAME -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
          else
            ${{github.workspace}}/bot/bot release --release $RELEASE_TAG_NAME --tag $BRANCH_TAG --id ${{ github.sha }} --version $RELEASE_TAG_NAME -ba GITHUB_TOKEN=${GITHUB_TOKEN} --container-build-retries 3
          fi
