name: Release Backport

# Currently only supports backporting to release branches
on:
  pull_request:
    types: [closed]

jobs:
  backport:
    if: github.event.pull_request.merged == true && contains(toJson(github.event.pull_request.labels.*.name), 'backport-to-release/')
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch full history for backporting
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract target releases from labels
        id: extract
        run: |
          # Store JSON in a variable to avoid shell interpretation issues
          LABELS_JSON='${{ toJson(github.event.pull_request.labels) }}'

          # Extract all backport labels
          LABELS=$(echo "$LABELS_JSON" | jq -r '.[] | select(.name | startswith("backport-to-release/")) | .name')

          if [ -z "$LABELS" ]; then
            echo "Error: No backport labels found!"
            exit 1
          fi

          # Convert labels to target branches and create space-delimited list
          TARGET_BRANCHES=""
          while IFS= read -r LABEL; do
            if [ -n "$LABEL" ]; then
              RELEASE_BRANCH=${LABEL#backport-to-}

              if [ -z "$RELEASE_BRANCH" ]; then
                echo "Error: Could not extract release branch from label: $LABEL"
                exit 1
              fi

              echo "Found backport label: $LABEL"
              echo "Target release branch: $RELEASE_BRANCH"

              # Add to space-delimited list
              if [ -z "$TARGET_BRANCHES" ]; then
                TARGET_BRANCHES="$RELEASE_BRANCH"
              else
                TARGET_BRANCHES="$TARGET_BRANCHES $RELEASE_BRANCH"
              fi
            fi
          done <<< "$LABELS"

          echo "All target release branches: $TARGET_BRANCHES"
          echo "target_branches=$TARGET_BRANCHES" >> "$GITHUB_OUTPUT"

      - name: Create backport PRs
        uses: korthout/backport-action@v3
        with:
          # The source PR number to backport
          source_pr_number: ${{ github.event.pull_request.number }}
          # The target branches to backport to (space-delimited list)
          target_branches: ${{ steps.extract.outputs.target_branches }}
          # GitHub token for authentication
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # Custom title for the backport PRs (will be applied to each target branch)
          pull_title: "[Backport to ${target_branch}] ${{ github.event.pull_request.title }}"
        id: backport
