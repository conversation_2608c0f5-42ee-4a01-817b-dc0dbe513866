import json
import logging
import multiprocessing
import time
from typing import Any, Optional

import boto3
import numpy as np
import numpy.typing as npt
import panel as pn
import requests
from PIL import Image

from server.config import IMAGE_SERVICE_URL
from server.db import get_session, tables
from server.db.utils import session_scope_decorator
from server.utils.helpers import split_s3_url

LOG = logging.getLogger(__name__)


NUM_IMAGES_PER_ROW = 6
NUM_WORKERS = 25
PAGE_SIZE = 25
MARGIN = 200
GALLERY_GRID_SIZE = 150
MIN_CHIP_SIZE = 400
GET_CHIP_MAX_RETRIES = 200
GET_CHIP_SLEEP_TIME = 0.1

s3 = boto3.client("s3")


def get_chip(point: tables.LabelPoint, url: str) -> Any:
    key = url.split("s3://", 1)[1]

    width = int(2 * point.radius)
    height = int(2 * point.radius)
    if width < MIN_CHIP_SIZE:
        width = MIN_CHIP_SIZE
    if height < MIN_CHIP_SIZE:
        height = MIN_CHIP_SIZE

    if "bud" in key:
        x = int(point.x - width / 2)
        y = int(point.y - height / 2)
        if x + width > 3000:
            width = 3000 - x
        if y + height > 4096:
            height = 4096 - y
    elif "slayer" in key or "reaper" in key:
        x = int(point.x - height / 2)
        y = int(point.y - width / 2)
        if x + width > 4096:
            width = 4096 - x
        if y + height > 3000:
            height = 3000 - y
    else:
        raise ValueError(f"Unknown key: {key}")

    response = requests.get(
        f"http://{IMAGE_SERVICE_URL}/{key}?x={x}&y={y}&width={width}&height={height}&op=crop&fill=ffffff&warm=1",
        timeout=30,
    )

    count = 0
    while not response.ok or response.content == b"":
        if count > GET_CHIP_MAX_RETRIES:
            raise Exception(f"Failed to retrieve item ({key}) from cache: {response.text}")

        time.sleep(GET_CHIP_SLEEP_TIME)
        response = requests.get(
            f"http://{IMAGE_SERVICE_URL}/{key}?x={x}&y={y}&width={width}&height={height}&op=crop&fill=ffffff&warm=1",
            timeout=30,
        )
        count += 1

    image_url = json.loads(response.content.decode("utf-8"))["s3_uri"]
    bucket, key = split_s3_url(image_url)
    pre_signed_url = s3.generate_presigned_url("get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=60)

    return pre_signed_url


def get_point_datasets() -> list[tables.PointDataset]:
    session = get_session()

    point_datasets: list[tables.PointDataset] = session.query(tables.PointDataset).all()

    return point_datasets


def create_point_dataset(name: str) -> tables.PointDataset:
    session = get_session()

    point_dataset = tables.PointDataset(name=name, is_private=False)
    session.add(point_dataset)
    session.commit()

    session.refresh(point_dataset)

    return point_dataset


def get_point_dataset(point_dataset_id: str) -> Optional[tables.PointDataset]:
    session = get_session()
    point_dataset: Optional[tables.PointDataset] = session.query(tables.PointDataset).get(point_dataset_id)
    return point_dataset


def get_label_points(model_id: str, page: int, page_size: int) -> list[tables.LabelPoint]:
    session = get_session()
    label_points: list[tables.LabelPoint] = (
        session.query(tables.LabelPoint)
        .filter(tables.LabelPoint.embeddings.any(tables.Embedding.model_id == model_id))
        .order_by(tables.LabelPoint.id)
        .offset((page - 1) * page_size)
        .limit(page_size)
        .all()
    )

    return label_points


def add_category_to_point_dataset(category_name: str, dataset: tables.PointDataset) -> None:
    session = get_session()

    point_dataset_categories = (
        session.query(tables.PointDatasetCategory)
        .filter(
            tables.PointDatasetCategory.name == category_name,
            tables.PointDatasetCategory.point_dataset_id == dataset.id,
        )
        .all()
    )

    if len(point_dataset_categories) > 0:  # If the category already exists in the dataset
        return

    dataset.categories.append(tables.PointDatasetCategory(name=category_name, color_hex="#000000"))
    session.commit()


def remove_category_from_point_dataset(category: tables.PointDatasetCategory) -> None:
    session = get_session()

    session.delete(category)
    session.commit()


def add_label_point_to_point_dataset_category(
    label_point: tables.LabelPoint, category: tables.PointDatasetCategory
) -> None:
    session = get_session()

    category.label_points.append(label_point)
    session.commit()


def remove_label_point_from_point_dataset_category(
    label_point: tables.LabelPoint, category: tables.PointDatasetCategory
) -> None:
    session = get_session()

    category.label_points.remove(label_point)
    session.commit()


def cosine_cdist(x1: npt.NDArray[Any], x2: npt.NDArray[Any]) -> npt.NDArray[Any]:
    x1 = x1 / np.linalg.norm(x1, axis=1).reshape(-1, 1)
    x2 = x2 / np.linalg.norm(x2, axis=1).reshape(-1, 1)

    result: npt.NDArray[Any] = x1 @ x2.T

    return result


def knn_with_k1(support: npt.NDArray[Any], query: npt.NDArray[Any]) -> list[int]:
    distances = cosine_cdist(support, query)
    nn: list[int] = np.argmax(distances, axis=0).tolist()
    return nn


class Page:
    def __init__(self) -> None:
        self.page = 1
        self.page_size = PAGE_SIZE
        self.image_cache: dict[str, Image.Image] = {}

        self.tabs: Optional[pn.Tabs] = None
        self.active_tab: Optional[int] = None

        self.point_dataset: Optional[tables.PointDataset] = None
        self.support_set: Optional[dict[str, tables.LabelPoint]] = None
        self.query_set: Optional[dict[str, tables.LabelPoint]] = None

        self.spinner = pn.indicators.LoadingSpinner(value=False, size=30)

        # Dataset options
        self.widget_model_id = pn.widgets.TextInput(placeholder="Model ID")
        self.widget_point_dataset = pn.widgets.AutocompleteInput(
            options=[d.name for d in get_point_datasets()], placeholder="Support set search"
        )

        self.widget_new_point_dataset = pn.widgets.TextInput(placeholder="New Point Dataset Name")
        self.widget_new_point_dataset_button = pn.widgets.Button(name="Create Support Set", button_type="primary")

        pn.bind(self.on_new_point_dataset_button_click, self.widget_new_point_dataset_button, watch=True)

        # Category options
        self.widget_new_category = pn.widgets.TextInput(placeholder="New Category Name")
        self.widget_new_category_button = pn.widgets.Button(name="Create Category", button_type="success")
        self.widget_delete_category_button = pn.widgets.Button(name="Delete Category", button_type="danger")

        self.widget_page_size = pn.widgets.IntSlider(name="Page Size", start=5, end=100, value=self.page_size)

        point_dataset = pn.bind(self.update_point_dataset, self.widget_point_dataset, watch=True)
        support_set = pn.bind(self.update_support_set, point_dataset)
        query_set = pn.bind(self.update_query_set, self.widget_model_id, support_set)

        pn.bind(self.on_new_category_button_click, self.widget_new_category_button, watch=True)
        pn.bind(self.on_delete_current_category_button_click, self.widget_delete_category_button, watch=True)

        pn.bind(self.update_page_size, self.widget_page_size, watch=True)

        # Page Options
        self.page_left_button = pn.widgets.Button(name="<< Page Left")
        self.page_right_button = pn.widgets.Button(name="Page Right >>")
        self.current_page_indicator = pn.pane.Markdown(f"**{self.page}**")

        pn.bind(self.page_left, self.page_left_button, watch=True)
        pn.bind(self.page_right, self.page_right_button, watch=True)

        classifications = pn.bind(self.update_classifications, point_dataset, support_set, query_set)

        self.title = self.render_title()
        self.dataset_options = self.render_dataset_options()
        self.category_options = pn.bind(self.render_category_options, point_dataset)
        self.image_tabs = pn.bind(
            self.render_image_tabs,
            self.widget_model_id,
            point_dataset,
            support_set,
            query_set,
            classifications,
        )
        self.page_buttons = pn.bind(self.render_page_buttons, point_dataset)

    def update_page_size(self, event: bool) -> None:
        LOG.info(f"Ran update_page_size: {event} {self.widget_page_size.value}")
        self.spinner.value = True
        if event:
            self.page_size = self.widget_page_size.value

            self.widget_point_dataset.param.trigger("value")

        return None

    def page_left(self, event: bool) -> None:
        self.spinner.value = True
        if self.page > 1 and event:
            LOG.info(f"Moving Page {self.page} -> {self.page - 1}")
            self.page -= 1
            self.current_page_indicator.object = f"**{self.page}**"

            self.widget_point_dataset.param.trigger("value")

        return None

    def page_right(self, event: bool) -> None:
        self.spinner.value = True
        if event:
            LOG.info(f"Moving Page {self.page} -> {self.page + 1}")
            self.page += 1
            self.current_page_indicator.object = f"**{self.page}**"

            self.widget_point_dataset.param.trigger("value")

        return None

    def on_new_point_dataset_button_click(self, event: bool) -> None:
        LOG.info(f"Ran on_new_point_dataset_button_click: {event}")
        if not event:
            return None

        new_dataset_name = self.widget_new_point_dataset.value

        if new_dataset_name == "":
            return None

        create_point_dataset(new_dataset_name)

        self.widget_point_dataset.options = [d.name for d in get_point_datasets()]
        self.widget_point_dataset.value = new_dataset_name
        self.widget_point_dataset.param.trigger("value")

        return None

    def on_delete_current_category_button_click(self, event: bool) -> None:
        LOG.info(f"Ran on_delete_current_category_button_click: {event}")

        if not event:
            return None

        category_name = self.widget_new_category.value

        if category_name == "":
            return None

        if self.point_dataset is None:
            return None

        category = [category for category in self.point_dataset.categories if category.name == category_name][0]

        remove_category_from_point_dataset(category)

        self.widget_point_dataset.param.trigger("value")

    def on_new_category_button_click(self, event: bool) -> None:
        LOG.info(f"Ran on_new_category_button_click: {event}")

        if not event:
            return None

        category_name = self.widget_new_category.value

        if category_name == "":
            return None

        if self.point_dataset is None:
            return None

        add_category_to_point_dataset(category_name, self.point_dataset)

        self.widget_new_category.value = ""
        self.widget_point_dataset.param.trigger("value")

    def update_point_dataset(self, dataset_name: Optional[str]) -> Optional[tables.PointDataset]:
        point_dataset: Optional[tables.PointDataset] = None
        LOG.info(f"Dataset Name: {dataset_name}")

        if dataset_name != "":
            point_datasets = get_point_datasets()
            names = [d.name for d in point_datasets]
            point_dataset = point_datasets[names.index(dataset_name)]
        else:
            point_dataset = None

        LOG.info(f"Ran update_point_dataset: {point_dataset is not None}")

        self.point_dataset = point_dataset

        return point_dataset

    def update_support_set(self, point_dataset: Optional[tables.PointDataset]) -> dict[str, tables.LabelPoint]:
        if point_dataset is None:
            return {}

        support_set = {}

        for category in point_dataset.categories:
            for point in category.label_points:
                if len(point.embeddings) > 0:
                    support_set[point.id] = point

        LOG.info(f"Ran update_support_set: {len(support_set)} points")

        self.support_set = support_set

        return support_set

    def update_query_set(
        self, model_id: Optional[str], support_set: dict[str, tables.LabelPoint]
    ) -> dict[str, tables.LabelPoint]:
        if model_id is None:
            return {}

        points = get_label_points(model_id, self.page, self.page_size)
        query_set = {point.id: point for point in points if point.id not in support_set.keys()}

        LOG.info(f"Ran update_query_set: {len(query_set)} points")

        self.query_set = query_set

        return query_set

    def update_classifications(
        self,
        point_dataset: Optional[tables.PointDataset],
        support_set: dict[str, tables.LabelPoint],
        query_set: dict[str, tables.LabelPoint],
    ) -> dict[str, tables.PointDatasetCategory]:
        classifications = {}

        if point_dataset is not None:
            point_id2category = {}
            for category in point_dataset.categories:
                for point in category.label_points:
                    point_id2category[point.id] = category

            support_set_list = list(support_set.values())
            query_set_list = list(query_set.values())

            support_set_embeddings = np.array([point.embeddings[0].embedding for point in support_set_list])
            query_set_embeddings = np.array([point.embeddings[0].embedding for point in query_set_list])

            if len(support_set_embeddings.shape) == 1:
                support_set_embeddings = support_set_embeddings.reshape(-1, 1)
            if len(query_set_embeddings.shape) == 1:
                query_set_embeddings = query_set_embeddings.reshape(-1, 1)

            if support_set_embeddings.shape[0] != 0 and query_set_embeddings.shape[0] != 0:
                raw_classifications = knn_with_k1(support_set_embeddings, query_set_embeddings)

                for query_index, support_index in enumerate(raw_classifications):
                    support_set_point = support_set_list[support_index]
                    query_set_point = query_set_list[query_index]
                    category = point_id2category[support_set_point.id]
                    classifications[query_set_point.id] = category

        LOG.info(f"Ran update_classifications: {len(classifications)} points")

        return classifications

    def render_title(self) -> pn.Column:
        return pn.Column(pn.pane.Markdown("# Episode Evaluator"))

    def render_dataset_options(self) -> pn.Column:
        return pn.Column(
            pn.Row(
                pn.Row(self.widget_model_id, self.widget_point_dataset, self.spinner),
                pn.layout.HSpacer(),
                pn.Row(self.widget_new_point_dataset, self.widget_new_point_dataset_button, align="end"),
            )
        )

    def render_category_options(self, point_dataset: Optional[tables.PointDataset]) -> pn.Column:
        if point_dataset is None:
            return pn.Column()
        else:
            return pn.Column(
                pn.Row(
                    self.widget_new_category,
                    self.widget_new_category_button,
                    self.widget_delete_category_button,
                    pn.layout.HSpacer(),
                    self.widget_page_size,
                ),
                sizing_mode="stretch_width",
            )

    def render_page_buttons(self, point_dataset: Optional[tables.PointDataset]) -> pn.Column:
        self.spinner.value = False

        if point_dataset is None:
            return pn.Column()
        else:
            return pn.Column(
                pn.Row(
                    self.page_left_button,
                    pn.layout.HSpacer(),
                    self.current_page_indicator,
                    pn.layout.HSpacer(),
                    self.page_right_button,
                ),
                sizing_mode="stretch_width",
            )

    def render_image_tabs(
        self,
        model_id: Optional[str],
        point_dataset: Optional[tables.PointDataset],
        support_set: dict[str, tables.LabelPoint],
        query_set: dict[str, tables.LabelPoint],
        classifications: dict[str, tables.PointDatasetCategory],
    ) -> pn.Column:
        self.spinner.value = True
        if model_id is None or len(model_id) == 0 or point_dataset is None:
            return pn.Column()

        galleries = [self.get_image_gallery(point_dataset, support_set, query_set, classifications)]
        names = ["All"] + [category.name for category in point_dataset.categories]

        if point_dataset is not None:
            for category in point_dataset.categories:
                gallery = self.get_image_gallery(point_dataset, support_set, query_set, classifications, category)
                galleries.append(gallery)

        self.tabs = pn.Tabs(*[(x, y) for x, y in zip(names, galleries)])

        if self.active_tab is not None:
            self.tabs.active = self.active_tab
        else:
            self.active_tab = 0

        return pn.Column(self.tabs)

    def get_category(
        self, point_id: str, point_dataset: Optional[tables.PointDataset]
    ) -> Optional[tables.PointDatasetCategory]:
        if point_dataset is None:
            return None

        category: tables.PointDatasetCategory
        for category in point_dataset.categories:
            for point in category.label_points:
                if point.id == point_id:
                    return category

        return None

    def get_image_gallery(
        self,
        point_dataset: Optional[tables.PointDataset],
        support_set: dict[str, tables.LabelPoint],
        query_set: dict[str, tables.LabelPoint],
        classifications: dict[str, tables.PointDatasetCategory],
        category: Optional[tables.PointDatasetCategory] = None,
    ) -> pn.Column:
        if point_dataset is None:
            return pn.Column()
        if category is None:
            filtered_support_set = support_set
            filtered_query_set = query_set
        else:
            point_ids = [point.id for point in category.label_points]
            filtered_support_set = {point_id: point for point_id, point in support_set.items() if point_id in point_ids}
            cats = [self.get_category(id, point_dataset) for id in point_ids]
            prediction_ids = [
                point_id
                for point_id, cat in classifications.items()
                if cat.name == category.name and point_id in query_set
            ]
            filtered_query_set = {point_id: query_set[point_id] for point_id in prediction_ids}

        filtered_support_set_list = list(filtered_support_set.values())
        filtered_query_set_list = list(filtered_query_set.values())

        if len(filtered_support_set_list) + len(filtered_query_set_list) == 0:
            return pn.Column(pn.pane.Markdown("No images found."))

        images = {}
        points = {}

        for point in filtered_support_set_list:
            points[point.id] = point

        for point in filtered_query_set_list:
            points[point.id] = point

        with multiprocessing.Pool(NUM_WORKERS) as pool:
            futures = []
            for i, (point_id, point) in enumerate(points.items()):
                if point_id in self.image_cache:
                    images[i] = self.image_cache[point_id]
                bucket, key = split_s3_url(point.image.url)
                futures.append((pool.apply_async(get_chip, (point, point.image.url)), i, point_id))

            for future, i, point_id in futures:
                image_url = future.get()
                images[i] = image_url
                self.image_cache[point_id] = image_url

        image_cols = []

        for i, (point_id, point) in enumerate(points.items()):
            image_url = images[i]

            image_pane = pn.pane.Image(image_url, height=GALLERY_GRID_SIZE, width=GALLERY_GRID_SIZE)
            menu_items = [c.name for c in point_dataset.categories] + ["Query Set"]

            menu_button = pn.widgets.Select(options=menu_items, width=GALLERY_GRID_SIZE)

            current_category = self.get_category(point_id, point_dataset)
            menu_button.value = current_category.name if current_category is not None else "Query Set"
            img_bind = pn.bind(
                lambda event, point=point, point_dataset=point_dataset: self.on_dropdown_select(
                    event, point, point_dataset
                ),
                menu_button,
            )

            if point_id in filtered_support_set:
                text = pn.pane.Str(f"Support Set")
            else:
                classification = classifications.get(point_id)

                if classification is None:
                    text = pn.pane.Str("Query Set", styles={"font-size": "10pt"})
                else:
                    text = pn.pane.Str(f"{classifications[point_id].name}", styles={"font-size": "10pt"})

            image_column = pn.Column(image_pane, menu_button, text, img_bind)
            image_cols.append(image_column)

        flex_box = pn.FlexBox(
            *image_cols,
            sizing_mode="stretch_width",
            align_content="space-evenly",
            justify_content="space-evenly",
        )
        return pn.Column(flex_box, sizing_mode="stretch_width")

    def on_dropdown_select(
        self, new_category_name: str, point: tables.LabelPoint, point_dataset: tables.PointDataset
    ) -> None:
        current_category = self.get_category(point.id, point_dataset)

        if current_category is None:
            current_category_name = "Query Set"
        else:
            current_category_name = current_category.name

        if new_category_name == current_category_name:
            return None

        LOG.info(f"Moving {current_category_name} to {new_category_name}")

        new_category = None
        if new_category_name == "Query Set":
            assert current_category is not None
            remove_label_point_from_point_dataset_category(point, current_category)
        else:
            new_category = [c for c in point_dataset.categories if c.name == new_category_name][0]

            if current_category_name == "Query Set":
                assert new_category is not None
                add_label_point_to_point_dataset_category(point, new_category)
            else:
                assert current_category is not None
                remove_label_point_from_point_dataset_category(point, current_category)
                add_label_point_to_point_dataset_category(point, new_category)

        if self.tabs is not None:
            active_tab = self.tabs.active
            assert isinstance(active_tab, int)
            self.active_tab = active_tab

        self.widget_point_dataset.param.trigger("value")


@session_scope_decorator(do_commit=True)
def get_page() -> pn.Column:
    page = Page()

    return pn.Column(
        pn.Spacer(max_height=50, sizing_mode="stretch_both"),
        pn.Row(
            pn.Spacer(max_width=MARGIN, sizing_mode="stretch_both"),
            pn.Column(
                page.title,
                page.dataset_options,
                page.category_options,
                page.image_tabs,
                page.page_buttons,
                sizing_mode="stretch_width",
                min_width=360,
            ),
            pn.Spacer(max_width=MARGIN, sizing_mode="stretch_both"),
            sizing_mode="stretch_width",
            align=("center", "center"),
        ),
    )
