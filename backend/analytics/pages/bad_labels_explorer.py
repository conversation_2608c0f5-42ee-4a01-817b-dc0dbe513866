import json
from typing import Any, Optional

import boto3
import pandas
import panel
import plotly
import requests

from server import config, utils
from server.db import queries, tables
from server.db.utils import session_scope_decorator


def get_data(image_id: str) -> Optional[dict[str, Any]]:
    if image_id == "":
        return None

    s3 = boto3.client("s3")
    response = s3.list_objects_v2(Bucket="carbon-ml", Prefix=f"bad-label-mining/{config.NAMESPACE}/{image_id}")

    results: list[str] = []
    for item in response.get("Contents", []):
        results.append(item["Key"])

    if len(results) == 0:
        return None

    results.sort(key=lambda x: x.split("/")[-1])

    key = results[-1]

    if key is None or key == "":
        return None

    object = utils.get_s3_object_body("carbon-ml", key)

    if object is None:
        return None

    result: dict[str, Any] = json.loads(object.decode("utf-8"))

    return result


def get_object_ids(data: Optional[dict[str, Any]]) -> Optional[dict[str, Any]]:
    if data is None:
        return None
    return {"image_id": data.get("image_id"), "label_id": data.get("label_id")}


def format_data(data: Optional[dict[str, Any]]) -> Optional[pandas.DataFrame]:
    if data is None:
        return None

    results = data.get("results", {})

    flattened_result = []
    for point_id, result in results.items():
        for model_id, detections in result.items():
            for detection in detections:
                flattened_result.append(
                    {
                        "point_id": point_id,
                        "model_id": model_id,
                        "plant": detection["plant"],
                        "crop": detection["crop"],
                        "weed": detection["weed"],
                    }
                )

    df = pandas.DataFrame.from_records(flattened_result)

    return df


def get_figure(df: Optional[pandas.DataFrame], num_models: int) -> Optional[panel.Column]:
    if df is None:
        return None

    figure = plotly.express.scatter(df, y=["plant", "weed", "crop"], x="model_id")

    figure.update_xaxes({"range": (-0.5, num_models + 0.5), "autorange": False})
    figure.layout.autosize = True

    return panel.Column(
        panel.pane.Plotly(figure, config={"responsive": True}), min_height=600, sizing_mode="stretch_width"
    )


def get_image_subset(image: Optional[tables.Image], point: Optional[tables.LabelPoint]) -> Optional[panel.Column]:
    if image is None or point is None:
        return None

    if config.IMAGE_SERVICE_URL is None:
        return None

    image_url = f"https://{config.IMAGE_SERVICE_URL}//{image.url[5:]}?height=200&width=200&op=crop"

    image_subset = panel.pane.Image(image_url, sizing_mode="stretch_width", align=("start", "center"))

    return image_subset


def get_analysis(
    df: Optional[pandas.DataFrame], image: Optional[tables.Image], label: Optional[tables.Label]
) -> Optional[panel.Column]:
    if df is None or image is None or label is None:
        return None

    points = sorted(label.point_objs, key=lambda x: x.id)

    index2point_id = {index: point.id for index, point in enumerate(points)}
    index2point = {index: point for index, point in enumerate(points)}

    index = panel.widgets.EditableIntSlider(
        name="Label Point Index", start=0, end=len(label.point_objs) - 1, step=1, value=0
    )

    point_id = panel.bind(lambda x: index2point_id[x], index)
    point = panel.bind(lambda x: index2point[x], index)
    subset_df = panel.bind(lambda x: df[df["point_id"] == x], point_id)

    classification = panel.bind(
        lambda x: f"Human classification: {queries.point_categories.get(x.point_category_id)}", point
    )
    figure = panel.bind(get_figure, subset_df, len(df["model_id"].unique()))
    image_subset = panel.bind(get_image_subset, image, point)

    analysis = panel.Column(index, classification, figure, image_subset, sizing_mode="stretch_width")

    return analysis


@session_scope_decorator(do_commit=True)
def get_page() -> panel.Column:
    panel.extension(loading_spinner="dots", loading_color="#1e52d6")
    panel.extension("mathjax")
    panel.extension("plotly")

    panel.param.ParamMethod.loading_indicator = True

    image_id = panel.widgets.TextInput(placeholder="Enter an image ID here...", sizing_mode="stretch_width")
    data = panel.bind(get_data, image_id)

    object_ids = panel.bind(get_object_ids, data)

    image = panel.bind(lambda x: queries.images.get(x["image_id"]) if x is not None else None, object_ids)
    label = panel.bind(lambda x: queries.labels.get(x["label_id"]) if x is not None else None, object_ids)

    verification = panel.bind(lambda x: panel.indicators.BooleanStatus(value=x is not None, color="success"), image)

    df = panel.bind(format_data, data)

    analysis = panel.bind(get_analysis, df, image, label)

    page = panel.Column(
        panel.Spacer(max_height=50, sizing_mode="stretch_both"),
        panel.Row(
            panel.Spacer(max_width=400, sizing_mode="stretch_both"),
            panel.Column(
                panel.pane.Markdown("# Bad Label Miner"),
                panel.Row(image_id, panel.Column(panel.Spacer(height=5), verification)),
                analysis,
                sizing_mode="stretch_width",
                max_width=1600,
                min_width=360,
            ),
            panel.Spacer(max_width=400, sizing_mode="stretch_both"),
            sizing_mode="stretch_width",
        ),
    )

    return page
