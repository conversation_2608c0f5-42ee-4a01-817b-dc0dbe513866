import logging
from typing import Optional

import boto3
import requests

from analytics.config import MOD<PERSON>
from server.config import IMAGE_SERVICE_URL
from server.constants import MODE_DEVELOPMENT
from server.db import queries, tables
from server.utils.helpers import split_s3_url

from .constants import MIN_CHIP_SIZE, PAGE_SIZE

ROSY_ENDPOINT = "/internal/v1/profiles"

LOG = logging.getLogger(__name__)

s3 = boto3.client("s3")


def get_chip_presigned_url_with_retries(point: tables.PredictionPoint, retries: int = 60) -> str:
    result = None
    for _ in range(retries):
        if MODE == MODE_DEVELOPMENT:
            result = "https://images.squarespace-cdn.com/content/v1/606b788e71df0270bd2616de/8d136b76-e59f-45e6-b77d-07addb8a223f/cr-logo_combo-wide-color-white_400px.png?format=1500w"
        else:
            result = get_chip_presigned_url(point)

        if result is not None:
            break

    if result is None:
        raise RuntimeError(f"Failed to get presigned url: {point.id}")

    return result


def get_chip_presigned_url(point: tables.PredictionPoint | tables.LabelPoint) -> Optional[str]:
    if isinstance(point, tables.LabelPoint):
        url = point.label.tsk.images[0].url
    else:
        url = point.prediction.image.url

    key = url.split("s3://", 1)[1]
    width = int(2 * point.radius)
    height = int(2 * point.radius)
    if width < MIN_CHIP_SIZE:
        width = MIN_CHIP_SIZE
    if height < MIN_CHIP_SIZE:
        height = MIN_CHIP_SIZE
    x = int(point.x - width / 2)
    y = int(point.y - height / 2)

    request_url = (
        f"http://{IMAGE_SERVICE_URL}/{key}?x={x}&y={y}&width={width}&height={height}&op=crop&fill=000000&warm=1"
    )

    LOG.info(f"{request_url}")

    response = requests.get(request_url, timeout=30)

    LOG.info(f"{response.json()}")

    result: Optional[str] = None
    if response.ok:
        bucket, key = split_s3_url(response.json().get("s3_uri"))
        result = s3.generate_presigned_url("get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=600)

    return result


def get_similar_points(
    model: tables.Model,
    embedding: list[float],
    page_size: int,
    page: int,
    crop_id: Optional[str] = None,
    robot_id: Optional[str] = None,
    point_category_id: Optional[str] = None,
    min_radius: Optional[float] = None,
    max_radius: Optional[float] = None,
) -> tuple[list[tables.LabelPoint], list[float]]:
    results = queries.label_points.similarity_search(
        model,
        embedding,
        page_size=page_size,
        page=page,
        crop_id=crop_id,
        robot_id=robot_id,
        point_category_id=point_category_id,
        min_radius=min_radius,
        max_radius=max_radius,
    )
    label_points = []
    distances = []

    for embedding, distance in results:
        label_points.append(embedding.point)
        distances.append(distance)

    return label_points, distances


def get_point(point_id: str) -> Optional[tables.LabelPoint | tables.PredictionPoint]:
    point: Optional[tables.LabelPoint | tables.PredictionPoint] = None
    point = queries.prediction_points.get(point_id)
    if point is None:
        point = queries.label_points.get(point_id)

    return point


def get_model(model_id: str) -> Optional[tables.Model]:
    model: Optional[tables.Model] = queries.models.get(model_id)
    return model
