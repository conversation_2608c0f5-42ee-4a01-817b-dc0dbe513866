import json
import logging
import time
from typing import Any, List, Optional, Tuple

import boto3
import numpy as np
import numpy.typing as npt
import requests

from analytics.config import MOD<PERSON>, ROSY_ADDRESS
from server.config import IMAGE_SERVICE_URL
from server.constants import MODE_DEVELOPMENT
from server.db import tables
from server.utils.helpers import split_s3_url

from .constants import CATEGORY_COLLECTION_PROFILE_ENUM, CATEGORY_PROFILE_ENUM, MIN_CHIP_SIZE
from .types import CategoryCollectionProfile, CategoryProfile

ROSY_ENDPOINT = "/internal/v1/profiles"

LOG = logging.getLogger(__name__)

s3 = boto3.client("s3")


def cosine_cdist(x1: npt.NDArray[Any], x2: npt.NDArray[Any]) -> npt.NDArray[Any]:
    x1 = x1 / np.linalg.norm(x1, axis=1).reshape(-1, 1)
    x2 = x2 / np.linalg.norm(x2, axis=1).reshape(-1, 1)
    result: npt.NDArray[Any] = x1 @ x2.T
    return result


def get_category_collection_by_id(profile_id: str) -> Tuple[Optional[CategoryCollectionProfile], List[CategoryProfile]]:
    collection = None
    category_profiles: list[CategoryProfile] = []
    if MODE != MODE_DEVELOPMENT and len(profile_id) > 0:
        response = requests.get(f"http://{ROSY_ADDRESS}{ROSY_ENDPOINT}/{profile_id}")
        if response.status_code != 200:
            raise RuntimeError(f"Failed to get category collection: {profile_id}")

        elif response.json()["profileType"] != CATEGORY_COLLECTION_PROFILE_ENUM:
            raise RuntimeError(f"Profile {profile_id} is not a category collection")

        collection = CategoryCollectionProfile.model_validate_json(response.json()["profile"])
        assert collection.categoryIds is not None, "No category Profile IDs in collection"
        for category_id in collection.categoryIds:
            response = requests.get(f"http://{ROSY_ADDRESS}{ROSY_ENDPOINT}/{category_id}")
            if response.status_code != 200:
                raise RuntimeError(f"Failed to get category: {category_id}")

            elif response.json()["profileType"] != CATEGORY_PROFILE_ENUM:
                raise RuntimeError(f"Profile {category_id} is not a category")

            category_profiles.append(CategoryProfile.model_validate_json(response.json()["profile"]))
    elif MODE == MODE_DEVELOPMENT and len(profile_id) > 0:
        collection_json = '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"2986c254-7efc-498a-970a-dc930eac1816","profileType":7,"profile":"{\\"id\\":\\"2986c254-7efc-498a-970a-dc930eac1816\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Baby lettuce 2.0\\", \\"categoryIds\\":[\\"692a0b10-e4d3-436e-a05a-16e9bc64b13a\\", \\"e7263d80-aedc-48cf-a140-1f9446617632\\", \\"656e7bc0-1d81-41bb-b4d2-209c6476305c\\", \\"0d34eef3-db70-4229-a837-7a7fdcd12b40\\", \\"23ef2ba2-e758-46f0-abfd-11a61e686f9d\\", \\"5874b57d-8876-4e6c-8264-3521919cbcd1\\"]}","protected":false,"updatedAt":1749063323383,"deleted":false}'
        category_jsons = [
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"692a0b10-e4d3-436e-a05a-16e9bc64b13a","profileType":8,"profile":"{\\"id\\":\\"692a0b10-e4d3-436e-a05a-16e9bc64b13a\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Broadleaf\\", \\"chipIds\\":[\\"271cfc64-9760-4234-9ebe-6a2cb3f1e510\\", \\"ec7923f9-210e-483d-9d78-e0ed475ec503\\", \\"7026e744-c060-4c00-9e49-4fc537d6d69e\\", \\"93dfda0f-eeed-4877-af09-7bfcf325ff7f\\", \\"af5a404b-1783-4763-af00-8a704dd67d69\\", \\"5dfd09fa-383f-4e03-90b5-2b279829e66b\\", \\"54a48530-2bdf-4cad-8d50-be326c9fbd7f\\", \\"cbddb976-b414-4ce4-b7b7-fe0bbbc4e427\\", \\"598690fe-9ddb-4f8f-b1b7-012eb137dd1b\\", \\"ad615e12-8048-4c66-a23c-3990c57d2041\\", \\"a090162b-aa85-4355-a86d-5644641e51de\\", \\"6bd65e8c-9ca8-4553-9d78-64b6761ccd08\\", \\"6040843f-9ed7-48ba-8574-6646c7f9dd54\\", \\"923b6d96-79d3-4a6b-9f9a-26a8f76d75a7\\", \\"d20d8818-d0af-4575-b9d3-75184ffa67f6\\"]}","protected":false,"updatedAt":1749063323196,"deleted":false}',
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"e7263d80-aedc-48cf-a140-1f9446617632","profileType":8,"profile":"{\\"id\\":\\"e7263d80-aedc-48cf-a140-1f9446617632\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Disregard\\"}","protected":false,"updatedAt":1749063323235,"deleted":false}',
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"656e7bc0-1d81-41bb-b4d2-209c6476305c","profileType":8,"profile":"{\\"id\\":\\"656e7bc0-1d81-41bb-b4d2-209c6476305c\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Grass\\", \\"chipIds\\":[\\"9f549112-e08f-4299-aec1-fd1112533a23\\", \\"dcb91bac-1042-40b9-b455-e48ca153f881\\", \\"f7d698bb-bce7-4e22-be80-969f2e81cd7b\\", \\"47132663-47bd-461b-8ab2-d615cf153a75\\", \\"2a6658a1-4f1e-48a3-acf6-f4c9b296e837\\", \\"17bfbf89-b845-4a4e-a52f-6ae000a73846\\", \\"c1ef175f-69ca-4159-82bc-5e0c09be5953\\", \\"4bfc8ac3-edaa-44c1-9911-a05e25b4744a\\", \\"b49acb6d-87ec-4728-bc8e-bda12035789d\\", \\"6077a406-716e-47d0-a69c-2d3edd8c3a97\\", \\"1ff932b0-943c-48bf-809f-54ec36190ca0\\", \\"a0f42659-3d81-48b9-8630-4e513492929e\\", \\"9fb9cad3-913f-43b9-9b14-b82ccdec549c\\", \\"601acf1c-8c74-466b-bb84-0c2deb2a085d\\", \\"b3718afa-18ea-4028-b809-6daa619f498a\\", \\"71a69999-be10-4b01-8ec8-08e706f45a8d\\", \\"aa8b13eb-bd75-4161-8b9b-3cb96b2b4e68\\", \\"31d2928a-aab0-4703-b7f6-e581202635f2\\", \\"9bcc65d5-9ac1-49fe-b320-412a61c5f7b6\\", \\"4ba40b45-9694-4272-bc63-b4a06130c83d\\"]}","protected":false,"updatedAt":1749063323265,"deleted":false}',
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"0d34eef3-db70-4229-a837-7a7fdcd12b40","profileType":8,"profile":"{\\"id\\":\\"0d34eef3-db70-4229-a837-7a7fdcd12b40\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Offshoot\\", \\"chipIds\\":[\\"f6315124-2e20-48e5-a7a4-b3d4eba6d4e1\\", \\"b2318cfa-60e1-4cdf-b1ae-f77f370ab46b\\", \\"28bbc42c-677a-4249-8313-10c116921df0\\", \\"4961946c-7069-4c2c-a08d-02d90ff492dc\\", \\"4ec956a4-da9d-41d7-8f25-856aab0c8922\\", \\"5c2ee102-75de-4566-b488-ac54cd24efe4\\", \\"3abbac0d-29df-4388-ac8b-e01b8cd660f8\\", \\"3cb9fbdd-eb65-4498-9ca5-05ba3dc03c89\\", \\"67713fa9-a26c-4c04-9e26-984944be1721\\", \\"300f3059-5e50-41d0-93e9-4da2ed1528af\\", \\"548f023f-03ee-496c-b38b-0a10e10f64ea\\", \\"fabdaa7b-a60c-4621-b381-19373cd2db79\\", \\"13ea758f-ea4b-4103-a98f-ace9730e5cfb\\", \\"b5264854-e725-421d-acc2-895f6cbaa3b9\\", \\"65cadcab-0d51-49cf-9c60-84002677af8d\\", \\"68ed38fe-eca9-4260-9238-0b8620b1343f\\", \\"448e792d-c9bf-44e6-845f-171b69f48c5d\\", \\"6cd5142b-4a51-4362-b31b-c36c22e54c91\\", \\"8bf03df4-1584-4212-89c6-17a9d0a42e2b\\", \\"493897c3-443b-46e2-9005-f0a2e03b8471\\", \\"52301035-8bd1-4beb-a7a6-2e4722e074ca\\", \\"79f7d6de-0aff-416a-ba05-c5d7fff75868\\", \\"6085369b-d17f-46bf-8cf1-2fb3fd30e4a7\\", \\"866127ba-2b6e-4abf-b23e-748b6555afb5\\", \\"60b8a5fb-78b7-4675-8175-31ae970481a1\\"]}","protected":false,"updatedAt":1749063323293,"deleted":false}',
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"23ef2ba2-e758-46f0-abfd-11a61e686f9d","profileType":8,"profile":"{\\"id\\":\\"23ef2ba2-e758-46f0-abfd-11a61e686f9d\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Purslane\\", \\"chipIds\\":[\\"8d59bf87-045a-4443-9441-9e6da3956084\\", \\"f73f1904-1dff-43d5-9831-61202a43d72f\\", \\"14047892-fe25-4f20-91bc-fd748f401fd4\\", \\"2aea20d2-ef9a-4567-9c0e-64acb4dd08d3\\", \\"28ee1640-12d0-4bfb-8654-fd9efb207c2d\\", \\"ad86f00d-d0c3-4a3e-8583-791c8677f89e\\", \\"1c8ec532-bfff-4ed7-b88b-917c3fbac644\\", \\"dc4432c6-5f74-4a6e-96e5-9a3f28cc5441\\", \\"f1b8b224-049b-445b-abfb-2426827ea3a4\\", \\"00a3ce48-90a6-4d3f-9f86-06168056f027\\", \\"f6734af2-4cb2-4a09-8434-c75c03157a40\\", \\"a6ff4f75-0084-43db-999d-188ff98a83df\\", \\"a94d169c-5de1-411f-9e0c-54ec22d5aed9\\", \\"830a56ba-1e2a-497d-a96a-5fe29f606f56\\", \\"cf300198-2acf-467b-b161-abbf97880d71\\", \\"c7d67667-09bc-4c1e-be5a-5e8e52be123a\\", \\"eaf10c49-d4df-403b-bc2a-d0411a734f26\\", \\"8a1f2f1e-917e-4b72-8181-00f5a0d22966\\", \\"27c79449-ffa9-4568-a2e3-5138d8a487f6\\", \\"1470f18d-0133-43eb-b859-8442cf40a32f\\"]}","protected":false,"updatedAt":1749063323327,"deleted":false}',
            '{"customerId":"e59eb2b1-55f6-4043-8371-87bfa8d35928","Id":"5874b57d-8876-4e6c-8264-3521919cbcd1","profileType":8,"profile":"{\\"id\\":\\"5874b57d-8876-4e6c-8264-3521919cbcd1\\", \\"customerId\\":\\"e59eb2b1-55f6-4043-8371-87bfa8d35928\\", \\"name\\":\\"Crop\\", \\"chipIds\\":[\\"428d6008-8980-4204-8e39-6e1091e82987\\", \\"ffce7899-c7aa-4417-badf-b53af2d41395\\", \\"bc45721f-06cb-4be2-bb24-c3141a36fec6\\", \\"53169018-be7e-4537-be0d-496912ad09de\\", \\"8b7be368-3361-45a6-9bc2-bd48bb80417f\\", \\"f3e12a9c-ac56-411c-b992-28cf09e761de\\", \\"bd56a699-aefc-4b9c-8837-5bafbd6b54ed\\", \\"0ecb8ec0-7292-45a4-9f6e-36ee7122ed34\\", \\"2ed3fd8b-e00f-4c24-83f3-5f56ea6d33c0\\", \\"6f53f7bd-fe53-45f3-b169-20a04348f6e4\\", \\"86e73ede-8566-45c5-a72a-3815985cdf79\\", \\"761805a1-f2fd-41c3-a82d-9de21838f2b4\\", \\"4584fa2f-3b75-49e0-ac7a-3c3ef2ebae8a\\", \\"1ddedb43-fe97-47c5-9389-305193ab7969\\", \\"5989f4b5-03f8-452f-8812-e05c6a7e9816\\", \\"e0a9d1c2-1054-4521-b215-202e27027fa4\\", \\"a3f5607a-8413-4efe-bc60-e0a967b5b184\\", \\"62017681-60be-4878-a4e7-be7bfbffc07c\\", \\"9265fc9b-e73c-46e4-bee1-a8c6945e81ad\\", \\"cb630cfd-6e3e-40af-bddd-133b1d4fd7ed\\", \\"6fb3e0ba-8b22-494e-996b-7fcfcaa689bf\\", \\"b40f6986-aafa-44e0-9a75-4e1393ee1485\\", \\"2a3d6e94-3aef-4fc7-8614-e3673515adf7\\", \\"64df6481-5e6c-42eb-810f-1d2173e79efe\\", \\"9bc540e5-aef3-455f-965c-193bba7d6138\\", \\"445cab21-7315-49f5-92da-3f23b7db0087\\", \\"f9c8ede8-589e-44c8-9c57-88353d718216\\", \\"6c3f9341-e6c0-44f9-928b-ce888e127466\\", \\"9ad3908d-e07b-4dc1-8141-50369de45d7e\\", \\"8eb6b18c-a22a-430a-9ead-c72015411bf6\\", \\"ff87750c-f9b6-42f4-8773-0478c711f6f5\\", \\"3a75e6c3-5886-489a-a4a7-84a87b9f8bc4\\", \\"e2d161c1-549b-42c8-a44e-4bef038bb31c\\"]}","protected":false,"updatedAt":1749063323354,"deleted":false}',
        ]
        collection = CategoryCollectionProfile.model_validate_json(json.loads(collection_json)["profile"])
        category_profiles = []
        for category_json in category_jsons:
            category_profiles.append(CategoryProfile.model_validate_json(json.loads(category_json)["profile"]))

    return collection, category_profiles


def get_chip_presigned_url_with_retries(
    point: tables.PredictionPoint, retries: int = 60, url: Optional[str] = None
) -> str:
    result = None
    for _ in range(retries):
        if MODE == MODE_DEVELOPMENT:
            result = "https://images.squarespace-cdn.com/content/v1/606b788e71df0270bd2616de/8d136b76-e59f-45e6-b77d-07addb8a223f/cr-logo_combo-wide-color-white_400px.png?format=1500w"
        else:
            result = get_chip_presigned_url(point, url)

        if result is not None:
            break

    if result is None:
        raise RuntimeError(f"Failed to get presigned url: {point.id}")

    return result


def get_chip_presigned_url(point: tables.PredictionPoint, url: Optional[str] = None) -> Optional[str]:
    if url is None:
        url = point.prediction.image.url
    key = url.split("s3://", 1)[1]
    width = int(2 * point.radius)
    height = int(2 * point.radius)
    if width < MIN_CHIP_SIZE:
        width = MIN_CHIP_SIZE
    if height < MIN_CHIP_SIZE:
        height = MIN_CHIP_SIZE
    x = int(point.x - width / 2)
    y = int(point.y - height / 2)
    response = requests.get(
        f"http://{IMAGE_SERVICE_URL}/{key}?x={x}&y={y}&width={width}&height={height}&op=crop&fill=000000&warm=1",
        timeout=30,
    )
    result: Optional[str] = None
    if response.ok:
        bucket, key = split_s3_url(response.json().get("s3_uri"))
        result = s3.generate_presigned_url("get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=600)
    return result
