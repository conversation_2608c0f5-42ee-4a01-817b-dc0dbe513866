import json
import logging
from http import HTTPStatus
from typing import Any, Dict, List, Optional

from flask import jsonify, request
from pydantic import BaseModel, Field, ValidationError
from werkzeug import Response

from server import utils
from server.apis.frontend.helpers import ErrorCode, required_response
from server.integrations import s3_cache_proxy_service
from server.integrations.s3_cache_proxy_service import BatchImageThumbnailItem
from server.routes import route

LOG = logging.getLogger(__name__)


class ImageThumbnailRequest(BaseModel):
    resource_path: str = Field(pattern=r"^s3://")
    top_left_x: int
    top_left_y: int
    width: int
    height: int
    fill_hex: str = Field(default="000000", pattern=r"^[0-9A-Fa-f]{6}$")


@route("frontend", "/image/thumbnail", methods=["GET"])
def get_image_thumbnail() -> Response:
    """Gets a cropped thumbnail for an image

    Arguments:
    {
        - "resource_path": str - Required - the resource path in s3 where the image is stored.
        - "top_left_x": int - Required - the pixel space x coordinate of the top left corner of the image
        - "top_left_y": int - Required - the pixel space y coordinate of the top left corner of the image
        - "width": int - Required - the desired width of the thumbnail
        - "height": int - Required - the desired height of the thumbnail
        - "fill_hex": str - Optional - the hex value of the fill for the background of images that don't fully cover the area eg "000000" (no # needed)
    }

    Returns: presigned url to image resource
    """
    request_params = request.args.to_dict()
    try:
        thumbnail_request = ImageThumbnailRequest(**request_params)  # type: ignore
        bucket, key = utils.split_s3_url(thumbnail_request.resource_path)
        thumbnail_request_url = s3_cache_proxy_service.get_image_thumbnail_request_url(
            bucket=bucket,
            key=key,
            top_left_x=thumbnail_request.top_left_x,
            top_left_y=thumbnail_request.top_left_y,
            width=thumbnail_request.width,
            height=thumbnail_request.height,
            fill=thumbnail_request.fill_hex,
            op="crop",
        )
        presigned_url = s3_cache_proxy_service.get_image_thumbnail_presigned_url(thumbnail_request_url)
        return Response(presigned_url)
    except ValidationError as e:
        return Response("Invalid Request", status=HTTPStatus.BAD_REQUEST.value)


class ImageThumbnailsRequestItem(ImageThumbnailRequest):
    id: str


class ImageThumbnailsRequest(BaseModel):
    images: List[ImageThumbnailsRequestItem]


# TODO: refactor so that these replace the typedDict version in the frontend.helpers file
class FormError(BaseModel):
    key: str
    message: str


class FormErrors(BaseModel):
    code: int
    message: str
    errors: list[FormError]


# using a POST here instead of a GET because the input is too complex for reliable query parameterization
@route("frontend", "/image/thumbnails", methods=["POST"])
def get_image_thumbnails() -> Response:
    """Gets a batch of cropped thumbnails

    Arguments:{
        - "images":
        [{
            - "id": str - Required - a unique way to identify the image request
            - "resource_path": str - Required - the resource path in s3 where the image is stored.
            - "top_left_x": int - Required - the pixel space x coordinate of the top left corner of the image
            - "top_left_y": int - Required - the pixel space y coordinate of the top left corner of the image
            - "width": int - Required - the desired width of the thumbnail
            - "height": int - Required - the desired height of the thumbnail
            - "fill_hex": str - Optional - the hex value of the fill for the background of images that don't fully cover the area eg "000000" (no # needed)
        }]
    }

    Returns: {
        - "data":
        [{
            "id": str - the unique identifier that was passed in
            "url": str - the accessible url to the image resource
        }],
        - "errors":
            - "code" - int - type of error
            - "message" - str - what went wrong
            - "errors": -
            [{
                - "key": str - which input errored
                - "message": str - the specific error for this input
            }] - Optional - exists if there were errors
    }
    """
    request_data = request.get_json()
    response: Dict[str, Any] = {"data": []}
    try:
        thumbnails_user_request = ImageThumbnailsRequest(**request_data)  # type: ignore
        thumbnail_request_urls: list[BatchImageThumbnailItem] = []
        for image in thumbnails_user_request.images:
            bucket, key = utils.split_s3_url(image.resource_path)
            thumbnail_request_urls.append(
                BatchImageThumbnailItem(
                    id=image.id,
                    url=s3_cache_proxy_service.get_image_thumbnail_request_url(
                        bucket=bucket,
                        key=key,
                        top_left_x=image.top_left_x,
                        top_left_y=image.top_left_y,
                        width=image.width,
                        height=image.height,
                        fill=image.fill_hex,
                        op="crop",
                    ),
                )
            )

        thumbnail_urls_response = s3_cache_proxy_service.batch_get_image_thumbnail_presigned_url(thumbnail_request_urls)

        errors_response: Optional[Dict[str, Any]] = None
        for error_id in thumbnail_urls_response.errors:
            if not errors_response:
                errors_response = FormErrors(
                    code=ErrorCode.EXTERNAL_SERVICE_ERROR, message="Error fetching from image service", errors=[]
                ).model_dump()
            errors_response["errors"].append(FormError(key=error_id, message=f"Error fetching {error_id}").model_dump())

        for success_response in thumbnail_urls_response.data:
            response["data"].append(success_response.model_dump())
    except ValidationError as e:
        validation_errors: list[FormError] = []
        for error in e.errors():
            error_loc = error["loc"]
            if error_loc[0] == "images" and len(error_loc) > 1 and request_data:
                index = int(error_loc[1])
                request_images: list[Dict[str, Any]] = request_data.get("images", [{}])
                error_key: str = (
                    request_images[index].get("id", "") if index >= 0 and index < len(request_images) else ""
                )
                validation_errors.append(FormError(key=error_key, message=error["msg"]))

        response["errors"] = FormErrors(
            code=ErrorCode.INPUT_VALIDATION_ERROR, message="Request input error", errors=validation_errors
        ).model_dump()

    return jsonify(response)
