import logging
import math
from collections import OrderedDict
from typing import Optional
from urllib.parse import urlencode

import boto3
import requests
from pydantic import BaseModel

from server import config, utils
from server.utils import helpers


def get_image_thumbnail_request_url(
    bucket: str,
    key: str,
    top_left_x: float,
    top_left_y: float,
    width: int,
    height: int,
    fill: Optional[str] = None,
    op: Optional[str] = None,
) -> str:
    fill = fill or "000000"
    url = f"http://{config.IMAGE_SERVICE_URL}/{bucket}/{key}"
    params = OrderedDict(x=top_left_x, y=top_left_y, width=width, height=height, fill=fill, warm=1)
    if op:
        params["op"] = op
    params_encoded = urlencode(params)
    return f"{url}?{params_encoded}"


def get_cropped_image_around_center(
    bucket: str, key: str, center_x: float, center_y: float, width: int, height: int, fill: Optional[str] = None
) -> str:
    """
    Given an image stored in s3 bucket and key, crop the image around the center point with the given width and height.
    """
    top_left_x = math.floor(center_x - width / 2)
    top_left_y = math.floor(center_y - height / 2)

    request_url = get_image_thumbnail_request_url(bucket, key, top_left_x, top_left_y, width, height, fill, op="crop")
    response = requests.get(request_url)

    if not response.ok:
        if response.json() and response.json().get("error"):
            raise RuntimeError(f"ImageService call failed: {response.json().get('error')}")
        raise RuntimeError(f"ImageService call failed: {response.text}")
    json_response = response.json()

    s3_uri = json_response.get("s3_uri", None)
    if not s3_uri:
        raise ValueError(f"Response from image service did not contain the s3 uri: {json_response}")
    return str(s3_uri)


class CroppedImageMetadata(BaseModel):
    s3_uri: str
    presigned_uri: str
    s3_integrity_metadata: Optional[helpers.S3IntegrityMetadata] = None


def get_presigned_url_for_cropped_image(
    image_url: str,
    center_x: float,
    center_y: float,
    width: int,
    height: int,
    fill: Optional[str] = None,
    get_integrity_metadata: bool = False,
) -> CroppedImageMetadata:
    bucket, key = utils.split_s3_url(image_url)
    s3_uri = get_cropped_image_around_center(bucket, key, center_x, center_y, width, height, fill)
    bucket, key = utils.split_s3_url(s3_uri)
    pre_signed_url: Optional[str] = helpers.generate_s3_presigned_url(bucket, key)
    if not pre_signed_url:
        raise RuntimeError(f"Failed to generate presigned url for {bucket}/{key}")

    cropped_image_metadata = CroppedImageMetadata(s3_uri=s3_uri, presigned_uri=pre_signed_url)

    if get_integrity_metadata:
        cropped_image_metadata.s3_integrity_metadata = helpers.get_s3_integrity_metadata(s3_uri)
    return cropped_image_metadata


def get_image_thumbnail_presigned_url(request_url: str) -> str:
    response = requests.get(request_url)
    assert response.ok, response.text

    try:
        json_response = response.json()
        s3_uri = json_response.get("s3_uri", None)
        if not s3_uri:
            raise ValueError(f"Response does not contain the expected field 's3_uri': {json_response}")
        bucket, key = utils.split_s3_url(s3_uri)
        pre_signed_url: str = boto3.client("s3").generate_presigned_url(
            "get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=60
        )
        return pre_signed_url
    except ValueError:
        raise RuntimeError(f"Invalid response: {response}")


class BatchImageThumbnailItem(BaseModel):
    id: str
    url: str


class BatchImageThumnailsResponse(BaseModel):
    data: list[BatchImageThumbnailItem]
    errors: list[str]


def batch_get_image_thumbnail_presigned_url(
    thumbnails_info: list[BatchImageThumbnailItem],
) -> BatchImageThumnailsResponse:
    result = BatchImageThumnailsResponse(data=[], errors=[])
    for thumbnail in thumbnails_info:
        response = requests.get(thumbnail.url)
        try:
            assert response.ok, response.text
            json_response = response.json()
            s3_uri = json_response.get("s3_uri", None)
            if not s3_uri:
                raise ValueError(f"response does not contain the expected field 's3_uri': {json_response}")
            bucket, key = utils.split_s3_url(s3_uri)
            pre_signed_url: str = boto3.client("s3").generate_presigned_url(
                "get_object", Params={"Bucket": bucket, "Key": key}, ExpiresIn=60
            )
            result.data.append(BatchImageThumbnailItem(id=thumbnail.id, url=pre_signed_url))
        except ValueError:
            result.errors.append(thumbnail.id)

    return result
