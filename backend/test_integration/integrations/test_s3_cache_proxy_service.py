import importlib
import os
from pathlib import Path
from types import MethodType
from unittest import mock
from unittest.mock import patch

import boto3
import pytest
from moto import mock_aws

from server import config
from server.integrations.s3_cache_proxy_service import (
    get_cropped_image_around_center,
    get_presigned_url_for_cropped_image,
)

RESOURCE_DIR = Path(__file__).parent.parent / "resources"


class Const:
    BUCKET = "bucket"
    TEMP_BUCKET = "temp_bucket"

    CENTER_X = 100
    CENTER_Y = 100
    WIDTH = 100
    HEIGHT = 50

    # These are the checksum and content length of the local image in 'resources/chip_sample_img.png'
    LOCAL_RESOURCE_CONTENT_LENGTH = 34507
    LOCAL_RESOURCE_CHECKSUM = "da5ecb7f5052405c2d07b1971e7ece71"

    FILL = "111111"

    DUMMY_IMG_SERVICE_URL = "img-service-test.com"

    IMG_KEY = "img/storage/image.png"

    IMG_SERVICE_RESPONSE = mock.Mock(ok=True, json=mock.Mock(return_value={"s3_uri": f"s3://{TEMP_BUCKET}/{IMG_KEY}"}))

    IMG_URL = f"s3://{BUCKET}/{IMG_KEY}"


class TestGetChoppedImageAroundCenter:
    def setup_method(self) -> None:
        # Set the image service url as an environment variable
        os.environ["VESELKA_IMAGE_SERVICE_URL"] = Const.DUMMY_IMG_SERVICE_URL

        importlib.reload(config)

        self.patch_requests_get = patch(
            "server.integrations.s3_cache_proxy_service.requests.get", return_value=Const.IMG_SERVICE_RESPONSE
        )
        self.mock_requests_get = self.patch_requests_get.start()

    def teardown_method(self) -> None:
        self.patch_requests_get.stop()

    def test_WHEN_api_params_are_valid_THEN_accurately_crop_img(self) -> None:
        s3_uri = get_cropped_image_around_center(
            Const.BUCKET, Const.IMG_KEY, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT, Const.FILL
        )

        assert s3_uri == f"s3://{Const.TEMP_BUCKET}/{Const.IMG_KEY}"

        expected_top_left_x = int(Const.CENTER_X - Const.WIDTH / 2)
        expected_top_left_y = int(Const.CENTER_Y - Const.HEIGHT / 2)
        self.mock_requests_get.assert_called_once_with(
            f"http://{Const.DUMMY_IMG_SERVICE_URL}/{Const.BUCKET}/{Const.IMG_KEY}?x={expected_top_left_x}&y={expected_top_left_y}&width={Const.WIDTH}&height={Const.HEIGHT}&fill={Const.FILL}&warm=1&op=crop"
        )

    def test_WHEN_api_params_missing_fill_THEN_use_default_fill(self) -> None:
        s3_uri = get_cropped_image_around_center(
            Const.BUCKET, Const.IMG_KEY, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT
        )

        assert s3_uri == f"s3://{Const.TEMP_BUCKET}/{Const.IMG_KEY}"

        expected_top_left_x = int(Const.CENTER_X - Const.WIDTH / 2)
        expected_top_left_y = int(Const.CENTER_Y - Const.HEIGHT / 2)
        self.mock_requests_get.assert_called_once_with(
            f"http://{Const.DUMMY_IMG_SERVICE_URL}/{Const.BUCKET}/{Const.IMG_KEY}?x={expected_top_left_x}&y={expected_top_left_y}&width={Const.WIDTH}&height={Const.HEIGHT}&fill=000000&warm=1&op=crop"
        )

    def test_WHEN_call_to_image_service_fails_THEN_throw_assertion_exception(self) -> None:
        self.mock_requests_get.return_value = mock.Mock(ok=False, json=mock.Mock(return_value={"error": "ERROR"}))

        with pytest.raises(RuntimeError):
            get_cropped_image_around_center(
                Const.BUCKET, Const.IMG_KEY, Const.CENTER_X, Const.CENTER_Y, Const.HEIGHT, Const.WIDTH, Const.FILL
            )

    def test_WHEN_call_to_image_service_does_not_return_s3_uri_THEN_throw_value_error(self) -> None:
        self.mock_requests_get.return_value = mock.Mock(ok=True, json=mock.Mock(return_value={}))

        with pytest.raises(ValueError):
            get_cropped_image_around_center(
                Const.BUCKET, Const.IMG_KEY, Const.CENTER_X, Const.CENTER_Y, Const.HEIGHT, Const.WIDTH, Const.FILL
            )


@mock_aws
class TestGetPresignedUrlForCroppedImage:
    def setup_method(self, aws: MethodType) -> None:
        # Set the image service url as an environment variable
        os.environ["VESELKA_IMAGE_SERVICE_URL"] = Const.DUMMY_IMG_SERVICE_URL

        importlib.reload(config)

        self.patch_requests_get = patch(
            "server.integrations.s3_cache_proxy_service.requests.get", return_value=Const.IMG_SERVICE_RESPONSE
        )
        self.mock_requests_get = self.patch_requests_get.start()

        # Upload cropped image to simulate the image service response
        s3 = boto3.client("s3")
        s3.create_bucket(Bucket=Const.TEMP_BUCKET)

        s3.upload_file(Filename=f"{RESOURCE_DIR}/chip_sample_img.png", Bucket=Const.TEMP_BUCKET, Key=Const.IMG_KEY)

    def teardown_method(self, aws: MethodType) -> None:
        self.patch_requests_get.stop()

        s3 = boto3.client("s3")
        s3.delete_object(Bucket=Const.TEMP_BUCKET, Key=Const.IMG_KEY)
        s3.delete_bucket(Bucket=Const.TEMP_BUCKET)

    def test_WHEN_api_params_are_valid_THEN_accurately_generate_presigned_url_and_default_integrity_metadata_to_none(
        self,
    ) -> None:
        cropped_image_metadata = get_presigned_url_for_cropped_image(
            Const.IMG_URL, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT, Const.FILL
        )

        assert cropped_image_metadata.s3_uri == f"s3://{Const.TEMP_BUCKET}/{Const.IMG_KEY}"
        assert cropped_image_metadata.presigned_uri.startswith(
            f"https://s3.amazonaws.com/{Const.TEMP_BUCKET}/{Const.IMG_KEY}?AWSAccessKeyId="
        )
        assert cropped_image_metadata.s3_integrity_metadata is None

        expected_top_left_x = int(Const.CENTER_X - Const.WIDTH / 2)
        expected_top_left_y = int(Const.CENTER_Y - Const.HEIGHT / 2)
        self.mock_requests_get.assert_called_once_with(
            f"http://{Const.DUMMY_IMG_SERVICE_URL}/{Const.BUCKET}/{Const.IMG_KEY}?x={expected_top_left_x}&y={expected_top_left_y}&width={Const.WIDTH}&height={Const.HEIGHT}&fill={Const.FILL}&warm=1&op=crop"
        )

    def test_WHEN_api_params_includes_get_integrity_metadata_THEN_accurately_generate_presigned_url_and_integrity_metadata(
        self,
    ) -> None:
        cropped_image_metadata = get_presigned_url_for_cropped_image(
            Const.IMG_URL,
            Const.CENTER_X,
            Const.CENTER_Y,
            Const.WIDTH,
            Const.HEIGHT,
            Const.FILL,
            get_integrity_metadata=True,
        )

        assert cropped_image_metadata.s3_uri == f"s3://{Const.TEMP_BUCKET}/{Const.IMG_KEY}"
        assert cropped_image_metadata.presigned_uri.startswith(
            f"https://s3.amazonaws.com/{Const.TEMP_BUCKET}/{Const.IMG_KEY}?AWSAccessKeyId="
        )
        assert cropped_image_metadata.s3_integrity_metadata is not None
        assert cropped_image_metadata.s3_integrity_metadata.checksum == Const.LOCAL_RESOURCE_CHECKSUM
        assert cropped_image_metadata.s3_integrity_metadata.content_length == Const.LOCAL_RESOURCE_CONTENT_LENGTH

        expected_top_left_x = int(Const.CENTER_X - Const.WIDTH / 2)
        expected_top_left_y = int(Const.CENTER_Y - Const.HEIGHT / 2)
        self.mock_requests_get.assert_called_once_with(
            f"http://{Const.DUMMY_IMG_SERVICE_URL}/{Const.BUCKET}/{Const.IMG_KEY}?x={expected_top_left_x}&y={expected_top_left_y}&width={Const.WIDTH}&height={Const.HEIGHT}&fill={Const.FILL}&warm=1&op=crop"
        )

    def test_WHEN_call_to_image_service_fails_THEN_throw_assertion_exception(self) -> None:
        self.mock_requests_get.return_value = mock.Mock(ok=False, json=mock.Mock(return_value={"error": "ERROR"}))

        with pytest.raises(RuntimeError):
            get_presigned_url_for_cropped_image(
                Const.IMG_URL, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT, Const.FILL
            )

    def test_WHEN_call_to_image_service_does_not_return_s3_uri_THEN_throw_value_error(self) -> None:
        self.mock_requests_get.return_value = mock.Mock(ok=True, json=mock.Mock(return_value={}))

        with pytest.raises(ValueError):
            get_presigned_url_for_cropped_image(
                Const.IMG_URL, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT, Const.FILL
            )

    def test_WHEN_call_to_generate_pre_signed_url_fails_THEN_throw_runtime_error(self) -> None:
        with mock.patch(
            "server.integrations.s3_cache_proxy_service.helpers.generate_s3_presigned_url"
        ) as mock_generate_s3_presigned_url:
            mock_generate_s3_presigned_url.return_value = None

            with pytest.raises(RuntimeError):
                get_presigned_url_for_cropped_image(
                    Const.IMG_URL, Const.CENTER_X, Const.CENTER_Y, Const.WIDTH, Const.HEIGHT, Const.FILL
                )
