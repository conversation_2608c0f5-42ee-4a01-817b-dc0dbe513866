import asyncio
import datetime
import os
import threading
from typing import Any, Dict, Optional, cast

import pytz

from core.controls.commander.notification import (
    NOTIFY_DRIVE_PLAN_PAUSE,
    NOTIFY_DRIVE_PLAN_RESUME,
    NOTIFY_ENTERING_ROW,
    NOTIFY_EXITING_ROW,
)
from core.controls.driver.drivebot.client.client import DrivebotClient
from core.controls.driver.driver import Driver
from core.controls.driver.location_state import DriverLocationState
from core.controls.exterminator.model.exterminator import Exterminator
from core.cv.retina.retina import Retina
from core.model.actuator import Actuator
from core.model.node.type import NodeType
from generated.core.controls.driver.model.auto_drive_request import AutoDriveRequest
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

GEO_LOCATION = os.environ.get("MAKA_GEO_LOCATION", "unset")


class CommanderNode(Actuator):
    def __init__(self, *argv: Any, **kwargs: Any):
        Actuator.__init__(self, *argv, **kwargs)
        self._weeding: bool = False
        self._lock = threading.Lock()
        self._drivebot = DrivebotClient()
        self._last_button_press_check: int = maka_control_timestamp_ms()

    @property
    def location(self) -> str:
        return GEO_LOCATION

    def notify(self, topic: str) -> None:
        assert self._exterminator is not None
        assert self._driver is not None
        LOG.info(f"Notifying Commander: {topic}")
        with self._lock:
            if not self._weeding:
                return
        if topic == NOTIFY_ENTERING_ROW or topic == NOTIFY_DRIVE_PLAN_RESUME:
            self._exterminator.makannihilate_from_sync(wait=False)
        elif topic == NOTIFY_EXITING_ROW or topic == NOTIFY_DRIVE_PLAN_PAUSE:
            self._exterminator.cancel_makannihilate_from_sync()

    def format_session_dir_name(self) -> str:
        return f"{GEO_LOCATION}-{pytz.timezone('America/Los_Angeles').fromutc(datetime.datetime.utcnow()).date()}"

    def start_weeding(self, direction: str, speed_mph: float) -> None:
        assert self._exterminator is not None
        assert self._driver is not None
        with self._lock:
            self._weeding = True
        self._driver.handle_auto_drive_request(
            AutoDriveRequest(
                timestamp_ms=maka_control_timestamp_ms(), source="WTF", plan=f"gps_field_{direction}", datum=speed_mph
            )
        )
        LOG.info(f"Started Weeding in Direction: {direction}")

    def stop_weeding(self) -> None:
        assert self._exterminator is not None
        assert self._driver is not None
        self._driver.cancel_plan()
        self._exterminator.cancel_makannihilate_from_sync()
        with self._lock:
            self._weeding = False
        LOG.info("Stopped Weeding")

    def pause_weeding(self) -> None:
        assert self._exterminator is not None
        assert self._driver is not None
        self._driver.plan_pause_threadsafe()
        LOG.info("Paused Weeding")

    def resume_weeding(self) -> None:
        assert self._exterminator is not None
        assert self._driver is not None
        self._driver.plan_continue_threadsafe()
        LOG.info("Resumed Weeding")

    ###########################################################################
    # Node APIs
    ###########################################################################

    def _cache_named_references(self) -> None:
        # Exterminator
        self._exterminator: Optional[Exterminator] = cast(Optional[Exterminator], self.get_node(NodeType.EXTERMINATOR))
        if self._exterminator is None:
            LOG.warning(f"{self.device_path} Initializing with no exterminator.")

        # Driver
        self._driver: Optional[Driver] = cast(Optional[Driver], self.get_node(NodeType.DRIVER))
        if self._driver is None:
            LOG.warning(f"{self.device_path} Initializing with no driver.")
            self._driver_location_state_subscription = None
        else:
            # Commander events from the driver
            self._driver.notify_commander_callback = self.notify
            self._driver_location_state_subscription = self.feed.subscribe(
                topic=Topic("/driver/location_state"), msg_type=DriverLocationState
            )

        self._retina: Optional[Retina] = cast(Optional[Retina], self.get_node(NodeType.RETINA))

    def dump_callback(self) -> Dict[str, Any]:
        return {
            "status": self.status_callback(),
        }

    def status_callback(self) -> Dict[str, Any]:
        return {}

    @property
    def _current_event(self) -> DriverLocationState:
        if self._driver_location_state_subscription is None:
            return DriverLocationState.IN_BOUNDS
        message = self._driver_location_state_subscription.read()
        assert message is not None
        return message

    async def _async_control_loop(self) -> None:
        while True:
            button_presses = await self._drivebot.get_button_presses()
            for button_press in button_presses.button_presses:
                if button_press.timestamp_ms < self._last_button_press_check:
                    continue

                if button_press.button == "left":
                    self.start_weeding("left", 1.0)
                elif button_press.button == "right":
                    self.start_weeding("right", 1.0)

            self._last_button_press_check = maka_control_timestamp_ms()

            await asyncio.sleep(1)

    def _control_loop(self) -> None:
        loop = get_event_loop_by_name()
        asyncio.run_coroutine_threadsafe(self._async_control_loop(), loop)
