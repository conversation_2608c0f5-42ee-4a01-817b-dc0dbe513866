import asyncio
from asyncio import CancelledError
from typing import Awaitable, Callable, Dict, List, Optional, Set

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.algos.emi_test import EMI_TEST_ALGO_NAME, emi_test_algo
from core.controls.exterminator.controllers.aimbot.algos.follow_forever import (
    FOLLOW_FOREVER_ALGO_NAME,
    follow_forever_algo,
)
from core.controls.exterminator.controllers.aimbot.algos.laser_test import LASER_TEST_ALGO_NAME, laser_test_algo
from core.controls.exterminator.controllers.aimbot.algos.rotary_and_p2p import (
    ROTARY_AND_P2P_ALGO_NAME,
    rotary_and_p2p_algo,
)
from core.controls.exterminator.controllers.aimbot.algos.shoot_randomly import (
    SHOOT_RANDOMLY_ALGO_NAME,
    shoot_randomly_algo,
)
from core.controls.exterminator.controllers.aimbot.algos.simulator_shoot import SIMULATOR_SHOOT_ALGO_NAME, sim_kill
from core.controls.exterminator.controllers.aimbot.algos.sleepy import SLEEPY_ALGO_NAME, sleepy_algo
from core.controls.exterminator.controllers.aimbot.algos.timestamped_follow import (
    TIMESTAMP_FOLLOW_ALGO_NAME,
    timestamp_follow_algo,
)
from core.controls.exterminator.controllers.aimbot.error import (
    AimbotException,
    OutOfRangeAimbotException,
    P2PContextFailure,
    P2PTargetDistanceExceeded,
    P2PTimeoutError,
    TargetChangeException,
    WeedNotFoundAimbotException,
)
from core.controls.exterminator.controllers.aimbot.metrics import (
    SCANNER_ERROR_COUNT,
    TimedMetric,
    get_scanner_overhead_processor,
)
from core.controls.exterminator.controllers.aimbot.utils.load_estimator import LoadEstimator
from core.controls.exterminator.controllers.targeting.p2p import P2PContextNotFoundException
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.devices.device import DeviceStatusCode
from lib.common.time import maka_control_timestamp_ms
from scheduling.pybind.scheduling_python import GlobalScheduler
from shooting.pybind.shooting_python import LaserOntimeTracker
from trajectory.pybind.trajectory_python import TrackedItemKillStatus, Trajectory

LOG = lib.common.logging.get_logger(__name__)

AIMBOT_ALGORITHMS: Dict[
    str, Callable[["AimbotScanner", Optional[Trajectory], Optional[TimedMetric]], Awaitable[None]]
] = {
    ROTARY_AND_P2P_ALGO_NAME: rotary_and_p2p_algo,
    TIMESTAMP_FOLLOW_ALGO_NAME: timestamp_follow_algo,
    FOLLOW_FOREVER_ALGO_NAME: follow_forever_algo,
    SHOOT_RANDOMLY_ALGO_NAME: shoot_randomly_algo,
    LASER_TEST_ALGO_NAME: laser_test_algo,
    EMI_TEST_ALGO_NAME: emi_test_algo,
    SLEEPY_ALGO_NAME: sleepy_algo,
    SIMULATOR_SHOOT_ALGO_NAME: sim_kill,
}

AIMBOT_NONE_ALGORITHMS: Set[str] = {
    TIMESTAMP_FOLLOW_ALGO_NAME,
    FOLLOW_FOREVER_ALGO_NAME,
    SHOOT_RANDOMLY_ALGO_NAME,
    LASER_TEST_ALGO_NAME,
    EMI_TEST_ALGO_NAME,
    SLEEPY_ALGO_NAME,
}


def get_aimbot_algorithms() -> List[str]:
    return list(AIMBOT_ALGORITHMS.keys())


async def aimbot_destroy_weed(
    scanner: "AimbotScanner", weed: Optional[Trajectory], overhead_metric: Optional[TimedMetric], algo: str
) -> None:
    return await AIMBOT_ALGORITHMS[algo](scanner, weed, overhead_metric)


def _get_next_weed_from_scheduler(scanner: AimbotScanner, scheduler: GlobalScheduler) -> Optional[Trajectory]:
    return scheduler.get_next_weed(scanner.numeric_id)


def _register_scanner_error(scanner_id: str, error: str) -> None:
    SCANNER_ERROR_COUNT.labels(scanner_id, error).inc()


def _handle_completion(scanner: AimbotScanner, weed: Optional[Trajectory], status: TrackedItemKillStatus) -> None:
    scanner.scanner_wrapper.stop()
    if weed is not None:
        weed.set_kill_status(status)


async def aimbot(  # noqa: C901
    scanner: AimbotScanner,
    algo: str,
    load_estimator: Optional[LoadEstimator] = None,
    scheduler: Optional[GlobalScheduler] = None,
) -> None:
    try:
        await _aimbot(scanner, algo, load_estimator, scheduler)
    except Exception as ex:
        LOG.error(ex)
        raise


async def _aimbot(  # noqa: C901
    scanner: AimbotScanner,
    algo: str,
    load_estimator: Optional[LoadEstimator] = None,
    scheduler: Optional[GlobalScheduler] = None,
) -> None:
    scanner.set_running(True)

    while not bot_stop_handler.stopped:
        overhead_metric = TimedMetric(await get_scanner_overhead_processor(scanner.id))
        overhead_metric.start("aimbot_start")
        if not scanner.running:
            return
        if (await scanner.get_status()).code != DeviceStatusCode.OK:
            await asyncio.sleep(1)
            continue
        if algo in AIMBOT_NONE_ALGORITHMS:
            weed = None
        else:
            overhead_metric.start("get_next_weed")
            if scheduler is not None:
                try:
                    weed = await asyncio.get_event_loop().run_in_executor(
                        None, lambda: _get_next_weed_from_scheduler(scanner, scheduler)
                    )
                except Exception as ex:
                    LOG.error(f"Error Occured during Weed Ranking: {ex}")
                    overhead_metric.end(abnormal=True)
                    raise
            else:
                assert False
            overhead_metric.stop()
            if weed is None:
                start_time_ms = maka_control_timestamp_ms()
                await asyncio.get_event_loop().run_in_executor(
                    None, lambda: scheduler.await_weed_available(scanner.numeric_id)
                )
                elapsed = maka_control_timestamp_ms() - start_time_ms
                LaserOntimeTracker().add_idle_time(scanner.scanner_wrapper, elapsed)
                if load_estimator is not None:
                    await load_estimator.add_down_time(elapsed)
                continue
        LaserOntimeTracker().target_aquired(scanner.scanner_wrapper)
        try:
            overhead_metric.start("aimbot_start_destroy")
            await aimbot_destroy_weed(scanner, weed, overhead_metric, algo=algo)
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kShot)
            )
        except OutOfRangeAimbotException as e:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, f"out_of_range_{e.range_type}")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kPartiallyShot)
            )
            LOG.debug(f"Out of range aimbot error: {e}")
        except TargetChangeException:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "target_changed")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kPartiallyShot)
            )
        except WeedNotFoundAimbotException as e:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, f"weed_not_found_{e.failure_type}")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kP2PNotFound)
            )
            LOG.debug(f"Weed not found aimbot error: {e}")
        except P2PTargetDistanceExceeded as e:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "p2p_target_distance_exceeded")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kP2PNotFound)
            )
            LOG.debug(f"Weed not found aimbot error: {e}")
        except P2PContextFailure as e:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "p2p_context_failure")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kP2PNotFound)
            )
            LOG.debug(f"Weed P2P context failure: {e}")
        except AimbotException as ex:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "aimbot_ex")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kError)
            )
            LOG.error(f"Aimbot error: {ex}")
        except CancelledError:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "cancelled")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kNotShot)
            )
            LOG.info("Cancelled")
            overhead_metric.end(abnormal=True)
            raise
        except P2PContextNotFoundException:
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kP2PMissingContext)
            )
            LOG.debug("Perspective not found")
        except P2PTimeoutError as e:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "p2p_timeout_error")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kP2PNotFound)
            )
            LOG.debug(f"Target P2P not found in time failure: {e}")
        except Exception as ex:
            overhead_metric.start("register_error")
            _register_scanner_error(scanner.id, "unknown")
            await asyncio.get_event_loop().run_in_executor(
                None, lambda: _handle_completion(scanner, weed, TrackedItemKillStatus.kError)
            )
            LOG.error(f"Unknown Exception Occured while Killing Weed: {ex}")
        overhead_metric.end()
