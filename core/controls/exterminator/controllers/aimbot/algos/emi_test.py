import asyncio
import itertools
from typing import Optional

import numpy as np

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from lib.common.time import maka_control_timestamp_ms
from lib.common.types import GimbalPosition
from trajectory.pybind.trajectory_python import Trajectory

LOG = lib.common.logging.get_logger(__name__)


EMI_TEST_ALGO_NAME = "EmiTest"


async def emi_test_algo(
    scanner: AimbotScanner, _: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    LOG.debug(f"Laser Test Algo with scanner: {scanner.id}")
    subscriber = get_global_config_subscriber()

    pan_positions = np.linspace(scanner.gimbal.pan.min, scanner.gimbal.pan.max, num=10)
    tilt_positions = np.linspace(scanner.gimbal.tilt.min, scanner.gimbal.tilt.max, num=10)

    while True:
        pulse_time_ms = subscriber.get_config_node("aimbot", "emi_test_pulse_time_ms").get_uint_value()
        for pan_pos, tilt_pos in itertools.product(pan_positions, tilt_positions):
            start_time_ms = maka_control_timestamp_ms()
            await scanner.gimbal.async_set_position(GimbalPosition(int(pan_pos), int(tilt_pos)), await_settle=True)
            duration_ms = maka_control_timestamp_ms() - start_time_ms
            await asyncio.sleep(max(pulse_time_ms - duration_ms, 0) / 1000)
            try:
                await scanner.laser.async_on()
                await asyncio.sleep(pulse_time_ms / 1000)
            finally:
                await scanner.laser.async_off()
