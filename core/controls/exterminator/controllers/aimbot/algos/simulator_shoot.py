import asyncio
from asyncio import Future
from typing import Any, Awaitable, List, Optional, Set, Tuple

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.error import TargetChangeException
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.controllers.aimbot.utils.tasks import (
    _ltt_await,
    _ltt_start_shooting,
    _ltt_stop_shooting,
    scanner_await_target_change_task,
)
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from shooting.pybind.shooting_python import LaserOntimeTracker
from trajectory.pybind.trajectory_python import Trajectory

SIMULATOR_SHOOT_ALGO_NAME = "SimulatorShoot"

LOG = lib.common.logging.get_logger(__name__)


async def sim_kill(
    scanner: AimbotScanner, optional_weed: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    assert optional_weed is not None
    assert scanner.target
    pause_lasing_event = asyncio.Event()
    target_change_task = asyncio.get_event_loop().create_task(scanner_await_target_change_task(scanner))
    laser_shoot_task = asyncio.shield(
        laser_on_duration_task_safe(
            scanner, pause_signal=pause_lasing_event, trajectory=optional_weed, additional_time=0,
        )
    )
    send_change_signal = True
    tasks: List[Future[Any]] = [target_change_task, laser_shoot_task]
    all_tasks: Tuple[Set[Future[Any]], Set[Future[Any]]] = await asyncio.wait(
        tasks, return_when=asyncio.FIRST_COMPLETED
    )
    done: Set[Future[Any]] = all_tasks[0]
    try:
        if target_change_task in done:
            send_change_signal = False
            raise TargetChangeException(f"scanner {scanner.id}: changed targets", optional_weed.id())

        if send_change_signal:
            tasks.append(
                asyncio.get_event_loop().run_in_executor(None, scanner.scanner_wrapper.notify_target_change)
            )  # Force target change task to end no matter what
    finally:
        for task in tasks:
            await task


async def laser_on_duration_task_safe(
    scanner: AimbotScanner, pause_signal: asyncio.Event, trajectory: Trajectory, additional_time: int,
) -> None:
    ltt = LaserOntimeTracker()
    await_fut: Optional[Awaitable[None]] = None
    started = False
    try:
        started = True
        await asyncio.shield(_ltt_start_shooting(ltt, scanner, additional_time, trajectory))
        await_fut = await _ltt_await(ltt, scanner, trajectory, pause_signal)
    except Exception as ex:
        LOG.error(f"Scanner: {scanner.numeric_id} in error laser on task {ex}")
    except asyncio.CancelledError:
        LOG.error(f"Scanner: {scanner.numeric_id} laser on task / subtask was cancelled")
    finally:
        tasks: List[Awaitable[Any]] = []
        stop: Optional[Awaitable[None]] = None
        if started:
            stop = asyncio.shield(_ltt_stop_shooting(ltt, scanner, trajectory))
            tasks.append(stop)
        if await_fut is not None:
            tasks.append(await_fut)
        await asyncio.gather(*tasks, return_exceptions=True)
