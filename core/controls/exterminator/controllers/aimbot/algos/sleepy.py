import asyncio
import random
from typing import Optional

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from trajectory.pybind.trajectory_python import Trajectory

LOG = lib.common.logging.get_logger(__name__)


SLEEPY_ALGO_NAME = "Sleepy"


async def sleepy_algo(
    scanner: AimbotScanner, weed: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    LOG.debug(f"Sleepy Algo with scanner: {scanner.id}")
    assert weed is None
    sleepy_time_ms = random.randint(100, 500)
    await asyncio.sleep(sleepy_time_ms)
