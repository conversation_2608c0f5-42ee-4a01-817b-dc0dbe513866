import asyncio
from asyncio import Future
from typing import Any, List, Optional, Set, Tuple

import lib.common.logging
from core.controls.exterminator.controllers.aimbot.error import AimbotException
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.controllers.aimbot.utils.helper import get_adjusted_tilt_velocity_with_pos_from_vel
from core.controls.exterminator.controllers.aimbot.utils.tasks import (
    get_next_velocity_nofx_task,
    get_velocity_mph_nofx_task,
)
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from core.controls.exterminator.model.gimbal import Gimbal2D, GimbalException
from lib.common.math import subtract_tuples
from lib.common.time import maka_control_timestamp_ms
from trajectory.pybind.trajectory_python import Trajectory

LOG = lib.common.logging.get_logger(__name__)


TIMESTAMP_FOLLOW_ALGO_NAME = "TimestampFollow"


async def timestamp_follow_algo(
    scanner: AimbotScanner, _: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    LOG.debug(f"Timestamp Algo with scanner: {scanner.id}")

    # This is hacky setup code for velocity
    # Hacky Code, TODO Remove once better data path is created
    gimbal = scanner.gimbal
    nofx = gimbal.hacky_nofx_device
    assert nofx is not None

    # ### P To S Initial Move ### #
    location: Tuple[int, int] = (int(scanner.gimbal.pan.center), int(scanner.gimbal.tilt.max - 1000))  # GoTo Top
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_task(scanner, nofx, None, None)
    last_vel: Tuple[int, int] = (0, int(last_velocity * 1000))
    p_to_s_start_time = maka_control_timestamp_ms()
    go_to_result = await scanner.gimbal.go_to_timestamp(
        timestamp_ms=p_to_s_start_time,
        mode=Gimbal2D.MODE_IMMEDIATE,
        position=location,
        velocity_mrpm=(590000, 590000),
        follow_velocity=last_vel,
        follow_accel=(0, 0),
        interval_sleep_time_ms=30,
    )

    last_pan_pos: int = go_to_result[1][1][0]
    last_tilt_pos: int = go_to_result[1][1][1]
    __, vel_mph = await get_velocity_mph_nofx_task(nofx, None)
    last_velocity = get_adjusted_tilt_velocity_with_pos_from_vel(scanner, last_pan_pos, last_tilt_pos, vel_mph, None)

    current_vel = (0, int(last_velocity * 1000))
    current_accel = subtract_tuples(current_vel, last_vel)
    last_vel = current_vel

    velocity_task = asyncio.get_event_loop().create_task(
        get_next_velocity_nofx_task(scanner, nofx, last_velocity_timestamp_ms, None)
    )
    move_task = asyncio.get_event_loop().create_task(
        scanner.gimbal.follow_timestamp(
            timestamp_ms=maka_control_timestamp_ms(), follow_velocity=current_vel, follow_accel=current_accel,
        )
    )

    try:
        while True:

            tasks: List[asyncio.Task[Any]] = [move_task, velocity_task]
            all_tasks: Tuple[Set[Future[Any]], Set[Future[Any]]] = await asyncio.wait(
                tasks, return_when=asyncio.FIRST_COMPLETED
            )
            done: Set[Future[Any]] = all_tasks[0]

            if velocity_task in done:
                last_velocity_timestamp_ms, last_velocity = await velocity_task
                current_vel = (0, int(last_velocity * 1000))
                current_accel = subtract_tuples(current_vel, last_vel)
                last_vel = current_vel
                velocity_task = asyncio.get_event_loop().create_task(
                    get_next_velocity_nofx_task(scanner, nofx, last_velocity_timestamp_ms, None)
                )

            if move_task in done:

                try:
                    last_location = location
                    result = await move_task
                    last_tilt_pos = result[1][1]
                    location = (result[1][0], result[1][1])
                    if abs(last_location[1] - location[1]) <= 4 and last_velocity != 0:
                        return  # We're done here, assuming this is bottom.
                except GimbalException:
                    raise AimbotException("Failed to completely kill weed")

                move_task = asyncio.get_event_loop().create_task(
                    scanner.gimbal.follow_timestamp(
                        timestamp_ms=maka_control_timestamp_ms(),
                        follow_velocity=current_vel,
                        follow_accel=current_accel,
                    )
                )

    finally:
        pass
