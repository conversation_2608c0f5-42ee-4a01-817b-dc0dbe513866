import asyncio
from asyncio import Future
from typing import Any, Dict, List, Optional, Set, Tuple

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.error import AimbotException
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.controllers.aimbot.utils.tasks import get_next_velocity_nofx_task
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from core.controls.exterminator.model.gimbal import Gimbal2D, GimbalException
from lib.common.geometric.cpp.geometric_python import GeometricScanner, get_global_height_estimator
from lib.common.types import GimbalPosition
from trajectory.pybind.trajectory_python import Trajectory

LOG = lib.common.logging.get_logger(__name__)


FOLLOW_FOREVER_ALGO_NAME = "FollowForever"

HEIGHT_ESTIMATOR_KEYS: Dict[str, Tuple[int, Tuple[str, int]]] = {}


async def get_height_key_for_scanner_pos(scanner: AimbotScanner, pan_pos: int) -> Tuple[str, int]:
    if scanner.id in HEIGHT_ESTIMATOR_KEYS and HEIGHT_ESTIMATOR_KEYS[scanner.id][0] == pan_pos:
        return HEIGHT_ESTIMATOR_KEYS[scanner.id][1]
    geo_scanner: GeometricScanner = scanner.geometric_scanner

    def _get_height_key() -> Tuple[str, int]:
        key: Tuple[str, int] = geo_scanner.get_height_key_from_servo_pos(pan_pos)
        return key

    key: Tuple[str, int] = await asyncio.get_event_loop().run_in_executor(None, _get_height_key)
    HEIGHT_ESTIMATOR_KEYS[scanner.id] = (pan_pos, key)
    return key


async def follow_forever_algo(
    scanner: AimbotScanner, _: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    LOG.debug(f"Follow Forever Algo with scanner: {scanner.id}")
    subscriber = get_global_config_subscriber()

    # This is hacky setup code for velocity
    # Hacky Code, TODO Remove once better data path is created
    gimbal = scanner.gimbal
    nofx = gimbal.hacky_nofx_device
    assert nofx is not None

    # ### P To S Initial Move ### #
    pan_pos = (
        round(
            float(scanner.gimbal.pan.max - scanner.gimbal.pan.min)
            * subscriber.get_config_node("aimbot", "follow_forever_pan_pos").get_float_value()
        )
        + scanner.gimbal.pan.min
    )
    height_key = await get_height_key_for_scanner_pos(scanner, pan_pos)
    z_height_mm = get_global_height_estimator().get_height_mm_for_key(height_key)
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_task(scanner, nofx, None, z_height_mm)
    p_to_s_follow_time_ms = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value()
    location: GimbalPosition = GimbalPosition(int(pan_pos), int(scanner.gimbal.tilt.max))  # GoTo Top
    await scanner.gimbal.async_set_position(location, await_settle=True)
    follow_vector: GimbalPosition = GimbalPosition(0, int(last_velocity * p_to_s_follow_time_ms))
    location = await scanner.gimbal.go_to_delta_follow(
        GimbalPosition(0, 0),
        follow_velocity_vector=follow_vector,
        follow_time_ms=p_to_s_follow_time_ms,
        interval_sleep_time_ms=0,
        mode=Gimbal2D.MODE_IMMEDIATE,
    )

    # TODO While this system is quite efficient, we should think of some nice abstractions to make this cleaner and simpler
    # Tasks
    step_follow_time_ms: int = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value()
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_task(scanner, nofx, None, z_height_mm)
    follow_vector = GimbalPosition(0, int(last_velocity * step_follow_time_ms))

    velocity_task = asyncio.get_event_loop().create_task(
        get_next_velocity_nofx_task(scanner, nofx, last_velocity_timestamp_ms, z_height_mm)
    )
    move_task = asyncio.get_event_loop().create_task(
        scanner.gimbal.go_to_delta_follow(
            GimbalPosition(0, 0),
            follow_velocity_vector=follow_vector,
            follow_time_ms=step_follow_time_ms,
            interval_sleep_time_ms=subscriber.get_config_node("aimbot", "go_to_interval_sleep_ms").get_uint_value(),
            mode=Gimbal2D.MODE_IMMEDIATE,
        )
    )

    while True:
        if not subscriber.get_config_node("aimbot", "height_estimation/enabled").get_bool_value():
            get_global_height_estimator().force_update_height(
                subscriber.get_config_node("aimbot", "tilt_mirror_height_mm").get_float_value()
                + subscriber.get_config_node("aimbot", "tilt_mirror_z_offset_mm").get_float_value()
            )

        tasks: List[asyncio.Task[Any]] = [move_task, velocity_task]
        all_tasks: Tuple[Set[Future[Any]], Set[Future[Any]]] = await asyncio.wait(
            tasks, return_when=asyncio.FIRST_COMPLETED
        )
        done: Set[Future[Any]] = all_tasks[0]

        if velocity_task in done:
            last_velocity_timestamp_ms, last_velocity = await velocity_task
            velocity_task = asyncio.get_event_loop().create_task(
                get_next_velocity_nofx_task(scanner, nofx, last_velocity_timestamp_ms, z_height_mm)
            )

        if move_task in done:
            step_follow_time_ms = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value()
            try:
                last_location = location
                location = await move_task
                if (
                    abs(last_location.tilt - location.tilt) <= 4
                    and follow_vector.tilt != 0
                    and location.tilt <= scanner.gimbal.tilt.min + 100
                ):
                    return  # We're done here, assuming this is bottom.
            except GimbalException:
                raise AimbotException("Failed to completely kill weed")

            wanted_delta = GimbalPosition(0, 0)

            if last_velocity is not None:
                follow_vector = GimbalPosition(0, int(last_velocity * step_follow_time_ms))
            else:
                follow_vector = GimbalPosition(0, 0)

            move_task = asyncio.get_event_loop().create_task(
                scanner.gimbal.go_to_delta_follow(
                    wanted_delta,
                    follow_velocity_vector=follow_vector,
                    follow_time_ms=step_follow_time_ms,
                    interval_sleep_time_ms=subscriber.get_config_node(
                        "aimbot", "go_to_interval_sleep_ms"
                    ).get_uint_value(),
                    mode=Gimbal2D.MODE_IMMEDIATE,
                )
            )
