import asyncio
import concurrent
import contextlib
import json
import math
import os
import random
from asyncio import CancelledError
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any, Awaitable, List, Optional, Set, Tuple

import core.controls.exterminator.controllers.targeting.p2p as p2p_targeting
import lib.common.logging
from config.client.cpp.config_client_python import (
    ConfigAtomicAccessorBool,
    ConfigAtomicAccessorFloat,
    ConfigAtomicAccessorUInt,
    ConfigSubscriber,
    ConfigTree,
    get_global_config_subscriber,
)
from core.controls.exterminator.controllers.aimbot.error import (
    <PERSON>mbotException,
    OutOfRangeAimbotException,
    P2PTargetDistanceExceeded,
    TargetChangeException,
    WeedNotFoundAimbotException,
)
from core.controls.exterminator.controllers.aimbot.metrics import (
    BOTTOM_1_COUNT,
    BOTTOM_2_COUNT,
    GIMBAL_OTHER_COUNT,
    G<PERSON><PERSON><PERSON>_OUT_COUNT,
    INITIAL_P2P_MATCH,
    P2P_ERROR_COUNT,
    SCANNER_TIME_METRICS,
    SUBSEQUENT_P2P_MATCH,
    VELOCITY_ERROR_COUNT,
    TimedMetric,
    increment_speculative_shoot,
)
from core.controls.exterminator.controllers.aimbot.utils.helper import (
    get_delta_gimbal_position_from_delta_target_with_dt,
    get_gimbal_position_from_abs_pos_geometric,
    get_mm_offset_from_ticks,
    get_nofx_from_scanner,
    get_target_delta,
)
from core.controls.exterminator.controllers.aimbot.utils.tasks import (
    get_next_image_and_p2p_task,
    get_next_velocity_nofx_task,
    get_next_velocity_nofx_with_pos_task,
    get_next_velocity_nofx_with_wait_task,
    laser_on_duration_task_safe,
    ltt_speculative_shoot,
    scanner_await_target_change_task,
)
from core.controls.exterminator.controllers.targeting.p2p import P2PContextNotFoundException
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from core.controls.exterminator.model.gimbal import Gimbal2D, GimbalOutOfBoundsException
from core.fs.defaults import MEDIA_DIR
from generated.proto.cv.cv_pb2 import P2PCaptureReason
from lib.common.asyncio.simple_task_group import SimpleTaskGroup
from lib.common.generation import is_bud
from lib.common.perf.perf_categories import PerfCategory  # used by grafana still
from lib.common.time import iso8601_timestamp, maka_control_timestamp_ms
from lib.common.types import GimbalPosition
from metrics.pybind.metrics_python import P2PAccuracy
from trajectory.pybind.trajectory_python import TrackedItemCentroid, Trajectory
from weed_tracking.cpp.weed_tracking_python import WeedingDiagnostics

LOG = lib.common.logging.get_logger(__name__)

if TYPE_CHECKING:
    from generated.proto.cv.cv_pb2 import P2PCaptureReasonValue

ROTARY_AND_P2P_ALGO_NAME = "RotaryAndP2P"

rp2p_executor = concurrent.futures.ThreadPoolExecutor(thread_name_prefix="rp2p_", max_workers=100)


class SingeltonConfigAccessorData(object):
    def __new__(cls, *args: Any, **kwargs: Any) -> "SingeltonConfigAccessorData":
        if not hasattr(cls, "instance"):
            cls.instance = super(SingeltonConfigAccessorData, cls).__new__(cls)
            cls.instance._init = False  # type: ignore
        return cls.instance

    def __init__(self, subscriber: ConfigSubscriber) -> None:
        if self._init:  # type: ignore
            return
        rotary_p2p_tree = subscriber.get_config_node("common", "shooting/rotary_and_p2p")
        self.p2p_on_target_count = ConfigAtomicAccessorUInt(rotary_p2p_tree.get_node("p2p_on_target_count"))
        self.max_p2p_target_switch_dist = ConfigAtomicAccessorFloat(
            rotary_p2p_tree.get_node("max_p2p_target_switch_dist")
        )
        self.p2p_target_switch_count = ConfigAtomicAccessorUInt(rotary_p2p_tree.get_node("p2p_target_switch_count"))
        self.p2p_switch_rate = ConfigAtomicAccessorFloat(rotary_p2p_tree.get_node("p2p_target_switch_capture_rate"))
        self.speculative_enabled = ConfigAtomicAccessorBool(
            subscriber.get_config_node("common", "feature_flags/speculative_laser_feature")
        )
        self.speculative_dist_sqrd = ConfigAtomicAccessorFloat(
            subscriber.get_config_node("aimbot", "geometric/calibrator/max_dist_speculative_laser"), lambda x: x ** 2
        )
        self.first_p2p_cap_rate = ConfigAtomicAccessorFloat(rotary_p2p_tree.get_node("first_p2p_capture_rate"))
        self.first_p2p_miss_cap_rate = ConfigAtomicAccessorFloat(
            rotary_p2p_tree.get_node("first_p2p_miss_capture_rate")
        )
        self.p2p_miss_cap_rate = ConfigAtomicAccessorFloat(rotary_p2p_tree.get_node("p2p_miss_capture_rate"))
        self.use_fh_traj = ConfigAtomicAccessorBool(subscriber.get_config_node("aimbot", "use_fh_trajectory"))
        self._init = True
        LOG.info("Rotary and P2P config data loaded")


def check_rate(rate: float) -> bool:
    if rate <= 0.0:
        return False
    return rate > random.random()


async def capture_p2p(
    scanner: AimbotScanner,
    timestamp: int,
    reason: "P2PCaptureReasonValue",
    should_capture: Optional[bool] = None,
    write_to_disk: bool = False,
) -> None:
    async def _inner() -> None:
        if should_capture is not None:
            if not should_capture:
                return
        # TODO determine correct path
        target_cam_id = f"target{scanner.numeric_id}"
        datetime_string = iso8601_timestamp(replace_colon=True)
        machine_info = os.getenv("MAKA_ROBOT_NAME")
        row_info = os.getenv("MAKA_ROW")
        name = f"{machine_info}_row{row_info}_{target_cam_id}_{datetime_string}"
        LOG.info(f"Creating p2p capture {name} for reason {P2PCaptureReason.Name(reason)}")

        await scanner.cv_runtime_client.p2p_capture(target_cam_id, name, timestamp, write_to_disk, reason)

    asyncio.get_event_loop().create_task(_inner())


@dataclass
class ProperOffWrapper:
    is_off: bool = False


async def rotary_and_p2p_algo(
    scanner: AimbotScanner, optional_weed: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    assert optional_weed is not None
    assert scanner.target
    assert overhead_metric is not None
    overhead_metric.start("algo_start")
    target_cam_id = f"target{scanner.numeric_id}"
    tasks: List[asyncio.Future[Any]] = []
    start = maka_control_timestamp_ms()
    should_capture = False
    datetime_string = iso8601_timestamp(replace_colon=True)
    burst_record_metadata = {"iso8601_timestamp": datetime_string, "target_crosshair": list(scanner.target)}
    proper_off = ProperOffWrapper()
    try:
        async with SimpleTaskGroup() as tg:
            await _rotary_and_p2p_algo(scanner, optional_weed, overhead_metric, proper_off, tg)
        # TODO add specific error handlers to set should_capture
    finally:
        off_task: Optional[asyncio.Future[bool]] = None
        if not proper_off.is_off:
            off_task = asyncio.shield(scanner.laser.async_off())
            tasks.append(off_task)
        overhead_metric.start("algo_finally_capture")
        end = maka_control_timestamp_ms()
        subscriber = get_global_config_subscriber()
        marked_for_diagnostics_images = WeedingDiagnostics.get().is_marked_for_images(optional_weed.id())
        if marked_for_diagnostics_images:
            WeedingDiagnostics.get().attempting_to_save_predict(optional_weed.id())
        was_node = subscriber.get_config_node("aimbot", "weed_accuracy_sampling")
        collect_accuracy_samples: bool = random.random() < was_node.get_node("rate").get_float_value()
        if not should_capture:
            burst_capture_config_node: ConfigTree = subscriber.get_config_node("aimbot", "target_burst_capture")
            burst_capture_filter = [
                node.get_uint_value() for node in burst_capture_config_node.get_node("filter").get_children_nodes()
            ]
            should_capture = (
                (
                    burst_capture_config_node.get_node("rate").get_float_value() > random.random()
                    or (collect_accuracy_samples and was_node.get_node("record_enabled").get_bool_value())
                    or marked_for_diagnostics_images
                )
                and (not bool(burst_capture_filter) or scanner.numeric_id in burst_capture_filter)
                and (optional_weed.checkout_for_burst_recording())
            )

        burst_capture_base_dir = f"{MEDIA_DIR}/burst_recordings/full_burst_recordings"
        machine_info = os.getenv("MAKA_ROBOT_NAME")
        row_info = os.getenv("MAKA_ROW")
        burst_record_path = (
            f"/data/ftp/recorder/diagnostics/burst_records/{optional_weed.id()}_burst_record"
            if marked_for_diagnostics_images
            else f"{burst_capture_base_dir}/{machine_info}_row{row_info}_{target_cam_id}_{datetime_string}"
        )
        predict_burst_record_path = None
        if marked_for_diagnostics_images:
            predict_burst_record_path = f"/data/ftp/recorder/diagnostics/p2p_predicts/{optional_weed.id()}"

        if should_capture:
            LOG.info(f"Saving burst recording to {burst_record_path}")
            os.makedirs(burst_record_path, exist_ok=True)
            if predict_burst_record_path:
                os.makedirs(predict_burst_record_path, exist_ok=True)
            with open(f"{burst_record_path}/meta.json", "w") as fp:
                json.dump(burst_record_metadata, fp)

            tasks.append(
                asyncio.get_event_loop().create_task(
                    scanner.cv_runtime_client.save_buffered_burst(
                        target_cam_id,
                        burst_record_path,
                        start,
                        end,
                        dont_capture_predict=False,
                        predict_path=predict_burst_record_path,
                        save_predict_metadata=True,
                        plant_size_px=optional_weed.get_last_centroid_with_perspective(False).get_size(),
                    )
                )
            )
        overhead_metric.start("algo_finally_task_gather")
        try:
            await asyncio.gather(*tasks, return_exceptions=True)
            for task in tasks:
                # This will raise the exceptions if they occur so they can be logged
                await task
        except Exception as ex:
            LOG.error(f"Unknown exception in finally block for {optional_weed.id()}: {ex}")
        finally:
            WeedingDiagnostics.get().finished_attempting_to_save_predict(optional_weed.id())


# TODO break down this function and modularize
async def _rotary_and_p2p_algo(  # noqa: C901
    scanner: AimbotScanner,
    weed: Trajectory,
    overhead_metric: TimedMetric,
    proper_off: ProperOffWrapper,
    tg: SimpleTaskGroup,  # noqa: C901
) -> None:  # noqa: C901
    overhead_metric.start("initial_compute")
    LOG.debug(f"Rotary And P2P Algo with scanner: {scanner.id}")
    use_traj = not is_bud()
    SCANNER_TIME_METRICS.start_overhead(scanner.numeric_id)
    if scanner.last_weed["id"] != weed.id():
        scanner.last_weed["id"] = weed.id()
        scanner.last_weed["failed"] = False
    weed.add_assigned_laser(scanner.numeric_id)
    subscriber = get_global_config_subscriber()
    cfg_data = SingeltonConfigAccessorData(subscriber)

    # This is hacky setup code for velocity
    # Hacky Code, TODO Remove once better data path is created
    nofx = get_nofx_from_scanner(scanner)

    # ### P To S Initial Move ### #
    interval_sleep_ms = scanner.board.settle_time
    interval_sleep_extra_ms = 5

    overhead_metric.start("initial_get_estimated_position")
    tracked_position = await asyncio.get_event_loop().run_in_executor(
        rp2p_executor, lambda: weed.get_estimated_position_mm_from_now(interval_sleep_ms + interval_sleep_extra_ms)
    )

    pcam: int = weed.tracker_id()

    overhead_metric.start("initial_compute_geometry")
    offsets = scanner.geometric_scanner.get_offset(
        pcam, tracked_position.get_x(), tracked_position.get_y(), tracked_position.get_height_mm()
    )

    pos: Tuple[float, float, float] = (
        tracked_position.get_x() + offsets[0],
        tracked_position.get_y() + offsets[1],
        tracked_position.get_height_mm() + offsets[2],
    )

    pan_max = scanner.gimbal.max[0]
    tilt_max = scanner.gimbal.max[1]

    location: GimbalPosition = await asyncio.get_event_loop().run_in_executor(
        rp2p_executor, lambda: get_gimbal_position_from_abs_pos_geometric(scanner, pos)
    )

    goal_tilt = location[1]
    percent_from_bottom = goal_tilt / tilt_max
    percent_from_top = 1 - percent_from_bottom
    await asyncio.get_event_loop().run_in_executor(
        rp2p_executor, lambda: scanner.scanner_wrapper.add_tilt_start_point(goal_tilt)
    )

    goal_pan = location[0]
    percent_from_right = goal_pan / pan_max
    percent_from_left = 1 - percent_from_right

    centroid, opt_centroid = await asyncio.get_event_loop().run_in_executor(
        rp2p_executor, lambda: weed.get_centroid_with_perspective(percent_from_top, percent_from_left)
    )
    if centroid is None:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        raise P2PContextNotFoundException("Trajectory has no valid Centroid")

    if not centroid.get_has_perspective():
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        raise AimbotException("Centroid has no Perpective in life")

    centroid_pcam = centroid.get_tracker_id()
    if centroid_pcam != pcam:
        # using x cam duplicate data need to switch pcams
        pcam = centroid_pcam
        offsets = scanner.geometric_scanner.get_offset(
            pcam, tracked_position.get_x(), tracked_position.get_y(), tracked_position.get_height_mm()
        )
        pos = (
            tracked_position.get_x() + offsets[0],
            tracked_position.get_y() + offsets[1],
            tracked_position.get_height_mm() + offsets[2],
        )
        location = await asyncio.get_event_loop().run_in_executor(
            rp2p_executor, lambda: get_gimbal_position_from_abs_pos_geometric(scanner, pos)
        )

    move_time = maka_control_timestamp_ms()

    step_follow_time_ms: int = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value() * 2
    z_height_mm = pos[2]
    overhead_metric.start("get_initial_vel")
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_with_pos_task(
        scanner, nofx, None, z_height_mm, location.pan, location.tilt
    )
    set_initial_p2p_context_task = await tg.create_task(
        get_next_image_and_p2p_task(
            scanner,
            weed,
            centroid,
            0,
            forward=last_velocity <= 0,
            fallback_centroid=opt_centroid,
            set_context_only=True,
        )
    )

    use_traj = use_traj and cfg_data.use_fh_traj.get_value()
    gimbal_mode = Gimbal2D.MODE_TRAJECTORY if use_traj else Gimbal2D.MODE_IMMEDIATE
    min_p2p_on_target_count = cfg_data.p2p_on_target_count.get_value()
    max_p2p_target_switch_dist = cfg_data.max_p2p_target_switch_dist.get_value()
    max_p2p_target_switch_count = cfg_data.p2p_target_switch_count.get_value()
    p2p_target_switch_count = 0

    first_move_velocity_mrpm: Optional[Tuple[int, int]] = (100000, 100000)

    overhead_metric.start("initial_set_position")
    try:
        req_pan = round(location.pan)
        req_tilt = round(location.tilt)
        _, post_pos = await scanner.gimbal.go_to_timestamp(
            timestamp_ms=move_time,
            mode=gimbal_mode,
            position=(req_pan, req_tilt),
            velocity_mrpm=first_move_velocity_mrpm,
            follow_velocity=(0, round(last_velocity * 1000)),
            follow_accel=(0, 0),
            interval_sleep_time_ms=interval_sleep_ms,
            timeout_ms=200,
            query_latest_pos_for_vel=False,
        )
        pan_delta_req = abs(post_pos[1][0] - req_pan)
        tilt_delta_req = abs(post_pos[1][1] - req_tilt)
        scanner.add_to_failure_detection((pan_delta_req, tilt_delta_req))

    except GimbalOutOfBoundsException:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(abnormal=True)
        raise OutOfRangeAimbotException(f"{scanner.id}: Weed Out Of Bound Before Initial Move", "initial_p2s") from None
    overhead_metric.start("post_initial_move")
    end_of_predict_ms = maka_control_timestamp_ms()

    initial_follow_task_stop = asyncio.Event()

    delta_follow_timeout_ms = 100

    async def initial_follow_task_func(wait_time_ms: int) -> None:
        if wait_time_ms > 0:
            await asyncio.sleep(wait_time_ms / 1000)
        initial_follow_start_time_ms = maka_control_timestamp_ms()
        while maka_control_timestamp_ms() - initial_follow_start_time_ms < 300:

            _, initial_last_velocity = await get_next_velocity_nofx_task(scanner, nofx, None, z_height_mm)

            await scanner.gimbal.go_to_delta_follow(
                GimbalPosition(0, 0),
                follow_velocity_vector=GimbalPosition(0, int(initial_last_velocity * step_follow_time_ms)),
                follow_time_ms=step_follow_time_ms,
                interval_sleep_time_ms=0,
                mode=Gimbal2D.MODE_IMMEDIATE,
                timeout_ms=delta_follow_timeout_ms,
            )

            with contextlib.suppress(asyncio.TimeoutError):
                await asyncio.wait_for(initial_follow_task_stop.wait(), 0.010)

            if initial_follow_task_stop.is_set():
                return

    speculative_enabled = cfg_data.speculative_enabled.get_value()
    speculative_dist_sqrd = cfg_data.speculative_dist_sqrd.get_value()
    speculative_enabled = (
        speculative_enabled
        and weed.speculative_allowed()
        and scanner.geometric_scanner.get_dist_in_range(
            pcam, tracked_position.get_x(), tracked_position.get_y(), tracked_position.get_height_mm()
        )
    )
    speculative_laser_start = maka_control_timestamp_ms()

    speculative_on_task: Optional[asyncio.Task[None]] = None
    if speculative_enabled:
        speculative_on_task = await tg.create_task(scanner.laser.async_on())

    p2p_time_offset_ms = 0
    try:
        wait_time = 0
        initial_follow_task = await tg.create_task(initial_follow_task_func(wait_time))

        # TODO start moving based on wheel encoders here already
        # ### First Delta Target Move ### #

        step_go_to_interval_ms: int = subscriber.get_config_node("aimbot", "go_to_interval_sleep_ms").get_uint_value()
        overhead_metric.start("await_set_initial_context")
        await set_initial_p2p_context_task
        overhead_metric.start("first_p2p")
        p2p_ts = maka_control_timestamp_ms()
        p2p_ts += p2p_time_offset_ms
        first_p2p_match = await get_next_image_and_p2p_task(
            scanner, weed, centroid, p2p_ts, forward=last_velocity <= 0, fallback_centroid=opt_centroid
        )
    except Exception as ex:  # noqa
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(abnormal=True)
        raise ex
    finally:
        initial_follow_task_stop.set()
        if speculative_on_task is not None:
            await speculative_on_task

    INITIAL_P2P_MATCH.labels(PerfCategory.AIMBOT.lower(), scanner.id).observe(first_p2p_match.found)
    if not first_p2p_match.found:
        scanner.geometric_scanner.add_failure(
            pcam, tracked_position.get_x(), tracked_position.get_y(), tracked_position.get_height_mm()
        )
        await capture_p2p(
            scanner,
            first_p2p_match.timestamp_ms,
            P2PCaptureReason.P2PCaptureReason_MISS,
            check_rate(cfg_data.first_p2p_miss_cap_rate.get_value()),
        )
        weed.increment_extermination_failures()
        if not scanner.last_weed["failed"]:
            scanner.last_weed["failed"] = True
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(True)
        raise WeedNotFoundAimbotException(
            f"{scanner.id} Weed {weed.id()} not in expected Location with height estimate {pos[2]} mm. Total failed extermination instances for weed: {weed.extermination_failures()}",
            "initial_p2p",
        )
    overhead_metric.start("get_initial_vel2")
    assert first_p2p_match.coord is not None and first_p2p_match.timestamp_ms is not None
    await capture_p2p(
        scanner,
        first_p2p_match.timestamp_ms,
        P2PCaptureReason.P2PCaptureReason_First_P2P,
        check_rate(cfg_data.first_p2p_cap_rate.get_value()),
    )
    scanner.last_weed["failed"] = False

    dt_ms = maka_control_timestamp_ms() - end_of_predict_ms
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_with_pos_task(
        scanner, nofx, None, z_height_mm, location.pan, location.tilt + int(last_velocity * dt_ms),
    )

    overhead_metric.start("get_pos")
    delta_target: Tuple[float, float] = get_target_delta(scanner, first_p2p_match.coord)
    delta_servos = get_delta_gimbal_position_from_delta_target_with_dt(scanner, delta_target, dt_ms, (0, last_velocity))
    pan_offset_mm, tilt_offset_mm = get_mm_offset_from_ticks(scanner, delta_servos.pan, delta_servos.tilt, z_height_mm)
    # TODO Consider if repeated no bueno could be helped by change in height, etc.
    scanner.geometric_scanner.add_offset_val(
        pcam,
        tracked_position.get_x(),
        tracked_position.get_y(),
        tracked_position.get_height_mm(),
        pan_offset_mm + offsets[0],
        tilt_offset_mm + offsets[1],
        0 + offsets[2],
    )
    p2p_match_count = 1

    P2PAccuracy().add(scanner.numeric_id, pan_offset_mm, tilt_offset_mm, p2p_match_count)

    additional_time = 0
    speculative_shoot_used = speculative_enabled and (pan_offset_mm ** 2 + tilt_offset_mm ** 2) < speculative_dist_sqrd
    await tg.create_task(increment_speculative_shoot(scanner.id, speculative_enabled, speculative_shoot_used))
    if speculative_shoot_used:
        additional_time = maka_control_timestamp_ms() - speculative_laser_start
        if await ltt_speculative_shoot(scanner, additional_time, weed):
            overhead_metric.end(True)  # don't count towards get_pos
            return

    servos = scanner.gimbal.get_goal_position()
    servos = GimbalPosition(round(servos.pan), round(servos.tilt + (dt_ms * last_velocity)))
    interval_offset = 3
    wanted_delta = GimbalPosition(
        int(delta_servos[0]),
        int(
            delta_servos[1]
            + ((step_go_to_interval_ms + interval_offset) * last_velocity if last_velocity is not None else 0)
        ),
    )
    follow_vector = GimbalPosition(0, int(last_velocity * step_follow_time_ms),)
    overhead_metric.start("await_initial_follow")
    try:
        await initial_follow_task
        overhead_metric.start("subsequent_delta_follow")
        subsequent_df_task = await tg.create_task(
            scanner.gimbal.go_to_delta_follow(
                wanted_delta,
                follow_velocity_vector=follow_vector,
                follow_time_ms=step_follow_time_ms,
                interval_sleep_time_ms=step_go_to_interval_ms,
                mode=Gimbal2D.MODE_IMMEDIATE,
                fast_return=False,
                timeout_ms=delta_follow_timeout_ms,
            )
        )
    except GimbalOutOfBoundsException:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(True)
        raise OutOfRangeAimbotException("Failed to move to start killing weed", "initial_follow") from None

    # TODO While this system is quite efficient, we should think of some nice abstractions to make this cleaner and simpler
    # Tasks

    pause_lasing_event = asyncio.Event()
    resume_lasing_event = asyncio.Event()
    exit_event = asyncio.Event()

    step_follow_time_ms = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value()
    last_velocity_timestamp_ms, last_velocity = await get_next_velocity_nofx_task(scanner, nofx, None, z_height_mm)
    follow_vector = GimbalPosition(0, int(last_velocity * step_follow_time_ms))
    this_step_go_to_interval_ms = 0
    wanted_delta = GimbalPosition(0, 0)
    p2p_match = p2p_targeting.P2PMatch(None, False, 0)
    p2p_match_fails = []
    match_fail_count: int = 0
    try:
        await subsequent_df_task
    except GimbalOutOfBoundsException as ex:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(True)
        err_str = "first_p2p"
        if ex.error_in_pan:
            weed.remove_viable_scanner(scanner.numeric_id)
            err_str += "_bad_pan"
        if ex.error_in_tilt:
            err_str += "_bad_tilt"
        raise OutOfRangeAimbotException("Failed to move to start killing weed", err_str) from None
    overhead_metric.start("post_subsequent_move")
    last_move_timestamp_ms: int = maka_control_timestamp_ms()

    # The below are for synchronization between P2P image capture, we should revisit
    # and find a cleaner way at some point
    next_p2p_ready: bool = False
    doing_p2p: bool = True
    p2p_move: bool = False

    p2p_task: Optional[asyncio.Task[p2p_targeting.P2PMatch]] = await tg.create_task(
        get_next_image_and_p2p_task(
            scanner,
            weed,
            centroid,
            last_move_timestamp_ms + p2p_time_offset_ms,
            forward=last_velocity <= 0,
            fallback_centroid=opt_centroid,
        )
    )
    scanner.geometric_scanner.add_dist(
        pcam,
        tracked_position.get_x(),
        tracked_position.get_y(),
        tracked_position.get_height_mm(),
        pan_offset_mm,
        tilt_offset_mm,
    )
    if speculative_shoot_used:
        additional_time = maka_control_timestamp_ms() - speculative_laser_start

    laser_shoot_task = asyncio.shield(
        laser_on_duration_task_safe(
            scanner,
            pause_signal=pause_lasing_event,
            resume_signal=resume_lasing_event,
            exit_signal=exit_event,
            trajectory=weed,
            additional_time=additional_time,
        )
    )
    velocity_task = await tg.create_task(get_next_velocity_nofx_with_wait_task(scanner, nofx, 10, z_height_mm))

    async def _new_centroid_with_wait(
        location: GimbalPosition, extra_wait_ms: int = 50
    ) -> Tuple[Optional[TrackedItemCentroid], Optional[TrackedItemCentroid]]:
        await asyncio.sleep(extra_wait_ms / 1000)
        goal_tilt = location[1]
        tilt_max = scanner.gimbal.max[1]
        percent_from_bottom = goal_tilt / tilt_max
        percent_from_top = 1 - percent_from_bottom

        goal_pan = location[0]
        pan_max = scanner.gimbal.max[0]
        percent_from_right = goal_pan / pan_max
        percent_from_left = 1 - percent_from_right

        return await asyncio.get_event_loop().run_in_executor(
            rp2p_executor, lambda: weed.get_centroid_with_perspective(percent_from_top, percent_from_left)
        )

    new_centroid_task = await tg.create_task(_new_centroid_with_wait(location, 0))
    # TODO Consider What this move actually does, maybe it should be zero...
    move_task: Optional[asyncio.Task[GimbalPosition]] = await tg.create_task(
        scanner.gimbal.go_to_delta_follow(
            wanted_delta,
            follow_velocity_vector=follow_vector,
            follow_time_ms=step_follow_time_ms,
            interval_sleep_time_ms=this_step_go_to_interval_ms,
            mode=Gimbal2D.MODE_IMMEDIATE,
            timeout_ms=delta_follow_timeout_ms,
        )
    )
    last_velocity_timestamp_applied_ms = last_velocity_timestamp_ms

    target_change_task = await tg.create_task(scanner_await_target_change_task(scanner))

    tasks: List[Awaitable[Any]] = []
    unfiltered_tasks: List[Optional[asyncio.Future[Any]]] = []
    send_change_signal = True

    rotary_p2p_tree = subscriber.get_config_node("common", "shooting/rotary_and_p2p")
    write_to_disk_node = rotary_p2p_tree.get_node("p2p_captures_write_to_disk")

    overhead_metric.stop()
    try:
        while True:

            unfiltered_tasks = [p2p_task, laser_shoot_task, move_task, velocity_task, target_change_task]
            tasks = [t for t in unfiltered_tasks if t is not None]

            all_tasks: Tuple[Set[asyncio.Task[Any]], Set[asyncio.Task[Any]]] = await asyncio.wait(
                tasks, return_when=asyncio.FIRST_COMPLETED
            )
            done = all_tasks[0]
            if laser_shoot_task in done:
                proper_off.is_off = await laser_shoot_task
                break
            if target_change_task in done:
                send_change_signal = False
                raise TargetChangeException(f"scanner {scanner.id}: changed targets", weed.id())
            if p2p_task is not None and p2p_task in done and doing_p2p:
                try:
                    p2p_match = await p2p_task
                    p2p_task = None
                except Exception:
                    P2P_ERROR_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                    raise
                SUBSEQUENT_P2P_MATCH.labels(PerfCategory.AIMBOT.lower(), scanner.id).observe(p2p_match.found)

                if p2p_match.safe:
                    pause_lasing_event.clear()
                    resume_lasing_event.set()
                else:
                    resume_lasing_event.clear()
                    pause_lasing_event.set()

                if not p2p_match.found:
                    match_fail_count += 1
                    p2p_match_fails.append(p2p_match)
                    if match_fail_count > subscriber.get_config_node("aimbot", "max_p2p_match_fail").get_uint_value():
                        await capture_p2p(
                            scanner,
                            random.choice(p2p_match_fails).timestamp_ms,
                            P2PCaptureReason.P2PCaptureReason_MISS,
                            check_rate(cfg_data.p2p_miss_cap_rate.get_value()),
                        )
                        weed.increment_extermination_failures()
                        raise WeedNotFoundAimbotException("Too many P2P Failures", "subsequent_p2ps")
                else:
                    match_fail_count = 0
                    p2p_match_fails = []
                doing_p2p = False

            if velocity_task in done:
                try:
                    last_velocity_timestamp_ms, last_velocity = await velocity_task
                except Exception:
                    VELOCITY_ERROR_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                    raise
                velocity_task = await tg.create_task(
                    get_next_velocity_nofx_with_wait_task(scanner, nofx, 10, z_height_mm)
                )

            if new_centroid_task in done:
                new_centroid, new_opt_centroid = await new_centroid_task
                if new_centroid is not None and new_opt_centroid is not None and new_centroid.get_has_perspective():
                    centroid = new_centroid
                    opt_centroid = new_opt_centroid
                else:
                    raise P2PContextNotFoundException("Trajectory has no valid Centroid")
                new_centroid_task = await tg.create_task(_new_centroid_with_wait(location))

            if move_task in done or move_task is None:
                step_follow_time_ms = subscriber.get_config_node("aimbot", "short_follow_time_ms").get_uint_value()
                step_go_to_interval_ms = subscriber.get_config_node(
                    "aimbot", "go_to_interval_sleep_ms"
                ).get_uint_value()

                if move_task is not None:
                    try:
                        location = await move_task
                        # location can be 0 if an axis returns an error on delta follow.
                        # Since we keep our gimbal from ever reaching 0, we can assume if 0 then this
                        # is actually an error not a valid location.
                        if location.tilt == 0 or location.pan == 0:
                            LOG.warning(f"{scanner.id} location = ({location.pan}, {location.tilt})")
                        elif location.tilt >= scanner.gimbal.tilt.max - 250 and follow_vector.tilt > 0:
                            BOTTOM_1_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                            raise OutOfRangeAimbotException(
                                f"scanner {scanner.id}: Failed To Completely Kill, Reached Bottom of Range",
                                "reached_bottom",
                            )
                        elif location.tilt <= scanner.gimbal.tilt.min + 250 and follow_vector.tilt < 0:
                            BOTTOM_2_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                            raise OutOfRangeAimbotException(
                                f"scanner {scanner.id}: Failed To Completely Kill, Reached Top of Range", "reached_top"
                            )
                    except GimbalOutOfBoundsException as ex:
                        if ex.error_in_pan:
                            weed.remove_viable_scanner(scanner.numeric_id)
                        GIMBAL_OUT_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                        raise OutOfRangeAimbotException(
                            f"scanner {scanner.id}: Failed to completely kill weed", "subsequent_df"
                        )
                    except Exception:
                        GIMBAL_OTHER_COUNT.labels(PerfCategory.AIMBOT.lower(), scanner.id).inc()
                        raise

                if not doing_p2p and p2p_move:
                    next_p2p_ready = True

                found = False
                if p2p_match.found:
                    assert p2p_match.coord is not None
                    # Positional Delta Correction
                    dt_ms = 5
                    delta_target = get_target_delta(scanner, p2p_match.coord)
                    delta_servos = get_delta_gimbal_position_from_delta_target_with_dt(
                        scanner, delta_target, dt_ms, (0, last_velocity)
                    )
                    pan_offset_mm, tilt_offset_mm = get_mm_offset_from_ticks(
                        scanner, delta_servos.pan, delta_servos.tilt, pos[2]
                    )
                    found = True
                    if p2p_match_count > min_p2p_on_target_count:
                        dist = math.sqrt(pan_offset_mm ** 2 + tilt_offset_mm ** 2)
                        if dist > max_p2p_target_switch_dist:
                            # It appears we jumped to a different target
                            this_step_go_to_interval_ms = 0
                            wanted_delta = GimbalPosition(0, 0)

                            found = False
                            p2p_target_switch_count += 1
                            await capture_p2p(
                                scanner,
                                p2p_match.timestamp_ms,
                                P2PCaptureReason.P2PCaptureReason_JUMP,
                                check_rate(cfg_data.p2p_switch_rate.get_value()),
                                write_to_disk_node.get_bool_value(),
                            )
                            if p2p_target_switch_count > max_p2p_target_switch_count:
                                weed.increment_extermination_failures()
                                raise P2PTargetDistanceExceeded(
                                    "Too many P2P Failures, p2p matching with different target"
                                )
                            if p2p_match.timestamp_ms is not None:
                                last_move_timestamp_ms = p2p_match.timestamp_ms  # force next p2p
                            else:
                                last_move_timestamp_ms = maka_control_timestamp_ms()  # force next p2p
                    p2p_match = p2p_targeting.P2PMatch(None, safe=True, timestamp_ms=0)
                if found:
                    p2p_match_count += 1
                    P2PAccuracy().add(scanner.numeric_id, pan_offset_mm, tilt_offset_mm, p2p_match_count)

                    servos = scanner.gimbal.get_goal_position()
                    servos = GimbalPosition(round(servos.pan), round(servos.tilt + (dt_ms * last_velocity)))
                    this_step_go_to_interval_ms = step_go_to_interval_ms
                    wanted_delta = GimbalPosition(
                        int(delta_servos[0]),
                        int(
                            delta_servos[1]
                            + (
                                (this_step_go_to_interval_ms + interval_offset) * last_velocity
                                if last_velocity is not None
                                else 0
                            )
                        ),
                    )
                    p2p_move = True
                else:
                    p2p_move = False
                    this_step_go_to_interval_ms = 0
                    wanted_delta = GimbalPosition(0, 0)
                    if not doing_p2p:
                        next_p2p_ready = True
                        last_move_timestamp_ms = maka_control_timestamp_ms()

                if last_velocity is not None:
                    follow_vector = GimbalPosition(0, int(last_velocity * step_follow_time_ms))
                else:
                    follow_vector = GimbalPosition(0, 0)

                if p2p_move or last_velocity_timestamp_applied_ms != last_velocity_timestamp_ms:
                    move_task = await tg.create_task(
                        scanner.gimbal.go_to_delta_follow(
                            wanted_delta,
                            follow_velocity_vector=follow_vector,
                            follow_time_ms=step_follow_time_ms,
                            interval_sleep_time_ms=this_step_go_to_interval_ms,
                            mode=Gimbal2D.MODE_IMMEDIATE,
                            timeout_ms=delta_follow_timeout_ms,
                        )
                    )
                    last_move_timestamp_ms = maka_control_timestamp_ms()
                    last_velocity_timestamp_applied_ms = last_velocity_timestamp_ms
                else:
                    move_task = None

            if next_p2p_ready:
                doing_p2p = True
                p2p_task = await tg.create_task(
                    # TODO Config Service the +10
                    get_next_image_and_p2p_task(
                        scanner,
                        weed,
                        centroid,
                        last_move_timestamp_ms + p2p_time_offset_ms,
                        forward=last_velocity <= 0,
                        fallback_centroid=opt_centroid,
                    )
                )
                next_p2p_ready = False
    except TargetChangeException:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(abnormal=True)
        raise
    except Exception:
        SCANNER_TIME_METRICS.abnormal_exit(scanner.numeric_id)
        overhead_metric.end(abnormal=True)
        raise
    finally:
        # TODO consider how all cleanup tasks can be merged even with caller
        overhead_metric.start("loop_finally_event")
        exit_event.set()
        pause_lasing_event.set()
        resume_lasing_event.set()
        unfiltered_tasks = [p2p_task, move_task, velocity_task, target_change_task]
        cancellable_tasks = [t for t in unfiltered_tasks if t is not None]
        tasks = [t for t in cancellable_tasks]
        off_task: Optional[asyncio.Future[bool]] = None
        if not proper_off.is_off:
            off_task = asyncio.shield(scanner.laser.async_off())
            tasks.append(off_task)
        overhead_metric.start("loop_finally_stop_p2p")
        try:
            overhead_metric.start("loop_finally_signal")
            if send_change_signal:
                tasks.append(
                    asyncio.get_event_loop().run_in_executor(
                        rp2p_executor, scanner.scanner_wrapper.notify_target_change
                    )
                )  # Force target change task to end no matter what

            overhead_metric.start("loop_finally_cancel_await_task")
            for task in cancellable_tasks:
                task.cancel()  # Force task to end if not already done
            tasks.append(laser_shoot_task)

            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except CancelledError:
                pass
            finally:
                if off_task is not None:
                    proper_off.is_off = await off_task
        except GimbalOutOfBoundsException:
            pass
