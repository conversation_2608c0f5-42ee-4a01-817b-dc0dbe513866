import asyncio
import itertools
from typing import Optional

import numpy as np

import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.metrics import TimedMetric
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from lib.common.types import GimbalPosition
from trajectory.pybind.trajectory_python import Trajectory

LOG = lib.common.logging.get_logger(__name__)


LASER_TEST_ALGO_NAME = "LaserTest"


async def laser_test_algo(
    scanner: AimbotScanner, _: Optional[Trajectory], overhead_metric: Optional[TimedMetric] = None
) -> None:
    LOG.debug(f"Laser Test Algo with scanner: {scanner.id}")
    subscriber = get_global_config_subscriber()

    pan_positions = np.linspace(scanner.gimbal.pan.min, scanner.gimbal.pan.max, num=10)
    tilt_positions = np.linspace(scanner.gimbal.tilt.min, scanner.gimbal.tilt.max, num=10)

    key = f"{scanner.id}"
    pan_pos_dict = {
        AimbotScanner.make_identifier(1): 0,
        AimbotScanner.make_identifier(2): 3000,
        AimbotScanner.make_identifier(3): 7000,
        AimbotScanner.make_identifier(4): 10000,
        AimbotScanner.make_identifier(5): 0,
        AimbotScanner.make_identifier(6): 3000,
        AimbotScanner.make_identifier(7): 7000,
        AimbotScanner.make_identifier(8): 10000,
    }

    while True:
        for pan_pos, tilt_pos in itertools.product(pan_positions, tilt_positions):
            tilt_pos = 5000
            pan_pos = pan_pos_dict[key]
            await scanner.gimbal.async_set_position(GimbalPosition(int(pan_pos), int(tilt_pos)), await_settle=True)
            try:
                await scanner.laser.async_on()
                await asyncio.sleep(subscriber.get_config_node("aimbot", "laser_test_time_ms").get_uint_value() / 1000)

            finally:
                await scanner.laser.async_off()
