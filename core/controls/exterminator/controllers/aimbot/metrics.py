import asyncio
import collections
import os
from contextlib import contextmanager
from types import TracebackType
from typing import Awaitable, De<PERSON>ultD<PERSON>, Deque, Dict, Generator, List, Optional, Tuple, Type

from prometheus_client import Counter, Gauge, Histogram, Info

from config.client.cpp.config_client_python import get_global_config_subscriber
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.collections.keydefaultdict import keydefaultdict
from lib.common.logging import get_logger
from lib.common.redis_client import RedisClient
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

# Metrics for aimbot
sub_system = "aimbot"

AIMBOT_ALGO = Info(subsystem=sub_system, name="algo", documentation="static information about aimbot")

INITIAL_P2P_MATCH = Histogram(
    subsystem=sub_system, name="initial_p2p_match", documentation="todo", labelnames=["category", "scanner_id"]
)

P2P_ERROR_COUNT = Counter(
    subsystem=sub_system, name="p2p_error_total", documentation="todo", labelnames=["category", "scanner_id"]
)

ALL_ERROR_COUNT = Counter(
    subsystem=sub_system, name="all_error_total", documentation="todo", labelnames=["category", "scanner_id"]
)

LASER_TIME_MS = Histogram(
    subsystem=sub_system, name="laser_time_milliseconds", documentation="todo", labelnames=["category", "scanner_id"]
)

SINGLE_LOOP_TIME_MS = Histogram(
    subsystem=sub_system, name="single_loop_time_milliseconds", documentation="todo", labelnames=["category"]
)

SUBSEQUENT_P2P_MATCH = Histogram(
    subsystem=sub_system, name="subsequent_p2p_match", documentation="todo", labelnames=["category", "scanner_id"]
)

VELOCITY_ERROR_COUNT = Counter(
    subsystem=sub_system, name="velocity_error_total", documentation="todo", labelnames=["category", "scanner_id"]
)

BOTTOM_1_COUNT = Counter(
    subsystem=sub_system, name="bottom_1_total", documentation="todo", labelnames=["category", "scanner_id"]
)

BOTTOM_2_COUNT = Counter(
    subsystem=sub_system, name="bottom_2_total", documentation="todo", labelnames=["category", "scanner_id"]
)

GIMBAL_OUT_COUNT = Counter(
    subsystem=sub_system, name="gimbal_out_total", documentation="todo", labelnames=["category", "scanner_id"]
)

GIMBAL_OTHER_COUNT = Counter(
    subsystem=sub_system, name="gimbal_other_total", documentation="todo", labelnames=["category", "scanner_id"]
)

SPECULATIVE_SHOOTING = Counter(
    subsystem=sub_system,
    name="speculative_shooting",
    documentation="How often speculative shooting was used",
    labelnames=["scanner_id", "state"],
)

SCANNER_OVERHEAD_TIME_GAUGE = Gauge(
    subsystem=sub_system,
    name="scanner_overhead_time_ms",
    documentation="scanner average overhead (moving to target) time in milliseconds",
    labelnames=["scanner_id"],
)

SCANNER_FIRING_TIME_GAUGE = Gauge(
    subsystem=sub_system,
    name="scanner_firing_time_ms",
    documentation="scanner average firing time in milliseconds",
    labelnames=["scanner_id"],
)

SCANNER_IDLE_TIME_GAUGE = Gauge(
    subsystem=sub_system,
    name="scanner_idle_time_ms",
    documentation="scanner idle time in milliseconds",
    labelnames=["scanner_id"],
)

SCANNER_ERROR_COUNT = Counter(
    subsystem=sub_system,
    name="scanner_errors_total",
    documentation="scanner error counts",
    labelnames=["scanner_id", "error"],
)

LASER_FIRE_TOTAL_COUNT_GAUGE = Gauge(
    subsystem=sub_system,
    name="laser_total_fire_count",
    documentation="number of times laser fired",
    labelnames=["scanner_id"],
)

LASER_FIRE_TOTAL_TIME_GAUGE = Gauge(
    subsystem=sub_system,
    name="laser_total_fire_time_ms",
    documentation="total time that laser was lasing in ms",
    labelnames=["scanner_id"],
)

SCANNER_OVERHEAD_BREAKDOWN_GAUGE = Gauge(
    subsystem=sub_system,
    name="scanner_overhead_breakdown",
    documentation="Avg overhead time by section",
    labelnames=["section", "scanner_id"],
)

WHEEL_ENCODER_TICKS_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_ticks",
    documentation="Total ticks per wheel encoder",
    labelnames=["encoder"],
)

WHEEL_ENCODER_DELTA_TICKS_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_delta_ticks",
    documentation="delta ticks per same unit time per wheel encoder",
    labelnames=["encoder"],
)
WHEEL_ENCODER_AVG_VEL_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_avg_vel_mps",
    documentation="vel averaged per unit time per wheel encoder [m/s]",
    labelnames=["encoder"],
)
WHEEL_ENCODER_ERROR_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_error",
    documentation="Anomaly detection is actively detecting an error on this encoder.",
    labelnames=["encoder"],
)

SERVO_FAILURE_DETECTOR_SUCCESS_RATE = Gauge(
    subsystem=sub_system,
    name="servo_failure_detector_success_rate",
    documentation="Success rate of initial servo moves",
    labelnames=["scanner_id", "servo"],
)


async def increment_speculative_shoot(scanner_id: str, attempted: bool, used: bool) -> None:
    state = "not_available"
    if attempted and used:
        state = "used"
    elif attempted:
        state = "available_not_used"
    SPECULATIVE_SHOOTING.labels(scanner_id, state).inc()


class RollingWindowMetric:
    def __init__(self, window_len: int, gauge: Gauge, labels: Tuple[str, ...]):
        self._window_len = window_len
        self._window: Deque[int] = collections.deque(maxlen=window_len)
        self._sum = 0.0
        self._gauge = gauge.labels(*labels)

    def add(self, val: int) -> None:
        if len(self._window) >= self._window_len:
            self._sum -= self._window.popleft()
        self._window.append(val)
        self._sum += val
        self._gauge.set(self._sum / len(self._window))


class ScannerTimeMetric:
    def __init__(self, window_len: int, scanner_id: int):
        self.overhead_ts = 0
        self.fire_ts = 0
        self.end_fire_ts = 0
        labels = (str(scanner_id),)
        self.idle_window = RollingWindowMetric(window_len, SCANNER_IDLE_TIME_GAUGE, labels)
        self.overhead_window = RollingWindowMetric(window_len, SCANNER_OVERHEAD_TIME_GAUGE, labels)
        self.firing_window = RollingWindowMetric(window_len, SCANNER_FIRING_TIME_GAUGE, labels)

    def start_overhead(self) -> None:
        self.overhead_ts = maka_control_timestamp_ms()
        idle = self.overhead_ts - self.end_fire_ts if self.end_fire_ts != 0 else 0
        self.idle_window.add(idle)
        self.fire_ts = 0
        self.end_fire_ts = 0

    def abnormal_exit(self) -> None:
        if self.overhead_ts != 0:
            self.end_fire_ts = (
                maka_control_timestamp_ms()
            )  # capture end time so that next start_overhead will count time spent as idle
            if self.fire_ts == 0:  # didn't fire
                overhead = self.end_fire_ts - self.overhead_ts
                self.overhead_window.add(overhead)
            else:  # tried to fire
                fire = self.end_fire_ts - self.fire_ts
                self.firing_window.add(fire)
        self.overhead_ts = 0
        self.fire_ts = 0

    def start_fire(self) -> None:
        if self.fire_ts != 0:
            # on called while already on
            return
        self.fire_ts = maka_control_timestamp_ms()
        if self.overhead_ts != 0:  # if fire called outside of shooting algo this will be 0
            overhead = self.fire_ts - self.overhead_ts
            self.overhead_window.add(overhead)

    def end_fire(self) -> int:
        self.end_fire_ts = maka_control_timestamp_ms()
        if self.fire_ts == 0:
            return 0  # shouldn't happen
        fire = self.end_fire_ts - self.fire_ts
        self.firing_window.add(fire)
        self.fire_ts = 0
        return fire


class LaserFireInfo:
    def __init__(self, fire_time_ms: int, fire_count: int) -> None:
        self.fire_time_ms = fire_time_ms
        self.fire_count = fire_count
        self.last_sync_ms = maka_control_timestamp_ms()

    def add_fire_with_time(self, time_ms: int) -> None:
        self.fire_time_ms += time_ms
        self.fire_count += 1

    def fix(self, time_ms: int, count: int) -> None:
        self.fire_time_ms = time_ms
        self.fire_count = count

    def reset(self) -> None:
        self.fire_time_ms = 0
        self.fire_count = 0
        self.last_sync_ms = 0


class ScannerTimeMetrics:
    magic_reset_num = -987654321
    magic_not_fix_count = -987654321

    def __init__(self, window_len: int):
        self.scanners: Dict[int, ScannerTimeMetric] = {}
        self.window_len = window_len
        self.laser_fire_info: Dict[int, LaserFireInfo] = {}
        self.row = f"row{os.getenv('MAKA_ROW')}"

    def fire_time_key(self, id: int) -> str:
        return f"weeding_metrics/{self.row}/laser_fire_time_total/{id}"

    def fire_count_key(self, id: int) -> str:
        return f"weeding_metrics/{self.row}/laser_fire_count_total/{id}"

    async def init(self, scanner_ids: List[int]) -> None:
        for id in scanner_ids:
            self.scanners[id] = ScannerTimeMetric(self.window_len, id)
        self._queue: asyncio.Queue[Tuple[int, int, int]] = asyncio.Queue()

        self.redis = RedisClient()
        await self.redis.connect()
        for id in scanner_ids:
            try:
                t = int(await self.redis.get_def(self.fire_time_key(id), "0"))
                c = int(await self.redis.get_def(self.fire_count_key(id), "0"))
                LOG.debug(f"ScannerTimeMetrics: read laser fire stats, id={id}, time={t}, count={c}")
                self.laser_fire_info[id] = LaserFireInfo(t, c)
                LASER_FIRE_TOTAL_COUNT_GAUGE.labels(str(id)).set(c)
                LASER_FIRE_TOTAL_TIME_GAUGE.labels(str(id)).set(t)
            except Exception as ex:
                LOG.error(f"Error reading laser fire stats from redis: {ex}")
        self.update_task = asyncio.get_event_loop().create_task(self._track_data())

    async def _track_data(self) -> None:
        interval_cfg = get_global_config_subscriber().get_config_node(
            "common", "weeding_metrics/laser_time_persist_interval_sec"
        )
        with bot_stop_handler.scoped_bot_stop_blocker("laser_metrics_sync") as bot_stop:
            while not bot_stop.is_stopped():
                try:
                    id, time_ms, count = await self._queue.get()
                    if id not in self.laser_fire_info:
                        LOG.warning(f"Unknown laser id {id}")
                        continue
                    info = self.laser_fire_info[id]
                    if time_ms == self.magic_reset_num:
                        info.reset()
                        LASER_FIRE_TOTAL_COUNT_GAUGE.labels(str(id)).set(0)
                        LASER_FIRE_TOTAL_TIME_GAUGE.labels(str(id)).set(0)
                    elif count == self.magic_not_fix_count:
                        info.add_fire_with_time(time_ms)
                        LASER_FIRE_TOTAL_COUNT_GAUGE.labels(str(id)).inc()
                        LASER_FIRE_TOTAL_TIME_GAUGE.labels(str(id)).inc(time_ms)
                    else:
                        info.fix(time_ms, count)
                        LASER_FIRE_TOTAL_COUNT_GAUGE.labels(str(id)).set(info.fire_count)
                        LASER_FIRE_TOTAL_TIME_GAUGE.labels(str(id)).set(info.fire_time_ms)
                    now = maka_control_timestamp_ms()
                    if (now - info.last_sync_ms) > (interval_cfg.get_uint_value() * 1000):
                        await self.redis.set(self.fire_time_key(id), str(info.fire_time_ms))
                        await self.redis.set(self.fire_count_key(id), str(info.fire_count))
                        info.last_sync_ms = now
                except Exception as ex:
                    LOG.error(f"Unknonwn error in laser shoot time tracker: {ex}")
            try:
                for id, info in self.laser_fire_info.items():
                    await self.redis.set(self.fire_time_key(id), str(info.fire_time_ms))
                    await self.redis.set(self.fire_count_key(id), str(info.fire_count))
            except Exception as ex:
                LOG.error(f"Unknonwn error in final laser shoot time sync: {ex}")

    def start_overhead(self, scanner_id: int) -> None:
        if scanner_id not in self.scanners:
            return  # shouldn't happen
        self.scanners[scanner_id].start_overhead()

    def abnormal_exit(self, scanner_id: int) -> None:
        if scanner_id not in self.scanners:
            return  # shouldn't happen

        self.scanners[scanner_id].abnormal_exit()

    def start_fire(self, scanner_id: int) -> None:
        if scanner_id not in self.scanners:
            return  # shouldn't happen
        self.scanners[scanner_id].start_fire()

    def end_fire(self, scanner_id: int) -> None:
        if scanner_id not in self.scanners:
            return  # shouldn't happen
        fire_time = self.scanners[scanner_id].end_fire()
        self._queue.put_nowait((scanner_id, fire_time, self.magic_not_fix_count))

    def reset_metrics(self, scanner_id: int) -> bool:
        self._queue.put_nowait((scanner_id, self.magic_reset_num, self.magic_not_fix_count))
        return True

    def fix_metrics(self, scanner_id: int, fire_count: int, fire_time: int) -> bool:
        self._queue.put_nowait((scanner_id, fire_time, fire_count))
        return True


SCANNER_TIME_METRICS = ScannerTimeMetrics(1000)


class WindowedAvgMetricsProcessor:
    def __init__(self, gauge: Gauge, window_size: int = 1000, extra_label: str = "") -> None:
        self._metrics: DefaultDict[str, RollingWindowMetric] = keydefaultdict(
            lambda key: RollingWindowMetric(window_size, gauge, (key, extra_label))
        )
        self._running = False

    def add(self, times: List[Tuple[str, int]]) -> None:
        if not self._running:
            return
        for t in times:
            self._queue.put_nowait(t)

    async def start(self) -> Awaitable[None]:
        return asyncio.get_event_loop().create_task(self.process_loop())

    @contextmanager
    def time(self, name: str) -> Generator[None, None, None]:
        try:
            start = maka_control_timestamp_ms()
            yield
        finally:
            self._queue.put_nowait((name, maka_control_timestamp_ms() - start))

    async def process_loop(self) -> None:
        self._queue: asyncio.Queue[Tuple[str, int]] = asyncio.Queue()
        self._running = True
        while True:
            data = await self._queue.get()
            self._metrics[data[0]].add(data[1])


SCANNER_OVERHEAD_PROCESSOR_DICT_LOCK = asyncio.Lock()
SCANNER_OVERHEAD_PROCESSOR_DICT: Dict[str, WindowedAvgMetricsProcessor] = {}


async def get_scanner_overhead_processor(scanner_id: str) -> WindowedAvgMetricsProcessor:
    async with SCANNER_OVERHEAD_PROCESSOR_DICT_LOCK:
        if scanner_id not in SCANNER_OVERHEAD_PROCESSOR_DICT:
            SCANNER_OVERHEAD_PROCESSOR_DICT[scanner_id] = WindowedAvgMetricsProcessor(
                SCANNER_OVERHEAD_BREAKDOWN_GAUGE, extra_label=scanner_id
            )
            await SCANNER_OVERHEAD_PROCESSOR_DICT[scanner_id].start()
        return SCANNER_OVERHEAD_PROCESSOR_DICT[scanner_id]


class TimedMetric:
    def __init__(self, processor: WindowedAvgMetricsProcessor) -> None:
        self._name = ""
        self._start_time = 0
        self._completed: List[Tuple[str, int]] = []
        self._processor = processor

    def __del__(self) -> None:
        if self._completed:
            # we may miss a single in flight metric, but at least we get majority
            self._processor.add(self._completed)

    def start(self, name: str) -> None:
        now = maka_control_timestamp_ms()
        if self._name:
            self._completed.append((self._name, now - self._start_time))
        self._start_time = now
        self._name = name

    def stop(self) -> None:
        self.start("")
        self._start_time = 0

    def __call__(self, name: str) -> None:
        self.start(name)

    def __enter__(self) -> None:
        return

    def __exit__(
        self,
        exc_type: Optional[Type[BaseException]],
        exc_value: Optional[BaseException],
        traceback: Optional[TracebackType],
    ) -> None:
        self.stop()

    def end(self, abnormal: bool = False) -> None:
        if abnormal and self._name:
            self._name = f"abnormal_{self._name}"
        self.stop()
        self._processor.add(self._completed)
        self._completed = []
