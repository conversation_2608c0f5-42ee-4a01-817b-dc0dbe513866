import asyncio
from typing import Dict, List, Optional

import numpy as np

from config.client.cpp.config_client_python import get_global_config_subscriber
from lib.common.time import maka_control_timestamp_ms


class LoadEntry:
    def __init__(self, duration_ms: int) -> None:
        self.timestamp_ms = maka_control_timestamp_ms()
        self.duration_ms = duration_ms


class LoadEstimator:
    def __init__(self) -> None:
        self._load_entries: List[LoadEntry] = []
        self._lock = asyncio.Lock()
        self._subscriber = get_global_config_subscriber()

    async def _unsafe_prune(self, timestamp_ms: int) -> None:
        pop_count = 0
        for load_entry in self._load_entries:
            if timestamp_ms - load_entry.timestamp_ms > (
                self._subscriber.get_config_node("aimbot", "load/lookback_ms").get_uint_value() * 5
            ):
                pop_count += 1
            else:
                break
        self._load_entries = self._load_entries[pop_count:]

    async def add_down_time(self, duration_ms: int) -> None:
        entry = LoadEntry(duration_ms)
        async with self._lock:
            await self._unsafe_prune(entry.timestamp_ms)
            self._load_entries.append(entry)

    async def get_estimated_load(self, look_back_ms: int, timestamp_ms: Optional[int] = None) -> float:
        timestamp_ms = maka_control_timestamp_ms() if timestamp_ms is None else timestamp_ms
        async with self._lock:
            idle_time_ms: int = 0
            for load_entry in reversed(self._load_entries):
                if timestamp_ms - load_entry.timestamp_ms > look_back_ms:
                    break
                idle_time_ms += load_entry.duration_ms
        return 1 - (idle_time_ms / look_back_ms)


class AimbotLoadEstimator:
    def __init__(self) -> None:
        self._lock = asyncio.Lock()
        self._scanner_load_estimators: Dict[str, LoadEstimator] = {}
        self._subscriber = get_global_config_subscriber()

    async def get_load_estimator(self, scanner_id: str) -> LoadEstimator:
        async with self._lock:
            if scanner_id not in self._scanner_load_estimators:
                self._scanner_load_estimators[scanner_id] = LoadEstimator()
            return self._scanner_load_estimators[scanner_id]

    async def get_estimated_load(self, look_back_ms: int) -> float:
        timestamp_ms = maka_control_timestamp_ms()
        async with self._lock:
            estimates = [
                await load_estimator.get_estimated_load(look_back_ms, timestamp_ms=timestamp_ms)
                for load_estimator in self._scanner_load_estimators.values()
            ]
        return float(
            np.percentile(estimates, self._subscriber.get_config_node("aimbot", "load/percentile").get_uint_value())
        )
