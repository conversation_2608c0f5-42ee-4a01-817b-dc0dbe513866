import asyncio
import concurrent
from typing import Any, Awaitable, List, Optional, Set, Tuple

import core.controls.exterminator.controllers.targeting.p2p as p2p_targeting
import lib.common.logging
from config.client.cpp.config_client_python import get_global_config_subscriber
from core.controls.exterminator.controllers.aimbot.utils.helper import get_adjusted_tilt_velocity_with_pos_from_vel
from core.controls.exterminator.controllers.targeting.p2p import P2PContextNotFoundException
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.time.time import maka_control_timestamp_ms
from shooting.pybind.shooting_python import LaserOntimeTracker
from trajectory.pybind.trajectory_python import TrackedItemCentroid, Trajectory

LOG = lib.common.logging.get_logger(__name__)

tasks_executor = concurrent.futures.ThreadPoolExecutor(thread_name_prefix="tasks_", max_workers=32)


async def get_velocity_mph_nofx_task(device: NoFXBoardDevice, last_timestamp_ms: Optional[int],) -> Tuple[int, float]:
    rotary_vel = (
        await device.get_cur_avg_vel()
        if last_timestamp_ms is None
        else await device.get_next_avg_vel(last_timestamp_ms)
    )

    vel_mph = rotary_vel.vel_mm * -56.8182 / 25.4
    return rotary_vel.msec, vel_mph


async def get_next_velocity_nofx_task(
    scanner: AimbotScanner, device: NoFXBoardDevice, last_timestamp_ms: Optional[int], z_height_mm: Optional[float],
) -> Tuple[int, float]:
    ts, vel_mph = await get_velocity_mph_nofx_task(device, last_timestamp_ms)
    pan_pos = scanner.gimbal.pan.goal_position
    tilt_pos = scanner.gimbal.tilt.goal_position
    vel_tilt_ticks_ms = await asyncio.get_event_loop().run_in_executor(
        tasks_executor,
        lambda: get_adjusted_tilt_velocity_with_pos_from_vel(
            scanner, round(pan_pos), round(tilt_pos), vel_mph, z_height_mm
        ),
    )
    return ts, vel_tilt_ticks_ms


async def get_next_velocity_nofx_with_wait_task(
    scanner: AimbotScanner, device: NoFXBoardDevice, wait_ms: int, z_height_mm: Optional[float],
) -> Tuple[int, float]:
    await asyncio.sleep(wait_ms / 1000)
    _, vel_mph = await get_velocity_mph_nofx_task(device, None)
    pan_pos = scanner.gimbal.pan.goal_position
    tilt_pos = scanner.gimbal.tilt.goal_position
    vel_tilt_ticks_ms = await asyncio.get_event_loop().run_in_executor(
        tasks_executor,
        lambda: get_adjusted_tilt_velocity_with_pos_from_vel(
            scanner, round(pan_pos), round(tilt_pos), vel_mph, z_height_mm
        ),
    )
    return maka_control_timestamp_ms(), vel_tilt_ticks_ms


async def get_next_velocity_nofx_with_pos_task(
    scanner: AimbotScanner,
    device: NoFXBoardDevice,
    last_timestamp_ms: Optional[int],
    z_height_mm: Optional[float],
    pan_pos: int,
    tilt_pos: int,
) -> Tuple[int, float]:
    ts, vel_mph = await get_velocity_mph_nofx_task(device, last_timestamp_ms)
    vel_tilt_ticks_ms = await asyncio.get_event_loop().run_in_executor(
        tasks_executor,
        lambda: get_adjusted_tilt_velocity_with_pos_from_vel(
            scanner, round(pan_pos), round(tilt_pos), vel_mph, z_height_mm
        ),
    )
    return ts, vel_tilt_ticks_ms


async def get_next_image_and_p2p_task(
    scanner: AimbotScanner,
    trajectory: Trajectory,
    centroid: TrackedItemCentroid,
    last_move_timestamp_ms: int,
    forward: bool,
    fallback_centroid: Optional[TrackedItemCentroid],
    set_context_only: bool = False,
) -> p2p_targeting.P2PMatch:
    assert centroid.get_has_perspective()
    subscriber = get_global_config_subscriber()
    p2p_capture_cnf = subscriber.get_config_node("aimbot", "p2p_capture")

    try:
        return await p2p_targeting.get_p2p_prediction_async(
            scanner,
            last_move_timestamp_ms + p2p_capture_cnf.get_node("delay_ms").get_uint_value(),
            centroid,
            subscriber.get_config_node("aimbot", "image_interval_sleep_ms").get_uint_value(),
            forward,
            fallback_centroid,
            set_context_only,
            trajectory.id(),
        )
    except P2PContextNotFoundException:
        trajectory.remove_perspective_from_centroid(centroid.get_timestamp_ms(), centroid.get_tracker_id())
        raise


ltt_executor = concurrent.futures.ThreadPoolExecutor(thread_name_prefix="ltt_", max_workers=32)


def __ltt_start_shooting(
    ltt: LaserOntimeTracker, scanner: AimbotScanner, additional_time: int, trajectory: Trajectory
) -> None:
    ltt.start_shooting(scanner.scanner_wrapper, additional_time, trajectory)


def __ltt_stop_shooting(ltt: LaserOntimeTracker, scanner: AimbotScanner, trajectory: Trajectory) -> None:
    ltt.stop_shooting(scanner.scanner_wrapper, trajectory)


def __ltt_await_change(ltt: LaserOntimeTracker, scanner: AimbotScanner, trajectory: Trajectory) -> None:
    ltt.await_change(scanner.scanner_wrapper, trajectory)


async def _ltt_start_shooting(
    ltt: LaserOntimeTracker, scanner: AimbotScanner, additional_time: int, trajectory: Trajectory
) -> None:
    await asyncio.get_event_loop().run_in_executor(
        ltt_executor, lambda: __ltt_start_shooting(ltt, scanner, additional_time, trajectory)
    )


async def _ltt_stop_shooting(ltt: LaserOntimeTracker, scanner: AimbotScanner, trajectory: Trajectory) -> None:
    await asyncio.get_event_loop().run_in_executor(ltt_executor, lambda: __ltt_stop_shooting(ltt, scanner, trajectory))


async def _ltt_await(
    ltt: LaserOntimeTracker, scanner: AimbotScanner, trajectory: Trajectory, stop_event: asyncio.Event
) -> Awaitable[None]:
    await_task = asyncio.get_event_loop().run_in_executor(
        ltt_executor, lambda: __ltt_await_change(ltt, scanner, trajectory)
    )
    stop_task = asyncio.get_event_loop().create_task(stop_event.wait())
    all_tasks: Tuple[Set[asyncio.Future[Any]], Set[asyncio.Future[Any]]] = await asyncio.wait(
        [await_task, stop_task], loop=asyncio.get_event_loop(), return_when=asyncio.FIRST_COMPLETED
    )
    done: Set[asyncio.Future[Any]] = all_tasks[0]
    if stop_task not in done:
        stop_task.cancel()
    try:
        await stop_task
    except asyncio.CancelledError:
        pass
    return await_task


async def ltt_speculative_shoot(scanner: AimbotScanner, additional_time: int, trajectory: Trajectory) -> bool:
    ltt = LaserOntimeTracker()

    def _inner() -> bool:
        return ltt.speculative_shoot(scanner.scanner_wrapper, additional_time, trajectory)

    return await asyncio.get_event_loop().run_in_executor(ltt_executor, _inner)


async def laser_on_duration_task_safe(
    scanner: AimbotScanner,
    resume_signal: asyncio.Event,
    pause_signal: asyncio.Event,
    exit_signal: asyncio.Event,
    trajectory: Trajectory,
    additional_time: int,
) -> bool:
    ltt = LaserOntimeTracker()
    await_fut: Optional[Awaitable[None]] = None
    started = False
    try:

        await scanner.laser.async_on()
        started = True
        await asyncio.shield(_ltt_start_shooting(ltt, scanner, additional_time, trajectory))
        await_fut = await _ltt_await(ltt, scanner, trajectory, pause_signal)
    except Exception as ex:
        LOG.error(f"Scanner: {scanner.numeric_id} in error laser on task {ex}")
    except asyncio.CancelledError:
        LOG.error(f"Scanner: {scanner.numeric_id} laser on task / subtask was cancelled")
    finally:
        tasks: List[Awaitable[Any]] = []
        off = asyncio.shield(scanner.laser.async_off())  # Turn off laser no matter what
        tasks.append(off)
        stop: Optional[Awaitable[None]] = None
        if started:
            stop = asyncio.shield(_ltt_stop_shooting(ltt, scanner, trajectory))
            tasks.append(stop)
        if await_fut is not None:
            tasks.append(await_fut)
        await asyncio.gather(*tasks, return_exceptions=True)
        return await off


async def scanner_await_target_change_task(scanner: AimbotScanner) -> None:
    await asyncio.get_event_loop().run_in_executor(tasks_executor, scanner.scanner_wrapper.await_target_change)
