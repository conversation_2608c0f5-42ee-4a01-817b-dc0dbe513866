from typing import Op<PERSON>, <PERSON><PERSON>, cast

import lib.common.logging
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.geometric.cpp.geometric_python import Geometric<PERSON>am, GeometricScanner
from lib.common.geometric.geometric_space import get_geometric_space
from lib.common.math import subtract_tuples
from lib.common.types import GimbalPosition

LOG = lib.common.logging.get_logger(__name__)


def get_nofx_from_scanner(scanner: AimbotScanner) -> "NoFXBoardDevice":
    gimbal = scanner.gimbal
    nofx = gimbal.hacky_nofx_device
    assert nofx is not None
    return nofx


def get_gimbal_position_from_predict_geometric(
    scanner: AimbotScanner, pcam_id: str, pcoords: Tuple[float, float], z_height_mm: float
) -> GimbalPosition:
    geometric_space = get_geometric_space()
    predict: GeometricCam = geometric_space.get_device(GeometricCam, pcam_id)
    geo_scanner: GeometricScanner = scanner.geometric_scanner
    real_world_coords = predict.get_abs_position_from_px(pcoords)
    return GimbalPosition(*geo_scanner.get_servo_position_from_ground((*real_world_coords[:2], z_height_mm)))


def get_gimbal_position_from_abs_pos_geometric(
    scanner: AimbotScanner, pos: Tuple[float, float, float]
) -> GimbalPosition:
    assert scanner.target is not None
    geo_scanner: GeometricScanner = scanner.geometric_scanner
    raw_pos = GimbalPosition(*geo_scanner.get_servo_position_from_ground(pos))
    delta_servo = geo_scanner.get_servo_delta_for_crosshair(scanner.target, (raw_pos.pan, raw_pos.tilt))
    final_pos = GimbalPosition(raw_pos.pan + delta_servo[0], raw_pos.tilt + delta_servo[1])
    return final_pos


def get_delta_gimbal_position_from_delta_target(
    scanner: AimbotScanner, delta_tcoords: Tuple[float, float]
) -> GimbalPosition:
    assert scanner.gimbal is not None
    servos = scanner.gimbal.get_goal_position()
    geo_scanner = scanner.geometric_scanner
    delta_px = tuple(map(round, delta_tcoords))
    dservos = geo_scanner.get_servo_delta_for_delta_px(cast(Tuple[int, int], delta_px), servos)
    return GimbalPosition(*dservos)


def get_delta_gimbal_position_from_delta_target_with_dt(
    scanner: AimbotScanner, delta_tcoords: Tuple[float, float], dt_ms: int, velocity_ms: Tuple[float, float]
) -> GimbalPosition:
    assert scanner.gimbal is not None
    servos = scanner.gimbal.get_goal_position()
    servos = GimbalPosition(round(servos.pan + (dt_ms * velocity_ms[0])), round(servos.tilt + (dt_ms * velocity_ms[1])))
    geo_scanner = scanner.geometric_scanner
    delta_px = cast(Tuple[int, int], tuple(map(round, delta_tcoords)))
    dservos = geo_scanner.get_servo_delta_for_delta_px(delta_px, servos)
    return GimbalPosition(*dservos)


def get_delta_gimbal_pos_from_img_with_dt(
    scanner: AimbotScanner,
    delta_tcoords: Tuple[float, float],
    pos_img: Tuple[int, int],
    pos_now: Tuple[int, int],
    dt_ms: int,
    velocity_ms: Tuple[float, float],
) -> GimbalPosition:
    geo_scanner = scanner.geometric_scanner
    delta_px = cast(Tuple[int, int], tuple(map(round, delta_tcoords)))
    pos_ticks_img = geo_scanner.get_servo_position_for_delta_px(delta_px, pos_img)
    pos_ticks_now = (
        round(pos_ticks_img[0] + (dt_ms * velocity_ms[0])),
        round(round(pos_ticks_img[1] + (dt_ms * velocity_ms[1]))),
    )
    return GimbalPosition(pos_ticks_now[0] - pos_now[0], pos_ticks_now[1] - pos_now[1])


def get_target_delta(scanner: AimbotScanner, tcoords: Tuple[float, float]) -> Tuple[float, float]:
    assert scanner.target is not None, f"{scanner.id} has a none target"
    return subtract_tuples(scanner.target, tcoords)


def get_adjusted_tilt_velocity_with_pos_from_vel(
    scanner: AimbotScanner, pan_pos: int, tilt_pos: int, velocity_mph: float, z_height_mm: Optional[float]
) -> float:
    geo_scanner = scanner.geometric_scanner
    servo_angle_ticks = geo_scanner.get_adjusted_tilt_velocity_with_pos_from_vel(
        pan_pos, tilt_pos, velocity_mph, z_height_mm
    )
    return servo_angle_ticks  # This is in ticks per ms


def get_mm_offset_from_ticks(
    scanner: AimbotScanner, delta_pan: int, delta_tilt: int, height: Optional[float] = None
) -> Tuple[float, float]:
    cur_servos = (int(scanner.gimbal.pan.goal_position), int(scanner.gimbal.tilt.goal_position))
    delta_servos = (
        int(scanner.gimbal.pan.goal_position + delta_pan),
        int(scanner.gimbal.tilt.goal_position + delta_tilt),
    )
    if height is None:
        cur_pos = scanner.geometric_scanner.get_abs_position_from_servo(cur_servos)
        delta_pos = scanner.geometric_scanner.get_abs_position_from_servo(delta_servos)
    else:
        cur_pos = scanner.geometric_scanner.get_abs_position_from_servo_with_z(cur_servos, height)
        delta_pos = scanner.geometric_scanner.get_abs_position_from_servo_with_z(delta_servos, height)
    pan_offset_mm = delta_pos[0] - cur_pos[0]
    tilt_offset_mm = delta_pos[1] - cur_pos[1]
    return pan_offset_mm, tilt_offset_mm
