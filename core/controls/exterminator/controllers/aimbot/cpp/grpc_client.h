#pragma once

#include "generated/core/controls/exterminator/controllers/aimbot/process/proto/aimbot.grpc.pb.h"

#include <config/tree/cpp/config_tree.hpp>
#include <grpcpp/grpcpp.h>
#include <memory>
#include <mutex>
#include <string>

namespace aimbot {

std::string get_aimbot_address();

class AimbotClient {
protected:
  std::string addr_;
  std::shared_ptr<grpc::Channel> channel_;
  std::shared_ptr<AimbotService::Stub> stub_;
  uint32_t max_backoff_ms_;
  double backoff_multiplier_;
  std::mutex channel_setup_mutex_;

public:
  AimbotClient(const std::string &addr = get_aimbot_address(), uint32_t max_backoff_ms = 30000,
               double backoff_multiplier = 1.5);

  bool await_connection(uint64_t timeout_ms = 0);
  void reset();
  std::string get_addr() const;
  void ping();
  AimbotState get_aimbot_state();
  std::tuple<std::map<std::string, std::map<std::string, std::vector<double>>>, double> get_bedtop_height_profile();
  std::string get_target_camera_serial_number(const std::string &name);

private:
  grpc::Status exec_grpc(std::function<grpc::Status()> func);
  void setup_grpc(bool reconnect_if_down = true);
  std::shared_ptr<AimbotService::Stub> get_grpc_stub(bool reconnect_if_down = true);
  void reset_stub();
};

} // namespace aimbot
