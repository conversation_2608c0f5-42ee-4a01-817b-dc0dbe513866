#include "grpc_client.h"

#include <fmt/format.h>
#include <functional>
#include <set>
#include <spdlog/spdlog.h>

#include "lib/common/cpp/exceptions.h"

namespace aimbot {

std::string get_aimbot_address() {
  std::string role = "invalid";
  char *role_ptr = std::getenv("MAKA_ROLE");
  if (role_ptr != NULL) {
    role = role_ptr;
  }

  if (role == "row-secondary") {
    std::string row = "invalid";
    char *row_ptr = std::getenv("MAKA_ROW");
    if (row_ptr != NULL) {
      row = row_ptr;
    }

    if (row == "1") {
      return "**********:6942";
    } else if (row == "2") {
      return "**********:6942";
    } else if (row == "3") {
      return "**********:6942";
    }
  }

  return "127.0.0.1:6942";
}
AimbotClient::AimbotClient(const std::string &addr, uint32_t max_backoff_ms, double backoff_multiplier)
    : addr_(addr), channel_(nullptr), max_backoff_ms_(max_backoff_ms), backoff_multiplier_(backoff_multiplier) {}

void AimbotClient::setup_grpc(bool reconnect_if_down) {
  // Not Thread Safe
  if (this->channel_ != nullptr && this->channel_->GetState(true) != GRPC_CHANNEL_READY) {
    spdlog::warn("Resetting Channel due to lost connection, state: {}", this->channel_->GetState(true));
    this->reset_stub();
  }
  if (this->channel_ == nullptr) {
    if (!reconnect_if_down) {
      return;
    }
    this->channel_ = grpc::CreateChannel(this->addr_, grpc::InsecureChannelCredentials());
  }
  if (this->stub_ == nullptr) {
    this->stub_ = std::make_shared<AimbotService::Stub>(this->channel_);
  }
}

std::shared_ptr<AimbotService::Stub> AimbotClient::get_grpc_stub(bool reconnect_if_down) {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  this->setup_grpc(reconnect_if_down);
  return this->stub_;
}

void AimbotClient::reset() {
  std::lock_guard<std::mutex> lock(this->channel_setup_mutex_);
  return this->reset_stub();
}

void AimbotClient::reset_stub() {
  // Not Thread Safe
  this->stub_ = nullptr;
  this->channel_ = nullptr;
}

grpc::Status AimbotClient::exec_grpc(std::function<grpc::Status()> func) {
  try {
    return func();
  } catch (const std::exception &ex) { // TODO Better Exception Handling?
    this->reset_stub();
    throw;
  }
}

bool AimbotClient::await_connection(uint64_t timeout_ms) {
  auto start = std::chrono::steady_clock::now();
  std::chrono::milliseconds chrono_timeout_ms{timeout_ms};
  uint32_t backoff_ms = 1000;
  std::chrono::duration<double> duration_s;
  while (true) {
    duration_s = std::chrono::steady_clock::now() - start;
    if (timeout_ms != 0 && duration_s > chrono_timeout_ms) {
      return false;
    }
    try {
      this->ping();
      return true;
    } catch (const std::exception &ex) {
    }
    backoff_ms = (uint32_t)(backoff_ms * this->backoff_multiplier_);
    backoff_ms = backoff_ms <= this->max_backoff_ms_ ? backoff_ms : this->max_backoff_ms_;

    if (timeout_ms != 0 &&
        (duration_s > chrono_timeout_ms || std::chrono::milliseconds(backoff_ms) > (chrono_timeout_ms - duration_s))) {
      return false;
    }
    spdlog::warn("Awaiting Connection to Aimbot with Backoff: {} ms", backoff_ms);
    std::this_thread::sleep_for(std::chrono::milliseconds(backoff_ms));
  }
}

void AimbotClient::ping() {
  grpc::ClientContext context;
  PingRequest req;
  PongReply resp;
  req.set_x(42);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&AimbotService::Stub::Ping, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(fmt::format("Invalid Ping Status Code: {}", status.error_code()));
  }

  if (resp.x() != 42) {
    throw maka_error(fmt::format("Invalid Pong Value: {}, Expected: {}", resp.x(), 42));
  }
}

std::string AimbotClient::get_addr() const { return addr_; }

AimbotState AimbotClient::get_aimbot_state() {
  grpc::ClientContext context;
  Empty req;
  AimbotState resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&AimbotService::Stub::GetAimbotState, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(
        fmt::format("Invalid GetAimbotState Status Code {}: {}", status.error_code(), status.error_message()), false);
  }
  return resp;
}

std::tuple<std::map<std::string, std::map<std::string, std::vector<double>>>, double>
AimbotClient::get_bedtop_height_profile() {
  grpc::ClientContext context;
  Empty req;
  TrackerBedtopHeightProfile resp;
  auto stub = this->get_grpc_stub();
  grpc::Status status =
      this->exec_grpc(std::bind(&AimbotService::Stub::GetBedtopHeightProfile, stub, &context, req, &resp));
  std::map<std::string, std::map<std::string, std::vector<double>>> profiles;

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(
        fmt::format("Invalid GetBedtopHeightProfile Status Code {}: {}", status.error_code(), status.error_message()));
  }

  for (auto &profile : resp.profiles()) {
    std::vector<double> weed_heights;
    std::vector<double> crop_heights;

    for (auto &height : profile.weed_height_columns()) {
      weed_heights.push_back(height);
    }

    for (auto &height : profile.crop_height_columns()) {
      crop_heights.push_back(height);
    }

    profiles[profile.pcam_id()]["weed"] = weed_heights;
    profiles[profile.pcam_id()]["crop"] = crop_heights;
  }

  return std::make_tuple(profiles, (double)resp.bbh_offset_mm());
}
std::string AimbotClient::get_target_camera_serial_number(const std::string &name) {
  grpc::ClientContext context;
  GetTargetCamSNRequest req;
  GetTargetCamSNResponse resp;
  req.set_camera_id(name);
  auto stub = this->get_grpc_stub();
  grpc::Status status = this->exec_grpc(std::bind(&AimbotService::Stub::GetTargetCamSN, stub, &context, req, &resp));

  if (status.error_code() != grpc::StatusCode::OK) {
    throw maka_error(fmt::format("Invalid response. Status Code: {}", status.error_code()));
  }

  return resp.serial_number();
}

} // namespace aimbot
