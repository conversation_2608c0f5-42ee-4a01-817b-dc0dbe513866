from lib.common.error import MakaException


class AimbotException(MakaException):
    pass


class OutOfRangeAimbotException(MakaException):
    def __init__(self, msg: str, range_type: str):
        super().__init__(msg)
        self.range_type = range_type


class TargetChangeException(MakaException):
    def __init__(self, msg: str, current_id: int):
        super().__init__(msg)
        self.current = current_id


class WeedNotFoundAimbotException(MakaException):
    def __init__(self, msg: str, failure_type: str):
        super().__init__(msg)
        self.failure_type = failure_type


class P2PTargetDistanceExceeded(MakaException):
    pass


class P2PContextFailure(MakaException):
    pass


class P2PTimeoutError(MakaException):
    pass
