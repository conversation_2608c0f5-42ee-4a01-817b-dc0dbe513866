# import gil_load
# import sys
# gil_load.init()

import asyncio
import faulthandler
import json
import logging
import re
import sys
from argparse import ArgumentParser
from typing import Any, Awaitable, Dict, List, Optional, Set, Tuple, cast

import grpc
from aioredis import Redis
from prometheus_client import start_http_server

import generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2 as aimbot_pb
import generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2_grpc as aimbot_grpc
import generated.weed_tracking.proto.weed_tracking_pb2 as weed_tracking_pb
import lib.common.logging
from almanac.pybind.almanac_python import Almanac, Discriminator, Modelinator
from config.client.cpp.config_client_python import (
    ConfigSubscriber,
    ConfigTree,
    get_command_computer_config_prefix,
    get_computer_config_prefix,
    get_global_config_subscriber,
)
from core.controls.exterminator.controllers.aimbot.aimbot import AI<PERSON><PERSON>_<PERSON>G<PERSON><PERSON>H<PERSON>, AIMBOT_NONE_ALG<PERSON><PERSON>H<PERSON>, aimbot
from core.controls.exterminator.controllers.aimbot.metrics import AI<PERSON>OT_ALGO, SCANNER_TIME_METRICS
from core.controls.exterminator.controllers.aimbot.utils.helper import get_nofx_from_scanner
from core.controls.exterminator.controllers.aimbot.utils.load_estimator import AimbotLoadEstimator
from core.controls.exterminator.model.aimbot_scanner import AimbotScanner
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_gimbal import PulczarBoardGimbal
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_laser import PulczarBoardLaser
from core.controls.exterminator.nodes.pulczar_board.pulczar_board_servo import PulczarBoardServo
from core.cv.retina.auto_focus import auto_focus, single_auto_focus
from core.fs.filesystem import FileSystem
from core.model.node.type import NodeType
from core.model.path import DeviceExpression, DevicePath, Instance, ServoInstance
from cv.runtime.client import CVRuntimeClient
from ingest.pybind.ingest_python import Ingest, IngestProducer
from lib.common.bot.stop_handler import bot_stop_handler
from lib.common.crosshair.auto_crosshair_calibration import AutoCrossHairCalManager
from lib.common.devices.boards.pulczar.pulczar_board_device import PulczarBoardDevice
from lib.common.devices.device_configuration import get_devices_for_service, get_skip_list
from lib.common.devices.registry import DeviceRegistry, DeviceRegistryException
from lib.common.error import MakaException
from lib.common.generation import is_bud, is_reaper, is_slayer
from lib.common.geometric.cpp.geometric_python import (
    GeometricCam,
    GlobalHeightEstimatorCollection,
    get_global_height_estimator,
)
from lib.common.geometric.geometric_calibration import (
    get_height_to_lights_mm_offset,
    get_predict_cam_calibration,
    get_predict_cam_pos,
)
from lib.common.geometric.geometric_space import get_geometric_space
from lib.common.module_server.client import ModuleServerClient
from lib.common.profile import gil_load_profiling
from lib.common.protocol.feed.inproc import InprocFeed
from lib.common.redis_client import RedisClient
from lib.common.robot_definition.pybind.robot_definition_python import RobotDefinition
from lib.common.sentry_reporter import sentry_reporter
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import maka_control_timestamp_ms
from metrics.pybind.metrics_python import DailyTimezone, MetricsAggregator
from scanner.pybind.scanner_python import ScannerMapper, ScannerWrapper, ScannerWrapperOwner
from scheduling.pybind.scheduling_python import GlobalScheduler
from targeting.pybind.targeting_python import TargetingModeWatcher
from thinning.pybind.thinning_python import ThinningController
from tools.laser.json_to_laser import burn_individual_img, burn_individual_img_safe
from weed_tracking.cpp.weed_tracking_python import (
    AimbotTrackerOwner,
    AimbotWeedTracker,
    CropLineDetector,
    WeedingDiagnostics,
)
from weed_tracking_libs.arbiter.pybind.arbiter_python import Arbiter, ArbiterTrigger
from weed_tracking_libs.crop_based_weed_targeting.pybind.crop_based_weed_targeting_python import CropBasedWeedTargeter
from weed_tracking_libs.decision_line.pybind.decision_line_python import init_mm_space_decision_line
from weed_tracking_libs.deduplicator.pybind.deduplicator_python import DeDuplicator
from weed_tracking_libs.plant_captcha.pybind.plant_captcha_python import PlantCaptcha
from weed_tracking_libs.protector.pybind.protector_python import Protector
from weed_tracking_libs.trajectory_monitor.pybind.trajectory_monitor_python import TrajectoryMonitor
from weed_tracking_service.pybind.weed_tracking_service_python import WeedTrackingService

LOG = lib.common.logging.get_logger(__name__)


class AimbotServerException(MakaException):
    pass


AUTO_CROSSHAIR_CALIBRATION_ALL_ID = None


class AutoFocusTask:
    def __init__(self, pulczar_board: PulczarBoardDevice, client: CVRuntimeClient, scanner_id: int) -> None:
        self._progress = 0
        self._progress_lock = asyncio.Lock()
        self._task = asyncio.get_event_loop().create_task(
            single_auto_focus(pulczar_board, client, scanner_id, self._update_progress)
        )

    async def _update_progress(self, new_progress: int) -> None:
        async with self._progress_lock:
            self._progress = new_progress

    async def get_progress(self) -> float:
        async with self._progress_lock:
            return self._progress

    @property
    def task(self) -> "asyncio.Task[Any]":
        return self._task


class AutoFocusTaskManager:
    def __init__(self) -> None:
        self._manual_af_tasks: Dict[int, AutoFocusTask] = {}
        self._tasks_lock = asyncio.Lock()

    async def start(self, pulczar_board: PulczarBoardDevice, scanner_id: int, client: CVRuntimeClient) -> None:
        async with self._tasks_lock:
            self._manual_af_tasks[scanner_id] = AutoFocusTask(pulczar_board, client, scanner_id)

    async def stop(self, scanner_id: int) -> None:
        async with self._tasks_lock:
            af_task = self._manual_af_tasks.get(scanner_id)
            if af_task is not None and not af_task.task.done():
                af_task.task.cancel()

    async def get_progress(self, scanner_id: int) -> Optional[float]:
        async with self._tasks_lock:
            af_task = self._manual_af_tasks.get(scanner_id)
            if af_task is None or af_task.task.done():
                return None

            return await af_task.get_progress()


class AimbotActuationTaskManager:
    def __init__(self,) -> None:
        self._lock = asyncio.Lock()
        self._active_task: Optional[asyncio.Task[None]] = None

    async def is_task_running(self) -> bool:
        async with self._lock:
            return self._active_task is not None

    async def cancel_task(self) -> None:
        active_task: Optional[asyncio.Task[None]] = None
        async with self._lock:
            active_task = self._active_task
        if active_task is not None:
            active_task.cancel()
            # TODO Should we await here? Since it could lead to upstream Deadline exceeded maybe not

    async def _perform_task(self, func: Awaitable[None]) -> None:
        try:
            await func
        finally:
            async with self._lock:
                self._active_task = None

    async def perform_task(self, func: Awaitable[None]) -> None:
        async with self._lock:
            self._active_task = asyncio.get_event_loop().create_task(self._perform_task(func))


class Aimbot:
    def __init__(
        self, config_subscriber: ConfigSubscriber, event_loop: asyncio.AbstractEventLoop, no_weed_tracking: bool = False
    ) -> None:
        self._feed = InprocFeed()
        self._fs = FileSystem("/dev/null", "/dev/null", "/dev/null", "/dev/null", "/dev/null", i_dont_care=True)
        self._algo: str = "rotary_and_p2p"
        self.armed: bool = False

        self._registry = DeviceRegistry()
        self._scanner_map: Dict[int, AimbotScanner] = {}

        self._aimbot_lock = asyncio.Lock()
        self._aimbot_tasks: List[asyncio.Task[Any]] = []
        self._aimbot_running = False
        self._aimbot_actuation_task_manager = AimbotActuationTaskManager()
        self._af_tasks: List[asyncio.Task[Any]] = []
        self._event_loop = event_loop

        self._weed_trackers: List[AimbotWeedTracker] = []
        self._load_estimator = AimbotLoadEstimator()
        self._scheduler: Optional[GlobalScheduler] = None
        self._targeter: Optional[CropBasedWeedTargeter] = None
        self._ingest: Optional[Ingest] = None
        self._arbiter: Optional[Arbiter] = None
        self._thinning_controller: Optional[ThinningController] = None

        self._no_weed_tracking = no_weed_tracking
        self._config_subscriber = config_subscriber

        self._max_pos_mm_y = 0.0
        self._booted_lock = asyncio.Lock()
        self._booted = False
        self._ready = False
        self._tmw = TargetingModeWatcher()
        self._redis_client: Optional[Redis] = None
        self._cv_clients: List[CVRuntimeClient] = []
        self._af_task_manager = AutoFocusTaskManager()

        self._module_server_client = ModuleServerClient()
        self._module_id = 0
        self._is_leader = True

    async def _get_redis(self) -> Redis:
        if self._redis_client is None:
            self._redis_client = await RedisClient.build()
        return self._redis_client

    @property
    def actuation_task_manager(self) -> AimbotActuationTaskManager:
        return self._aimbot_actuation_task_manager

    @property
    async def booted(self) -> bool:
        async with self._booted_lock:
            return self._booted

    def set_ready(self) -> None:
        self._ready = True

    @property
    def ready(self) -> bool:
        return self._ready

    @property
    def registry(self) -> DeviceRegistry:
        return self._registry

    @property
    def scanner_map(self) -> Dict[int, AimbotScanner]:
        return self._scanner_map

    @property
    def load_estimator(self) -> AimbotLoadEstimator:
        return self._load_estimator

    @property
    def algo(self) -> str:
        return self._algo

    @property
    def config_subscriber(self) -> ConfigSubscriber:
        return self._config_subscriber

    @property
    def scheduler(self) -> Optional[GlobalScheduler]:
        return self._scheduler

    @property
    def max_pos_mm_y(self) -> float:
        return self._max_pos_mm_y

    @property
    def tmw(self) -> TargetingModeWatcher:
        return self._tmw

    @property
    def no_weed_tracking(self) -> bool:
        return self._no_weed_tracking

    @property
    def af_task_manager(self) -> AutoFocusTaskManager:
        return self._af_task_manager

    @property
    def weed_trackers(self) -> List[AimbotWeedTracker]:
        return self._weed_trackers

    async def _start_laser_test(self, task: aimbot_pb.LaserTestActuationTask) -> None:
        LOG.info(f"Starting Laser Test: {task}")
        if task.scanner_id != 0:
            scanners = [self.scanner_map[task.scanner_id]]
        else:
            scanners = [scanner for scanner in self.scanner_map.values()]
        duration_ms = task.duration_ms

        async def __laser_test(scanner: AimbotScanner) -> None:
            start_time_ms = maka_control_timestamp_ms()
            try:
                while maka_control_timestamp_ms() - start_time_ms < duration_ms:
                    await scanner.laser.on_with_safety()
                    await asyncio.sleep(0.2)
            finally:
                await scanner.laser.off_with_safety()

        cmds = [__laser_test(scanner) for scanner in scanners]

        errs = await asyncio.gather(*cmds, return_exceptions=True,)
        for err in errs:
            if err is None:
                continue
            LOG.warning(f"Error occured during Laser Test: {err}")

    async def _start_image_draw(self, task: aimbot_pb.ImageDrawActuationTask) -> None:
        LOG.info(f"Starting Image Draw: {task}")
        if task.scanner_id != 0:
            scanners = [self.scanner_map[task.scanner_id].board]
        else:
            scanners = [scanner.board for scanner in self.scanner_map.values()]
        speed = task.speed_mmps if task.speed_mmps > 1.0 else 50.0
        height = get_global_height_estimator().get_ground_position_z_mm()
        cmds = [
            burn_individual_img_safe(
                scanner, self.scanner_map[scanner.scanner_id].geometric_scanner, height, speed, "",
            )
            for scanner in scanners
        ]

        errs = await asyncio.gather(*cmds, return_exceptions=True,)
        for err in errs:
            if err is None:
                continue
            LOG.warning(f"Error occured burning img: {err}")

    async def _start_range_draw(self, task: aimbot_pb.RangeDrawActuationTask) -> None:
        LOG.info(f"Starting Range Draw: {task}")
        if task.scanner_id != 0:
            scanners = [self.scanner_map[task.scanner_id]]
        else:
            scanners = [scanner for scanner in self.scanner_map.values()]
        duration_s = int(task.duration_s)

        cmds = [scanner.trace_boundaries(duration_s) for scanner in scanners]

        errs = await asyncio.gather(*cmds, return_exceptions=True,)
        for err in errs:
            if err is None:
                continue
            LOG.warning(f"Error occured during Range Draw: {err}")

    async def actuation_task_builder(self, task: aimbot_pb.ActuationTaskRequest) -> Awaitable[None]:
        task_type = task.WhichOneof("task")

        if task_type == "image_draw":
            return self._start_image_draw(task.image_draw)
        elif task_type == "laser_test":
            return self._start_laser_test(task.laser_test)
        elif task_type == "range_draw":
            return self._start_range_draw(task.range_draw)
        else:
            raise Exception("Invalid Task Type")

    async def _auto_focus(self, board_id: int) -> None:
        pulczar_board = await self.registry.get_device(PulczarBoardDevice, f"pulczar{board_id}")
        cv_client = self._scanner_map[board_id].cv_runtime_client

        async def pause_callback() -> bool:
            return await self._af_task_manager.get_progress(board_id) is not None

        await auto_focus(pulczar_board, cv_client, board_id, pause_callback)

    async def pre_boot(self) -> bool:
        if is_reaper():
            while self._module_id == 0 and not bot_stop_handler.stopped:
                await asyncio.sleep(5)
                LOG.info("Fetching module identity...")
                identity = await self._module_server_client.GetModuleIdentity()
                LOG.info(f"Received Module Identity: {identity}")
                self._module_id = identity.id
            RobotDefinition.get().initialize_for_module(self._module_id)
            self._is_leader = RobotDefinition.get().get_local_row_definition().get_aimbot_leader() == self._module_id
            if not self._is_leader:
                LOG.info("Not the leader, skipping aimbot boot")
                return False
        return True

    async def boot(self) -> None:
        if not await self.pre_boot():
            async with self._booted_lock:
                self._booted = True
            return

        device_conf = self._config_subscriber.get_config_node("aimbot", "device_overrides")
        common_conf = self._config_subscriber.get_config_node("common", "")

        self._cv_clients = [CVRuntimeClient()]
        disabled_pulczars: Set[str] = set()
        disabled_predicts: Set[str] = set()
        cumulative_offsets_mm: List[float] = []
        pid_to_cv_address: Dict[str, str] = {}
        tid_to_cv_address: Dict[str, str] = {}
        sid_to_address_and_ports: Dict[int, Tuple[str, int, int]] = {}

        if is_reaper():
            row_definition = RobotDefinition.get().get_local_row_definition()
            disabled_pulczars = row_definition.get_disabled_pulczars()  # pulczar<id>
            disabled_predicts = row_definition.get_disabled_predicts()  # predict<id>
            cumulative_offsets_mm = row_definition.get_cumulative_offsets_mm()
            pid_to_cv_address = row_definition.get_pid_to_cv_address()  # predict<id> -> cv_address
            tid_to_cv_address = row_definition.get_tid_to_cv_address()  # target<id> -> cv_address
            sid_to_board_address = row_definition.get_sid_to_board_address()  # id -> board_address
            sid_to_port = row_definition.get_sid_to_port()
            sid_to_boot_loader_port = row_definition.get_sid_to_boot_loader_port()
            for sid, board_address in sid_to_board_address.items():
                sid_to_address_and_ports[sid] = (board_address, sid_to_port[sid], sid_to_boot_loader_port[sid])

            # for now just look at the predict cv clients since the targets are on guaranteed to be the same cv client
            unique_cv_addresses = [
                cv_address for pid, cv_address in pid_to_cv_address.items() if pid not in disabled_predicts
            ]
            self._cv_clients = [CVRuntimeClient(hostname=cv_hostname) for cv_hostname in unique_cv_addresses]

        skip_list = get_skip_list(device_conf)
        skip_list.update(disabled_pulczars)
        await self._registry.boot_devices_from_iterable(
            get_devices_for_service(
                "aimbot", device_conf, common_conf, sid_to_address_and_ports=sid_to_address_and_ports
            ),
            skip_list=skip_list,
        )
        LOG.info("Registry booted")

        nofx_board_optional = None
        await asyncio.get_event_loop().run_in_executor(
            None, get_geometric_space, cumulative_offsets_mm
        )  # Initialize Lazy Loaded Space

        scanners = await self._boot_all_scanners(tid_to_cv_address)
        self._scanner_map = dict([(scanner.numeric_id, scanner) for scanner in scanners])
        LOG.info(f"Scanners: {self._scanner_map.keys()}")
        max_gimbal_pos_list: List[float] = []
        for scanner in self._scanner_map.values():
            await self._load_estimator.get_load_estimator(scanner.id)
            if nofx_board_optional is None:
                nofx_board_optional = get_nofx_from_scanner(scanner)
            geo_scanner = scanner.geometric_scanner
            pan_middle = round(sum(scanner.gimbal.pan.limits()) / 2)
            pos_mm_min = geo_scanner.get_abs_position_from_servo((pan_middle, scanner.gimbal.tilt.limits()[1]))
            pos_mm_max = geo_scanner.get_abs_position_from_servo((pan_middle, scanner.gimbal.tilt.limits()[0]))
            LOG.info(f"{scanner.id}: Y position min = {pos_mm_min[1]}, Y position max = {pos_mm_max[1]}")
            max_gimbal_pos_list.append(pos_mm_max[1])
        await SCANNER_TIME_METRICS.init(list(self._scanner_map.keys()))
        if self._no_weed_tracking:
            async with self._booted_lock:
                self._booted = True
            return
        self._max_pos_mm_y = (
            max(max_gimbal_pos_list)
            + self._config_subscriber.get_config_node("aimbot", "max_pos_y_offset_mm").get_float_value()
        )
        max_number_of_weeds = self._config_subscriber.get_config_node("aimbot", "max_number_of_weeds").get_uint_value()
        assert nofx_board_optional is not None
        width, height = get_predict_cam_calibration().image_size

        pids: List[str]
        if is_bud() or is_slayer():
            pids = [f"predict{i}" for i in range(1, 5)]
        elif is_reaper():
            pids = list(pid_to_cv_address.keys())
            pids.sort()  # sort to ensure ascending order
        num_lanes = self._config_subscriber.get_config_node("aimbot", "height_estimation/num_lanes").get_uint_value()
        await asyncio.get_event_loop().run_in_executor(
            None, lambda: GlobalHeightEstimatorCollection.instance().configure(pids, num_lanes, width)
        )

        for id in self._scanner_map:
            self._af_tasks.append(asyncio.get_event_loop().create_task(self._auto_focus(id)))

        # todo range num modules
        for i, pid in enumerate(pids, start=1):
            if pid in disabled_predicts:
                LOG.info(f"Skipping disabled predict {pid}")
                continue
            await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self._weed_trackers.append(
                    AimbotWeedTracker(
                        pid,
                        i,
                        width,
                        height,
                        log_ticks=False,  # TODO [Raven] This may need to be true at some point if we remove controls
                        geo_cam=get_geometric_space().get_device(GeometricCam, pid),
                        max_pos_mm_y=self._max_pos_mm_y,
                        score_images=True,
                        max_number_of_weeds=max_number_of_weeds,
                        cv_address=pid_to_cv_address.get(pid, "localhost") + ":15053",
                    )
                ),
            )
        AimbotTrackerOwner().set(self._weed_trackers)
        await asyncio.get_event_loop().run_in_executor(None, self._boot_ingest)
        height_estimation_enabled = self.config_subscriber.get_config_node(
            "aimbot", "height_estimation/enabled"
        ).get_bool_value()
        GlobalHeightEstimatorCollection.instance().toggle_estimation(height_estimation_enabled)
        if not height_estimation_enabled:
            GlobalHeightEstimatorCollection.instance().force_update_height(
                self._config_subscriber.get_config_node("aimbot", "tilt_mirror_height_mm").get_float_value()
                + self._config_subscriber.get_config_node("aimbot", "tilt_mirror_z_offset_mm").get_float_value()
            )
        async with self._booted_lock:
            self._booted = True
        self._wt_service.booted()

    def _boot_ingest(self) -> None:
        LOG.info("Starting ingest")
        weeding_diagnostics = WeedingDiagnostics.get()

        MetricsAggregator.get().start()
        self._scheduler = GlobalScheduler(weeding_diagnostics)
        assert self._scheduler is not None
        self._targeter = CropBasedWeedTargeter()
        assert self._targeter is not None
        cld = CropLineDetector()
        cld.start()
        captcha = PlantCaptcha()

        arbiter_clients = [
            Protector.get(),
            cld,
            self._scheduler,
            self._targeter,
            captcha,
        ]

        all_predict_cams = list(get_geometric_space().get_devices_by_type(GeometricCam))
        left_y = all_predict_cams[0].get_abs_position_mm()[1]
        right_y = all_predict_cams[-1].get_abs_position_mm()[1]
        avg_y = (left_y + right_y) / 2
        init_mm_space_decision_line(avg_y)

        debug_arbiter_monitor = self.config_subscriber.get_config_node(
            "aimbot", "debug_arbiter_monitor"
        ).get_bool_value()
        self._arbiter = Arbiter(cast(List[ArbiterTrigger], self._weed_trackers), arbiter_clients, debug_arbiter_monitor)
        assert self._arbiter is not None
        LOG.info("Arbiter booted")

        ingest_clients = [
            DeDuplicator.get(),
            ScannerMapper(),
            weeding_diagnostics,
            MetricsAggregator.get(),
            self._arbiter,
        ]
        if self.config_subscriber.get_config_node("aimbot", "debug_trajectory_monitor").get_bool_value():
            LOG.info("Adding trajectory monitor")
            ingest_clients.append(TrajectoryMonitor())
        self._ingest = Ingest(cast(List[IngestProducer], self._weed_trackers), ingest_clients)
        self._thinning_controller = ThinningController(self._scheduler, self._targeter, self._arbiter)
        self._wt_service = WeedTrackingService(cld, MetricsAggregator.get(), captcha,)
        LOG.info("ingest booted")

    def _boot_scanner(
        self, scanner_id: int, redis_client: Redis, dtz: DailyTimezone, cv_hostname: str
    ) -> Optional[AimbotScanner]:
        LOG.info(f"Booting Scanner {scanner_id}")
        try:
            pan = PulczarBoardServo(
                device_path=DevicePath(
                    [
                        DeviceExpression(NodeType.SCANNER, Instance(scanner_id)),
                        DeviceExpression(NodeType.GIMBAL),
                        DeviceExpression(NodeType.SERVO, ServoInstance.PAN),
                    ]
                ),
                feed=self._feed,
                filesystem=self._fs,
                device_id=f"pulczar{scanner_id}",
                device_registry=self._registry,
            )
            tilt = PulczarBoardServo(
                device_path=DevicePath(
                    [
                        DeviceExpression(NodeType.SCANNER, Instance(scanner_id)),
                        DeviceExpression(NodeType.GIMBAL),
                        DeviceExpression(NodeType.SERVO, ServoInstance.TILT),
                    ]
                ),
                feed=self._feed,
                filesystem=self._fs,
                device_id=f"pulczar{scanner_id}",
                device_registry=self._registry,
            )
            gimbal = PulczarBoardGimbal(
                device_path=DevicePath(
                    [DeviceExpression(NodeType.SCANNER, Instance(scanner_id)), DeviceExpression(NodeType.GIMBAL)]
                ),
                feed=self._feed,
                filesystem=self._fs,
                device_id=f"pulczar{scanner_id}",
                device_registry=self._registry,
                tilt_mirror_height_in=33.5,
                pan_override=pan,
                tilt_override=tilt,
            )
            laser = PulczarBoardLaser(
                device_path=DevicePath(
                    [DeviceExpression(NodeType.SCANNER, Instance(scanner_id)), DeviceExpression(NodeType.LASER)]
                ),
                feed=self._feed,
                filesystem=self._fs,
                device_id=f"pulczar{scanner_id}",
                device_registry=self._registry,
            )
            scanner = AimbotScanner(
                laser=laser,
                gimbal=gimbal,
                numeric_id=scanner_id,
                redis_client=redis_client,
                dtz=dtz,
                cv_hostname=cv_hostname,
            )
            return scanner
        except DeviceRegistryException as ex:
            LOG.warning(f"{ex}")
        return None

    async def _boot_all_scanners(self, tid_to_cv_hostname: Dict[str, str]) -> List[AimbotScanner]:
        scanners_registry_list = await self._registry.get_device_list_of_type(PulczarBoardDevice)
        redis_client = await self._get_redis()
        dtz = DailyTimezone(owner=False)

        async def boot_wrapper(id: int) -> Optional[AimbotScanner]:
            cv_hostname = tid_to_cv_hostname.get(f"target{id}", "localhost")
            return await asyncio.get_event_loop().run_in_executor(
                None, lambda: self._boot_scanner(id, redis_client, dtz, cv_hostname)
            )

        scanners: List[Optional[AimbotScanner]] = [await boot_wrapper(s.scanner_id) for s in scanners_registry_list]
        non_optional_scanners = cast(List[AimbotScanner], list(filter(lambda scanner: scanner is not None, scanners)))
        await asyncio.gather(*[scanner.laser.async_arm() for scanner in non_optional_scanners])
        self.armed = True
        self._tmw.set_armed(self.armed)
        scanner_wrappers: List[ScannerWrapper] = list(map(lambda s: s.scanner_wrapper, non_optional_scanners))
        ScannerWrapperOwner().set(scanner_wrappers)
        return non_optional_scanners

    def is_aimbot_running(self) -> bool:
        return self._tmw.targeting_enabled()

    def is_aimbot_tasks_running(self) -> bool:
        return self._aimbot_running

    async def start_aimbot(self, algo: str) -> None:
        assert self._scheduler is not None
        async with self._aimbot_lock:
            if self.is_aimbot_tasks_running():
                if algo == self._algo:
                    return
            if algo not in AIMBOT_ALGORITHMS:
                raise AimbotServerException(f"Invalid aimbot algorithm {algo}")
            # This is only needed for non weed tracking algos, and can take some time so only do here
            if algo in AIMBOT_NONE_ALGORITHMS:
                geometric_space = await asyncio.get_event_loop().run_in_executor(None, get_geometric_space)
                for cam in geometric_space.get_devices_by_type(GeometricCam):

                    def _set_min_max() -> None:
                        GlobalHeightEstimatorCollection.instance().set_min_max_x_mm_for_cam(cam)

                    await asyncio.get_event_loop().run_in_executor(None, _set_min_max)
            AIMBOT_ALGO.info({"algo": algo})
            self._algo = algo
            self._aimbot_tasks = [
                asyncio.get_event_loop().create_task(
                    aimbot(
                        scanner,
                        algo,
                        load_estimator=await self._load_estimator.get_load_estimator(scanner.id),
                        scheduler=self._scheduler,
                    )
                )
                for scanner in self._scanner_map.values()
                if scanner.enabled
            ]
            self._aimbot_running = True

    async def _stop_aimbot(self) -> None:
        for task in self._aimbot_tasks:
            task.cancel()
        self._aimbot_tasks = []
        self._aimbot_running = False
        for scanner in self._scanner_map.values():
            scanner.set_running(False)

    async def stop_aimbot(self) -> None:
        async with self._aimbot_lock:
            await self._stop_aimbot()

    async def wait_for_cv_runtime(self) -> None:
        for cv_client in self._cv_clients:
            await asyncio.get_event_loop().run_in_executor(None, cv_client.wait_for_cv)
        LOG.info("Successful Connection to CV Runtime")

    async def sync_target_state_cv(self) -> None:
        update = asyncio.Event()
        update.set()

        def _tmw_changed() -> None:
            update.set()

        self._tmw.register_callback(_tmw_changed)
        while not bot_stop_handler.stopped:
            try:
                await asyncio.wait_for(update.wait(), 1)
                update.clear()
                for cv_client in self._cv_clients:
                    await cv_client.set_targeting_state(self._tmw.weeding_enabled(), self._tmw.thinning_enabled())
            except asyncio.TimeoutError:
                pass
            except Exception as ex:
                LOG.warn(f"Unknown error setting targeting mode: {ex}")


class AimbotGRPCServicer(aimbot_grpc.AimbotServiceServicer):
    def __init__(self, aimbot: Aimbot, algo: ConfigTree):
        self._aimbot = aimbot
        self._algo = algo
        self._auto_crosshair_cal_manager = AutoCrossHairCalManager()

    async def _get_armed(self) -> bool:
        async def _inner(scanner: AimbotScanner) -> bool:
            if not scanner.enabled:
                return True
            armed = await scanner.laser.is_armed()
            return armed

        status = all(await asyncio.gather(*[_inner(scanner) for scanner in self._aimbot.scanner_map.values()]))
        armed = status and self._aimbot.armed
        return armed

    async def Ping(self, request: aimbot_pb.PingRequest, context: grpc.ServicerContext) -> aimbot_pb.PongReply:
        LOG.debug(f"Got A Ping: {request.x}")
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.PongReply(x=request.x)

    async def GetBooted(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.BootedReply:
        return aimbot_pb.BootedReply(booted=await self._aimbot.booted)

    async def ArmLasers(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.Empty:
        LOG.debug("Arming Lasers")
        self._aimbot.armed = True  # TODO consider what happens in failure cases
        await asyncio.gather(*[scanner.laser.async_arm() for scanner in self._aimbot.scanner_map.values()])
        self._aimbot._tmw.set_armed(self._aimbot.armed)
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def DisarmLasers(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.Empty:
        LOG.debug("Disarming Lasers")
        await asyncio.gather(*[scanner.laser.async_disarm() for scanner in self._aimbot.scanner_map.values()])
        self._aimbot.armed = False  # TODO consider what happens in failure cases
        self._aimbot._tmw.set_armed(self._aimbot.armed)
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def SetTargetingState(
        self, request: aimbot_pb.TargetingState, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.info("Setting Targeting State")
        await self._aimbot.actuation_task_manager.cancel_task()
        self._aimbot.tmw.set(request.weeding_enabled, request.thinning_enabled)
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def GetAimbotState(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.AimbotState:
        LOG.debug("Getting Aimbot State")
        if self._aimbot.scheduler is not None and not self._aimbot.scheduler.is_moving():
            safety_state = aimbot_pb.SafetyOverrideVelocityStop
        else:
            safety_state = aimbot_pb.SafetyOverrideNone
        weeding = self._aimbot.tmw.weeding_enabled()
        thinning = self._aimbot.tmw.thinning_enabled()
        state = aimbot_pb.AimbotState(
            algorithm=self._aimbot.algo,
            running=(weeding or thinning),
            armed=await self._get_armed(),
            targeting_state=aimbot_pb.TargetingState(weeding_enabled=weeding, thinning_enabled=thinning),
            ready=self._aimbot.ready,
            safety_override_state=safety_state,
            actuation_tasks_running=await self._aimbot.actuation_task_manager.is_task_running(),
        )
        context.set_code(grpc.StatusCode.OK)
        return state

    async def LensSet(self, request: aimbot_pb.LensSetRequest, context: grpc.ServicerContext) -> aimbot_pb.LensSetReply:
        LOG.debug(f"Setting Lens On Scanner {request.scanner_id}, Value: {request.value}")
        if (
            self._aimbot.config_subscriber.get_config_node("aimbot", "scanners").get_node(
                f"scanner{request.scanner_id}"
            )
            is None
        ):
            self._aimbot.config_subscriber.get_client().add_to_list(
                f"{get_computer_config_prefix()}/aimbot/scanners", f"scanner{request.scanner_id}"
            )
        self._aimbot.config_subscriber.get_client().set_int_value(
            f"{get_computer_config_prefix()}/aimbot/scanners/scanner{request.scanner_id}/focus", request.value
        )
        pulczar_board = await self._aimbot.registry.get_device(PulczarBoardDevice, f"pulczar{request.scanner_id}")
        await pulczar_board.lens_set(request.value)
        return aimbot_pb.LensSetReply()

    async def LensGetAll(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.LensGetAllReply:
        LOG.debug("Getting All Lenses")
        lens_statuses: List[aimbot_pb.LensGetReply] = []
        pulczar_boards = [x.board for x in self._aimbot.scanner_map.values() if x.enabled and not x.error]
        lens_values = await asyncio.gather(*[x.lens_get_cached() for x in pulczar_boards])
        for i, pulczar_board in enumerate(pulczar_boards):
            progress = await self._aimbot.af_task_manager.get_progress(pulczar_board.scanner_id)
            lens_statuses.append(
                aimbot_pb.LensGetReply(
                    value=lens_values[i],
                    max_value=int(pulczar_board.max_lens_value),
                    min_value=int(pulczar_board.min_lens_value),
                    manual_autofocusing=progress is not None,
                    manual_autofocus_percent=0 if progress is None else progress,
                    scanner_id=pulczar_board.scanner_id,
                )
            )
        return aimbot_pb.LensGetAllReply(lens_status=lens_statuses)

    async def LensGet(self, request: aimbot_pb.LensGetRequest, context: grpc.ServicerContext) -> aimbot_pb.LensGetReply:
        LOG.debug(f"Getting Lens On Scanner {request.scanner_id}")
        pulczar_board = await self._aimbot.registry.get_device(PulczarBoardDevice, f"pulczar{request.scanner_id}")
        lens_value = await pulczar_board.lens_get_cached()
        progress = await self._aimbot.af_task_manager.get_progress(pulczar_board.scanner_id)
        return aimbot_pb.LensGetReply(
            value=lens_value,
            max_value=int(pulczar_board.max_lens_value),
            min_value=int(pulczar_board.min_lens_value),
            manual_autofocusing=progress is not None,
            manual_autofocus_percent=0 if progress is None else progress,
            scanner_id=pulczar_board.scanner_id,
        )

    async def LensAutoFocus(
        self, request: aimbot_pb.LensAutoFocusRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.LensAutoFocusReply:
        LOG.debug(f"Auto Focusing Scanner {request.scanner_id}")
        assert not self._aimbot.is_aimbot_running()
        pulczar_board = await self._aimbot.registry.get_device(PulczarBoardDevice, f"pulczar{request.scanner_id}")
        assert pulczar_board is not None
        pulczar_board._assert_operational()
        cv_client = self._aimbot.scanner_map[request.scanner_id].cv_runtime_client
        await self._aimbot.af_task_manager.start(pulczar_board, request.scanner_id, cv_client)
        return aimbot_pb.LensAutoFocusReply()

    async def StopLensAutoFocus(
        self, request: aimbot_pb.LensAutoFocusRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.debug(f"Stopping Auto Focus on Scanner {request.scanner_id}")
        await self._aimbot.af_task_manager.stop(request.scanner_id)
        return aimbot_pb.Empty()

    async def LaserArm(
        self, request: aimbot_pb.LaserArmRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.LaserArmReply:
        LOG.debug(f"Laser Arm On Scanner {request.scanner_id}, status: {request.armed}")
        # TODO disable those calls for safety once rid of controls
        scanners: List[AimbotScanner] = []
        if request.scanner_id == 0:
            scanners = list(self._aimbot.scanner_map.values())
        elif request.scanner_id in self._aimbot.scanner_map:
            scanners.append(self._aimbot.scanner_map[request.scanner_id])
        else:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
        if request.armed:
            await asyncio.gather(*[scanner.laser.async_arm() for scanner in scanners])
        else:
            await asyncio.gather(*[scanner.laser.async_disarm() for scanner in scanners])
        return aimbot_pb.LaserArmReply()

    async def LaserSet(
        self, request: aimbot_pb.LaserSetRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.LaserSetReply:
        LOG.debug(f"Laser Set On Scanner {request.scanner_id}, status: {request.on}")
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
            return aimbot_pb.LaserSetReply()
        scanner = self._aimbot.scanner_map[request.scanner_id]
        if request.on:
            await scanner.laser.async_on()
        else:
            await scanner.laser.async_off()
        return aimbot_pb.LaserSetReply()

    async def ServoGoTo(
        self, request: aimbot_pb.ServoGoToRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.ServoGoToReply:
        LOG.debug(f"Servo Go To On Scanner {request.scanner_id} Servo {request.servo_type}")
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
            return aimbot_pb.ServoGoToReply()
        scanner = self._aimbot.scanner_map[request.scanner_id]
        if request.servo_type == aimbot_pb.ServoGoToRequest.ServoType.PAN:
            await scanner.gimbal.pan.async_set_position_unsafe(request.position)
        else:
            await scanner.gimbal.tilt.async_set_position_unsafe(request.position)
        return aimbot_pb.ServoGoToReply()

    async def ServoGetPosVel(
        self, request: aimbot_pb.ServoGetPosVelRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.ServoGetPosVelReply:
        LOG.debug(f"Servo Get Pos Vel On Scanner {request.scanner_id}")
        if request.scanner_id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
            return aimbot_pb.ServoGetPosVelReply(pan_position=-1000000000, tilt_position=-1000000000)
        scanner = self._aimbot.scanner_map[request.scanner_id]
        position = await scanner.gimbal.async_get_position()
        return aimbot_pb.ServoGetPosVelReply(pan_position=position.pan, tilt_position=position.tilt)

    async def ServoGetLimits(
        self, request: aimbot_pb.ServoGetLimitsRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.ServoGetLimitsReply:
        LOG.debug(f"Servo Get Limits On Scanner {request.scanner_id}")
        if request.scanner_id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
            return aimbot_pb.ServoGetLimitsReply(pan_min=0, pan_max=0, tilt_min=0, tilt_max=0,)
        scanner = self._aimbot.scanner_map[request.scanner_id]
        return aimbot_pb.ServoGetLimitsReply(
            pan_min=scanner.gimbal.pan.min,
            pan_max=scanner.gimbal.pan.max,
            tilt_min=scanner.gimbal.tilt.min,
            tilt_max=scanner.gimbal.tilt.max,
        )

    async def TuningParamsUpdate(
        self, request: aimbot_pb.TuningParamsUpdateRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.TuningParamsUpdateReply:
        LOG.debug("Tuning params has been deprecated use config service.")
        return aimbot_pb.TuningParamsUpdateReply()

    async def TuningParamsGet(
        self, request: aimbot_pb.TuningParamsGetRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.TuningParamsGetReply:
        LOG.debug("Tuning params has been deprecated use config service.")
        reply = aimbot_pb.TuningParamsGetReply()
        return reply

    async def GetLoadEstimate(
        self, request: aimbot_pb.GetLoadEstimateRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.GetLoadEstimateReply:
        LOG.debug("Getting Load Estimate")
        load_node = self._aimbot.config_subscriber.get_config_node("aimbot", "load")
        if self._aimbot.is_aimbot_running():
            return aimbot_pb.GetLoadEstimateReply(
                current_load=await self._aimbot.load_estimator.get_estimated_load(
                    load_node.get_node("lookback_ms").get_uint_value()
                ),
                target_load=load_node.get_node("target").get_float_value(),
            )
        return aimbot_pb.GetLoadEstimateReply(
            current_load=0, target_load=load_node.get_node("target").get_float_value(),
        )

    async def GetDiagnostic(
        self, request: aimbot_pb.GetDiagnosticRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.GetDiagnosticReply:
        diagnostics = await self._aimbot.registry.get_diagnostic()
        json_diagnostics: Dict[str, Any] = {}
        for device_id, diag in diagnostics.items():
            json_diagnostics[device_id] = diag.to_json()
        return aimbot_pb.GetDiagnosticReply(diagnostic=json.dumps(json_diagnostics))

    async def ResetDevices(
        self, request: aimbot_pb.ResetDevicesRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.ResetDevicesReply:
        LOG.debug(f"Resetting Device {request.device_id}")
        assert not self._aimbot.is_aimbot_running()
        devices = await self._aimbot.registry.get_device_list()
        for device in devices:
            if device.device_id == str(request.device_id) or str(request.device_id) == "":
                await device.reset()
        return aimbot_pb.ResetDevicesReply()

    async def ResetScanner(
        self, request: aimbot_pb.ResetScannerRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.ResetScannerReply:
        if request.scanner_id not in self._aimbot.scanner_map:
            err_msg = f"Reset Scanner: Scanner {request.scanner_id} does not appear to exist."
            LOG.warning(err_msg)
            raise Exception(f"Reset Scanner: Scanner {request.scanner_id} does not appear to exist.")
        scanner = self._aimbot.scanner_map[request.scanner_id]
        LOG.info(f"Resetting Scanner {request.scanner_id}")
        await scanner.request_reset(request.metrics_only)
        return aimbot_pb.ResetScannerReply()

    async def StartAutoCalibrateCrosshair(
        self, request: aimbot_pb.ScannerDescriptor, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        assert not self._aimbot.is_aimbot_running(), "Please stop weeding before starting crosshair calibration"
        assert await self._get_armed(), "Please arm lasers before starting crosshair calibration"

        if not await self._auto_crosshair_cal_manager.start([request.id]):
            raise RuntimeError("Auto crosshair calibration already in progress")
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def StartAutoCalibrateAllCrosshairs(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        assert not self._aimbot.is_aimbot_running(), "Please stop weeding before starting crosshair calibration"
        assert await self._get_armed(), "Please arm lasers before starting crosshair calibration"

        if not await self._auto_crosshair_cal_manager.start([]):
            raise RuntimeError("Auto crosshair calibration already in progress")
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def StopAutoCalibrate(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.Empty:
        LOG.debug("Stop Auto Calibrate All Crosshairs")
        await self._auto_crosshair_cal_manager.stop()
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.Empty()

    async def GetAutoCalibrationProgress(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.AutoXHairCalibrationProgress:
        LOG.debug("Get Auto Calibrate Crosshair progress")
        context.set_code(grpc.StatusCode.OK)
        return aimbot_pb.AutoXHairCalibrationProgress(
            in_progress=self._auto_crosshair_cal_manager.in_progress(),
            progress=self._auto_crosshair_cal_manager.progress,
        )

    async def SetCrosshairPosition(
        self, request: aimbot_pb.ScannerTargetPosition, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.debug(f"Set Crosshair Position for Scanner: {request.scanner_descriptor.id} at ({request.x}, {request.y})")
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_descriptor.id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_descriptor.id} does not appear to exist.")
            return aimbot_pb.Empty()
        scanner = self._aimbot.scanner_map[request.scanner_descriptor.id]
        await scanner.set_target((request.x, request.y))
        return aimbot_pb.Empty()

    async def GetScannerStatus(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.ScannerStatusReply:
        LOG.debug("Get status for scanners")
        resp = aimbot_pb.ScannerStatusReply(
            x_hair_progress=aimbot_pb.AutoXHairCalibrationProgress(
                in_progress=self._auto_crosshair_cal_manager.in_progress(),
                progress=self._auto_crosshair_cal_manager.progress,
            )
        )
        for scanner in self._aimbot.scanner_map.values():
            calibrating = self._auto_crosshair_cal_manager.in_progress_for_scanner(scanner.numeric_id)
            cal_failed = self._auto_crosshair_cal_manager.scanner_failed(scanner.numeric_id)
            target = scanner.target
            if target is None:
                target = (0, 0)
            target_traj = scanner.scanner_wrapper.get_target()
            resp.states.append(
                aimbot_pb.ScannerState(
                    scanner_descriptor=aimbot_pb.ScannerDescriptor(id=scanner.numeric_id),
                    laser_state=aimbot_pb.LaserState(
                        enabled=scanner.enabled,
                        firing=scanner.laser.firing(),
                        error=scanner.laser.error(),
                        power=scanner.laser.powered,
                        delta_temp=scanner.laser.latest_delta_temp(),
                        current=scanner.laser.latest_current(),
                        arced=scanner.laser.is_arced(),
                        power_level=scanner.power,
                    ),
                    crosshair_state=aimbot_pb.CrosshairState(
                        x=target[0], y=target[1], calibrating=calibrating, calibration_failed=cal_failed
                    ),
                    scanner_error=scanner.error,
                    error_code=scanner.status.code.name,
                    error_message=scanner.status.msg,
                    target_trajectory_id=target_traj.id() if target_traj is not None else 0,
                    pan_failure=scanner.servo_failure_detectors[0].is_failed(),
                    tilt_failure=scanner.servo_failure_detectors[1].is_failed(),
                )
            )
        return resp

    async def MoveScanner(
        self, request: aimbot_pb.ScannerTargetPosition, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.debug(f"Move Scanner: {request.scanner_descriptor.id} to ({request.x}, {request.y})")
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_descriptor.id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_descriptor.id} does not appear to exist.")
            return aimbot_pb.Empty()
        scanner = self._aimbot.scanner_map[request.scanner_descriptor.id]
        result = await asyncio.get_event_loop().run_in_executor(
            None, lambda: scanner.goto_target_coords((request.x, request.y), quiesce=False)
        )  # TODO make sure these old APIs still make sense
        assert result == 0
        return aimbot_pb.Empty()

    async def StartActuationTask(
        self, request: aimbot_pb.ActuationTaskRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        assert not self._aimbot.is_aimbot_running()
        func = await self._aimbot.actuation_task_builder(request)
        await self._aimbot._aimbot_actuation_task_manager.perform_task(func)
        return aimbot_pb.Empty()

    async def CancelActuationTask(self, request: aimbot_pb.Empty, context: grpc.ServicerContext) -> aimbot_pb.Empty:
        await self._aimbot._aimbot_actuation_task_manager.cancel_task()
        return aimbot_pb.Empty()

    async def LaserEnable(
        self, request: aimbot_pb.LaserEnableRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.LaserEnableReply:
        LOG.debug(f"Laser Enable: {request.scanner_id} enabled: {request.enabled}")
        restart_aimbot = self._aimbot.is_aimbot_tasks_running()
        if restart_aimbot:
            await self._aimbot.stop_aimbot()
        scanners: List[AimbotScanner] = []
        if request.scanner_id == 0:
            scanners = list(self._aimbot.scanner_map.values())
        elif request.scanner_id in self._aimbot.scanner_map:
            scanners.append(self._aimbot.scanner_map[request.scanner_id])
        else:
            errmsg = f"Scanner {request.scanner_id} does not appear to exist."
            LOG.warning(errmsg)
            return aimbot_pb.LaserEnableReply(status=False, message=errmsg)

        async def _enable_scanner(s: AimbotScanner) -> None:
            await asyncio.get_event_loop().run_in_executor(None, lambda: s.enable(request.enabled))

        await asyncio.gather(*[_enable_scanner(sc) for sc in scanners])
        if restart_aimbot:
            algo = self._algo.get_string_value()
            await self._aimbot.start_aimbot(algo)
        return aimbot_pb.LaserEnableReply(status=True, message="")

    async def LaserFire(
        self, request: aimbot_pb.LaserFireRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.LaserFireReply:
        LOG.debug(f"Laser Enable: {request.scanner_id} enabled: {request.fire}")
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_id not in self._aimbot.scanner_map:
            LOG.warning(f"Scanner {request.scanner_id} does not appear to exist.")
            return aimbot_pb.LaserFireReply()
        scanner = self._aimbot.scanner_map[request.scanner_id]
        assert scanner is not None
        if request.fire:
            await scanner.laser.on_with_safety()
        else:
            await scanner.laser.off_with_safety()
        return aimbot_pb.LaserFireReply()

    async def ResetLaserMetrics(
        self, scanner: aimbot_pb.ScannerDescriptor, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.debug(f"ResetLaserMetrics: {scanner.id}")
        assert SCANNER_TIME_METRICS.reset_metrics(scanner.id)
        return aimbot_pb.Empty()

    async def FixLaserMetrics(
        self, req: aimbot_pb.FixLaserMetricsRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.debug(f"FixLaserMetrics: {req.scanner.id}")
        assert SCANNER_TIME_METRICS.fix_metrics(req.scanner.id, req.total_fire_count, req.total_fire_time_ms)
        return aimbot_pb.Empty()

    async def BurnIdividualImage(
        self, request: aimbot_pb.BurnIdividualImagesRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        assert not self._aimbot.is_aimbot_running()
        if request.scanner_id:
            scanners = [
                self._aimbot.scanner_map[scanner].board
                for scanner in request.scanner_id
                if scanner in self._aimbot.scanner_map
            ]
        else:
            scanners = [scanner.board for scanner in self._aimbot.scanner_map.values()]
        speed = request.speed_mmps if request.speed_mmps > 1.0 else 50.0
        intensity = request.intensity if request.intensity > 0.0 else 1.0
        height = get_global_height_estimator().get_ground_position_z_mm()
        cmds = [
            burn_individual_img(
                scanner,
                self._aimbot.scanner_map[scanner.scanner_id].geometric_scanner,
                height,
                speed,
                request.json_img,
                scanner.intensity_percent_to_val(intensity),
                scanner.intensity,
            )
            for scanner in scanners
        ]

        async def _task_burn() -> None:
            errs = await asyncio.gather(*cmds, return_exceptions=True,)
            for err in errs:
                if err is None:
                    continue
                LOG.warning(f"Error occured burning img: {err}")

        asyncio.get_event_loop().create_task(_task_burn())
        return aimbot_pb.Empty()

    async def GetTargetVelocity(
        self, request: aimbot_pb.TargetVelocityRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.TargetVelocityReply:
        if self._aimbot.scheduler is None:
            return aimbot_pb.TargetVelocityReply(velocity_min=0.0, velocity_max=0.0)
        primary, secondary = self._aimbot.scheduler.target_speed_mmps()
        return aimbot_pb.TargetVelocityReply(velocity_min=primary, velocity_max=secondary)

    async def GetTrackingState(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.TrackingState:
        over_capacity = False
        if self._aimbot.scheduler is not None:
            over_capacity = self._aimbot.scheduler.is_over_capacity()
        states = []
        for tracker in self._aimbot._weed_trackers:
            id, items_tracked, capacity, rotary_timeout, deepweed_error = tracker.aimbot_weed_tracker_state()
            states.append(
                aimbot_pb.TrackerState(
                    id=id,
                    at_weed_limit=items_tracked >= capacity,
                    rotary_timeout=rotary_timeout,
                    deepweed_error=deepweed_error,
                )
            )

        return aimbot_pb.TrackingState(
            states=states, scheduler_state=aimbot_pb.SchedulerState(over_capacity=over_capacity),
        )

    async def GetBedtopHeightProfile(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.TrackerBedtopHeightProfile:
        offset_mm = get_height_to_lights_mm_offset()
        height_profiles = []
        for tracker in self._aimbot._weed_trackers:
            weed_columns = tracker.get_averages_for_all_columns()
            crop_columns = tracker.get_averages_for_all_columns()
            bedtop_profile = aimbot_pb.BedtopHeightProfile(
                weed_height_columns=weed_columns, crop_height_columns=crop_columns, pcam_id=tracker.get_pcam_id()
            )
            height_profiles.append(bedtop_profile)

        return aimbot_pb.TrackerBedtopHeightProfile(profiles=height_profiles, bbh_offset_mm=offset_mm)

    async def GetDimensions(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.GetDimensionsResponse:
        if not await self._aimbot.booted:
            raise Exception("Aimbot not booted yet")

        max_height = get_global_height_estimator().get_max_height_mm()
        max_y = self._aimbot.max_pos_mm_y

        avg_x = get_predict_cam_pos()[0][0]  # Reaper
        if is_reaper():
            # use all predict cams in geometric space not just enabled ones
            all_predict_cams = list(get_geometric_space().get_devices_by_type(GeometricCam))
            left_x = all_predict_cams[0].get_abs_position_mm()[0]
            right_x = all_predict_cams[-1].get_abs_position_mm()[0]
            avg_x = (left_x + right_x) / 2
        if is_slayer() or is_bud():
            avg_x = (get_predict_cam_pos()[1][0] + get_predict_cam_pos()[2][0]) / 2
        left_offset = 1016.0
        right_offset = 1016.0
        if is_slayer() or is_reaper():
            left_offset = (
                self._aimbot.config_subscriber.get_config_node("aimbot", "row_width_left_in").get_float_value() * 25.4
            )
            right_offset = (
                self._aimbot.config_subscriber.get_config_node("aimbot", "row_width_right_in").get_float_value() * 25.4
            )

        min_y = sys.float_info.max
        for tracker in self._aimbot.weed_trackers:
            geo_cam = tracker.get_geo_cam()
            y = geo_cam.get_abs_position_from_px_from_height_estimate((0, 0), max_height)[1]
            if y < min_y:
                min_y = y
        return aimbot_pb.GetDimensionsResponse(
            min_x_mm=avg_x - left_offset,
            max_x_mm=avg_x + right_offset,
            min_y_mm=min_y,
            max_y_mm=max_y,
            center_x_mm=avg_x,
        )

    async def GetTargetCamSN(
        self, request: aimbot_pb.GetTargetCamSNRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.GetTargetCamSNResponse:
        scanner_id = int(re.sub(r"\D", "", request.camera_id))
        use_scanner = not is_bud()
        scanner = self._aimbot.scanner_map.get(scanner_id)
        use_scanner = use_scanner and scanner is not None
        if use_scanner:
            assert scanner is not None
            try:
                serial_num = await scanner.board.get_serial_number_config()
                if serial_num:
                    return aimbot_pb.GetTargetCamSNResponse(serial_number=serial_num)
            except Exception:
                LOG.warning("Failed to read serial from scanner falling back to cfg")
        cfg_tree = self._aimbot.config_subscriber.get_config_node("cv", f"cameras/{request.camera_id}")
        if cfg_tree is None:
            LOG.warning(f"Failed to find camera configuration for {request.camera_id}")
            return aimbot_pb.GetTargetCamSNResponse(serial_number="")
        return aimbot_pb.GetTargetCamSNResponse(serial_number=cfg_tree.get_node("serial_number").get_string_value())

    async def ReloadAlmanacConf(
        self, request: aimbot_pb.ReloadAlmanacConfRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.info("reload almanac request received")
        Almanac().reload_required()
        return aimbot_pb.Empty()

    async def ReloadDiscriminatorConf(
        self, request: aimbot_pb.ReloadDiscriminatorConfRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.info("reload discriminator request received")
        Discriminator().reload_required()
        return aimbot_pb.Empty()

    async def ReloadModelinatorConf(
        self, request: aimbot_pb.ReloadModelinatorConfRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.info("reload modelinator request received")
        Modelinator().reload_required()
        return aimbot_pb.Empty()

    async def ReloadTVEProfile(
        self, request: aimbot_pb.ReloadTVEProfileRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.Empty:
        LOG.info("reload TVE request received")
        assert self._aimbot.scheduler is not None
        self._aimbot.scheduler.reload_tve_profile()
        return aimbot_pb.Empty()

    async def GetDistanceTrackedItems(
        self, request: aimbot_pb.TrackedItemsRequest, context: grpc.ServicerContext
    ) -> aimbot_pb.TrackedItemsResponse:
        aimbot_tracker: Optional[AimbotWeedTracker] = None
        for tracker in self._aimbot.weed_trackers:
            if tracker.get_pcam_id() == request.cam_id:
                aimbot_tracker = tracker
                break
        assert aimbot_tracker is not None, f"Aimbot tracker is none for {request.cam_id}"

        trajectories = aimbot_tracker.get_latest_weeds().get_trajectories()
        tracked_items = []
        for trajectory in trajectories:
            history = []
            for centroid in trajectory.get_distance_based_perspectives():
                max_class = max(centroid.get_classes(), key=lambda x: x[1])[0]
                history.append(
                    aimbot_pb.TrackedItemHistory(
                        timestamp_ms=centroid.get_timestamp_ms(),
                        detection=weed_tracking_pb.Detection(
                            x=centroid.get_x(),
                            y=centroid.get_y(),
                            size=centroid.get_size(),
                            clz=max_class,
                            is_weed=False,
                            out_of_band=False,  # TODO Get out of band information
                            id=trajectory.id(),
                            score=centroid.get_score(),
                            weed_score=centroid.get_weed_score(),
                            crop_score=centroid.get_crop_score(),
                            embedding=centroid.get_embedding(),
                            plant_score=centroid.get_plant_score(),
                        ),
                    )
                )
            tracked_items.append(aimbot_pb.TrackedItem(id=trajectory.id(), history=history,))

        resp = aimbot_pb.TrackedItemsResponse(tracked_items=tracked_items)
        return resp

    async def GetParticipation(
        self, request: aimbot_pb.Empty, context: grpc.ServicerContext
    ) -> aimbot_pb.ParticipationResponse:
        if not await self._aimbot.booted:
            raise Exception("Aimbot not booted yet")
        return aimbot_pb.ParticipationResponse(running_as_leader=self._aimbot._is_leader)


class AimbotServer:
    def __init__(
        self,
        event_loop: asyncio.AbstractEventLoop,
        port: int = 6942,
        metrics_port: int = 62101,
        no_weed_tracking: bool = False,
    ):
        self._loop = event_loop
        self._config_subscriber = get_global_config_subscriber()
        self._config_subscriber.add_config_tree("common", "common", "services/common.yaml")
        self._config_subscriber.add_config_tree(
            "hardware_manager",
            f"{get_command_computer_config_prefix()}/hardware_manager",
            "services/hardware_manager.yaml",
        )
        self._config_subscriber.add_config_tree(
            "aimbot", f"{get_computer_config_prefix()}/aimbot", "services/aimbot.yaml"
        )
        self._config_subscriber.add_config_tree("cv", f"{get_computer_config_prefix()}/cv", "services/cv.yaml")
        self._config_subscriber.start()
        self._config_subscriber.wait_until_ready()
        self._algo = self._config_subscriber.get_config_node("aimbot", "algorithm")
        if self._config_subscriber.get_config_node("common", "environment").get_string_value() == "production":
            sentry_reporter.initialize()
        self._aimbot = Aimbot(self._config_subscriber, self._loop, no_weed_tracking=no_weed_tracking)
        self._server = grpc.aio.server()
        self._loggingServicer = lib.common.logging.LoggingGRPCServicer(self._server, "aimbot.log")
        self._servicer = AimbotGRPCServicer(self._aimbot, self._algo)
        aimbot_grpc.add_AimbotServiceServicer_to_server(self._servicer, self._server)
        self._port = port
        self._metrics_port = metrics_port
        self._server.add_insecure_port(f"[::]:{self._port}")

    async def serve(self) -> None:
        bot_stop_handler.add_callback(self.shutdown)
        LOG.info("Starting Metrics server...")
        start_http_server(self._metrics_port)
        LOG.info("Opening Connection to Config Service...")
        await asyncio.get_event_loop().run_in_executor(None, lambda: self._config_subscriber.wait_until_ready())
        LOG.info("Starting Aimbot Boot...")
        await self._aimbot.boot()
        LOG.info("Boot Completed.")
        await self._server.start()
        LOG.info(f"Started Aimbot Server at 0.0.0.0:{self._port}")
        if self._aimbot._is_leader:
            if not self._aimbot.no_weed_tracking:
                await self._aimbot.wait_for_cv_runtime()
                asyncio.get_event_loop().create_task(self._aimbot.sync_target_state_cv())
            await self._aimbot.start_aimbot(self._algo.get_string_value())
            self._aimbot.set_ready()
        await self._server.wait_for_termination()

    def shutdown(self) -> None:
        asyncio.run_coroutine_threadsafe(self._server.stop(0), self._loop)


async def asyncio_watcher(watch_interval_ms: int, tolerance_ms: int) -> None:
    while True:
        start_time = maka_control_timestamp_ms()
        await asyncio.sleep(watch_interval_ms / 1000)
        end_time = maka_control_timestamp_ms()
        if end_time - start_time > watch_interval_ms + tolerance_ms:
            LOG.warning(f"Asyncio Loop may be overloaded, delay: {end_time - start_time - watch_interval_ms}")


async def gil_profiling(profiling_interval_s: int, profiling_duration_s: int, enabled: bool) -> None:
    while enabled:
        await asyncio.sleep(profiling_interval_s)
        out = await asyncio.get_event_loop().run_in_executor(None, lambda: gil_load_profiling(profiling_duration_s))
        LOG.info(out)


async def main(no_weed_tracking: bool = False) -> None:
    try:
        asyncio.get_event_loop().create_task(asyncio_watcher(100, 10))
        asyncio.get_event_loop().create_task(gil_profiling(30, 10, False))
        aimbot_server = AimbotServer(asyncio.get_event_loop(), no_weed_tracking=no_weed_tracking)
        await aimbot_server.serve()
    except Exception as ex:
        bot_stop_handler.exit_with_exception(ex)
    else:
        bot_stop_handler.exit_gracefully()


if __name__ == "__main__":
    faulthandler.enable()
    lib.common.logging.logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s.%(msecs)03d %(levelname)-s [%(module)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    LOG.info("Starting Aimbot Process")
    parser = ArgumentParser("Aimbot Process")
    parser.add_argument(
        "--no-weed-tracking",
        action="store_true",
        help="Disables Weed Tracking and Allow running without CV Runtime",
        default=False,
    )
    args, _ = parser.parse_known_args()
    loop = get_event_loop_by_name()
    if False:  # Debug Asyncio problems with this!
        logging.getLogger("asyncio").setLevel(logging.DEBUG)
        loop.slow_callback_duration = 0.010
        loop.set_debug(True)
    asyncio.run_coroutine_threadsafe(main(**vars(args)), loop)
    bot_stop_handler.ready_for_termination_event.wait()
    sys.exit(bot_stop_handler.error_code)
