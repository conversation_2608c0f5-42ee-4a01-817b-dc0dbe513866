import asyncio
import json
from argparse import Argument<PERSON>ars<PERSON>
from typing import Any, Dict, List, Optional, Tuple, cast

import grpc

import generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2 as aimbot_pb
import generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2_grpc as aimbot_grpc
import lib.common.logging
from lib.common.error import MakaException
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time.time import maka_control_timestamp_ms

LOG = lib.common.logging.get_logger(__name__)


class AimbotClientException(MakaException):
    pass


class AimbotClient:
    def __init__(self, hostname: str = "localhost", port: int = 6942) -> None:
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[aimbot_grpc.AimbotServiceStub] = None
        self._event_loop = get_event_loop_by_name()

    def _maybe_connect(self) -> None:
        if self._stub is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = aimbot_grpc.AimbotServiceStub(self._channel)

    async def await_connection(self) -> None:
        error_msg_printed: bool = False
        while True:
            loading = False
            try:
                assert await self.ping(42) == 42
                loading = True
                assert await self.get_booted()
                break
            except Exception:  # TODO Figure less precise exception
                if not error_msg_printed:
                    error_msg_printed = True
                    if loading:
                        LOG.warning("Connected to Aimbot. Awaiting Aimbot to boot...")
                    else:
                        LOG.warning("Failed To Connect to Aimbot. Awaiting available connection to Aimbot Process...")
            await asyncio.sleep(1)

    async def ping(self, x: int) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.PingRequest(x=x)
        response: aimbot_pb.PongReply = await self._stub.Ping(req)
        return int(response.x)

    async def get_booted(self) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        response: aimbot_pb.BootedReply = await self._stub.GetBooted(aimbot_pb.Empty())
        return bool(response.booted)

    async def set_crosshair_position(self, scanner_id: int, x: int, y: int) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ScannerTargetPosition(scanner_descriptor=aimbot_pb.ScannerDescriptor(id=scanner_id), x=x, y=y)
        await self._stub.SetCrosshairPosition(req)

    async def start_aimbot(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        raise Exception("Not implemented")

    async def stop_aimbot(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        raise Exception("Not implemented")

    async def lens_set(self, scanner_id: int, value: int) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LensSetRequest(scanner_id=scanner_id, value=value)
        await self._stub.LensSet(req)

    async def lens_get(self, scanner_id: int) -> int:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LensGetRequest(scanner_id=scanner_id)
        reply: aimbot_pb.LensGetReply = await self._stub.LensGet(req)
        return int(reply.value)

    async def lens_auto_focus(self, scanner_id: int) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LensAutoFocusRequest(scanner_id=scanner_id)
        await self._stub.LensAutoFocus(req)

    async def laser_arm(self, scanner_id: int, armed: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LaserArmRequest(scanner_id=scanner_id, armed=armed)
        await self._stub.LaserArm(req)

    async def get_aimbot_state(self) -> aimbot_pb.AimbotState:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.Empty()
        return cast(aimbot_pb.AimbotState, await self._stub.GetAimbotState(req))

    async def arm_lasers(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.Empty()
        await self._stub.ArmLasers(req)

    async def disarm_lasers(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.Empty()
        await self._stub.ArmLasers(req)

    async def laser_fire(self, scanner_id: int, fire: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LaserFireRequest(scanner_id=scanner_id, fire=fire)
        await self._stub.LaserFire(req)

    async def laser_set(self, scanner_id: int, on: bool) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.LaserSetRequest(scanner_id=scanner_id, on=on)
        await self._stub.LaserSet(req)

    async def burn_individual_img(
        self, scanner_ids: List[int] = [], speed_mmps: float = 50.0, intensity: float = 1.0, json_img: str = ""
    ) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.BurnIdividualImagesRequest(
            scanner_id=scanner_ids, speed_mmps=speed_mmps, intensity=intensity, json_img=json_img
        )
        await self._stub.BurnIdividualImage(req)

    async def get_scanner_state(self) -> aimbot_pb.ScannerStatusReply:
        self._maybe_connect()
        assert self._stub is not None
        return cast(aimbot_pb.ScannerStatusReply, await self._stub.GetScannerStatus(aimbot_pb.Empty()))

    async def move_scanner(self, scanner_id: int, pos: Tuple[int, int]) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ScannerTargetPosition(
            scanner_descriptor=aimbot_pb.ScannerDescriptor(id=scanner_id,), x=pos[0], y=pos[1],
        )
        await self._stub.MoveScanner(req)

    async def servo_go_to(
        self, scanner_id: int, servo_type: str, position: int, time_ms: int, await_settle: bool
    ) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ServoGoToRequest(
            scanner_id=scanner_id,
            servo_type=aimbot_pb.ServoGoToRequest.ServoType.PAN
            if servo_type == "pan"
            else aimbot_pb.ServoGoToRequest.ServoType.TILT,
            position=position,
            time_ms=time_ms,
            await_settle=await_settle,
        )
        await self._stub.ServoGoTo(req)

    async def servo_get_pos_vel(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ServoGetPosVelRequest(scanner_id=scanner_id)
        reply: aimbot_pb.ServoGetPosVelReply = await self._stub.ServoGetPosVel(req)
        return (int(reply.pan_position), int(reply.pan_velocity)), (int(reply.tilt_position), int(reply.tilt_velocity))

    async def servo_get_limits(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ServoGetLimitsRequest(scanner_id=scanner_id)
        reply: aimbot_pb.ServoGetLimitsReply = await self._stub.ServoGetLimits(req)
        return (int(reply.pan_min), int(reply.pan_max)), (int(reply.tilt_min), int(reply.tilt_max))

    async def tuning_params_update(self, params: Dict[str, Any]) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.TuningParamsUpdateRequest()
        for name, param in params.items():
            if type(param) == int and param >= 2 ** 31:
                req.params.append(aimbot_pb.TuningParam(name=name, v_uint=param))
            elif type(param) == int:
                req.params.append(aimbot_pb.TuningParam(name=name, v_int=param))
            elif type(param) == float:
                req.params.append(aimbot_pb.TuningParam(name=name, v_float=param))
            elif type(param) == bool:
                req.params.append(aimbot_pb.TuningParam(name=name, v_bool=param))
            elif type(param) == str:
                req.params.append(aimbot_pb.TuningParam(name=name, v_string=param))
            else:
                raise AimbotClientException(f"Invalid Parameter Type: {name}:{type(param)}")
        await self._stub.TuningParamsUpdate(req)

    async def tuning_params_get(self, name: Optional[str] = None) -> Dict[str, Any]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.TuningParamsGetRequest(name=name if name is not None else "")
        reply: aimbot_pb.TuningParamsGetReply = await self._stub.TuningParamsGet(req)
        params: Dict[str, Any] = {}
        item: aimbot_pb.TuningParam
        for item in reply.params:
            which_one = item.WhichOneof("value")
            if which_one == "v_uint":
                params[item.name] = int(item.v_uint)
            elif which_one == "v_int":
                params[item.name] = int(item.v_int)
            elif which_one == "v_bool":
                params[item.name] = bool(item.v_bool)
            elif which_one == "v_float":
                params[item.name] = float(item.v_float)
            elif which_one == "v_string":
                params[item.name] = str(item.v_string)
            else:
                raise AimbotClientException(f"Invalid Tuning Param: {item.name}, Unknown Type")
        return params

    def tuning_params_get_from_sync(self, name: Optional[str] = None) -> Dict[str, Any]:
        future = asyncio.run_coroutine_threadsafe(self.tuning_params_get(name), self._event_loop)
        return future.result()

    async def get_estimated_load(self) -> Tuple[float, float]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.GetLoadEstimateRequest()
        reply: aimbot_pb.GetLoadEstimateReply = await self._stub.GetLoadEstimate(req)
        return (reply.current_load, reply.target_load)

    async def get_diagnostic(self) -> Dict[str, Any]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.GetDiagnosticRequest()
        reply: aimbot_pb.GetDiagnosticReply = await self._stub.GetDiagnostic(req)
        diag = cast(Dict[str, Any], json.loads(reply.diagnostic))
        diag["timestamp_ms"] = maka_control_timestamp_ms()
        return diag

    async def reset_devices(self, device_id: str) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.ResetDevicesRequest(device_id=device_id)
        await self._stub.ResetDevices(req)

    async def get_target_velocity(self) -> Tuple[float, float]:
        self._maybe_connect()
        assert self._stub is not None
        req = aimbot_pb.TargetVelocityRequest()
        resp: aimbot_pb.TargetVelocityReply = await self._stub.GetTargetVelocity(req)
        return (resp.velocity_min, resp.velocity_max)

    async def reload_tve_profile(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        await self._stub.ReloadTVEProfile(aimbot_pb.ReloadTVEProfileRequest())

    # From Sync

    def await_connection_from_sync(self) -> None:
        future = asyncio.run_coroutine_threadsafe(self.await_connection(), self._event_loop)
        return future.result()

    def move_scanner_from_sync(self, scanner_id: int, pos: Tuple[int, int]) -> None:
        future = asyncio.run_coroutine_threadsafe(self.move_scanner(scanner_id, pos), self._event_loop)
        return future.result()

    def servo_go_to_from_sync(
        self, scanner_id: int, servo_type: str, position: int, time_ms: int, await_settle: bool
    ) -> None:
        future = asyncio.run_coroutine_threadsafe(
            self.servo_go_to(scanner_id, servo_type, position, time_ms, await_settle), self._event_loop
        )
        return future.result()

    def servo_get_pos_vel_from_sync(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        future = asyncio.run_coroutine_threadsafe(self.servo_get_pos_vel(scanner_id), self._event_loop)
        return future.result()

    def servo_get_limits_from_sync(self, scanner_id: int) -> Tuple[Tuple[int, int], Tuple[int, int]]:
        future = asyncio.run_coroutine_threadsafe(self.servo_get_limits(scanner_id), self._event_loop)
        return future.result()

    def laser_arm_from_sync(self, scanner_id: int, armed: bool) -> None:
        future = asyncio.run_coroutine_threadsafe(self.laser_arm(scanner_id, armed), self._event_loop)
        return future.result()

    def laser_set_from_sync(self, scanner_id: int, on: bool) -> None:
        future = asyncio.run_coroutine_threadsafe(self.laser_set(scanner_id, on), self._event_loop)
        return future.result()

    def lens_set_from_sync(self, scanner_id: int, value: int) -> None:
        future = asyncio.run_coroutine_threadsafe(self.lens_set(scanner_id, value), self._event_loop)
        return future.result()

    def lens_get_from_sync(self, scanner_id: int) -> int:
        future = asyncio.run_coroutine_threadsafe(self.lens_get(scanner_id), self._event_loop)
        return future.result()

    def lens_auto_focus_from_sync(self, scanner_id: int) -> None:
        future = asyncio.run_coroutine_threadsafe(self.lens_auto_focus(scanner_id), self._event_loop)
        return future.result()

    def get_estimated_load_from_sync(self) -> Tuple[float, float]:
        future = asyncio.run_coroutine_threadsafe(self.get_estimated_load(), self._event_loop)
        return future.result()


shared_aimbot_client = AimbotClient()


async def main() -> None:
    aimbot_client = AimbotClient()
    assert await aimbot_client.ping(42) == 42


if __name__ == "__main__":
    parser = ArgumentParser("Aimbot CLI client")
    args = parser.parse_args()
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result()
