from threading import Lock
from typing import Any, Dict, <PERSON><PERSON>

import lib.common.logging
from core.controls.exterminator.model.exterminator import Exterminator
from core.model.path import DevicePath

LOG = lib.common.logging.get_logger(__name__)


class ExterminatorProxy:
    def __init__(self, exterminator: Exterminator):
        self._exterminator = exterminator
        self._config_lock = Lock()

    def config_save(self, debug: bool = False) -> Tuple[DevicePath, str, Dict[str, Any]]:
        with self._config_lock:
            return (
                self._exterminator.device_path,
                f".{self._exterminator.calib_level}",
                self._exterminator.config_save(debug=debug),
            )
