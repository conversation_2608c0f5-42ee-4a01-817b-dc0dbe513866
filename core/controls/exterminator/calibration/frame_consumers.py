from queue import Queue
from typing import List, Tuple

import lib.common.logging
import lib.common.recipes
import lib.common.tasks
from core.controls.exterminator.calibration.frame import Fiducial<PERSON>rame, FiducialStrategy
from core.controls.exterminator.model.scanner import Scanner
from core.coords.samples import DeltaTargetToDeltaServosSample, PredictToServosSample
from lib.common.math import round_tuple, subtract_tuples

LOG = lib.common.logging.get_logger(__name__)


class DeltaTargetToDeltaServosCalibrator:
    """
    Consumer of pairs of FiducialFrames. Calibrates relationship from relative target motion to relative servo motion.
    """

    def __init__(self, scanner: Scanner, fiducial_strategy: FiducialStrategy):
        self.scanner: Scanner = scanner
        self.fiducial_strategy: FiducialStrategy = fiducial_strategy
        assert self.scanner.calib_samples
        self.output = self.scanner.calib_samples.delta_target_to_delta_servos
        assert self.output is not None and len(self.output) == 0, "Unexpected None or non-empty. Should be []"
        self.queues: List["Queue[Tuple[FiducialFrame, FiducialFrame]]"] = []
        self.finish: bool = False
        self.name: str = "dT->dS"  # default thread name
        self._processed: int = 0

    def consume(self, fiducial_frame_pair_queue: "Queue[Tuple[FiducialFrame, FiducialFrame]]") -> None:
        """
        Concurrently consume all of the given input queue, blocking to wait for new inputs.

        Does not publish to subscribed queue. Instead records 3-tuples to scanner's sample set.

        Runs forever until receives None from queue.
        """
        maka_task = lib.common.tasks.get_current()
        count = 0
        while True:
            if count % 1000 == 0:
                LOG.info(f"DtDs Step: {count}, In Queue: {fiducial_frame_pair_queue.qsize()}")
            frame_pair = lib.common.recipes.cancellable_blocking_queue_get(maka_task, fiducial_frame_pair_queue)
            if frame_pair is None:
                # end of queue
                break
            self.record_sample(*frame_pair)
            count += 1

        LOG.info("Done!")

    def record_sample(self, frame1: FiducialFrame, frame2: FiducialFrame) -> None:
        """
        Transform the target crosshair from frame1 to frame2. If successful, record sample.
        """
        ret, frame1_target_in_frame2 = frame1.interpolate_target(
            frame2, fallback=self.fiducial_strategy == FiducialStrategy.CHARUCO_ELSE_ARUCO,
        )
        if ret != 0:
            # TODO add TRACE logging
            # logger.debug('Could not transform fframe%d target to fframe%d due to error %d', self.index, other.index,
            # ret)
            return
        assert frame1_target_in_frame2

        servos_delta = subtract_tuples(frame2.servos, frame1.servos)
        target_delta = subtract_tuples(frame1_target_in_frame2, frame1.target)

        # record sample
        sample = DeltaTargetToDeltaServosSample(target_delta, servos_delta, (frame1.index, frame2.index), frame1.servos)
        self.output.append(sample)
        self._processed += 1

        success_pct = 100 * len(self.output) / self._processed
        LOG.debug(
            "{}: servos Δ of {} produced target Δ of {} [{}/{} ({:.2f}%)]".format(
                (frame1.index, frame2.index),
                servos_delta,
                round_tuple(target_delta, 2),
                len(self.output),
                self._processed,
                success_pct,
            )
        )
        LOG.debug(
            "%d: servos Δ of %s produced target Δ of %s", len(self.output), servos_delta, round_tuple(target_delta, 2),
        )


class PredictToServosCalibrator:
    """
    Consumer for FiducialFrames. Calibrates the predict coordinate systems to the servos
    based on given fiducial frames.

    No support for subscribing queues to the output feed. Output feed is automatically set
    based on given scanner during __init__.
    """

    def __init__(self, scanner: Scanner):
        self.scanner = scanner
        assert self.scanner.calib_samples
        self.output = self.scanner.calib_samples.predict_to_servos
        assert self.output is not None and len(self.output) == 0, "Unexpected None or non-empty. Should be []"
        self.name = "P->S"  # default thread name

    def consume(self, fiducial_frame_queue: "Queue[FiducialFrame]") -> None:
        """
        Concurrently consume all of the given input queue, blocking to wait for new inputs.

        Does not publish to subscribed queue. Instead records 3-tuples to scanner's sample set.

        Runs forever until receives None from queue.
        """
        maka_task = lib.common.tasks.get_current()
        while True:
            fiducial_frame = lib.common.recipes.cancellable_blocking_queue_get(maka_task, fiducial_frame_queue)

            if fiducial_frame is None:
                break

            # transform target crosshair from target coordinate system to predict coordinate system
            ret, predict_coords = fiducial_frame.interpolate_predict_coords()
            if ret != 0:
                LOG.error(
                    "{}: Interpolating crosshair to predict space failed with error {}".format(
                        fiducial_frame.index, ret
                    )
                )
                continue
            assert predict_coords

            # compute trigonometric prediction of where the predict coordinate should be
            # based on earlier distance calibration. This will be off since distance per
            # servo tick is nonlinear
            #
            # This is not required to produce the splines, but it runs quick and is useful
            # to spot bugs
            servos = fiducial_frame.servos

            # TODO revisit whether this is useful
            errors = (
                -1,
                -1,
            )  # we deprecated the old trigonometric way of doing this so for now just plumbing it

            # publish to output
            sample = PredictToServosSample(fiducial_frame.predict_index, predict_coords, servos, errors)
            self.output.append(sample)

            success_pct = 100 * len(self.output) / (fiducial_frame.index + 1)
            LOG.info(
                "{}: coord {} servos {} [{}/{} ({:.2f}%)]".format(
                    fiducial_frame.index,
                    round_tuple(predict_coords, 2),
                    servos,
                    len(self.output),
                    fiducial_frame.index + 1,
                    success_pct,
                )
            )

        LOG.info("Done!")
