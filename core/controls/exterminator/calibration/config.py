import os

from core.config.config import ConfigCategory, config_exists, load_config
from core.fs.defaults import CONFIG_DIR
from lib.common.config_file import ConfigFile


class CalibrationConfigFile(ConfigFile):
    FILENAME = "calibration.json"

    def __init__(self) -> None:
        dir_path = CONFIG_DIR
        if config_exists():
            robot_config = load_config()
            path = robot_config.get_no_fail(ConfigCategory.CALIBRATION, CalibrationConfigFile.FILENAME)
            if path is not None:
                dir_path = os.path.dirname(path)
        super().__init__(dir_path, CalibrationConfigFile.FILENAME)
