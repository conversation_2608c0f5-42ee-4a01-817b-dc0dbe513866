import inspect
from typing import TYPE_CHECKING, Iterable, cast

from aenum import AutoNumberEnum, OrderedEnum  # TODO use enum in 3.6

import lib.common.logging

if TYPE_CHECKING:
    from core.controls.exterminator.model.scanner import Scanner

LOG = lib.common.logging.get_logger(__name__)


class CalibrationLevel(OrderedEnum, AutoNumberEnum):
    """
    Ordinal enumeration of calibration levels.
    """

    _init_ = "value __doc__"  # syntax for enum docstrings; refer https://stackoverflow.com/a/52063239

    NONE: "CalibrationLevel" = cast("CalibrationLevel", ((), "Nothing calibrated; very little works"))

    TARGET_CROSSHAIR: "CalibrationLevel" = cast(
        "CalibrationLevel",
        (
            (),
            "Target crosshair location within target camera pixel space is known. This is done "
            "automatically via laser dot calibration with manual confirmation to verify coordinates.",
        ),
    )

    SAMPLES: "CalibrationLevel" = cast("CalibrationLevel", ((), "Samples are generated."))

    PCAM_LIMITS: "CalibrationLevel" = cast("CalibrationLevel", ((), "Image limits are known."))

    SERVO_DIMENSIONS: "CalibrationLevel" = cast("CalibrationLevel", ((), "Servo Dimensions are known."))

    GOTO_PREDICT: "CalibrationLevel" = cast(
        "CalibrationLevel",
        (
            (),
            "We have a fully correlated spline mapping between servo rotations (pan, tilt) and "
            "prediction space (px, py) [and also the fixed target crosshair (tx, ty) which does "
            "not vary].",
        ),
    )

    GOTO_TARGET: "CalibrationLevel" = cast(
        "CalibrationLevel",
        (
            (),
            "We have a fully correlated spline mapping between relative motion in target space "
            "delta(tx, ty) to relative motion in servo space delta(pan, tilt).",
        ),
    )

    REAL_WORLD: "CalibrationLevel" = cast("CalibrationLevel", ((), "Real world dimensions are known."))

    FULL: "CalibrationLevel" = cast(
        "CalibrationLevel",
        (
            (),
            "Fully calibrated. This is technically the same as the previous level,"
            "but semantically different in that we are saying we are totally done.",
        ),
    )

    def __str__(self) -> str:
        """
        Override default string formatting to just print the name (e.g. 'NONE' rather than 'CalibrationLevel.NONE')
        """
        return str(self.name)

    @staticmethod
    def check_level(scanner: "Scanner", level: "CalibrationLevel", logging: bool = False) -> bool:
        if level == CalibrationLevel.NONE:
            return True
        elif level == CalibrationLevel.TARGET_CROSSHAIR:
            return CalibrationLevel._check_level_target_crosshair(scanner, logging=logging)
        elif level == CalibrationLevel.SAMPLES:
            return CalibrationLevel._check_level_samples(scanner, logging=logging)
        elif level == CalibrationLevel.PCAM_LIMITS:
            return CalibrationLevel._check_level_pcam_limits(scanner, logging=logging)
        elif level == CalibrationLevel.SERVO_DIMENSIONS:
            return CalibrationLevel._check_level_servo_tdims(scanner, logging=logging)
        elif level == CalibrationLevel.GOTO_PREDICT:
            return CalibrationLevel._check_level_goto_predict(scanner, logging=logging)
        elif level == CalibrationLevel.GOTO_TARGET:
            return CalibrationLevel._check_level_goto_target(scanner, logging=logging)
        elif level == CalibrationLevel.REAL_WORLD or level == CalibrationLevel.FULL:
            return CalibrationLevel._check_level_real_world(scanner, logging=logging)
        else:
            return False

    @staticmethod
    def _check_level_target_crosshair(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.target is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing target")
        return False

    @staticmethod
    def _check_level_samples(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.calib_samples is not None and scanner.calib_samples.is_calibrated():
            return True
        elif logging:
            LOG.info("Calibration: missing samples")
        return False

    @staticmethod
    def _check_level_pcam_limits(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.pcam_limits is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing pcam limits")
        return False

    @staticmethod
    def _check_level_servo_tdims(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.servo_tdims is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing servo tdims")
        return False

    @staticmethod
    def _check_level_goto_predict(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.predict_to_servos is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing P->S interpolation")
        return False

    @staticmethod
    def _check_level_goto_target(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.delta_target_to_delta_servos is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing dT->dS interpolation")
        return False

    @staticmethod
    def _check_level_real_world(scanner: "Scanner", logging: bool = False) -> bool:
        if scanner.real_world_dims is not None:
            return True
        elif logging:
            LOG.info("Calibration: missing real world dimensions")
        return False

    @staticmethod
    def set_level(scanner: "Scanner", logging: bool = False) -> None:
        """
        Advances the calibration level one level at a time based on the presence of predicated state.

        :param scanner: The scanner to update level
        :param logging: Log why we are not at the next calibration level. Useful for debugging config load
        """
        level = CalibrationLevel.NONE
        for calib_level in cast(Iterable[CalibrationLevel], CalibrationLevel):
            if CalibrationLevel.check_level(scanner, calib_level, logging=logging):
                level = calib_level
            else:
                break

        scanner.calib_level = level

    @staticmethod
    def reset_level(scanner: "Scanner", level: "CalibrationLevel") -> None:
        """
        Resets the given scanner to the desired level, clearing any state set after this level.
        """
        before = scanner.calib_level
        if level < CalibrationLevel.REAL_WORLD:
            scanner.real_world_dims = None

        if level < CalibrationLevel.GOTO_TARGET:
            scanner.delta_target_to_delta_servos = None

        if level < CalibrationLevel.GOTO_PREDICT:
            scanner.predict_to_servos = None

        if level < CalibrationLevel.SERVO_DIMENSIONS:
            scanner.servo_tdims = None

        if level < CalibrationLevel.PCAM_LIMITS:
            scanner.pcam_limits = None

        if level < CalibrationLevel.SAMPLES:
            scanner.calib_samples = None

        if level < CalibrationLevel.TARGET_CROSSHAIR:
            if scanner.target_cam is not None:
                scanner.clear_target()

        CalibrationLevel.set_level(scanner, logging=True)
        after = scanner.calib_level
        LOG.info("{} Reset from {} to {}".format(scanner.device_path, before, after))

    @staticmethod
    def min_level(scanner: "Scanner", threshold: "CalibrationLevel", logging: bool = True) -> bool:
        """Return whether the given scanner is calibrated at or above the specified level."""
        if scanner.calib_level < threshold:
            if logging:
                LOG.info(
                    "{} {} fails minimum level requirement: > {} at level {}".format(
                        scanner.device_path, inspect.stack()[1].function, threshold, scanner.calib_level,
                    )
                )
            return False
        return True

    @staticmethod
    def max_level(scanner: "Scanner", threshold: "CalibrationLevel", logging: bool = True) -> bool:
        """Return whether the given scanner is calibrated at or below the specified level."""
        if scanner.calib_level > threshold:
            if logging:
                LOG.info(
                    "{} {} fails maximum requirement: <= {} at level {}".format(
                        scanner.device_path, inspect.stack()[1].function, threshold, scanner.calib_level,
                    )
                )
            return False
        return True

    @staticmethod
    def from_str(level: str) -> "CalibrationLevel":
        return cast(CalibrationLevel, CalibrationLevel.__members__[level])


# Ensure Enum is properly ordered. (Issues with aenum require this)
assert CalibrationLevel.NONE < CalibrationLevel.FULL
