from lib.common.error import MakaException

ERROR_CALIBRATE_BELOW_MIN_LEVEL = 20001  # minimum calibration level not met
ERROR_CALIBRATE_ABOVE_MAX_LEVEL = 20002  # calibration level is too advanced
ERROR_CALIBRATE_SPLINE = 20003  # unable to build spline from samples
ERROR_CALIBRATE_FILTER_OUTLIERS_TOO_MANY = 20004  # too many outlier samples
ERROR_CALIBRATE_FILTER_OUTLIERS_BAD_FORMAT = 20005  # wrong sample format / bad input
ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS = 20006  # samples outside of image bounds
ERROR_CALIBRATE_NO_MARKERS = 20007  # no ArUco markers
ERROR_CALIBRATE_MISSING_MARKER = 20008  # a specific marker was not found
ERROR_CALIBRATE_NO_DIAMONDS = 20009  # no ChArUco diamonds
ERROR_CALIBRATE_MISSING_DIAMOND = 20010  # a specific diamond was not found
ERROR_CALIBRATE_MISSING_PARTIAL_DIAMOND = 20011  # a specific diamond was partially not found
ERROR_CALIBRATE_LENS_UNDISTORTION_CAMERA_MATRIX = 20012  # camera calibration failed


# This is a WIP based on discussions
class CalibrationException(MakaException):
    """Raise when there is a calibration exception."""

    pass
