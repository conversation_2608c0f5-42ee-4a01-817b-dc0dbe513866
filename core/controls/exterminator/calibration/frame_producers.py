import argparse
from queue import Queue
from typing import Any, Callable, Generator, List, Optional, Tuple, cast

import numpy.typing as npt

import lib.common.logging
import lib.common.math
import lib.common.recipes
import lib.common.tasks
from core.controls.exterminator.calibration.frame import (
    ArucoMarkersFrame,
    CharucoDiamondsFrame,
    FiducialFrame,
    ServoImageFrame,
)
from core.controls.exterminator.model.scanner import Scanner
from core.cv.retina.camera.node import Cam
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds
from lib.common.fiducials.draw import draw_fiducials

LOG = lib.common.logging.get_logger(__name__)


SERVO_LOCATION_RETRIES = 10


class ServoImageFrameProducer:
    """
    Producer for ServoImageFrames. Designed for use during calibration.
    """

    def __init__(
        self, scanner: Scanner, target_capture_stack: int, save_images: bool = True, save_subdir: Optional[str] = None,
    ):
        assert scanner is not None
        self.scanner: Scanner = scanner
        self.save_images: bool = save_images
        self.save_subdir: str = save_subdir if save_subdir is not None else self.scanner.fs.rel_media_subdir_calibration
        self.queues: List["Queue[Optional[ServoImageFrame]]"] = []
        self.target_captures: int = target_capture_stack
        self.name: str = "sframes"  # default thread name

    def subscribe_consumer_queue(self, q: "Queue[Optional[ServoImageFrame]]") -> None:
        """
        Subscribe the given queue to receive produced frames

        :type q Queue
        """
        self.queues.append(q)

    def produce(self, servos_generator: Generator[Tuple[int, int], None, None]) -> None:
        """Produce fiducials based on the given servos generator"""
        # static predict image
        LOG.info("Capturing static predict image...")
        assert self.scanner.target_cam is not None

        target_crosshair = self.scanner.target
        assert target_crosshair
        # target images for each servo position
        task = lib.common.tasks.get_current()
        for i, servos in enumerate(servos_generator):
            task.tick()
            self.scanner.goto_servos(servos)

            # take target picture
            self.scanner.settle_delay()

            # get new servos position from scanner
            # (in case we went outside limits or had some sort of device bug getting there)
            arrived_at_servos = self.scanner.gimbal_position()
            if arrived_at_servos != servos:
                miss = lib.common.math.subtract_tuples(arrived_at_servos, servos)
                LOG.warning(
                    "{}: servo goto {} arrived {} away at {} after settling".format(i, servos, miss, arrived_at_servos)
                )

            target_image = self.scanner.target_cam.capture_stack(count=self.target_captures)
            if self.save_images:
                self.scanner.target_cam.save(
                    filename_suffix="calibrate-target%04d.jpg" % i, image=target_image, subdir=self.save_subdir
                )

            # construct and publish frame
            servo_image_frame = ServoImageFrame(
                index=i, servos=arrived_at_servos, target=target_crosshair, target_image=target_image,
            )
            self._publish(servo_image_frame)

        # it's nice to return to the center of servo space at the end in case we overshot bounds
        self.scanner.goto_servo_center()

        # publish terminal sentinel to subscribed queues
        self._publish(None)

        LOG.info("Done!")

    def _publish(self, frame: Optional[ServoImageFrame]) -> None:
        """
        Push the given frame (or terminal sentinel) to all subscribed queues

        :type frame ServoImageFrame
        """
        for q in self.queues:
            q.put(frame)


class ValidatedMarkersGenerator:
    def __init__(
        self, scanner: Scanner, predict_capture_stack: int, save_images: bool = True, save_subdir: Optional[str] = None,
    ):
        assert scanner is not None
        self.scanner: Scanner = scanner
        self.save_images: bool = save_images
        self.save_subdir: str = save_subdir if save_subdir is not None else self.scanner.fs.rel_media_subdir_calibration
        self.predict_capture_stack: int = predict_capture_stack

    def _save_image_markers(
        self, pcam: Cam, image: npt.NDArray[Any], markers: ArucoMarkers, index: Optional[int] = None
    ) -> None:
        if self.save_images:
            img_to_save = image.copy()
            draw_fiducials(img_to_save, markers, None)
            if index is not None:
                pcam.save(
                    filename_suffix=f"calibrate-predict-markers_{index}.jpg",
                    image=img_to_save,
                    subdir=self.save_subdir,
                )
            pcam.save(filename_suffix="calibrate-predict-markers.jpg", image=img_to_save, subdir=self.save_subdir)

    def _save_image_diamonds(
        self,
        pcam: Cam,
        image: npt.NDArray[Any],
        markers: ArucoMarkers,
        diamonds: CharucoDiamonds,
        index: Optional[int] = None,
    ) -> None:
        if self.save_images:
            img_to_save = image.copy()
            draw_fiducials(img_to_save, markers, diamonds)
            if index is not None:
                pcam.save(
                    filename_suffix=f"calibrate-predict-diamonds_{index}.jpg",
                    image=img_to_save,
                    subdir=self.save_subdir,
                )
            pcam.save(filename_suffix="calibrate-predict-diamonds.jpg", image=img_to_save, subdir=self.save_subdir)

    def _generate_validated_markers(self, max_attempts: int) -> List[ArucoMarkers]:
        assert max_attempts > 0
        assert self.scanner.predict_space is not None
        LOG.info("Generating validated predict markers...")
        result: List[ArucoMarkers] = []
        for pcam in self.scanner.predict_space.list_cameras():
            predict_image = pcam.capture_stack(count=self.predict_capture_stack)
            predict_markers: ArucoMarkers = self.scanner.fiducials_controller.fiducials.find_markers(predict_image)
            index = 0
            while not predict_markers.is_uniform() and index < max_attempts:
                self._save_image_markers(pcam, predict_image, predict_markers, index=index)
                LOG.warning(
                    "[{} / {}] predict_image markers failed "
                    "uniformity check and {} ids, retry ...".format(index + 1, max_attempts, len(predict_markers.ids))
                )
                predict_image = pcam.capture_stack(count=self.predict_capture_stack)
                new_markers = self.scanner.fiducials_controller.fiducials.find_markers(predict_image)
                predict_markers = predict_markers.merged(new_markers)
                index += 1
            self._save_image_markers(pcam, predict_image, predict_markers)
            if index == max_attempts:
                LOG.warning(f"Failed uniform {pcam.id} markers attempts {index}")
            else:
                LOG.info(f"Successful uniform {pcam.id} markers image found ({len(predict_markers.ids)} ids)")
            result.append(predict_markers)

        return result


class ArucoMarkersFrameProducer(ValidatedMarkersGenerator):
    """
    Producer for ArucoMarkersFrame. Designed for use during calibration.
    """

    def __init__(
        self,
        args: argparse.Namespace,
        scanner: Scanner,
        predict_capture_stack: int,
        save_images: bool = True,
        save_subdir: Optional[str] = None,
    ):
        super().__init__(
            scanner, predict_capture_stack=predict_capture_stack, save_images=save_images, save_subdir=save_subdir
        )
        self.queues: List["Queue[Optional[ArucoMarkersFrame]]"] = []
        self.name: str = "amframes"  # default thread name
        self.predict_markers = self._generate_validated_markers(args.calibrate_uniformity_attempts_markers)

    def subscribe_consumer_queue(self, q: "Queue[Optional[ArucoMarkersFrame]]") -> None:
        """
        Subscribe the given queue to receive produced frames

        :type q Queue
        """
        self.queues.append(q)

    def produce(self, servo_image_frame_queue: "Queue[Optional[ServoImageFrame]]") -> None:
        """
        Concurrently consume all of the given input queue, blocking to wait for new inputs.

        Publishes generated ArucoMarkersFrame to all subscribed queues.

        Runs until terminal sentinel is received from given queue.

        :type servo_image_frame_queue Queue
        """
        assert servo_image_frame_queue is not None and type(servo_image_frame_queue) == Queue
        assert self.scanner.target_cam is not None

        i = 0
        maka_task = lib.common.tasks.get_current()
        while True:
            servo_image_frame = lib.common.recipes.cancellable_blocking_queue_get(maka_task, servo_image_frame_queue)
            if servo_image_frame is None:
                break

            target_markers = self.scanner.fiducials_controller.fiducials.find_markers(
                servo_image_frame.target_image, ignore_small=True
            )
            if self.save_images:
                img_to_save = servo_image_frame.target_image.copy()
                draw_fiducials(img_to_save, target_markers, None)
                self.scanner.target_cam.save(
                    filename_suffix="calibrate-target%04d-annotated.jpg" % servo_image_frame.index,
                    image=img_to_save,
                    subdir=self.save_subdir,
                )

            for pindex, pmarkers in enumerate(self.predict_markers):
                output_frame = ArucoMarkersFrame(
                    index=i,
                    predict_index=pindex,
                    servos=servo_image_frame.servos,
                    target=servo_image_frame.target,
                    predict_markers=pmarkers,
                    target_markers=target_markers,
                )
                self._publish(output_frame)

            i += 1

        # publish terminal sentinel to subscribed queues
        self._publish(None)

        LOG.info("Done!")

    def _publish(self, frame: Optional[ArucoMarkersFrame]) -> None:
        """
        Push the given frame (or terminal sentinel) to all subscribed queues

        :type frame ArucoMarkersFrame
        """
        for q in self.queues:
            q.put(frame)


class CharucoDiamondsFrameProducer(ValidatedMarkersGenerator):
    """
    Producer for CharucoDiamondsFrame. Designed for use during calibration.
    """

    def __init__(
        self,
        args: argparse.Namespace,
        scanner: Scanner,
        predict_capture_stack: int,
        save_images: bool = True,
        save_subdir: Optional[str] = None,
    ):
        super().__init__(
            scanner, predict_capture_stack=predict_capture_stack, save_images=save_images, save_subdir=save_subdir
        )
        self.queues: List["Queue[Optional[CharucoDiamondsFrame]]"] = []
        self.name: str = "cdframes"  # default thread name
        self.predict_markers = self._generate_validated_markers(args.calibrate_uniformity_attempts_markers)
        self.predict_diamonds = self._generate_validated_diamonds(args.calibrate_uniformity_attempts_diamonds)

    def _generate_validated_diamonds(self, max_attempts_diamonds: int) -> List[CharucoDiamonds]:
        assert self.predict_markers is not None  # guard against bugs introduced from refactor (should not trigger)
        assert self.scanner.predict_space is not None
        assert max_attempts_diamonds > 0

        LOG.info("Generating validated predict diamonds...")
        result: List[CharucoDiamonds] = []
        for pindex, pcam in enumerate(self.scanner.predict_space.list_cameras()):
            predict_image = pcam.capture_stack(count=self.predict_capture_stack)
            predict_diamonds = self.scanner.fiducials_controller.fiducials.find_diamonds(
                predict_image, self.predict_markers[pindex]
            )
            index = 0
            while not predict_diamonds.is_uniform() and index < max_attempts_diamonds:
                self._save_image_diamonds(
                    pcam, predict_image, self.predict_markers[pindex], predict_diamonds, index=index
                )
                LOG.warning(
                    "[{} / {}] predict_image diamonds failed "
                    "uniformity check and {} ids, {} corners, retry ...".format(
                        index + 1, max_attempts_diamonds, len(predict_diamonds.ids), len(predict_diamonds.corners),
                    )
                )
                predict_image = pcam.capture_stack(count=self.predict_capture_stack)
                predict_diamonds = predict_diamonds.merged(
                    self.scanner.fiducials_controller.fiducials.find_diamonds(
                        predict_image, self.predict_markers[pindex]
                    )
                )
                index += 1
            self._save_image_diamonds(pcam, predict_image, self.predict_markers[pindex], predict_diamonds)
            if index == max_attempts_diamonds:
                LOG.warning("Failed uniform predict diamonds attempts {}".format(index))
            else:
                LOG.info("Successful uniform predict diamonds image found")
            result.append(predict_diamonds)
        return result

    def subscribe_consumer_queue(self, q: "Queue[Optional[CharucoDiamondsFrame]]") -> None:
        """
        Subscribe the given queue to receive produced frames

        :type q Queue
        """
        self.queues.append(q)

    def produce(self, servo_image_frame_queue: "Queue[Optional[ServoImageFrame]]") -> None:
        """
        Concurrently consume all of the given input queue, blocking to wait for new inputs.

        Publishes generated CharucoDiamondsFrame to all subscribed queues.

        Runs until terminal sentinel is received from given queue.

        :type servo_image_frame_queue Queue
        """
        assert servo_image_frame_queue is not None and type(servo_image_frame_queue) == Queue
        assert self.scanner.predict_space is not None

        i = 0
        maka_task = lib.common.tasks.get_current()
        LOG.info("Calibrating Predict Space")
        self.scanner.predict_space.calibrate_with_markers(self.predict_diamonds)
        LOG.info("Predict Space Calibrated")
        while True:
            servo_image_frame = lib.common.recipes.cancellable_blocking_queue_get(maka_task, servo_image_frame_queue)
            if servo_image_frame is None:
                break

            target_markers = self.scanner.fiducials_controller.fiducials.find_markers(
                servo_image_frame.target_image, ignore_small=True
            )
            target_diamonds = self.scanner.fiducials_controller.fiducials.find_diamonds(
                servo_image_frame.target_image, target_markers
            )
            if self.save_images:
                img_to_save = servo_image_frame.target_image.copy()
                draw_fiducials(img_to_save, target_markers, target_diamonds)
                cast(Cam, self.scanner.target_cam).save(
                    filename_suffix="calibrate-target%04d-annotated.jpg" % servo_image_frame.index,
                    image=img_to_save,
                    subdir=self.save_subdir,
                )

            for pindex, (pmarkers, pdiamonds) in enumerate(zip(self.predict_markers, self.predict_diamonds)):
                output_frame = CharucoDiamondsFrame(
                    index=i,
                    predict_index=pindex,
                    servos=servo_image_frame.servos,
                    target=servo_image_frame.target,
                    predict_markers=pmarkers,
                    target_markers=target_markers,
                    predict_diamonds=pdiamonds,
                    target_diamonds=target_diamonds,
                )
                self._publish(output_frame)

            i += 1

        # publish terminal sentinel to subscribed queues
        self._publish(None)

        LOG.info("Done!")

    def _publish(self, frame: Optional[CharucoDiamondsFrame]) -> None:
        """
        Push the given frame (or terminal sentinel) to all subscribed queues

        :type frame CharucoDiamondsFrame
        """
        for q in self.queues:
            q.put(frame)


class FiducialFramePairProducer:
    """
    Producer for pairs of FiducialFrames. Designed for use during calibration.
    """

    def __init__(self, filter_: Callable[[FiducialFrame, FiducialFrame], bool] = lambda x, y: True):
        self.filter: Callable[[FiducialFrame, FiducialFrame], bool] = filter_
        self.queues: List["Queue[Optional[Tuple[FiducialFrame, FiducialFrame]]]"] = []
        self.frames: List[FiducialFrame] = []
        self.name: str = "fframe-pairs"  # default thread name

    def subscribe_consumer_queue(self, q: "Queue[Optional[Tuple[FiducialFrame, FiducialFrame]]]") -> None:
        """
        Subscribe the given queue to receive produced frames

        :type q Queue
        """
        self.queues.append(q)

    def produce(self, fiducial_frame_queue: "Queue[Optional[FiducialFrame]]") -> None:
        """
        Concurrently consume all of the given input queue, blocking to wait for new inputs.

        Publishes pairs of FiducialFrames to all subscribed queues.

        Runs until terminal sentinel is received from given queue.

        :type fiducial_frame_queue Queue
        """
        assert fiducial_frame_queue is not None and type(fiducial_frame_queue) == Queue

        maka_task = lib.common.tasks.get_current()
        while True:
            fiducial_frame = lib.common.recipes.cancellable_blocking_queue_get(maka_task, fiducial_frame_queue)

            if fiducial_frame is None:
                break
            assert isinstance(fiducial_frame, FiducialFrame), "Expected type FiducialFrame but got: {}".format(
                type(fiducial_frame)
            )

            if fiducial_frame.predict_index != 0:
                continue  # Fiducials frames are duplicated for every predict index so we only consider the first one

            for other_frame in self.frames:
                if self.filter(other_frame, fiducial_frame):
                    self._publish((other_frame, fiducial_frame))
                if self.filter(fiducial_frame, other_frame):
                    self._publish((fiducial_frame, other_frame))

            self.frames.append(fiducial_frame)

        # publish terminal sentinel to subscribed queues
        self._publish(None)

        LOG.info("Done!")

    def _publish(self, frame_pair: Optional[Tuple[FiducialFrame, FiducialFrame]]) -> None:
        """
        Push the given pair of frames (or terminal sentinel) to all subscribed queues

        :type frame_pair Tuple
        """
        for q in self.queues:
            q.put(frame_pair)
