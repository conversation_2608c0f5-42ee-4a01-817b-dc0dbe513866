import math
from typing import TYPE_CHECKING, List, Optional, Tuple

import numpy as np

import lib.common.logging
from core.controls.exterminator.calibration.errors import (
    ERROR_CALIBRATE_BELOW_MIN_LEVEL,
    ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS,
)
from core.controls.exterminator.calibration.level import CalibrationLevel
from core.controls.exterminator.model.gimbal import GimbalPosition
from core.coords.dimensions import ServoDimensions
from lib.common.math import deg2rad

if TYPE_CHECKING:
    from core.controls.exterminator.model.scanner import Scanner

LOG = lib.common.logging.get_logger(__name__)


# BELOW FUNCTIONS MOVED from CalibrationWizard to unwind circular dependency
# TODO find better home for post-processing logic and delete this file - this is hopefully short-lived


def post_process_samples(scanner: "Scanner", log_level: bool = True) -> int:
    """Auto set image limits and one-tick servo dimensions based on samples"""

    # help mypy
    assert scanner.calib_samples

    LOG.info(
        "Post-processing samples (total: %d, %d)...",
        len(scanner.calib_samples.predict_to_servos),
        len(scanner.calib_samples.delta_target_to_delta_servos),
    )

    # ensure level is set properly (e.g. might not be set yet during bootup)
    CalibrationLevel.set_level(scanner, logging=False)
    ret = calibrate_pcam_limits(scanner, log_level=log_level)
    if ret != 0:
        return ret

    ret = calibrate_servo_tdims(scanner)
    if ret != 0:
        return ret
    CalibrationLevel.set_level(scanner, logging=log_level)

    LOG.info(
        "Post-processed samples (total: %d, %d).",
        len(scanner.calib_samples.predict_to_servos),
        len(scanner.calib_samples.delta_target_to_delta_servos),
    )

    return 0


def calibrate_servo_tdims(scanner: "Scanner") -> int:
    """
    Process the given samples to determine the average distance moved in target space for a single pan/tilt tick.
    """
    if not CalibrationLevel.min_level(scanner, CalibrationLevel.SAMPLES):
        return ERROR_CALIBRATE_BELOW_MIN_LEVEL

    # help mypy
    assert scanner.calib_samples
    assert scanner.gimbal

    LOG.info("Computing mean target movement per servo tick...")
    samples = [(s.delta_tcoords, s.delta_servos) for s in scanner.calib_samples.delta_target_to_delta_servos]
    a = np.array(samples)
    # transform shape to [Δtx, Δty, Δpan, Δtilt]
    a = a.reshape(a.shape[0], -1)
    # match rows where either Δpan or Δtilt is 0 but not both
    a = a[((a[:, 2] == 0) | (a[:, 3] == 0)) & ((a[:, 2] != 0) | (a[:, 3] != 0))]
    assert 0 not in a.shape, "No rows found where Δpan or Δtilt is 0 but not both"

    # normalize by dividing by pan/tilt
    ticks_moved = a[:, 2] + a[:, 3]  # one of these will be zero
    a[:, 0] = a[:, 0] / ticks_moved  # this will also normalize sign
    a[:, 1] = a[:, 1] / ticks_moved
    a[:, 2] = a[:, 2] / ticks_moved
    a[:, 3] = a[:, 3] / ticks_moved

    # compute pan distance
    pan_mean = np.mean(a[a[:, 2] == 1], axis=0)[[0, 1]]
    one_pan_theta = 2 * deg2rad(360 / scanner.gimbal.pan.resolution)  # *2 for optical angle

    # compute tilt distance
    tilt_mean = np.mean(a[a[:, 3] == 1], axis=0)[[0, 1]]
    one_tilt_theta = 2 * deg2rad(360 / scanner.gimbal.tilt.resolution)  # *2 for optical angle

    tdims: ServoDimensions = ServoDimensions(one_pan_theta, pan_mean, one_tilt_theta, tilt_mean)
    scanner.servo_tdims = tdims
    LOG.info("pan one tick moves %s = %f in target space", pan_mean, tdims.pan.xy)
    LOG.info("tilt one tick moves %s = %f in target space", tilt_mean, tdims.tilt.xy)
    LOG.debug(
        "Estimated scanner z-height is %.2f target pixels using pan resolution", tdims.pan.z,
    )
    LOG.debug(
        "Estimated scanner z-height is %.2f target pixels using tilt resolution", tdims.tilt.z,
    )
    LOG.info("Estimated scanner z-height is %.2f target pixels", tdims.z)
    LOG.info("Successfully computed mean target movement per servo tick.")
    CalibrationLevel.set_level(scanner)

    return 0


def calibrate_pcam_limits(scanner: "Scanner", log_level: bool = True) -> int:
    """
    Automatically determine image limits.

    There are a number of known drawbacks in this naive implementation:
      (1) We can't actually pan/tilt to that location due to curvature in the mapping on the edges
      (2) We can pan/tilt to that location but it's actually outside of the desired laser target region
      (3) The bounds are too small because the random samples we got didn't end up on bigger/smaller values.
          This is kind of the inverse of (2).
    """
    if not CalibrationLevel.min_level(scanner, CalibrationLevel.SAMPLES):
        return ERROR_CALIBRATE_BELOW_MIN_LEVEL

    # help mypy
    assert scanner.calib_samples
    assert scanner.gimbal
    assert scanner.predict_space

    limits: List[Optional[List[Tuple[int, int]]]] = []
    for pindex, pcam in enumerate(scanner.predict_space.list_cameras()):
        pres = pcam.resolution
        assert pres  # more help

        LOG.info("Automatically determining image limits from min/max coords of samples...")
        pcoords = [
            s.pcoords
            for s in scanner.calib_samples.predict_to_servos
            if scanner.gimbal.within_limits(GimbalPosition(*s.servos)) and s.predict_index == pindex
        ]

        if len(pcoords) == 0:
            limits.append(None)
            continue

        # Determine limits
        # (Allow for 150 pixels of freedom outside predict camera
        # this can happen at the border in simulation, especially when crosshair is not centered
        x_error_threshold, y_error_threshold = 150, 150
        px_min = math.floor(min([pc[0] for pc in pcoords]))
        if px_min < -x_error_threshold:
            LOG.error("Found px_min = %.2f < %.2f", px_min, -x_error_threshold)
            return ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS

        px_max = math.ceil(max([pc[0] for pc in pcoords]))
        if px_max > pres[0] + x_error_threshold:
            LOG.error("Found px_max = %.2f > %d", px_max, pres[0] + x_error_threshold)
            return ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS

        py_min = math.floor(min([pc[1] for pc in pcoords]))
        if py_min < -y_error_threshold:
            LOG.error("Found py_min = %.2f < %.2f", py_min, -y_error_threshold)
            return ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS

        py_max = math.ceil(max([pc[1] for pc in pcoords]))
        if py_max > pres[1] + y_error_threshold:
            LOG.error("Found py_max = %.2f > %d", py_max, pres[1] + y_error_threshold)
            return ERROR_CALIBRATE_SAMPLES_OUTSIDE_IMAGE_BOUNDS

        corner1 = px_min, py_min
        corner2 = px_max, py_min
        corner3 = px_max, py_max
        corner4 = px_min, py_max
        image_limits = [corner1, corner2, corner3, corner4]
        limits.append(image_limits)

        limit_area = (px_max - px_min) * (py_max - py_min)
        total_area = pres[0] * pres[1]

        limit_pct_coverage = 100 * limit_area / total_area
        LOG.info(
            "{} Automated image limits are [{}:{}]x[{}:{}] of {}x{} {:.1f}% of view).".format(
                pcam.id, px_min, px_max, py_min, py_max, *pres, limit_pct_coverage
            )
        )

    scanner.pcam_limits = limits

    CalibrationLevel.set_level(scanner, logging=log_level)

    return 0
