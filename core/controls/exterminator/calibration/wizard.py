import os
from argparse import Namespace
from queue import Queue
from typing import (
    TYPE_CHECKING,
    Any,
    Callable,
    Dict,
    Generator,
    Iterator,
    List,
    Optional,
    Tuple,
    Union,
    ValuesView,
    cast,
)

import numpy as np
from bayes_opt import BayesianOptimization

import core.defaults as defs
import lib.common.logging
import lib.common.tasks
from core.controls.exterminator.calibration.errors import (
    ERROR_CALIBRATE_ABOVE_MAX_LEVEL,
    ERROR_CALIBRATE_BELOW_MIN_LEVEL,
    ERROR_CALIBRATE_NO_DIAMONDS,
    ERROR_CALIBRATE_NO_MARKERS,
    CalibrationException,
)
from core.controls.exterminator.calibration.frame import Fiducial<PERSON>rame, FiducialStrategy, ServoImageFrame
from core.controls.exterminator.calibration.frame_consumers import (
    DeltaTargetToDeltaServosCalibrator,
    PredictToServosCalibrator,
)
from core.controls.exterminator.calibration.frame_producers import (
    ArucoMarkersFrameProducer,
    CharucoDiamondsFrameProducer,
    FiducialFramePairProducer,
    ServoImageFrameProducer,
)
from core.controls.exterminator.calibration.level import CalibrationLevel
from core.controls.exterminator.calibration.samples_post_process import calibrate_pcam_limits, calibrate_servo_tdims
from core.controls.exterminator.model.scanner import Scanner
from core.coords.dimensions import RealWorldDimensions, ServoDimensions
from core.coords.hyperparameters import TrainHyperParameters
from core.coords.interpolation import IndexedBivariateInterpolation, StratifiedBivariateSplinePairInterpolation
from core.coords.samples import CalibrationSamples, DeltaPoint, DeltaTargetToDeltaServosSample, Point, ServosPosition
from core.coords.strata import BivariateStrata, BivariateStratifiedSampler
from core.cv.retina.camera.node import Cam
from core.fiducials.mode import FiducialMode
from core.model.id import ReferenceId
from lib.common.fiducials.charuco_board import CharucoBoard
from lib.common.fiducials.fiducials import Fiducials
from lib.common.math import TupleVar, distance_2d
from lib.common.tasks import MakaTask
from lib.common.time import timestamp_filename

LOG = lib.common.logging.get_logger(__name__)

if TYPE_CHECKING:
    from core.controls.exterminator.model.exterminator import Exterminator
    from core.controls.exterminator.model.row_module import RowModule
    from core.robot import Robot


class ExterminatorCalibrationWizard:
    def __init__(self, bot: "Robot", exterminator: "Exterminator"):
        self._bot = bot
        self._args = bot.args
        self._exterminator = exterminator
        self._wizards: Dict[ReferenceId, RowModuleCalibrationWizard] = {
            row_module.id: RowModuleCalibrationWizard(self._bot, row_module)
            for row_module in self._exterminator.row_modules
        }

    @property
    def row_module_wizards(self) -> ValuesView["RowModuleCalibrationWizard"]:
        return self._wizards.values()

    def validate_level(self, level: CalibrationLevel) -> int:
        for wizard in self._wizards.values():
            ret = wizard.validate_level(level)
            if ret != 0:
                return ret
        return 0

    def _parallel(
        self,
        gen_func: Callable[[ReferenceId, "RowModuleCalibrationWizard"], MakaTask],
        step_name: str,
        required_level: CalibrationLevel,
    ) -> int:
        """
        Executes the requested function in parallel over all row module wizards
        """
        LOG.info("{} Exterminator Begin".format(step_name))
        ret = self.validate_level(required_level)
        if ret != 0:
            return ret
        tasks = [gen_func(row_module_id, wizard) for row_module_id, wizard in self._wizards.items()]
        lib.common.tasks.wait(tasks)
        for task in tasks:
            ret = task.get_result()
            if ret != 0:
                return ret
        LOG.info("{} Exterminator Completed".format(step_name))
        return 0

    # Complete calibration

    def calibrate_from_crosshair(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Calibrate Scanner: {}".format(row_module_id), wizard.calibrate_from_crosshair
            ),
            "Calibration",
            CalibrationLevel.TARGET_CROSSHAIR,
        )

    @staticmethod
    def _invalid_level_advance(level: CalibrationLevel) -> None:
        raise CalibrationException("Level Advance request invalid for exterminator: %s", level)

    def advance_to(self, level: CalibrationLevel) -> None:
        levels: Dict[CalibrationLevel, Callable[[], Any]] = {
            CalibrationLevel.SAMPLES: self.calibrate_samples,
            CalibrationLevel.PCAM_LIMITS: self.post_process_pcam_limits,
            CalibrationLevel.SERVO_DIMENSIONS: self.post_process_servo_tdims,
            CalibrationLevel.GOTO_PREDICT: self.construct_predict_to_servos_interpolation,
            CalibrationLevel.GOTO_TARGET: self.construct_delta_target_to_delta_servos_interpolation,
            CalibrationLevel.REAL_WORLD: self.calibrate_real_world_dimensions,
        }
        levels.get(level, lambda: ExterminatorCalibrationWizard._invalid_level_advance(level))()

    # Calibration Steps

    # 1
    # Must be done individually at the scanner level

    # 2
    # Removed

    # 3
    def calibrate_samples(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Calibrate Samples Scanner: {}".format(row_module_id), wizard.calibrate_samples
            ),
            "Calibrate Samples",
            CalibrationLevel.TARGET_CROSSHAIR,
        )

    # 4
    def post_process_pcam_limits(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Post Process PCAM Limits Scanner: {}".format(row_module_id), wizard.post_process_pcam_limits
            ),
            "Post Process PCAM Limits",
            CalibrationLevel.SAMPLES,
        )

    # 5
    def post_process_servo_tdims(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Post Process Servo TDims Scanner: {}".format(row_module_id), wizard.post_process_servo_tdims
            ),
            "Post Process Servo TDims",
            CalibrationLevel.PCAM_LIMITS,
        )

    # 6
    def construct_predict_to_servos_interpolation(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Construct P->S Scanner: {}".format(row_module_id), wizard.construct_predict_to_servos_interpolation
            ),
            "Construct P->S",
            CalibrationLevel.SERVO_DIMENSIONS,
        )

    # 7
    def construct_delta_target_to_delta_servos_interpolation(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Construct dT->dS Scanner: {}".format(row_module_id),
                wizard.construct_delta_target_to_delta_servos_interpolation,
            ),
            "Construct dT->dS",
            CalibrationLevel.GOTO_PREDICT,
        )

    # 8
    def calibrate_real_world_dimensions(self) -> int:
        return self._parallel(
            lambda row_module_id, wizard: lib.common.tasks.start(
                "Calibrate Real World Scanner: {}".format(row_module_id), wizard.calibrate_real_world_dimensions
            ),
            "Calibrate Real World",
            CalibrationLevel.GOTO_TARGET,
        )


class RowModuleCalibrationWizard:
    def __init__(self, bot: "Robot", row_module: "RowModule"):
        self._bot = bot
        self._args = bot.args
        self._row_module = row_module
        self._wizards = {scanner.id: CalibrationWizard(self._bot, scanner) for scanner in self._row_module.scanners}

    @property
    def scanner_wizards(self) -> ValuesView["CalibrationWizard"]:
        return self._wizards.values()

    def validate_level(self, level: CalibrationLevel) -> int:
        for scanner in self._row_module.scanners:
            if not CalibrationLevel.min_level(scanner, level):
                return ERROR_CALIBRATE_BELOW_MIN_LEVEL
            if not CalibrationLevel.max_level(scanner, level):
                return ERROR_CALIBRATE_ABOVE_MAX_LEVEL
        return 0

    def _parallel(
        self,
        gen_func: Callable[[ReferenceId, "CalibrationWizard"], MakaTask],
        step_name: str,
        required_level: CalibrationLevel,
    ) -> int:
        LOG.info("{} Row Module Begin".format(step_name))
        ret = self.validate_level(required_level)
        if ret != 0:
            return ret
        tasks = [gen_func(scanner_id, wizard) for scanner_id, wizard in self._wizards.items()]
        lib.common.tasks.wait(tasks)
        for task in tasks:
            ret = task.get_result()
            if ret != 0:
                return ret
        LOG.info("{} Row Module Completed".format(step_name))
        return 0

    # Complete calibration

    def calibrate_from_crosshair(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Calibrate Scanner: {}".format(scanner_id), wizard.calibrate_from_crosshair
            ),
            "Calibration",
            CalibrationLevel.TARGET_CROSSHAIR,
        )

    @staticmethod
    def _invalid_level_advance(level: CalibrationLevel) -> None:
        # TODO Use Calibration Exception when rebased
        raise Exception("Level Advance request invalid for row module: %s", level)

    def advance_to(self, level: CalibrationLevel) -> None:
        levels: Dict[CalibrationLevel, Callable[[], Any]] = {
            CalibrationLevel.SAMPLES: self.calibrate_samples,
            CalibrationLevel.PCAM_LIMITS: self.post_process_pcam_limits,
            CalibrationLevel.SERVO_DIMENSIONS: self.post_process_servo_tdims,
            CalibrationLevel.GOTO_PREDICT: self.construct_predict_to_servos_interpolation,
            CalibrationLevel.GOTO_TARGET: self.construct_delta_target_to_delta_servos_interpolation,
            CalibrationLevel.REAL_WORLD: self.calibrate_real_world_dimensions,
        }
        levels.get(level, lambda: RowModuleCalibrationWizard._invalid_level_advance(level))()

    # Calibration Steps

    # 1
    # Must be done individually at the scanner level

    # 2
    # Removed

    # 3
    def calibrate_samples(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Calibrate Samples Scanner: {}".format(scanner_id), wizard.calibrate_samples
            ),
            "Calibrate Samples",
            CalibrationLevel.TARGET_CROSSHAIR,
        )

    # 4
    def post_process_pcam_limits(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Post Process PCAM Limits Scanner: {}".format(scanner_id), wizard.post_process_pcam_limits
            ),
            "Post Process PCAM Limits",
            CalibrationLevel.SAMPLES,
        )

    # 5
    def post_process_servo_tdims(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Post Process Servo TDims Scanner: {}".format(scanner_id), wizard.post_process_servo_tdims
            ),
            "Post Process Servo TDims",
            CalibrationLevel.PCAM_LIMITS,
        )

    # 6
    def construct_predict_to_servos_interpolation(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Construct P->S Scanner: {}".format(scanner_id), wizard.construct_predict_to_servos_interpolation
            ),
            "Construct P->S",
            CalibrationLevel.SERVO_DIMENSIONS,
        )

    # 7
    def construct_delta_target_to_delta_servos_interpolation(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Construct dT->dS Scanner: {}".format(scanner_id),
                wizard.construct_delta_target_to_delta_servos_interpolation,
            ),
            "Construct dT->dS",
            CalibrationLevel.GOTO_PREDICT,
        )

    # 8
    def calibrate_real_world_dimensions(self) -> int:
        return self._parallel(
            lambda scanner_id, wizard: lib.common.tasks.start(
                "Calibrate Real World Scanner: {}".format(scanner_id), wizard.calibrate_real_world_dimensions
            ),
            "Calibrate Real World",
            CalibrationLevel.GOTO_TARGET,
        )


class CalibrationWizard:
    """
    Calibration wizard is a the interface for calibrating. It defines APIs for determining
    the calibration level, advancing to the next calibration level, etc. This functionally
    will be built out to be more and more user friendly and flexible over time.
    """

    def __init__(self, bot: "Robot", scanner: Scanner):
        self._bot: Robot = bot
        self._args = bot.args
        self.scanner = scanner

        # stratified target interpolation for choosing samples to build different interpolations
        self.target_interpolate_strata: Optional[BivariateStrata] = None
        self.stratified_sampler: Optional[BivariateStratifiedSampler] = None
        self.sample_servos_generator: Optional[Generator[Tuple[int, int], None, None]] = None
        assert self.scanner.gimbal
        self._set_stratas(self.scanner.gimbal.min, self.scanner.gimbal.max)

    # Saving files with camera settings to calibration folder.

    def _save_camera_settings(self, save_directory: str) -> None:
        assert self.scanner.predict_space is not None
        for pcam in self.scanner.predict_space.list_cameras():
            pcam.save_settings(save_directory=save_directory)
        assert self.scanner.target_cam
        self.scanner.target_cam.save_settings(save_directory=save_directory)

    # state initialization

    def _set_stratas(self, servo_min: Tuple[int, int], servo_max: Tuple[int, int]) -> None:
        """Set the strata based on the given servo limits. Separated since servo limits may not be set"""
        assert servo_min is not None and len(servo_min) == 2
        assert servo_max is not None and len(servo_max) == 2
        assert self.scanner.calib_sample_strata

        # sampler
        self.stratified_sampler = BivariateStratifiedSampler(self.scanner.calib_sample_strata)
        if self._args.calibrate_num_samples_per_strata != defs.CALIBRATE_MIN_STRATUM_SAMPLES:
            LOG.info(
                "Calibration override: num_samples_per_strata is %d", self._args.calibrate_num_samples_per_strata,
            )
        self.sample_servos_generator = self.stratified_sampler.generate_samples(
            self._args.calibrate_num_samples_per_strata
        )

        # target interpolation
        self.target_interpolate_strata = BivariateStrata.from_servo_limits(
            servo_min,
            servo_max,
            self._args.target_interpolate_num_strata_pan,
            self._args.target_interpolate_num_strata_tilt,
        )
        if self.target_interpolate_strata.num_strata_x != defs.INTERPOLATE_TARGET_NUM_STRATA_PAN:
            LOG.info(
                "Target Interpolation override: num_strata_pan is %d", self.target_interpolate_strata.num_strata_x,
            )
        if self.target_interpolate_strata.num_strata_y != defs.INTERPOLATE_TARGET_NUM_STRATA_TILT:
            LOG.info(
                "Target Interpolation override: num_strata_tilt is %d", self.target_interpolate_strata.num_strata_y,
            )

    # Sample Calibration APIs

    def get_fiducial_strategy(self) -> FiducialStrategy:
        mode_supported = self.scanner.fiducials_controller.mode()
        if mode_supported == FiducialMode.ARUCO_MARKERS:
            return FiducialStrategy.ARUCO
        else:
            return FiducialStrategy.CHARUCO_ELSE_ARUCO

    def _start_calibration_tasks(self) -> Iterator[MakaTask]:
        """The dependency is:

        [goto pan/tilt locations and capture images and produce sframes] ==> sframe queue
        sframe_queue ==> [find fiducials in predict/target images and produce fframes] ==> fframe queue
        fframe_queue ==> [produce pairs of frames] ==> fframe-pairs
        fframe_queue ==> [calibrate predict-->servos relationship]
        fframe-pairs ==> [calibrate Δ_target->Δ_servos relationship]

        where fframe can refer to either ArucoMarkersFrames or CharucoDiamondsFrames
        """

        # Relative path for asking the cameras to save
        dirname = timestamp_filename("{}".format(self.scanner.id))
        save_subdir = os.path.join(self._bot.fs.rel_media_subdir_calibration, dirname)

        # Two operations on full path, everything else will be relative to image dir
        full_path = os.path.join(self._bot.fs.abs_media_dir, save_subdir)
        os.makedirs(full_path)
        self._save_camera_settings(save_directory=full_path)

        LOG.debug("Saving calibration images to {}".format(save_subdir))
        parent_task = lib.common.tasks.get_current()

        # 0. Determine ChArUco vs ArUco
        fiducial_strategy = self.get_fiducial_strategy()
        LOG.info("Using fiducial strategy %s...", fiducial_strategy)
        fframe_producer: Union[ArucoMarkersFrameProducer, CharucoDiamondsFrameProducer] = (
            ArucoMarkersFrameProducer(
                self._args,
                self.scanner,
                predict_capture_stack=self._args.calibrate_predict_capture_stack,
                save_subdir=save_subdir,
            )
            if fiducial_strategy == FiducialStrategy.ARUCO
            else CharucoDiamondsFrameProducer(
                self._args,
                self.scanner,
                predict_capture_stack=self._args.calibrate_predict_capture_stack,
                save_subdir=save_subdir,
            )
        )

        # level 1
        task_dependency_depth = 0

        # 1. Create thread to traverse servo positions and take pictures
        task_dependency_depth = 0
        sframe_producer = ServoImageFrameProducer(
            self.scanner, target_capture_stack=self._args.calibrate_target_capture_stack, save_subdir=save_subdir
        )
        sframe_work_queue: "Queue[Optional[ServoImageFrame]]" = Queue()
        sframe_producer.subscribe_consumer_queue(sframe_work_queue)
        sframe_task = lib.common.tasks.start(
            name="{}/calibrate{}-{}".format(self.scanner.id, task_dependency_depth, sframe_producer.name),
            logger=LOG,
            target=sframe_producer.produce,
            parent_task=parent_task,
            args=(self.sample_servos_generator,),
        )
        LOG.debug("Created {} task".format(sframe_task.name))  # log for debugging thread create/start/join sequence
        yield sframe_task  # use yields so downstream logic can start threads immediately

        # level 2
        task_dependency_depth += 1

        # 2. Create thread which consumes servo/image frames and produces fiducial frames
        # subscribe consumer queues
        fframe_pairs_work_queue: "Queue[Optional[FiducialFrame]]" = Queue()
        # mypy: The producers are typed as ArucoMarkers/CharucoDiamondsFrame consuming, but we use base type here
        #       we could fix this with a large if statement, but this function already is gigantic
        #       so ignoring seems reasonable
        fframe_producer.subscribe_consumer_queue(fframe_pairs_work_queue)  # type: ignore
        predict_to_servos_fframe_work_queue: "Queue[Optional[FiducialFrame]]" = Queue()
        fframe_producer.subscribe_consumer_queue(predict_to_servos_fframe_work_queue)  # type: ignore
        fframe_task = lib.common.tasks.start(
            name="{}/calibrate{}-{}".format(self.scanner.id, task_dependency_depth, fframe_producer.name),
            logger=LOG,
            target=fframe_producer.produce,
            parent_task=parent_task,
            args=(sframe_work_queue,),
        )
        fframe_task.bidepends_on(sframe_task)
        LOG.debug("Created {} task".format(fframe_task.name))
        yield fframe_task

        # level 3
        task_dependency_depth += 1

        # 3A. Create thread which consumes fiducial frames and calibrates predict->servos relationship
        predict_to_servos_calibrator = PredictToServosCalibrator(self.scanner)
        predict_to_servos_calibrator_task = lib.common.tasks.start(
            name="{}/calibrate{}-{}".format(self.scanner.id, task_dependency_depth, predict_to_servos_calibrator.name),
            logger=LOG,
            target=predict_to_servos_calibrator.consume,
            parent_task=parent_task,
            args=(predict_to_servos_fframe_work_queue,),
        )
        predict_to_servos_calibrator_task.bidepends_on(fframe_task)
        # register the done callback with the scanner, but it doesn't care about the future
        predict_to_servos_calibrator_task.add_done_callback(lambda f: self.scanner.calibration_markers_complete())
        LOG.debug("Created {} task".format(predict_to_servos_calibrator_task.name))
        yield predict_to_servos_calibrator_task

        # 3B. Create thread which consumes fiducial frames and produces fiducial frame pairs
        fframe_pair_producer = FiducialFramePairProducer()
        target_to_servos_fframe_work_queue: "Queue[Optional[Tuple[FiducialFrame, FiducialFrame]]]" = Queue()
        fframe_pair_producer.subscribe_consumer_queue(target_to_servos_fframe_work_queue)
        fframe_pairs_task = lib.common.tasks.start(
            name="{}/calibrate{}-{}".format(self.scanner.id, task_dependency_depth, fframe_pair_producer.name),
            logger=LOG,
            target=fframe_pair_producer.produce,
            parent_task=parent_task,
            args=(fframe_pairs_work_queue,),
        )
        fframe_pairs_task.bidepends_on(fframe_task)
        LOG.debug("Created %s task", fframe_pairs_task.name)
        yield fframe_pairs_task

        # level 4
        task_dependency_depth += 1

        # 4. Create thread which consumes fiducial frame pairs and calibrates Δ_target->Δ_servos relationship
        target_to_servos_calibrator = DeltaTargetToDeltaServosCalibrator(
            self.scanner, fiducial_strategy=fiducial_strategy
        )
        target_to_servos_calibrator_task = lib.common.tasks.start(
            name="{}/calibrate{}-{}".format(self.scanner.id, task_dependency_depth, target_to_servos_calibrator.name),
            logger=LOG,
            target=target_to_servos_calibrator.consume,
            parent_task=parent_task,
            args=(target_to_servos_fframe_work_queue,),
        )
        target_to_servos_calibrator_task.bidepends_on(fframe_pairs_task)
        LOG.debug("Created {} task".format(target_to_servos_calibrator_task.name))
        yield target_to_servos_calibrator_task

    def _run_calibration_tasks(self) -> None:
        tasks = [t for t in self._start_calibration_tasks()]
        lib.common.tasks.wait(tasks)
        for t in tasks:
            # CRITICAL if failed
            # ERROR if cancelled
            # INFO if success
            log_f = LOG.critical if t.failed else LOG.error if t.cancelled else LOG.info
            log_f("{}: Calibration subtask {}: {}".format(self.scanner.device_path, t.name, t.status))

        success_tasks = [t for t in tasks if t.success]
        if len(success_tasks) != len(tasks):
            failed_tasks = [t for t in tasks if t.failed]
            if len(failed_tasks) == 0:
                raise CalibrationException("Sample Calibration Cancelled")

            # there were failures
            LOG.critical(
                "{} Failed calibrating samples due to task failure: {}".format(
                    self.scanner.device_path, [t.name for t in failed_tasks]
                )
            )

            # Kill this outer task
            raise CalibrationException(
                "Sample Calibration Failed. Failed tasks: {}".format(
                    [(t.name, t.get_exception()) for t in failed_tasks]
                )
            )

        LOG.info("{} Calibration subtasks all succeeded.".format(self.scanner.device_path))

    # Combined Calibration Steps

    def calibrate_from_crosshair(self) -> int:
        LOG.info("Begin Complete Calibration")
        # TODO Pull CalibrationLevel calls to be entirely in the Calibration Wizard

        # 1 Should already be done before this function is called
        if not CalibrationLevel.min_level(self.scanner, CalibrationLevel.TARGET_CROSSHAIR):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL
        if not CalibrationLevel.max_level(self.scanner, CalibrationLevel.TARGET_CROSSHAIR):
            return ERROR_CALIBRATE_ABOVE_MAX_LEVEL

        # 2
        # Removed

        # 3
        ret = self.calibrate_samples()
        if ret != 0:
            return ret

        # 4
        ret = self.post_process_pcam_limits()
        if ret != 0:
            return ret

        # 5
        ret = self.post_process_servo_tdims()
        if ret != 0:
            return ret

        # 6
        ret = self.construct_predict_to_servos_interpolation(save_config=True)
        if ret != 0:
            return ret

        # 7
        ret = self.construct_delta_target_to_delta_servos_interpolation(save_config=True)
        if ret != 0:
            return ret

        # 8
        ret = self.calibrate_real_world_dimensions()
        if ret != 0:
            return ret

        LOG.info("Complete Calibration Completed")
        return 0

    @staticmethod
    def _invalid_level_advance(level: CalibrationLevel) -> None:
        # TODO Use Calibration Exception when rebased
        raise Exception("Level Advance request invalid for scanner: %s", level)

    def advance_to(self, level: CalibrationLevel, *args: Any, **kwargs: Any) -> None:
        advance_func: Callable[..., Any] = {  # type: ignore
            CalibrationLevel.TARGET_CROSSHAIR: self.calibrate_crosshair,
            CalibrationLevel.SAMPLES: self.calibrate_samples,
            CalibrationLevel.PCAM_LIMITS: self.post_process_pcam_limits,
            CalibrationLevel.SERVO_DIMENSIONS: self.post_process_servo_tdims,
            CalibrationLevel.GOTO_PREDICT: self.construct_predict_to_servos_interpolation,
            CalibrationLevel.GOTO_TARGET: self.construct_delta_target_to_delta_servos_interpolation,
            CalibrationLevel.REAL_WORLD: self.calibrate_real_world_dimensions,
        }.get(level, lambda: CalibrationWizard._invalid_level_advance(level))
        advance_func(*args, **kwargs)

    # Calibration Steps

    # 1
    def calibrate_crosshair(self, tx_ty: Union[Tuple[int, int], TupleVar, None, Any]) -> int:
        """
        Calibrate the position of the scanner's crosshair.
        NONE -> TARGET_CROSSHAIR
        """

        self.scanner.set_target(cast(Tuple[float, float], (tx_ty)))
        CalibrationLevel.set_level(self.scanner, logging=True)
        self._bot.config_save()
        return 0

    # 2
    # Removed

    # 3
    def calibrate_samples(self) -> int:
        """
        Generate Samples for Calibration
        TARGET_CROSSHAIR -> SAMPLES
        """
        if not CalibrationLevel.min_level(self.scanner, CalibrationLevel.TARGET_CROSSHAIR):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL
        if not CalibrationLevel.max_level(self.scanner, CalibrationLevel.TARGET_CROSSHAIR):
            return ERROR_CALIBRATE_ABOVE_MAX_LEVEL

        assert self.scanner.calib_samples is None, "Unexpected non-None scanner samples at level = {}".format(
            self.scanner.calib_level
        )

        self.scanner.annotator.enable_fiducials_annotations()
        self.scanner.annotator.enable_predict_sample_annotation()

        self.scanner.calib_samples = CalibrationSamples()
        try:
            self._run_calibration_tasks()
        except CalibrationException:
            self.scanner.calib_samples = None
            raise
        except Exception:
            LOG.exception("Unexpected exception during calibration. This should not happen.")
            self.scanner.calib_samples = None
            raise

        CalibrationLevel.set_level(self.scanner, logging=True)
        self._bot.config_save()
        return 0

    # 4
    def post_process_pcam_limits(self) -> int:
        """
        Post Process Predict Camera Limits
        SAMPLES -> PCAM_LIMITS
        """
        ret = calibrate_pcam_limits(self.scanner, log_level=True)
        if ret != 0:
            return ret

        self._bot.config_save()
        return 0

    # 5
    def post_process_servo_tdims(self) -> int:
        """
        Post Process Servo Dimensions
        PCAM_LIMITS -> SERVO_DIMENSIONS
        """
        ret = calibrate_servo_tdims(self.scanner)
        if ret != 0:
            return ret

        self._bot.config_save()
        return 0

    # 6
    def construct_predict_to_servos_interpolation(self, save_config: bool = True) -> int:
        """
        Attempt to construct splines based on the set of predict->servos samples.
        SERVO_DIMENSIONS -> GOTO_PREDICT
        """
        if not CalibrationLevel.min_level(self.scanner, CalibrationLevel.SAMPLES):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL

        LOG.info("Constructing predict->servos interpolation...")

        samples: List[Tuple[int, Point, ServosPosition, DeltaPoint]] = [
            s.tuples() for s in cast(CalibrationSamples, self.scanner.calib_samples).predict_to_servos
        ]
        train_hyperparams = TrainHyperParameters.guess(self.scanner.resolution_range)
        self.scanner.predict_to_servos = IndexedBivariateInterpolation.construct(
            "P->S", samples, train_hyperparams, debug_context="(err_x, err_y)"
        )
        LOG.info(
            'Successfully constructed predict->servos interpolation "%s" with %d samples.',
            self.scanner.predict_to_servos.id,
            len(samples),
        )

        CalibrationLevel.set_level(self.scanner, logging=True)

        # do a basic goto to verify everything working
        assert self.scanner.predict_space is not None
        assert self.scanner.pcam_limits is not None
        ret = 0
        for pindex, _ in enumerate(self.scanner.predict_space.list_cameras()):
            if self.scanner.pcam_limits[pindex] is None or not self.scanner.predict_to_servos.is_index_calibrated(
                pindex
            ):
                continue
            ret = self.scanner.goto_predict_coords(
                pindex, cast(Tuple[float, float], tuple(np.mean(np.array(self.scanner.pcam_limits[pindex]), axis=0)))
            )
            if ret != 0:
                LOG.critical("{} Failed basic goto predict with error {}".format(self.scanner.device_path, ret))
                return ret

        if save_config:
            self._bot.config_save()

        return ret

    # 7
    def construct_delta_target_to_delta_servos_interpolation(
        self, override_strata: Optional[BivariateStrata] = None, save_config: bool = True
    ) -> int:
        """
        Attempt constructing dT->dS interpolation
        GOTO_PREDICT -> GOTO_TARGET
        """
        if not CalibrationLevel.min_level(self.scanner, CalibrationLevel.SAMPLES):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL

        strata = override_strata if override_strata is not None else self.target_interpolate_strata
        assert strata is not None
        LOG.info("Constructing Δ_target->Δ_servos interpolation with strata %s...", strata)
        train_hyperparams = TrainHyperParameters.guess(self.scanner.resolution_range)

        # construct interpolation with filtered samples
        assert self.scanner.calib_samples is not None
        samples: List[DeltaTargetToDeltaServosSample] = self.scanner.calib_samples.delta_target_to_delta_servos
        (ret, self.scanner.delta_target_to_delta_servos,) = StratifiedBivariateSplinePairInterpolation.construct(
            name="dT->dS",
            strata=strata,
            min_samples_per_stratum=self._args.target_interpolate_strata_min_samples,
            samples=samples,
            train_hyperparams=train_hyperparams,
            debug_context="(index0, index1, pan0, tilt0)",
        )
        if ret != 0:
            LOG.critical(
                "Constructing Δ_target->Δ_servos interpolation failed with error %d", ret,
            )
            return ret
        LOG.info(
            'Successfully constructed Δ_target->Δ_servos interpolation "%s" with strata %s.',
            cast("StratifiedBivariateSplinePairInterpolation", self.scanner.delta_target_to_delta_servos).id,
            strata,
        )

        CalibrationLevel.set_level(self.scanner, logging=True)

        # do a basic goto to verify everything working
        assert self.scanner.target is not None
        ret = self.scanner.goto_target_coords(self.scanner.target)
        if ret != 0:
            LOG.critical("{} Failed basic goto target with error {}".format(self.scanner.device_path, ret))
            return ret

        if save_config:
            self._bot.config_save()

        return ret

    # 8
    def calibrate_real_world_dimensions(self) -> int:
        """
        Estimate the real world dimensions for this scanner.
        GOTO_TARGET -> REAL_WORLD
        """
        if not CalibrationLevel.min_level(self.scanner, CalibrationLevel.SAMPLES):
            return ERROR_CALIBRATE_BELOW_MIN_LEVEL

        LOG.info("Calibrating real world dimensions...")

        # Estimate mm per target pixels by figuring out average diamond square length in px
        self.scanner.goto_servo_center()
        self.scanner.settle_delay()
        img = cast(Cam, self.scanner.target_cam).capture_stack()
        markers = self.scanner.fiducials_controller.fiducials.find_markers(img, ignore_small=True)
        if len(markers) == 0:
            return ERROR_CALIBRATE_NO_MARKERS
        diamonds = self.scanner.fiducials_controller.fiducials.find_diamonds(img, markers)
        if len(diamonds) == 0:
            return ERROR_CALIBRATE_NO_DIAMONDS
        dists = []
        for i in range(len(diamonds.ids) - 1):
            for j in range(i + 1, len(diamonds)):
                if abs(int(diamonds.ids[i] - diamonds.ids[j])) == 1:
                    d = distance_2d(diamonds.corners[i][0], diamonds.corners[j][0])
                    dists.append(d)
        diamond_square_length_tpx = np.mean(dists).item()
        LOG.debug("Average ChArUco square has length %.2f tpx", diamond_square_length_tpx)
        mm_per_tpx = self.scanner.fiducials_controller.fiducials.charuco_square_length_mm / diamond_square_length_tpx

        real_world_dims = RealWorldDimensions(cast(ServoDimensions, self.scanner.servo_tdims), mm_per_tpx)
        LOG.info(
            "Estimated %.3fmm/tpx (%.3ftpx/mm) based on %.3fin ChArUco squares",
            real_world_dims.mm_per_tpx,
            real_world_dims.tpx_per_mm,
            self.scanner.fiducials_controller.fiducials.charuco_square_length_in,
        )
        LOG.info(
            "Estimated scanner z-height is %.2fmm = %.2fin", real_world_dims.mm.z, real_world_dims.inches.z,
        )

        self.scanner.real_world_dims = real_world_dims
        CalibrationLevel.set_level(self.scanner, logging=True)
        self._bot.config_save()

        LOG.info("Calibrated real world dimensions.")

        return 0

    def _target_camera_parameters_estimator(self, **kwargs: Any) -> int:
        images_amount = 3
        LOG.info("Camera params combinations: %s", kwargs)

        assert self.scanner.gimbal
        assert self.scanner.target_cam
        for key, value in kwargs.items():
            self.scanner.target_cam.set_property(key=key, value=value)

        markers_amount = 0
        points = self.scanner.gimbal.points(center=True, corners=False, cardinals=False, quadrant_centroids=True)
        for pan, tilt in points:
            self.scanner.goto_servos((pan, tilt))
            self.scanner.settle_delay()

            image = self.scanner.target_cam.capture()
            markers = self.scanner.fiducials_controller.fiducials.find_markers(image, ignore_small=True)
            if len(markers) == 0:
                LOG.info("Target camera at position %s %s.Markers amount is 0", pan, tilt)
                return 0
            for i in range(images_amount):
                image = self.scanner.target_cam.capture()
                markers_add = self.scanner.fiducials_controller.fiducials.find_markers(image, ignore_small=True)
                if len(markers_add) == 0:
                    LOG.info("Target camera at position %s %s.Markers amount is 0", pan, tilt)
                    return 0
                markers = markers.merged(markers_add)
            LOG.info("Target camera at position %s %s. Markers amount is %s.", pan, tilt, len(markers))
            markers_amount += len(markers)

        LOG.info("Markers amount: %s", markers_amount)
        return markers_amount

    def calibrate_target_camera_hyperparameters(self, parameters_ids: Optional[List[str]] = None) -> None:
        target_cam = cast(Cam, self.scanner.target_cam)
        LOG.info("Starting %s camera hyperparameters bayesian optimization.", target_cam.id)

        if parameters_ids is None:
            parameters_ids = ["Gain", "Gamma", "ExposureTime"]

        camera_hyperparameters = target_cam.get_parameters(parameters_ids)
        LOG.info("Camera parameters %s", camera_hyperparameters)

        try:
            optimizer = BayesianOptimization(
                f=self._target_camera_parameters_estimator, pbounds=camera_hyperparameters, random_state=1
            )
            optimizer.maximize(init_points=4, n_iter=20)
        except Exception:
            LOG.exception("Target camera hyperparameters calibration failed.")

        markers_amount = optimizer.max["target"]
        parameters = optimizer.max["params"]

        LOG.info("Best camera parameters set is %s with markers amount is %s", parameters, markers_amount)

        for key, value in parameters.items():
            target_cam.set_property(key=key, value=value)

        LOG.info("Finished %s camera hyperparameters bayesian optimization.", target_cam.id)


class PredictCameraCalibrationWizard:
    def __init__(self, camera: Cam, args: Namespace):
        self._cam = camera
        board = CharucoBoard.create_with_default(
            res_x=args.board_res_x,
            res_y=args.board_res_y,
            pixels_per_bit=args.board_pixels_per_bit,
            bits=args.board_bits_per_marker,
            ratio=args.board_ratio,
            diagonal_size=args.board_diagonal,
        )
        self._fiducials = Fiducials(charuco_board=board)

    def _predict_camera_parameters_estimator(self, **kwargs: Any) -> int:
        images_amount = 5
        LOG.info("Camera params combinations: %s", kwargs)
        for key, value in kwargs.items():
            self._cam.set_property(key=key, value=value)
        image = self._cam.capture()
        markers = self._fiducials.find_markers(image, unique=False)
        if len(markers) == 0:
            return 0

        for i in range(images_amount):
            image = self._cam.capture()
            markers_add = self._fiducials.find_markers(image, unique=False)
            if len(markers_add) == 0:
                return 0
            markers = markers.merged(markers_add)

        LOG.info("Markers amount: %s", len(markers))
        return len(markers)

    def calibrate_predict_camera_hyperparameters(self, parameters_ids: Optional[List[str]] = None) -> None:
        LOG.info("Starting %s camera hyperparameters bayesian optimization.", self._cam.id)

        if parameters_ids is None:
            parameters_ids = ["Gain", "Gamma", "ExposureTime"]
        camera_hyperparameters = self._cam.get_parameters(parameters_ids)

        try:
            optimizer = BayesianOptimization(
                f=self._predict_camera_parameters_estimator, pbounds=camera_hyperparameters, random_state=1
            )
            optimizer.maximize(init_points=3, n_iter=25)
        except Exception:
            LOG.exception("Predict camera hyperparameters calibration failed.")

        markers_amount = optimizer.max["target"]
        parameters = optimizer.max["params"]

        LOG.info("Best camera parameters set is %s with markers amount is %s", parameters, markers_amount)

        for key, value in parameters.items():
            self._cam.set_property(key=key, value=value)
        LOG.info("Finished %s camera hyperparameters bayesian optimization.", self._cam.id)
