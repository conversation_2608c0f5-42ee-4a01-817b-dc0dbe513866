import abc
from typing import Any, Optional, Tuple, cast

import numpy.typing as npt
from aenum import IntEnum, unique

import lib.common.logging
from core.controls.exterminator.calibration.errors import (
    ERROR_CALIBRATE_MISSING_DIAMOND,
    ERROR_CALIBRATE_MISSING_MARKER,
    ERROR_CALIBRATE_MISSING_PARTIAL_DIAMOND,
    ERROR_CALIBRATE_NO_DIAMONDS,
    ERROR_CALIBRATE_NO_MARKERS,
)
from core.fiducials.scanner import ScannerFiducialsController
from lib.common.fiducials.aruco_markers import ArucoMarkers
from lib.common.fiducials.charuco_diamonds import CharucoDiamonds

LOG = lib.common.logging.get_logger(__name__)

Point = Tuple[float, float]
IntPoint = Tuple[int, int]


@unique
class FiducialStrategy(IntEnum):

    ARUCO: "FiducialStrategy" = cast("FiducialStrategy", 1)

    CHARUCO: "FiducialStrategy" = cast("FiducialStrategy", 2)

    CHARUCO_ELSE_ARUCO: "FiducialStrategy" = cast("FiducialStrategy", 3)  # default

    def __str__(self) -> str:
        """
        Override default string formatting to just print the name
        """
        return str(self.name)


class FiducialFrame(abc.ABC):
    """
    Wraps all known coordinate system state and fiducials information into a single object.
    This object implicitly knows what type of devices and corresponding coordinate systems
    are running.

    Fiducial information is based on either ArUco markers or ChArUco diamonds.

    This class should be immutable.

    Designed to be used during calibration in a producer/consumer model.
    """

    def __init__(self, *, index: int, predict_index: int, servos: IntPoint, target: IntPoint):
        self.index: int = index  # unique index
        self.predict_index = predict_index
        self.servos: IntPoint = servos  # the servo position
        self.target: IntPoint = target  # the target crosshair

    @abc.abstractmethod
    def interpolate_predict_coords(self) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in predict space.
        """
        pass

    @abc.abstractmethod
    def interpolate_target(self, other: "FiducialFrame", fallback: bool = False) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in the other frame.
        """
        pass


class ArucoMarkersFrame(FiducialFrame):
    """
    An instance of FiducialFrame based on ArUco markers.
    """

    def __init__(self, *, predict_markers: ArucoMarkers, target_markers: ArucoMarkers, **kwargs: Any):
        super().__init__(**kwargs)
        self.predict_markers: ArucoMarkers = predict_markers  # the ArUco markers seen by the predict camera
        self.target_markers: ArucoMarkers = target_markers  # the ArUco markers seen by the target camera

        # Locate nearest marker to target
        self.target_nearest_marker: Optional[ArucoMarkers] = target_markers.get_nearest(self.target)
        if self.target_nearest_marker is None:
            self.target_nearest_marker_id = None
            LOG.warning("No ArUco markers in frame{:04d}".format(self.index))
        else:
            self.target_nearest_marker_id = self.target_nearest_marker.ids[0][0]

    def interpolate_predict_coords(self) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in predict space by doing a perspective transform based on the
        marker corners of the marker closest to the target crosshair.

        If there are no markers in the target view, then None is returned. Likewise, if the closest marker is
        not seen in predict space, None is returned.
        """
        if self.target_nearest_marker_id is None:
            return ERROR_CALIBRATE_NO_MARKERS, None
        assert self.target_nearest_marker

        # Find closest marker to target crosshair, but look for it in predict space
        predict_marker = self.predict_markers.get(self.target_nearest_marker_id)
        if predict_marker is None:
            return ERROR_CALIBRATE_MISSING_MARKER, None

        # transform target crosshair from target coordinate system to predict coordinate system
        # via perspective transformation of nearest target marker corners
        return (
            0,
            ScannerFiducialsController.compute_apply_perspective_transform(
                self.target, self.target_nearest_marker, predict_marker
            ),
        )

    def interpolate_target(self, other: FiducialFrame, fallback: bool = False) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in the other frame by doing a perspective transform based on the
        marker corners of the marker closest to the target crosshair.

        If there are no markers in the target view, then None is returned. Likewise, if the closest marker is
        not seen in the other frame, None is returned.

        :type other: ArucoMarkersFrame
        :param fallback: should be False
        """
        assert isinstance(other, ArucoMarkersFrame)
        # TODO Consider same strategy as charuco, intersection then nearest
        assert not fallback, "fallback strategy not supported for ArucoMarkersFrame"
        nearest_marker_id = self.target_nearest_marker_id  # help mypy
        if nearest_marker_id is None:
            # bad frame
            return ERROR_CALIBRATE_NO_MARKERS, None

        # transform target crosshair from frame1 target coordinate system to frame2 target coordinate system
        other_marker = other.target_markers.get(nearest_marker_id)
        if other_marker is None:
            # This will happen frequently as we scan around target space
            return ERROR_CALIBRATE_MISSING_MARKER, None

        return (
            0,
            ScannerFiducialsController.compute_apply_perspective_transform(
                self.target, cast(ArucoMarkers, self.target_nearest_marker), other_marker
            ),
        )


class CharucoDiamondsFrame(ArucoMarkersFrame):
    """
    An instance of FiducialFrame based on ChArUco diamonds.
    """

    def __init__(self, *, predict_diamonds: CharucoDiamonds, target_diamonds: CharucoDiamonds, **kwargs: Any):
        super().__init__(**kwargs)

        self.predict_diamonds: CharucoDiamonds = predict_diamonds  # the ChArUco diamonds seen by the predict camera
        self.target_diamonds: CharucoDiamonds = target_diamonds  # the ChArUco diamonds seen by the target camera

        self.target_nearest_diamond = target_diamonds.get_nearest(self.target)
        if self.target_nearest_diamond is None:
            LOG.warning(
                "No nearest ChArUco diamond in frame{:04d} amongst {} corners".format(self.index, len(target_diamonds))
            )

    def interpolate_predict_coords(self) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in predict space by doing a perspective transform based on the
        marker corners of the marker closest to the target crosshair.

        If there are no markers in the target view, then None is returned. Likewise, if the closest marker is
        not seen in predict space, None is returned.
        """
        # TODO consider same strategy as target interpolation, intersection then nearest
        # this would solve the issue with 1 or 2 missing markers. Still failing the uniformity check, but if we go
        # through then the calibration will at least be good instead of garbage
        if self.target_nearest_diamond is None:
            return super().interpolate_predict_coords()

        # Find closest diamond to target crosshair, but look for it in predict space
        predict_nearest_corners_diamond = self.predict_diamonds.get_all(list(self.target_nearest_diamond.ids))
        if predict_nearest_corners_diamond is None:
            return ERROR_CALIBRATE_MISSING_DIAMOND, None
        if len(predict_nearest_corners_diamond) < len(self.target_nearest_diamond):
            LOG.warning(
                "Expected %d corners but found %d",
                len(self.target_nearest_diamond),
                len(predict_nearest_corners_diamond),
            )
            return ERROR_CALIBRATE_MISSING_PARTIAL_DIAMOND, None

        # transform target crosshair from target coordinate system to predict coordinate system
        # via perspective transformation of nearest target marker corners
        return (
            0,
            ScannerFiducialsController.compute_apply_perspective_transform(
                self.target, self.target_nearest_diamond, predict_nearest_corners_diamond,
            ),
        )

    def interpolate_target(self, other: FiducialFrame, fallback: bool = False) -> Tuple[int, Optional[Point]]:
        """
        Predict where the target crosshair is in the other frame by doing a perspective transform based on the
        diamond corners of the four closest diamond corners to the target crosshair.

        If there are no markers in the target view, then None is returned. Likewise, if the closest marker is
        not seen in the other frame, None is returned.

        If fallback is enabled and the interpolation is not possible using diamond corners, then aruco
        markers will be used instead.

        :param other: the frame with which to interpolate
        :param fallback: whether to try interpolation with aruco markers if using charuco diamonds doesn't work
        """
        assert isinstance(other, CharucoDiamondsFrame)
        current_matching_diamonds = self.target_diamonds.get_all(list(other.target_diamonds.ids))
        if current_matching_diamonds is None:
            if fallback:
                return self._interpolate_target_fallback(other)
            return ERROR_CALIBRATE_NO_DIAMONDS, None

        current_nearest_diamond = current_matching_diamonds.get_nearest(self.target)

        if current_nearest_diamond is None:
            if fallback:
                return self._interpolate_target_fallback(other)
            return ERROR_CALIBRATE_NO_DIAMONDS, None

        other_nearest_diamond = other.target_diamonds.get_all(list(current_nearest_diamond.ids))
        if other_nearest_diamond is None:
            if fallback:
                return self._interpolate_target_fallback(other)
            return ERROR_CALIBRATE_MISSING_DIAMOND, None

        if len(other_nearest_diamond) < 4:
            if fallback:
                return self._interpolate_target_fallback(other)
            return ERROR_CALIBRATE_MISSING_PARTIAL_DIAMOND, None

        return (
            0,
            ScannerFiducialsController.compute_apply_perspective_transform(
                self.target, current_nearest_diamond, other_nearest_diamond
            ),
        )

    def _interpolate_target_fallback(self, other: ArucoMarkersFrame) -> Tuple[int, Optional[Point]]:
        """
        Calls super().interpolate_target() and logs.
        """
        ret, coords = super().interpolate_target(other)
        if ret == 0:
            LOG.debug(
                "Recovered frame pair (%d, %d) via ArUco marker fallback", self.index, other.index,
            )
        return ret, coords


class ServoImageFrame:
    """
    Wraps all known coordinate system state and predict/target images into a single object.
    This object implicitly knows what type of devices and corresponding coordinate systems
    are running.

    This class should be immutable.

    Designed to be used during calibration in a producer/consumer model.
    """

    def __init__(self, *, index: int, servos: IntPoint, target: IntPoint, target_image: npt.NDArray[Any]):
        self.index: int = index  # unique index
        assert self.index is not None

        self.servos: IntPoint = servos  # the servo position
        assert self.servos is not None

        self.target: IntPoint = target  # the target crosshair
        assert self.target is not None

        self.target_image: npt.NDArray[Any] = target_image  # the fiducials seen by the target camera
        assert self.target_image is not None
