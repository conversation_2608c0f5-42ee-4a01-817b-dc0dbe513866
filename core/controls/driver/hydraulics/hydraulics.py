import abc
import math
from typing import Dict, List, Tuple, cast

import numpy as np

import lib.common.logging
from core.model.actuator import Actuator
from lib.common.error import MakaException

LOG = lib.common.logging.get_logger(__name__)


def pretty_angle_str(position: float) -> str:
    return f"{Hydraulics.transform_steer_to_frame_angle_deg(position):.2f}° ({position:.2f})"


class UnreachableHydraulicRotaryAngleException(MakaException):
    pass


class Hydraulics(Actuator):
    """
    A container object for various hydraulics.

    v1: For now, pretty much all driving logic is in here. The API is pretty
    simple as there are only about 3 things we can do while driving.

    Future: As we introduce more and different hydraulic systems, we will
    capture that similarity and split them out in into first class nodes
    themselves.
    """

    @property
    def pc_control(self) -> bool:
        return True

    @property
    @abc.abstractmethod
    def forward(self) -> float:
        """
        Return a float representing the "forward" state of the wheels. This
        will be the same for all wheels.
        """
        pass

    @property
    @abc.abstractmethod
    def rotaries(self) -> List[str]:
        """
        Return a list of rotaries.
        """
        pass

    @property
    @abc.abstractmethod
    def rotary_position_frame_angles_deg(self) -> Dict[str, float]:
        """
        Return a dictionary of the frame angle of each hydraulic leg.
        """
        pass

    @property
    @abc.abstractmethod
    def rotary_limits_frame_angles_deg(self) -> Dict[str, Tuple[float, float]]:
        """
        Return a dictionary of the reachable limits of the hydraulic rotary.
        """
        pass

    @property
    def wheels(self) -> List[str]:
        """
        Return a list of wheels.

        A rotary is different from a wheel, but generally can take on the same
        names within their own namespace.

        So the default implementation returns the same as the list of rotaries
        """
        return self.rotaries

    @abc.abstractmethod
    def drive(self, speed_mph: float) -> None:
        """
        Set all the wheels to the given speed in mph.

        Positive means turn in the currently specified direction for each wheel.
        Negative means turn in opposite direction.
        """
        pass

    @abc.abstractmethod
    def set_rotary_position(self, *, rotary: str, frame_angle_deg: float, log: bool = False) -> None:
        """
        Set the hydraulic rotary to the given angle.

        Zero is along the y-axis, heading 0.
        Positive means counterclockwise, looking down from above.
        Negative means clockwise, looking down from above.
        """
        pass

    @abc.abstractmethod
    def notify_positive_control(self) -> None:
        """
        Notifies hydraulic system that we have positive control, preventing deadman activation.
        """
        pass

    # TRANSFORMATION FUNCTIONS

    @staticmethod
    def transform_steer_to_frame_angle_deg(steer: float) -> float:
        # steer is [-1, 1] from left to right
        # frame_angle is counter clock wise
        # So flip sign and scaled by 90
        return -steer * 90

    @staticmethod
    def transform_frame_angle_deg_to_steer(steer: float) -> float:
        # steer is [-1, 1] from left to right
        # frame_angle is counter clock wise
        # So flip sign and scaled by 90
        return -steer / 90

    def steerable_range_factor(self) -> float:
        # passing in a steer of 1 needs to be adjusted to the allowable steering range.
        # This factor represents a tightening of the allowable steering range.
        #
        # For instance, to limit steering between +/-40, we use 2*abs(40) / 180.
        #
        # Value of +/- 40 is chosen so that we have 5 degree of buffer to not push
        # into the mounting plate.
        return 80 / 180

    def transform_frame_angle_deg_to_ackermann(
        self, frame_angle_deg: float, wheelbase_length_in: float, track_width_in: float
    ) -> float:
        sign = np.sign(frame_angle_deg)
        frame_angle_abs_rad = math.radians(abs(frame_angle_deg))

        desired_angle_abs_rad = math.atan(
            wheelbase_length_in / (wheelbase_length_in / math.tan(frame_angle_abs_rad) + track_width_in)
        )
        return cast(float, sign * math.degrees(desired_angle_abs_rad))
