from enum import Enum
from typing import Optional

from core.controls.driver.localization.geofence import RobotBodyGeo
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from lib.common.geo.boundary import BoundaryType
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel, Topic

LOG = get_logger(__name__)


class DriverLocationState(str, Enum):
    END_OF_ROW_TURNAROUND = "END_OF_ROW_TURNAROUND"
    IN_BOUNDS = "IN_BOUNDS"
    LOST_GPS = "LOST_GPS"
    OUT_OF_BOUNDS = "OUT_OF_BOUNDS"

    def is_safe(self) -> bool:
        return self in [DriverLocationState.END_OF_ROW_TURNAROUND, DriverLocationState.IN_BOUNDS]


DriverLocationStateSubscription = Optional[SubscriberChannel[DriverLocationState]]


class DriverLocationStateMachine:
    def __init__(self) -> None:
        self._state = DriverLocationState.LOST_GPS

    @staticmethod
    def log_info_ifne(old: DriverLocationState, new: DriverLocationState, msg: str) -> None:
        if old != new:
            LOG.info(msg)

    @staticmethod
    def log_warning_ifne(old: DriverLocationState, new: DriverLocationState, msg: str) -> None:
        if old != new:
            LOG.warning(msg)

    def update(
        self, gps: Optional[GeopositionLatLonAltMessage], body_geo: Optional[RobotBodyGeo]
    ) -> DriverLocationState:
        previous_state = self._state

        # no GPS available
        if gps is None or gps.lat == 0 or gps.lon == 0 or body_geo is None:
            self._state = DriverLocationState.LOST_GPS
            DriverLocationStateMachine.log_warning_ifne(
                previous_state, self._state, f"{self._state} Driver lost GPS: {gps}"
            )
            return self._state

        # Check force field first
        in_safe_geo = True
        for matches in [gm.matched for gm in body_geo.force_field_geomatches]:
            # this force field node is out of bounds
            if BoundaryType.FIELD not in [m.type for m in matches]:
                in_safe_geo = False
        if not in_safe_geo:
            self._state = DriverLocationState.OUT_OF_BOUNDS
            DriverLocationStateMachine.log_warning_ifne(
                previous_state, self._state, f"{self._state} Driver out of bounds: {gps}"
            )
            return self._state

        if BoundaryType.TURN_AROUND in [m.type for m in body_geo.robot_geomatch.matched]:
            self._state = DriverLocationState.END_OF_ROW_TURNAROUND
            DriverLocationStateMachine.log_info_ifne(
                previous_state, self._state, f"{self._state} Driver end of row turnaround: {gps}"
            )

        else:
            self._state = DriverLocationState.IN_BOUNDS
            DriverLocationStateMachine.log_info_ifne(
                previous_state, self._state, f"{self._state} Driver in bounds: {gps}"
            )

        return self._state


class DriverLocationStateChannel(SubscriberChannel[DriverLocationState]):
    def __init__(
        self,
        topic: Topic,
        geoposition_lla_subscription: SubscriberChannel[GeopositionLatLonAltMessage],
        body_geo_subscription: SubscriberChannel[RobotBodyGeo],
        no_gps: bool = False,
    ):
        super().__init__(topic)
        self._geoposition_lla_subscription = geoposition_lla_subscription
        self._body_geo_subscription = body_geo_subscription
        self._state_machine = DriverLocationStateMachine()
        self._no_gps = no_gps

    def read(self) -> Optional[DriverLocationState]:
        if self._no_gps:
            return DriverLocationState.IN_BOUNDS
        return self._state_machine.update(self._geoposition_lla_subscription.read(), self._body_geo_subscription.read())
