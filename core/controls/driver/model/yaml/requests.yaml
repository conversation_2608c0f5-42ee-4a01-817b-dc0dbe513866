auto_drive_request:

    compose:
      - timestamp

    fields:
      source:
        type: string
        required: true

      plan:
        type: string
        required: true

      datum:
        type: float
        required: false


stop_request:


manual_drive_request:

    compose:
      - timestamp

    fields:
      source:
        type: string
        required: true

      forward:
        type: float
        required: false
        validations:
          - unit_interval_inclusive

      steer:
        type: float
        required: false
        validations:
          - unit_interval_inclusive


update_drive_controller_request:

  compose:
    - timestamp

  fields:
    controller:
      type: string
      required: true


update_speed_limit_pct_request:

    compose:
        - timestamp

    fields:
      source:
        type: string
        required: true

      speed_limit_pct:
        type: float
        required: true
        validations:
          - positive
