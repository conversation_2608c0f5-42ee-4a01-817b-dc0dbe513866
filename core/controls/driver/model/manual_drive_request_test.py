from typing import Any, Dict, cast

import pytest

from generated.core.controls.driver.model.manual_drive_request import ManualDriveRequest
from generated.core.controls.driver.model.protobuf.requests_pb2 import ManualDriveRequestProto
from lib.common.time.time import maka_control_timestamp_ms


@pytest.fixture(scope="module")
def manual_drive_request() -> ManualDriveRequest:
    result: ManualDriveRequest = ManualDriveRequest(
        forward=0.5, steer=-0.5, source="test", timestamp_ms=maka_control_timestamp_ms()
    )
    # ensure uniqueness when testing serde
    assert result.forward != result.steer
    assert result.forward != result.timestamp_ms
    assert result.steer != result.timestamp_ms

    return result


def test_eq_ne(manual_drive_request: ManualDriveRequest) -> None:
    assert manual_drive_request != object()
    assert manual_drive_request == manual_drive_request

    # different forward
    other = ManualDriveRequest(
        forward=-cast(float, manual_drive_request.forward),
        steer=manual_drive_request.steer,
        source=manual_drive_request.source,
        timestamp_ms=manual_drive_request.timestamp_ms,
    )
    assert other != manual_drive_request

    # different steer
    other2 = ManualDriveRequest(
        forward=manual_drive_request.forward,
        steer=-cast(float, manual_drive_request.steer),
        source=manual_drive_request.source,
        timestamp_ms=manual_drive_request.timestamp_ms,
    )
    assert other2 != manual_drive_request

    # different source
    other3 = ManualDriveRequest(
        forward=manual_drive_request.forward,
        steer=manual_drive_request.steer,
        source=manual_drive_request.source + "2",
        timestamp_ms=manual_drive_request.timestamp_ms,
    )
    assert other3 != manual_drive_request

    # different timestamp_ms
    other4 = ManualDriveRequest(
        forward=manual_drive_request.forward,
        steer=manual_drive_request.steer,
        source=manual_drive_request.source,
        timestamp_ms=manual_drive_request.timestamp_ms + 1,
    )
    assert other4 != manual_drive_request


def test_manual_drive_request_to_from_json(manual_drive_request: ManualDriveRequest) -> None:
    # to json and back
    to_json: Dict[str, Any] = manual_drive_request.to_json()
    from_json = ManualDriveRequest.from_json(to_json)
    assert manual_drive_request == from_json

    # to json and back again
    #
    # for some reason this fails with
    # >  E       ValueError: Protocol message ManualDriveRequestProto has no "forward" field.
    # if you use ParseDict when deserializing inside the from_json implementation
    # ...but only the second time around
    #
    to_json_again = from_json.to_json()
    from_json_again = ManualDriveRequest.from_json(to_json_again)
    assert manual_drive_request == from_json_again
    str(from_json)


def test_init_manual_drive_request_proto(manual_drive_request: ManualDriveRequest) -> None:
    proto = ManualDriveRequestProto(
        forward=manual_drive_request.forward,
        steer=manual_drive_request.steer,
        source=manual_drive_request.source,
        timestamp_ms=manual_drive_request.timestamp_ms,
    )
    assert manual_drive_request == ManualDriveRequest.from_proto(proto)


def test_to_str(manual_drive_request: ManualDriveRequest) -> None:
    as_str = manual_drive_request.to_str()
    assert str(manual_drive_request) == as_str
    from_str = ManualDriveRequest.from_str(as_str)
    assert manual_drive_request == from_str


def test_manual_drive_request_to_from_proto(manual_drive_request: ManualDriveRequest) -> None:
    # to proto and back
    to_proto: ManualDriveRequestProto = manual_drive_request.to_proto()
    from_proto = ManualDriveRequest.from_proto(to_proto)
    assert manual_drive_request == from_proto

    # to proto and back again
    to_proto_again: ManualDriveRequestProto = from_proto.to_proto()
    from_proto_again = ManualDriveRequest.from_proto(to_proto_again)
    assert manual_drive_request == from_proto_again

    # now to json and back
    to_json: Dict[str, Any] = manual_drive_request.to_json()
    from_json = ManualDriveRequest.from_json(to_json)
    assert manual_drive_request == from_json
