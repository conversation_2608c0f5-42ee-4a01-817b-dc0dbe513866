from typing import Any, Dict, Optional, cast

from core.controls.driver.hydraulics.hydraulics import Hydraulics
from core.controls.driver.rotary_encoder import RotaryEncoderNode
from core.model.actuator import Actuator
from core.model.node.type import NodeType
from core.model.path import DevicePath, RelativeInstance
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class DriveSystem(Actuator):
    """
    The Drive system is primarily a container node for other nodes that  directly contribute to the movement of the robot body.
    This includes electric drive motors and hydraulic components like rotaries, torque dividers, etc.
    """

    def notify_positive_control(self) -> None:
        if self.hydraulics is not None:
            self.hydraulics.notify_positive_control()

    def _cache_named_references(self) -> None:
        self._hydraulics: Optional[Hydraulics] = cast(Optional[Hydraulics], self.get_node(NodeType.HYDRAULICS))

        self._back_left_rotary_encoder: Optional[RotaryEncoderNode] = None
        self._back_right_rotary_encoder: Optional[RotaryEncoderNode] = None
        self._front_left_rotary_encoder: Optional[RotaryEncoderNode] = None
        self._front_right_rotary_encoder: Optional[RotaryEncoderNode] = None

        self._back_left_rotary_encoder = cast(
            Optional[RotaryEncoderNode], self.get_node(NodeType.ROTARY_ENCODER, instance=RelativeInstance.BACK_LEFT)
        )
        self._back_right_rotary_encoder = cast(
            Optional[RotaryEncoderNode], self.get_node(NodeType.ROTARY_ENCODER, instance=RelativeInstance.BACK_RIGHT)
        )
        self._front_left_rotary_encoder = cast(
            Optional[RotaryEncoderNode], self.get_node(NodeType.ROTARY_ENCODER, instance=RelativeInstance.FRONT_LEFT)
        )
        self._front_right_rotary_encoder = cast(
            Optional[RotaryEncoderNode], self.get_node(NodeType.ROTARY_ENCODER, instance=RelativeInstance.FRONT_RIGHT)
        )

        self._rotary_encoders = {
            m.device_path: m
            for m in [
                self._back_left_rotary_encoder,
                self._back_right_rotary_encoder,
                self._front_left_rotary_encoder,
                self._front_right_rotary_encoder,
            ]
            if m is not None
        }

    @property
    def hydraulics(self) -> Optional[Hydraulics]:
        return self._hydraulics

    @property
    def rotary_encoders(self) -> Dict[DevicePath, RotaryEncoderNode]:
        return self._rotary_encoders

    def status_callback(self) -> Dict[str, Any]:
        result: Dict[str, Dict[str, Any]] = {}
        if self._hydraulics is not None:
            forward = self._hydraulics.forward
            rotary_positions_frame_angles_deg = self._hydraulics.rotary_position_frame_angles_deg
            rotary_limits_frame_angles_deg = self._hydraulics.rotary_limits_frame_angles_deg
            result = {
                leg: {
                    "forward": forward,
                    "position_frame_angle_deg": rotary_positions_frame_angles_deg.get(leg, 0),
                    "limits_frame_angle_deg": rotary_limits_frame_angles_deg.get(leg, (0, 0)),
                }
                for leg in RelativeInstance.quadrants()
            }

        return result

    def stop_motors(self, log_prefix: str = "") -> None:
        LOG.info(f"{log_prefix}STOP MOTORS!")
        if self._hydraulics is not None:
            self._hydraulics.drive(0)
