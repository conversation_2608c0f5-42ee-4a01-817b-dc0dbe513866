import math
from typing import TYPE_CHECKING, Any, Optional, Tuple, cast

import cv2
import numpy as np

from core.controls.annotate import DRIVER_LAYER
from core.controls.driver.driver_error import DriverError
from core.controls.driver.plan.instruction import (
    DriveAngleInstruction,
    DriveInstruction,
    DriverInstruction,
    StopInstruction,
    TargetAngleInstruction,
)
from core.controls.frame.mechanical import DriveCamMechanicalConfig
from core.controls.plan.instruction import TerminateInstruction
from core.controls.plan.state.info import PlanInfo
from core.cv.retina.camera.node import Cam
from lib.common.annotate import (
    BGR_BLUE,
    BGR_GREEN,
    BGR_GREY,
    BGR_PURPLE,
    BGR_RED,
    BGR_WHITE,
    BGR_YELLOW,
    Annotation,
    Annotator,
    annotate_text_line,
    draw_text,
    format_float_text,
    format_time_text,
)
from lib.common.image import CamImage
from lib.common.logging import get_logger
from lib.common.math import rad2deg
from lib.common.pid.pid import PIDControl
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed

if TYPE_CHECKING:
    from core.controls.driver.driver import Driver

LOG = get_logger(__name__)

_TEXT_FONT = cv2.FONT_HERSHEY_COMPLEX_SMALL

# How much center line to draw
CENTER_LINE_PCT = 15


class DriverAnnotator(Annotator):
    """
    Annotator for the Driver. Pretty straightforward.
    """

    def __init__(self, feed: Feed, driver: "Driver"):
        self._driver = driver
        self._feed = feed

        self._annotation_name_gear = "{}/gear".format(self._driver.id)
        self._annotation_name_location_state = "{}/location_state".format(self._driver.id)
        self._annotation_name_plan_execution_state = "{}/plan_execution_state".format(self._driver.id)
        self._annotation_name_last_drive_instruction = "{}/last_drive_instruction".format(self._driver.id)
        self._annotation_name_plan_status = "{}/plan_status".format(self._driver.id)
        self._annotation_name_plan_run_data = "{}/plan_run_data".format(self._driver.id)
        self._annotation_name_center_line = "{}/center_line".format(self._driver.id)
        self._annotation_name_pids = "{}/pids".format(self._driver.id)
        self._annotation_name_tracking_line = "{}/tracking_line".format(self._driver.id)
        self._annotation_target_mph = f"{self._driver.id}/target_mph"

    def attach_standard_annotations(self, cam: Cam) -> None:
        layer = DRIVER_LAYER
        cam.add_annotation(Annotation(name=self._annotation_name_gear, layer=layer + 1, f=self.annotate_gear))
        cam.add_annotation(
            Annotation(
                name=self._annotation_name_plan_execution_state, layer=layer + 3, f=self.annotate_plan_execution_state
            )
        )
        cam.add_annotation(
            Annotation(
                name=self._annotation_name_last_drive_instruction,
                layer=layer + 4,
                f=self.annotate_last_drive_instruction,
            )
        )
        cam.add_annotation(
            Annotation(name=self._annotation_name_plan_status, layer=layer + 5, f=self.annotate_plan_status)
        )
        cam.add_annotation(
            Annotation(name=self._annotation_name_plan_run_data, layer=layer + 6, f=self.annotate_plan_run_data)
        )
        cam.add_annotation(
            Annotation(name=self._annotation_name_center_line, layer=layer + 7, f=self.annotate_center_line)
        )
        cam.add_annotation(
            Annotation(name=self._annotation_name_tracking_line, layer=layer + 9, f=self.annotate_tracking_line)
        )
        cam.add_annotation(Annotation(name=self._annotation_target_mph, layer=layer + 10, f=self.annotate_target_mph))

    def attach(self) -> None:
        """
        Attach annotation functions to the cameras for this Driver.
        """
        if self._driver.front_left_camera is not None:
            self.attach_standard_annotations(self._driver.front_left_camera)
        if self._driver.front_right_camera is not None:
            self.attach_standard_annotations(self._driver.front_right_camera)
        if self._driver.back_left_camera is not None:
            self.attach_standard_annotations(self._driver.back_left_camera)
        if self._driver.back_right_camera is not None:
            self.attach_standard_annotations(self._driver.back_right_camera)

    def annotate_center_line(self, cam_image: CamImage) -> CamImage:
        drive_cam_mech_cfg = self._feed.subscribe(
            topic=Topic(f"/frame/mechanical/drive_cam/{cam_image.camera_id}"), msg_type=DriveCamMechanicalConfig
        ).read()
        assert drive_cam_mech_cfg is not None

        image = cam_image.image_bgr
        x = int(image.shape[1] / 2) + drive_cam_mech_cfg.x_offset_px
        y_start = 0
        y_steps = 50
        y_step_size = int(image.shape[1] / y_steps)
        height = image.shape[0]
        angle_offset_deg_tan = math.tan(drive_cam_mech_cfg.angle_offset_deg / 180 * math.pi)
        for i in [i for i in range(y_steps) if i % 2 == 0]:
            y1 = y_start + y_step_size * i
            x1 = int(x + (height - y1) * angle_offset_deg_tan)
            y2 = y_start + y_step_size * (i + 1)
            x2 = int(x + (height - y2) * angle_offset_deg_tan)
            cv2.line(
                image, (x1, y1), (x2, y2), color=BGR_RED, thickness=4,
            )
        return cam_image

    def annotate_tracking_line(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        plan_info: Optional[PlanInfo] = self._driver.plan_info
        if plan_info is None:
            return cam_image

        # tracking line
        driver_error: Optional[DriverError] = cast(DriverError, plan_info.run_data.get("driver_error"))
        if driver_error is not None:
            cv2.line(
                image,
                cam_image.center_xy,
                (int(cam_image.center_xy[0] + driver_error.error_x_pct * cam_image.width), cam_image.center_xy[1]),
                color=BGR_WHITE,
                thickness=2,
            )

        return cam_image

    def annotate_pids(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr

        # if no plan going on, don't annotate auto drive PIDs
        plan_info: Optional[PlanInfo] = self._driver.plan_info
        if plan_info is None:
            return cam_image

        if self._driver.controller.target_pid:
            self._annotate_pid(image, self._driver.controller.target_pid, 150)

        return cam_image

    def _annotate_pid(self, image: np.ndarray, pid: PIDControl, yoff: float) -> None:
        w = 28 * 10
        y = image.shape[0] - yoff
        y2 = image.shape[0] - yoff + 40
        x = image.shape[1] - 3 * w

        Kp, Ki, Kd = pid.gain_values()
        # P
        cv2.putText(image, f"{' ' if Kp >= 0 else ''}{Kp:.6f}", (x, y), _TEXT_FONT, 1.5, BGR_RED, 2)
        cv2.putText(image, f"{' ' if pid.Cp >= 0 else ''}{pid.Cp:.6f}", (x, y2), _TEXT_FONT, 1.5, BGR_RED, 2)
        # I
        cv2.putText(image, f"{' ' if Ki >= 0 else ''}{Ki:.6f}", (x + w, y), _TEXT_FONT, 1.5, BGR_GREEN, 2)
        cv2.putText(image, f"{' ' if pid.Ci >= 0 else ''}{pid.Ci:.6f}", (x + w, y2), _TEXT_FONT, 1.5, BGR_GREEN, 2)
        # D
        cv2.putText(image, f"{' ' if Kd >= 0 else ''}{Kd:.6f}", (x + 2 * w, y), _TEXT_FONT, 1.5, BGR_BLUE, 2)
        cv2.putText(image, f"{' ' if pid.Cd >= 0 else ''}{pid.Cd:.6f}", (x + 2 * w, y2), _TEXT_FONT, 1.5, BGR_BLUE, 2)

    def annotate_gear(self, cam_image: CamImage) -> CamImage:
        annotate_text_line(cam_image.image_bgr, text=self._driver.gear, y_index=1, color=BGR_YELLOW)
        return cam_image

    def annotate_location_state(self, cam_image: CamImage) -> CamImage:
        annotate_text_line(cam_image.image_bgr, text=self._driver.location_state, y_index=2, color=BGR_YELLOW)
        return cam_image

    def annotate_plan_execution_state(self, cam_image: CamImage) -> CamImage:
        annotate_text_line(
            cam_image.image_bgr,
            text=f"{self._driver.plan.program_name}: {self._driver.plan.execution_state}",
            y_index=3,
            color=BGR_YELLOW,
        )
        return cam_image

    def annotate_plan_status(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        plan_info: Optional[PlanInfo] = self._driver.plan_info
        if plan_info is None:
            return cam_image

        annotate_text_line(
            image, text=plan_info.status_text, y_index=4, color=BGR_YELLOW,
        )
        return cam_image

    def annotate_target_mph(self, cam_image: CamImage) -> CamImage:
        for i, text in enumerate(self._driver.controller.annotate_debug_text()):
            draw_text(cam_image.image_bgr, y_index=i, text=text, x_center=True)

        return cam_image

    def _color(self, instruction: DriverInstruction) -> Tuple[int, int, int]:
        return {"stop": BGR_RED, "drive_angle": BGR_BLUE, "target_angle": BGR_PURPLE, "no_op": BGR_GREY}.get(
            instruction.msg_type, BGR_YELLOW
        )

    def _text(self, instruction: DriverInstruction) -> str:
        text = format_time_text(timestamp_ms=instruction.timestamp_ms, ymd=False)
        text = f"{text} {instruction.msg_type}"
        if isinstance(instruction, DriveInstruction):
            text = (
                f"{text}(fwd={format_float_text(instruction.forward, decimals=3)}, "
                f"str={format_float_text(instruction.steer, decimals=3)}"
            )
            if instruction.actual_velocity_mph is not None:
                text += f", v={format_float_text(instruction.actual_velocity_mph)},"
            text += ")"

        elif isinstance(instruction, DriveAngleInstruction):
            error_angle_text = str(round(rad2deg(instruction.error_angle_rad), 2))
            error_vel_text = (
                str(round(instruction.error_velocity_mph, 4)) if instruction.error_velocity_mph is not None else "--"
            )
            text = f"{text}(a={error_angle_text}, v={error_vel_text})"

        elif isinstance(instruction, TargetAngleInstruction):
            error_angle_text = str(round(rad2deg(instruction.error_angle_rad), 2))
            error_vel_text = (
                str(round(instruction.error_velocity_mph, 4)) if instruction.error_velocity_mph is not None else "--"
            )
            direction_text = str(int(instruction.direction))
            text = f"{text}(a={error_angle_text}, v={error_vel_text}, d={direction_text})"

        elif isinstance(instruction, (StopInstruction, TerminateInstruction)):
            text = f'{text}("{instruction.reason}")'

        elif len(instruction.msg) > 0:
            text = f"{text}({str(list(instruction.msg.values()))[1:-1]})"

        return text

    def annotate_last_drive_instruction(self, cam_image: CamImage) -> CamImage:
        index = 5
        i = 0
        for d in list(self._driver.last_instructions):
            annotate_text_line(cam_image.image_bgr, text=self._text(d), y_index=index + i, color=self._color(d))
            i += 1
        return cam_image

    def annotate_plan_run_data(self, cam_image: CamImage) -> CamImage:
        image = cam_image.image_bgr
        plan_info: Optional[PlanInfo] = self._driver.plan_info
        if plan_info is None:
            return cam_image

        # global run data
        index = 10
        if len(plan_info.run_data) > 0:
            annotate_text_line(image, text="----------", y_index=index, color=BGR_YELLOW)
            index += 1
            for k, v in sorted(plan_info.run_data.items()):
                self._annotate_plan_run_date_item(cam_image, k, v, index)
                index += 1

        # delineation for humans to see what's going on
        annotate_text_line(image, text="----------", y_index=index, color=BGR_YELLOW)
        index += 1

        # local run data
        for k, v in sorted(plan_info.run_data_local.items()):
            self._annotate_plan_run_date_item(cam_image, k, v, index)
            index += 1

        return cam_image

    def _annotate_plan_run_date_item(self, cam_image: CamImage, key: str, value: Any, y_index: int) -> None:
        if isinstance(value, DriverError):
            e1 = format_float_text(value.error_angle_rad, positive_symbol="+") + "deg"
            e2 = format_float_text(value.error_velocity_mph, positive_symbol="+") + "mph"
            e3 = format_float_text(100 * value.error_x_pct, decimals=2, positive_symbol="+") + "%x"
            age = value.timestamp_ms - cam_image.timestamp_ms
            annotate_text_line(
                cam_image.image_bgr, text=f"{key}: {e1}, {e2}, {e3}, {age}ms", y_index=y_index, color=BGR_YELLOW,
            )
        elif isinstance(value, (int, float, str)):
            annotate_text_line(cam_image.image_bgr, text=f"{key}: {value}", y_index=y_index, color=BGR_YELLOW)
        else:
            annotate_text_line(
                cam_image.image_bgr, text=f"{key}: {value.__class__.__name__}", y_index=y_index, color=BGR_YELLOW
            )
