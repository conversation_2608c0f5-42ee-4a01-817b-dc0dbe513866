from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from core.controls.driver.gear import Gear
from core.controls.driver.limiter.speed_limiter import SpeedLimiter
from core.controls.driver.parameters import DriverParameter, DriverParameters
from generated.core.controls.driver.model.manual_drive_request import ManualDriveRequest
from lib.common.angle_direction import AngleDirection
from lib.common.annotate import Text
from lib.common.logging import get_logger
from lib.common.pid.pid import PIDControl
from lib.common.serialization.json import JsonSerializable
from lib.common.time import Timestamp

LOG = get_logger(__name__)

DRIVE_CONFIG_CONTROL_VALUES = "control_values"


class DriveControllerTick(JsonSerializable, Timestamp):
    def __init__(
        self,
        timestamp_ms: int,
        gear: Gear,
        actual_velocity_mph: Optional[float] = None,
        error_angle_rad: Optional[float] = None,
        error_velocity_mph: Optional[float] = None,
        forward: Optional[float] = None,
        steer_frame_angle_deg: Optional[float] = None,
        last_request: Optional[str] = None,
    ):

        self._timestamp_ms: int = timestamp_ms
        self._gear: Gear = gear

        self._actual_velocity_mph = actual_velocity_mph
        self._error_angle_rad = error_angle_rad
        self._error_velocity_mph = error_velocity_mph
        self._last_request = last_request

        # get updated later
        self.forward: Optional[float] = forward
        self.steer_frame_angle_deg: Optional[float] = steer_frame_angle_deg

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def gear(self) -> Gear:
        return self._gear

    @property
    def actual_velocity_mph(self) -> Optional[float]:
        return self._actual_velocity_mph

    @property
    def error_angle_rad(self) -> Optional[float]:
        return self._error_angle_rad

    @property
    def error_velocity_mph(self) -> Optional[float]:
        return self._error_velocity_mph

    @property
    def last_request(self) -> Optional[str]:
        return self._last_request

    def to_json(self) -> Dict[str, Any]:
        return {
            "timestamp_ms": self.timestamp_ms,
            "gear": self.gear.apply(),
            "actual_velocity_mph": self.actual_velocity_mph,
            "error_angle_rad": self.error_angle_rad,
            "error_velocity_mph": self.error_velocity_mph,
            "forward": self.forward,
            "steer_frame_angle_deg": self.steer_frame_angle_deg,
            "last_request": self.last_request,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "DriveControllerTick":
        gear_ratio = data["gear"]
        gear = Gear.FWD if gear_ratio > 0 else Gear.REV if gear_ratio < 0 else Gear.NEUTRAL
        return DriveControllerTick(
            timestamp_ms=data["timestamp_ms"],
            gear=gear,
            actual_velocity_mph=data["actual_velocity_mph"],
            error_angle_rad=data["error_angle_rad"],
            error_velocity_mph=data["error_velocity_mph"],
            forward=data["forward"],
            steer_frame_angle_deg=data["steer_frame_angle_deg"],
            last_request=data["last_request"],
        )


class DriveController(ABC):
    """
    A drive controller wraps actuators controlling movement, direction, and
    steering. It provides an interface for handling standardized drive
    requests, including automated driving.

    Generally, drive motors can run in either "direction", movement can be
    controlled by 2 or 4 motors, and steering can be controlled by 2+ actuators

    Examples:
        (2WD) Differential Drive (implied casters)]
        4WD 4 wheel steer
    """

    def __init__(
        self, name: str, gear: Gear, speed_limiter: SpeedLimiter, autodrive_params: DriverParameters,
    ):
        self._name: str = name
        self._gear = gear
        self._speed_limiter: SpeedLimiter = speed_limiter
        self._autodrive_params: DriverParameters = autodrive_params

        # pids initialization
        self._target_pid = PIDControl(
            Kp=lambda: autodrive_params[DriverParameter.PID_TARGET_GAIN_P].float_value,
            Ki=lambda: autodrive_params[DriverParameter.PID_TARGET_GAIN_I].float_value,
            Kd=lambda: autodrive_params[DriverParameter.PID_TARGET_GAIN_D].float_value,
        )
        LOG.debug(
            f"{self._name} Loaded target PID values: "
            + f"P: {self._target_pid.Kp}, I: {self._target_pid.Ki}, D: {self._target_pid.Kd}"
        )

    @property
    def name(self) -> str:
        return self._name

    @property
    def pc_control(self) -> bool:
        return True

    @property
    @abstractmethod
    def last_tick(self) -> DriveControllerTick:
        pass

    ##########################################################################################################
    # Gearing
    ##########################################################################################################

    @property
    def gear(self) -> Gear:
        return self._gear

    def set_gear(self, gear: Gear) -> None:
        self._gear = gear

    ##########################################################################################################
    # Speed Limiter
    ##########################################################################################################

    @property
    def speed_limiter(self) -> SpeedLimiter:
        return self._speed_limiter

    ##########################################################################################################
    # PIDs
    ##########################################################################################################

    @property
    def target_pid(self) -> PIDControl:
        return self._target_pid

    def reset_pids(self) -> None:
        self._target_pid.reset()

    ##########################################################################################################
    # Abstract API
    ##########################################################################################################

    @abstractmethod
    def align_straight(self) -> None:
        """
        Align wheels straight
        """
        pass

    @abstractmethod
    def drive(self, *, forward: float, steer: float = 0, actual_velocity_mph: Optional[float] = None,) -> None:
        """
        Master Drive API.
        """
        pass

    @abstractmethod
    def drive_angle(
        self,
        *,
        error_angle_rad: float,
        direction: AngleDirection = AngleDirection.DIRECT,
        error_velocity_mph: float,
        actual_velocity_mph: float,
    ) -> None:
        """
        Drive to reduce angular and velocity error
        """
        pass

    @abstractmethod
    def handle_manual_drive_request(self, dreq: ManualDriveRequest) -> None:
        """
        Handle manual request, possibly bypassing limits only for unit testing
        """
        pass

    @abstractmethod
    def target_angle(
        self,
        *,
        error_angle_rad: float,
        direction: AngleDirection,
        error_velocity_mph: float,
        actual_velocity_mph: float,
    ) -> None:
        """
        Target to reduce angular error while also reducing velocity error
        """
        pass

    @abstractmethod
    def stop_vehicle(self) -> None:
        """
        Stop vehicle as soon as possible
        """
        pass

    ##########################################################################################################
    # Debug
    ##########################################################################################################

    def annotate_debug_text(self) -> List[Text]:
        return []
