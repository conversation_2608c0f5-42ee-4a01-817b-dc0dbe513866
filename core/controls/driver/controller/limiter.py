from typing import Callable, List, Optional, Union, cast

from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class Limiter:
    """
    Rate limiter. Provides a generic API to throttle the change in a number
    stream centered around 0.
    """

    INCREASE_MAGNITUDE = 0
    DECREASE_MAGNITUDE = 1
    INCREASE = 2
    DECREASE = 3

    def __init__(
        self,
        name: str,
        *,
        max_change_per_sec: Union[float, Callable[[], float]],
        directions: Optional[List[int]] = None,
    ):
        self._name: str = name
        self._max_change_per_sec: Callable[[], float]
        if isinstance(max_change_per_sec, (float, int)):
            self._max_change_per_sec = lambda: cast(float, max_change_per_sec)
        else:
            self._max_change_per_sec = max_change_per_sec
        self._directions: List[int] = (
            directions if directions is not None else [Limiter.INCREASE_MAGNITUDE, Limiter.DECREASE_MAGNITUDE]
        )

        self._last_val: float = 0
        self._last_tick: Optional[float] = None

    def reset(self) -> None:
        self._last_val = 0
        self._last_tick = None

    def limit(self, val: float) -> float:
        """
        Limit a stream of values. Should be called repeatedly on a stream of
        values to be limited.
        """
        assert val is not None

        # first time
        if self._last_tick is None:
            self._last_tick = maka_control_timestamp_ms()

        # remember re-entry variables, check clock
        prev_val = self._last_val
        prev_tick = self._last_tick
        now_tick = maka_control_timestamp_ms()
        self._last_tick = now_tick

        # easy case: no change
        if val == prev_val:
            return val

        # compute limit variables
        elapsed_ms = now_tick - prev_tick
        max_change = (elapsed_ms / 1000) * self._max_change_per_sec()

        result = val
        upper_val = min(val, prev_val + max_change)
        lower_val = max(val, prev_val - max_change)

        # test limits
        # Note: abstract this as needed. I feel this is still readable
        if (
            Limiter.INCREASE_MAGNITUDE in self._directions
            and abs(val) > abs(prev_val)
            and ((val > 0 and val > upper_val) or (val < 0 and val < lower_val))
        ):
            result = upper_val
        elif Limiter.INCREASE in self._directions and val > prev_val:
            result = upper_val
        elif Limiter.DECREASE_MAGNITUDE in self._directions and val < lower_val and abs(val) < abs(prev_val):
            result = lower_val
        elif Limiter.DECREASE in self._directions and val < prev_val:
            result = lower_val

        # update re-entry variable
        self._last_val = result

        if result != val:
            LOG.debug(f"{self._name} limited {val:.4f} --> {result:.4f}")

        return result
