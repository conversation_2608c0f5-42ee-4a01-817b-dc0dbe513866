import abc
import math
from typing import Any, List, Optional, cast

import numpy as np

from core.controls.driver.controller.controller import Drive<PERSON><PERSON>roller, DriveControllerTick
from core.controls.driver.controller.limiter import Limiter
from core.controls.driver.gear import Gear
from core.controls.driver.hydraulics.hydraulics import Hydraulics
from core.controls.driver.parameters import DriverParameter
from core.controls.frame.mechanical import MechanicalConfig
from core.model.path import RelativeInstance
from generated.core.controls.driver.model.manual_drive_request import ManualDriveRequest
from lib.common.angle_direction import AngleDirection
from lib.common.annotate import (
    BGR_BLUE,
    BGR_GREEN,
    BGR_GREEN_MEDIUM,
    BGR_GREY,
    BGR_RED,
    BGR_YELLOW,
    Text,
    format_float_text,
)
from lib.common.logging import get_logger
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

# TODO tunable limiters (UI elements + drive.json?)
# This is a second layer of control beyond the limiting in the firmware.
# We should connect this with the UI for a dynamic toggle of this behavior.
# After testing with these safety guards initially, we updated the firmware
# for the hydraulic system to handle this limiting at a lower level.
MAX_FORWARD_PER_SEC = 100
MAX_STEER_PER_SEC = 100

REAR_ROTARIES = [RelativeInstance.BACK_LEFT, RelativeInstance.BACK_RIGHT]
FRONT_ROTARIES = [RelativeInstance.FRONT_LEFT, RelativeInstance.FRONT_RIGHT]


class DriveStateException(Exception):
    pass


class BaseHydraulicsDriveController(DriveController, abc.ABC):
    """
    Base controller that knows how to control Hydraulics. Various, specific
    drive controllers can mostly be implemented via linear adjustments to this
    underlying controller (e.g. invert steering of rear wheels for crab vs
    snake drive)
    """

    def __init__(
        self,
        *argv: Any,
        hydraulics: Hydraulics,
        max_turn_angle_unsigned: int = 45,
        mechanical_config: MechanicalConfig,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)
        self._hydraulics: Hydraulics = hydraulics
        self._max_turn_angle_unsigned = max_turn_angle_unsigned
        self._mechanical_config = mechanical_config

        self._acceleration_limiter = Limiter(
            "acceleration",
            max_change_per_sec=lambda: 1000 / self._autodrive_params[DriverParameter.DRIVE_RAMP_UP_MS].int_value,
            directions=[Limiter.INCREASE_MAGNITUDE],
        )

        self._deceleration_limiter = Limiter(
            "deceleration",
            max_change_per_sec=lambda: 1000 / self._autodrive_params[DriverParameter.DRIVE_RAMP_DOWN_MS].int_value,
            directions=[Limiter.DECREASE_MAGNITUDE],
        )
        # steer: limit any change in steering
        self._steer_limiter = Limiter(
            "steer", max_change_per_sec=MAX_STEER_PER_SEC, directions=[Limiter.INCREASE, Limiter.DECREASE]
        )

        self._last_autodrive_request: Optional[str] = None
        self._last_autodrive_speed_limited: Optional[bool] = None
        self._last_autodrive_accel_limited: Optional[bool] = None
        self._last_autodrive_speed_limit_debug_text: Optional[List[str]] = None

        self._last_autodrive_speed_sgn: int = 0
        self._last_autodrive_error_velocity_mph: Optional[float] = None
        self._last_autodrive_forward_text: Optional[List[str]] = None

        self._metrics: DriveControllerTick = DriveControllerTick(timestamp_ms=0, gear=self.gear)

    @property
    def pc_control(self) -> bool:
        return self._hydraulics.pc_control

    @abc.abstractmethod
    def _rotary_steer_factor(self, rotary: str) -> int:
        """
        One of [-1, 0, 1] depending on front wheel drive vs rear wheel drive vs snake mode, etc.
        """
        pass

    @property
    def last_tick(self) -> DriveControllerTick:
        return self._metrics

    ################################################################################
    # Steering
    ################################################################################

    def _set_rotary_position(self, rotary: str, frame_angle_deg: float) -> None:
        self._hydraulics.set_rotary_position(rotary=rotary, frame_angle_deg=frame_angle_deg)
        self._hydraulics.notify_positive_control()

    def _frame_angle_from_offset_angle(self, offset_angle_rad: float, direction: AngleDirection) -> float:
        frame_angle: float = 0
        if abs(offset_angle_rad) > math.pi / 4:
            # do large turn
            frame_angle = self._max_turn_angle_unsigned * np.sign(offset_angle_rad)
        else:
            frame_angle = self._max_turn_angle_unsigned * offset_angle_rad / (math.pi / 4)

        # These two cases below are where the shorter angle is opposite of the direction we've been instructed
        # to turn. In this case we go all the way in the instructed direction.
        if direction == AngleDirection.RIGHT and frame_angle < 0:
            frame_angle = self._max_turn_angle_unsigned
        if direction == AngleDirection.LEFT and frame_angle > 0:
            frame_angle = -self._max_turn_angle_unsigned

        return frame_angle

    def _steer(self, frame_angle_deg: float) -> None:
        """
        Set all hydraulics to the given frame_angle_deg
        """
        self.ensure_pc_control()
        for r in self._hydraulics.rotaries:
            # If wheels are to be CW from center, apply ackermann steering to left wheel.
            # If wheels are to be CCW from center, apply ackermann steering to right wheel.
            if ("left" in r and frame_angle_deg < 0) or ("right" in r and frame_angle_deg > 0):
                desired_angle_deg = self._hydraulics.transform_frame_angle_deg_to_ackermann(
                    frame_angle_deg, self._mechanical_config.wheelbase_length_in, self._mechanical_config.track_width_in
                )
            else:
                desired_angle_deg = frame_angle_deg
            self._hydraulics.set_rotary_position(rotary=r, frame_angle_deg=desired_angle_deg)

    def _steer_via_full_strategy(self, *, error_angle_rad: float, direction: AngleDirection) -> float:
        """
        Compute a turn angle based on teh "full" steer strategy

        TODO make this a strategy pattern
        """
        if direction != AngleDirection.DIRECT:
            return self._max_turn_angle_unsigned * direction

        return self._max_turn_angle_unsigned * cast(int, np.sign(error_angle_rad))

    def _steer_via_target_pid_strategy(self, *, error_angle_rad: float, direction: AngleDirection) -> float:
        """
        Compute a turn angle for the wheels using the target PID

        TODO make this a strategy pattern
        """
        target_pidval = self._target_pid.output(error_angle_rad)
        controlled_angle = np.sign(error_angle_rad) * min(self._max_turn_angle_unsigned, abs(target_pidval))
        frame_angle = self._frame_angle_from_offset_angle(controlled_angle, direction)
        if self.gear == Gear.REV:
            frame_angle = -frame_angle
        return frame_angle

    def target_angle(
        self,
        *,
        error_angle_rad: float,
        direction: AngleDirection,
        error_velocity_mph: float,
        actual_velocity_mph: float,
    ) -> None:
        now = maka_control_timestamp_ms()

        # For targeting just point the wheels all the way - don't mess around with PID angle targets for helacs
        frame_angle = self._steer_via_full_strategy(error_angle_rad=error_angle_rad, direction=direction)
        # Negate since steer uses frame angle where negative is CW and positive is CCW
        self._steer(-frame_angle)

        # speed
        speed_limit = self.speed_limiter()
        self._last_autodrive_speed_limit_debug_text = [f"limit: {speed_limit}", "", ""]
        target_velocity = self._compute_forward(
            speed_limit=speed_limit, error_velocity_mph=error_velocity_mph, actual_velocity_mph=actual_velocity_mph,
        )
        self._drive(target_velocity)

        # debug info
        self._last_autodrive_error_velocity_mph = error_velocity_mph
        self._last_autodrive_request = "target_angle"
        self._metrics = DriveControllerTick(
            timestamp_ms=now,
            gear=self.gear,
            actual_velocity_mph=actual_velocity_mph,
            error_angle_rad=error_angle_rad,
            error_velocity_mph=error_velocity_mph,
            forward=target_velocity,
            steer_frame_angle_deg=frame_angle,
            last_request="target_angle",
        )

    ################################################################################
    # Speed Limit
    ################################################################################

    # TODO split out, parameterize further
    def _speed_limit_angular_error_transform(self, *, error_angle_rad: float) -> float:
        """
        VPID input = sgn(a) * (1 - (b/a)^2)
          where
          a = angle
          b = min_error_angle
          a >= b

        It has the following properties:
          Signed with offset angle

          if a < 0, Bounded [-1, 0]
          if a > 0, bounded [0, 1]

          As angle ->    -pi,    vpid_input -> -1
          As angle ->  +/-b,     vpid_input ->  0
          As angle ->     pi,    vpid_input ->  1
        """
        assert abs(error_angle_rad) <= math.pi
        b = self._autodrive_params[DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG].float_value

        assert 0 <= b <= math.pi
        a = error_angle_rad
        if abs(a) <= b:
            return 0
        result = cast(float, np.sign(a) * (1 - (b / a) ** 2))

        # defensive
        if a > 0:
            assert 0 <= result <= 1, "Bad computed vpid input: {}".format(result)
        elif a < 0:
            assert -1 <= result <= 0, "Bad computed vpid input: {}".format(result)

        result = self._autodrive_params[DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_SCALAR].float_value * result
        return result

    def _speed_limit(self, *, error_angle_rad: float) -> float:
        """
        Compute the speed limit based on angular error

        TODO make this a class?
        """
        return 1

    ################################################################################
    # Desired Speed
    ################################################################################

    def _speed(self, *, error_velocity_mph: float, actual_velocity_mph: float) -> float:
        if error_velocity_mph is None or actual_velocity_mph is None:
            LOG.error(
                f"Bad input! Stopping driver! Got: "
                f"error_velocity_mph={error_velocity_mph}, "
                f"actual_velocity_mph={actual_velocity_mph} "
            )
            return 0

        speed = actual_velocity_mph - error_velocity_mph
        self._last_autodrive_speed_sgn = np.sign(speed)
        return speed

    def _apply_speed_limit(self, *, desired_speed: float, speed_limit: float) -> float:
        """
        Apply speed limit
        """
        target_speed = min(speed_limit, abs(desired_speed))
        if target_speed < abs(desired_speed):
            self._last_autodrive_speed_limited = True
            LOG.debug(f"Limit desired speed: {desired_speed} due to speed limit: {speed_limit}")
        else:
            self._last_autodrive_speed_limited = False
        return target_speed

    def _apply_acceleration_deceleration_limit(self, *, forward: float) -> float:
        """
        Apply acceleration limiter and deceleration_limiter
        """
        # adjust by acceleration limiter
        target_velocity_limited_accel = self._acceleration_limiter.limit(forward)
        self._last_autodrive_accel_limited = target_velocity_limited_accel != forward

        # adjust by deceleration limiter
        target_velocity_limited_decel = self._deceleration_limiter.limit(target_velocity_limited_accel)

        return target_velocity_limited_decel

    def _compute_forward(self, *, speed_limit: float, error_velocity_mph: float, actual_velocity_mph: float) -> float:
        assert 0 <= speed_limit <= 1

        # TODO integrate actual_velocity_mph * feed_forward term

        # desired velocity
        desired_speed = self._speed(error_velocity_mph=error_velocity_mph, actual_velocity_mph=actual_velocity_mph,)

        # adjust desired velocity by speed limit
        target_speed = self._apply_speed_limit(desired_speed=desired_speed, speed_limit=speed_limit)

        # apply gear directional ratio
        target_forward = self.gear.apply() * target_speed

        # apply ramp_up limits
        limited_target_velocity = self._apply_acceleration_deceleration_limit(forward=target_forward)

        self._last_autodrive_forward_text = [
            f"fwd: {round(self._hydraulics.forward, 4)} --> {round(limited_target_velocity, 4)}"
        ]
        LOG.debug(self._last_autodrive_forward_text[0])
        return limited_target_velocity

    def drive_angle(
        self,
        *,
        error_angle_rad: float,
        direction: AngleDirection = AngleDirection.DIRECT,
        error_velocity_mph: float,
        actual_velocity_mph: float,
    ) -> None:
        now = maka_control_timestamp_ms()
        # TODO utilize in PID

        # Steer
        frame_angle = self._steer_via_target_pid_strategy(error_angle_rad=error_angle_rad, direction=direction)
        self._steer(-frame_angle)

        # speed limit
        speed_limit = self._speed_limit(error_angle_rad=error_angle_rad)
        target_forward = self._compute_forward(
            speed_limit=speed_limit, error_velocity_mph=error_velocity_mph, actual_velocity_mph=actual_velocity_mph,
        )
        self._drive(target_forward)

        # debug info
        self._last_autodrive_error_velocity_mph = error_velocity_mph
        self._last_autodrive_request = "drive_angle"
        self._metrics = DriveControllerTick(
            timestamp_ms=now,
            gear=self.gear,
            actual_velocity_mph=actual_velocity_mph,
            error_angle_rad=error_angle_rad,
            error_velocity_mph=error_velocity_mph,
            forward=target_forward,
            steer_frame_angle_deg=frame_angle,
            last_request="drive_angle",
        )

    def drive(self, *, forward: float, steer: float = 0, actual_velocity_mph: Optional[float] = None,) -> None:
        now = maka_control_timestamp_ms()
        # same core logic as manual_drive
        self._handle_drive(forward=forward, steer=steer)
        self._last_autodrive_request = "drive"
        self._metrics = DriveControllerTick(
            timestamp_ms=now,
            gear=self.gear,
            actual_velocity_mph=actual_velocity_mph,
            forward=forward,
            steer_frame_angle_deg=self._hydraulics.rotary_position_frame_angles_deg[
                next(iter(self._hydraulics.rotary_position_frame_angles_deg))
            ],
            last_request="drive",
        )

    def _drive(self, value: float) -> None:
        # ok to stop and reset state when not in control
        if value != 0:
            self.ensure_pc_control()

        self._hydraulics.drive(value)
        self._hydraulics.notify_positive_control()

        if value != 0 and self.gear not in [Gear.REV, Gear.FWD]:
            LOG.warning(f"Attempted to drive in gear: {self.gear}")

    ################################################################################
    # Stop
    ################################################################################

    def stop_vehicle(self) -> None:
        # TODO reset PIDs
        # TODO reset limiters
        self._drive(0)
        self.reset_pids()
        self._acceleration_limiter.reset()
        self._steer_limiter.reset()
        self._last_autodrive_request = "stop"

    def align_straight(self) -> None:
        for h in self._hydraulics.rotaries:
            self._set_rotary_position(rotary=h, frame_angle_deg=0)
        self._last_autodrive_request = "align_straight"

    ################################################################################
    # Manual Drive
    ################################################################################

    def handle_manual_drive_request(self, dreq: ManualDriveRequest) -> None:
        # We always want to run the limitter so that it can update what it would otherwise do in
        # time on its own, but we might choose to ignore the limited amounts if the drive request
        # has requested to bypass the limits
        forward = self._acceleration_limiter.limit(dreq.forward) if dreq.forward is not None else None
        steer = self._steer_limiter.limit(dreq.steer) if dreq.steer is not None else None

        if forward != dreq.forward:
            print("LIMITED!")

        self._handle_drive(forward=forward, steer=steer)
        self._last_autodrive_request = "manual_drive"

    def ensure_pc_control(self) -> None:
        if not self.pc_control:
            raise DriveStateException("PC not in control! Check FORT!")

    def _handle_drive(self, *, forward: Optional[float], steer: Optional[float]) -> None:
        self.ensure_pc_control()

        # steer
        if steer is not None:
            # Determine the frame angle being requested to steer
            # rotary_steer_factor: one of [-1, 0, 1] which distinguishes FWD/RWD/Snake/Crab
            #                      by inverting/zeroing a pair of rotaries
            # steerable_range_factor: a continuous value [0, 1] limiting the steerable range
            #                         while still allowing the steer value to take on its
            #                         full [-1, 1] range
            scaled_steer = steer * self._hydraulics.steerable_range_factor()
            frame_angle_deg = self._hydraulics.transform_steer_to_frame_angle_deg(scaled_steer)
            self._steer(frame_angle_deg)

        # forward
        if forward is not None:
            # simple linear interpolation
            self._drive(forward)

    ################################################################################
    # Debug
    ################################################################################

    def annotate_debug_text(self) -> List[Text]:
        c = BGR_BLUE
        speed_color = (
            BGR_GREEN
            if self._last_autodrive_speed_sgn > 0
            else BGR_GREEN_MEDIUM
            if self._last_autodrive_speed_sgn < 0
            else BGR_GREY
        )
        fwd_color = (
            BGR_RED
            if self._last_autodrive_speed_limited
            else BGR_YELLOW
            if self._last_autodrive_accel_limited
            else speed_color
        )
        result: List[Text] = [Text(text=str(self._last_autodrive_request), color=c)]
        if self._last_autodrive_request in ["drive_angle", "target_angle"]:
            target_speed_mph = self._autodrive_params[DriverParameter.TARGET_SPEED_MPH].float_value
            result[0] = Text(text=f"{result[0].text}: {format_float_text(target_speed_mph)} mph", color=c)
            if self._last_autodrive_error_velocity_mph is not None:
                actual_velocity_mph = target_speed_mph + self._last_autodrive_error_velocity_mph
                result.append(Text(text=f"actual: {format_float_text(actual_velocity_mph)} mph", color=c))
                error_number_text = format_float_text(self._last_autodrive_error_velocity_mph, positive_symbol="+")
                result.append(Text(text=f"error: {error_number_text} mph", color=c))
            if self._last_autodrive_speed_limit_debug_text is not None:
                limit_color = BGR_RED if self._last_autodrive_speed_limited else BGR_GREY
                result += [Text(text=t, color=limit_color) for t in self._last_autodrive_speed_limit_debug_text]
            if self._last_autodrive_forward_text is not None:
                result += [Text(text=t, color=fwd_color) for t in self._last_autodrive_forward_text]
        elif self._last_autodrive_request == "target_angle":
            pass
        return result


FRONT_WHEEL_STEER = "front_wheel_steer"


class FrontWheelSteerHydraulicsDriveController(BaseHydraulicsDriveController):
    """
    Turns only front rotaries, keeping rear rotaries aligned straight. Applies
    the same torque in the same direction to all wheels.

    This is akin to front-wheel drive (FWD) in an automobile.
    """

    def _rotary_steer_factor(self, rotary: str) -> int:
        """
        Turn only front wheels. Keep rear wheels fixed.
        """
        return 1 if "back" not in rotary else 0
