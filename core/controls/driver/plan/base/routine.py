from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from core.controls.driver.parameters import DriverParameters
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.plan.ops.op import Op
from core.controls.plan.ops.prog import OpRoutine
from lib.common.protocol.feed.base import Feed


class DriverRoutine(OpRoutine, ABC):
    def __init__(
        self,
        feed: Feed,
        autodrive_params: DriverParameters,
        field_location_notifier: Optional[FieldLocationNotifier] = None,
        world_hud: Optional[WorldHUD] = None,
    ):
        self._feed = feed
        self._autodrive_params = autodrive_params
        self._field_location_notifier = field_location_notifier
        self._world_hud = world_hud

        # this is done so we don't have to expose init args so that children can just blindly do **kwargs
        ops = self.make_ops()

        super().__init__(*ops)

    @property
    def feed(self) -> Feed:
        return self._feed

    @property
    def kwargs(self) -> Dict[str, Any]:
        """
        Reflection helper method for children
        """
        return {
            "feed": self.feed,
            "autodrive_params": self._autodrive_params,
            "field_location_notifier": self._field_location_notifier,
            "world_hud": self._world_hud,
        }

    @abstractmethod
    def make_ops(self) -> List[Op]:
        pass


class Reversible:
    """
    mix-in
    """

    def __init__(self, reverse: bool = False):
        self._reverse = reverse

    @property
    def reverse(self) -> bool:
        return self._reverse
