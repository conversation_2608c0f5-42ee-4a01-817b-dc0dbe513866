from typing import Optional, <PERSON><PERSON>

from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.instruction import DriverInstruction, NoOpDriverInstruction, TerminateDriverInstruction
from core.controls.driver.plan.op.speed import Stop
from core.controls.plan.instruction import NoOpInstruction, PlanInstruction, TerminateInstruction
from core.controls.plan.runner import OpRunner
from core.controls.plan.state.info import PlanInfo
from core.model.topics import Topics
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.feed.base import Feed

LOG = get_logger(__name__)


class DriverOpRunner(OpRunner):
    """
    OpRunner for Driver
    """

    def __init__(self, feed: Feed):
        super().__init__()
        self._feed = feed

    @staticmethod
    def _translate_instruction(plan_instruction: Optional[PlanInstruction]) -> Optional[DriverInstruction]:
        """
        Convert from a base Plan Instruction to the corresponding DriverInstruction.

        This indirection is a bit annoying, but allows strong typing (DriverInstruction) downstream.
        """
        if plan_instruction is None:
            return None

        driver_instruction: DriverInstruction
        if isinstance(plan_instruction, NoOpInstruction):
            driver_instruction = NoOpDriverInstruction()
        elif isinstance(plan_instruction, TerminateInstruction):
            driver_instruction = TerminateDriverInstruction(reason=plan_instruction.reason)
        else:
            assert isinstance(plan_instruction, DriverInstruction), f"Got {type(plan_instruction)}: {plan_instruction}"
            driver_instruction = plan_instruction

        return driver_instruction

    def tick(self) -> Tuple[Optional[PlanInfo], Optional[DriverInstruction]]:
        plan_info, plan_instruction = super()._tick()

        # convert plan instruction to typed as Driver instruction
        return plan_info, DriverOpRunner._translate_instruction(plan_instruction)

    def build(self, routine: DriverRoutine) -> None:
        on_complete: Optional[Stop] = None
        if not isinstance(routine.ops[-1], Stop):
            on_complete = Stop(
                reason="plan complete",
                velocity_mph_subscription=self._feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage),
            )

        return super()._build(routine, on_complete)
