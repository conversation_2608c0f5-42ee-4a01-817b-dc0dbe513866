from abc import ABC
from typing import Any, List

from core.controls.driver.constants import DRIVE_FURROW_SPEED_MPH
from core.controls.driver.plan.base.routine import DriverRoutine, Reversible
from core.controls.driver.plan.op.distance import until_inches_traveled
from core.controls.driver.plan.op.furrows import drive_down_furrows
from core.controls.driver.plan.op.gear import set_forward, set_reverse
from core.controls.driver.plan.op.geo import until_distance_to_eorta, until_not_turn_around, until_turn_around
from core.controls.driver.plan.op.speed import set_dynamic_target_speed
from core.controls.plan.ops.log import plan_log
from core.controls.plan.ops.logical import _and
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op


class FurrowRoutine(DriverRoutine, ABC):
    pass


class ReversibleFurrowRoutine(FurrowRoutine, Reversible, ABC):
    def __init__(
        self,
        speed: float = DRIVE_FURROW_SPEED_MPH,
        opposite_cams: bool = False,
        reverse: bool = False,
        no_gps: bool = False,
        **kwargs: Any
    ):
        self._opposite_cams = opposite_cams
        self._speed = speed
        self._no_gps = no_gps
        Reversible.__init__(self, reverse=reverse)
        FurrowRoutine.__init__(self, **kwargs)


class EnterFurrow(ReversibleFurrowRoutine):
    def make_ops(self) -> List[Op]:
        gear_op = set_forward() if not self.reverse else set_reverse()
        return [
            # leave current end zone if in it
            gear_op,
            set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
            # Drive out of turn around zone
            loop(
                until_not_turn_around(feed=self.feed, no_gps=self._no_gps),
                set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
                drive_down_furrows(
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    ignore_front_cameras=self.reverse,
                    ignore_back_cameras=not self.reverse,
                ),
            ),
            #
            # drive a bit farther because GPS can wiggle a bit
            #
            loop(
                until_inches_traveled(feed=self.feed, inches=12 * 3),
                set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
                drive_down_furrows(
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    ignore_front_cameras=self.reverse,
                    ignore_back_cameras=not self.reverse,
                ),
            ),
        ]


class FollowFurrow(ReversibleFurrowRoutine):
    def make_ops(self) -> List[Op]:
        gear_op = set_forward() if not self._reverse else set_reverse()
        if self.reverse:
            ignore_front_cameras = not self._opposite_cams
            ignore_back_cameras = self._opposite_cams
        else:
            ignore_front_cameras = self._opposite_cams
            ignore_back_cameras = not self._opposite_cams

        return [
            # drive until we lose track of front right furrows
            gear_op,
            set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
            plan_log("Driving FRONT CAMS"),
            loop(
                _and(
                    until_turn_around(feed=self.feed, no_gps=self._no_gps),
                    until_distance_to_eorta(
                        feed=self.feed,
                        reverse=self._reverse,
                        distance_inches=12 * 40,  # 40 feet is plenty of furrows left
                        no_gps=self._no_gps,
                        field_location_notifier=self._field_location_notifier,
                    ),
                ),
                set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
                drive_down_furrows(
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    ignore_front_cameras=ignore_front_cameras,
                    ignore_back_cameras=ignore_back_cameras,
                ),
            ),
            plan_log("Driving REAR CAMS"),
            loop(
                until_turn_around(feed=self.feed, no_gps=self._no_gps),
                set_dynamic_target_speed(min_speed=0.1, max_speed=self._speed),
                drive_down_furrows(
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    ignore_front_cameras=not ignore_front_cameras,
                    ignore_back_cameras=not ignore_back_cameras,
                ),
            ),
        ]


class DriveFurrow(ReversibleFurrowRoutine):
    def make_ops(self) -> List[Op]:
        return [
            plan_log("EORTA: Entering Furrows"),
            EnterFurrow(speed=self._speed, reverse=self._reverse, no_gps=self._no_gps, **self.kwargs),
            plan_log("EORTA: Following Furrows"),
            FollowFurrow(speed=self._speed, reverse=self._reverse, no_gps=self._no_gps, **self.kwargs),
        ]
