from abc import ABC
from typing import Any, List

import numpy as np

from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.op.distance import until_inches_traveled
from core.controls.driver.plan.op.gear import set_forward, set_reverse
from core.controls.driver.plan.op.geo import until_turn_around
from core.controls.driver.plan.op.heading import (
    drive_heading,
    drive_heading_slot,
    lock_heading,
    turn_heading_offset,
    turn_left,
    turn_right,
)
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op
from lib.common.angle_direction import AngleDirection


class HeadingRoutine(DriverRoutine, ABC):
    pass


class ForwardTurnLeft(HeadingRoutine):
    def make_ops(self) -> List[Op]:
        return [set_forward(), turn_left(feed=self.feed, degrees=90, autodrive_params=self._autodrive_params)]


class ForwardTurnRight(HeadingRoutine):
    def make_ops(self) -> List[Op]:
        return [
            set_forward(),
            turn_right(feed=self.feed, degrees=90, autodrive_params=self._autodrive_params),
        ]


class TurnDegrees(HeadingRoutine):
    def __init__(self, degrees: int, **kwargs: Any):
        self._degrees = degrees
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        slotname: str = "original_heading"
        return [
            lock_heading(feed=self.feed, slotname=slotname),
            turn_heading_offset(
                feed=self.feed,
                slotname=slotname,
                offset_heading_deg=self._degrees,
                direction=AngleDirection.DIRECT,
                autodrive_params=self._autodrive_params,
            ),
        ]


class HeadingInches(HeadingRoutine):
    def __init__(self, inches: float, **kwargs: Any):
        self._inches = inches
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        speed: Op = set_forward()

        inches = self._inches
        if np.sign(inches) < 0:
            speed = set_reverse()
            inches = abs(inches)

        slotname: str = "target_heading"
        return [
            lock_heading(feed=self.feed, slotname=slotname),
            speed,
            loop(
                until_inches_traveled(feed=self.feed, inches=inches),
                drive_heading_slot(feed=self.feed, autodrive_params=self._autodrive_params, slotname=slotname),
            ),
        ]


class FollowHeading(HeadingRoutine):
    def make_ops(self) -> List[Op]:
        slotname: str = "target_heading"
        return [
            lock_heading(feed=self.feed, slotname=slotname),
            loop(
                until_turn_around(feed=self.feed),
                drive_heading_slot(feed=self.feed, autodrive_params=self._autodrive_params, slotname=slotname),
            ),
        ]


class HeadingTarget(HeadingRoutine):
    def __init__(self, target_heading: float, **kwargs: Any):
        self._target_heading = target_heading
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        gear_op: Op = set_forward()

        if np.sign(self._target_heading) < 0:
            gear_op = set_reverse()

        return [
            gear_op,
            loop(
                until_turn_around(feed=self.feed),
                drive_heading(
                    feed=self.feed, autodrive_params=self._autodrive_params, target_heading=abs(self._target_heading)
                ),
            ),
        ]
