from typing import Any, List

from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.op.calibration import (
    drive_until_nonzero_velocity,
    drive_until_velocity,
    drive_until_zero_velocity,
    keep_driving,
)
from core.controls.driver.plan.op.gear import set_forward
from core.controls.plan.ops.condition import until_elapsed_ms
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op


class CalibrateStaticFriction(DriverRoutine):
    def make_ops(self) -> List[Op]:
        return [
            set_forward(),
            drive_until_nonzero_velocity(**self.kwargs),
            loop(until_elapsed_ms(10000), keep_driving(self.feed)),
        ]


class CalibrateMinDrivableSpeed(DriverRoutine):
    def make_ops(self) -> List[Op]:
        return [
            CalibrateStaticFriction(**self.kwargs),
            drive_until_zero_velocity(**self.kwargs),
            loop(until_elapsed_ms(10000), keep_driving(self.feed)),
        ]


class CalibrateDriveRampUp(DriverRoutine):
    def __init__(self, until_velocity_mph: float, **kwargs: Any):
        self._until_velocity_mph: float = until_velocity_mph
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        return [
            set_forward(),
            drive_until_velocity(velocity_mph=self._until_velocity_mph, **self.kwargs),
            loop(until_elapsed_ms(10000), keep_driving(self.feed)),
        ]
