from abc import ABC
from typing import Any, List

from core.controls.driver.constants import EORTA_TURN_SPEED_MPH, REENTER_FURROW_SPEED_MPH
from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.op.gear import set_forward, set_reverse
from core.controls.driver.plan.op.heading import turn_s_shape_slot, until_aligned_slot
from core.controls.driver.plan.op.path import generate_eorta_path
from core.controls.driver.plan.op.speed import set_target_speed, stop
from core.controls.driver.plan.op.wheels import align_straight
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op
from lib.common.angle_direction import AngleDirection


class TurnAroundRoutine(DriverRoutine, ABC):
    pass


class EndOfRowTurnAroundRoutine(TurnAroundRoutine, ABC):
    pass


class GPSEndOfRowTurnAround(EndOfRowTurnAroundRoutine):
    def __init__(self, direction: AngleDirection, **kwargs: Any):
        self._direction = direction
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        reverse_position_threshold_m = 2.0
        reverse_heading_threshold_deg = 5

        position_threshold_m = 0.5
        # TODO(evanbro) This heading threshold should be lower. Seems to be limited by GPS performance.
        heading_threshold_deg = 5

        # Never reverse due to crops proximity
        crops_distance = 0
        return [
            generate_eorta_path(self.feed, self._direction, self._field_location_notifier, self._world_hud),
            set_forward(),
            set_target_speed(target_speed_mph=EORTA_TURN_SPEED_MPH),
            set_forward(),
            # Ignore crops intersection on first turn
            turn_s_shape_slot(
                "first_turn_target_point",
                "first_turn_target_heading",
                feed=self.feed,
                autodrive_params=self._autodrive_params,
                world_hud=self._world_hud,
                field_location_notifier=self._field_location_notifier,
                heading_threshold_deg=reverse_heading_threshold_deg / 2,
                position_threshold=reverse_position_threshold_m / 2,
                near_eorta_edge_distance_m_crops=crops_distance,
            ),
            loop(
                until_aligned_slot(
                    self.feed,
                    "first_turn_target_point",
                    "first_turn_target_heading",
                    heading_threshold_deg=reverse_heading_threshold_deg,
                    position_threshold=reverse_position_threshold_m,
                ),
                stop(reason="fwd -> rev", feed=self.feed),
                set_reverse(),
                turn_s_shape_slot(
                    "first_turn_reverse_point",
                    "first_turn_reverse_heading",
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    world_hud=self._world_hud,
                    field_location_notifier=self._field_location_notifier,
                    heading_threshold_deg=reverse_heading_threshold_deg / 2,
                    position_threshold=reverse_position_threshold_m / 2,
                    reversing=True,
                    near_eorta_edge_distance_m_crops=crops_distance,
                ),
                stop(reason="rev -> fwd", feed=self.feed),
                set_forward(),
                turn_s_shape_slot(
                    "first_turn_target_point",
                    "first_turn_target_heading",
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    world_hud=self._world_hud,
                    field_location_notifier=self._field_location_notifier,
                    heading_threshold_deg=reverse_heading_threshold_deg / 2,
                    position_threshold=reverse_position_threshold_m / 2,
                    near_eorta_edge_distance_m_crops=crops_distance,
                ),
            ),
            stop(reason="fwd -> rev", feed=self.feed),
            set_reverse(),
            turn_s_shape_slot(
                "reverse_point",
                "reverse_heading",
                feed=self.feed,
                autodrive_params=self._autodrive_params,
                world_hud=self._world_hud,
                field_location_notifier=self._field_location_notifier,
                heading_threshold_deg=reverse_heading_threshold_deg / 2,
                position_threshold=reverse_position_threshold_m / 2,
                reversing=True,
                near_eorta_edge_distance_m_crops=crops_distance,
            ),
            loop(
                until_aligned_slot(
                    self.feed,
                    "target_point",
                    "target_heading",
                    heading_threshold_deg=heading_threshold_deg,
                    position_threshold=position_threshold_m,
                ),
                stop(reason="rev -> fwd", feed=self.feed),
                set_forward(),
                turn_s_shape_slot(
                    "target_point",
                    "target_heading",
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    world_hud=self._world_hud,
                    field_location_notifier=self._field_location_notifier,
                    heading_threshold_deg=heading_threshold_deg / 2,
                    position_threshold=position_threshold_m / 2,
                    near_eorta_edge_distance_m_crops=crops_distance,
                ),
                stop(reason="fwd -> rev", feed=self.feed),
                set_reverse(),
                turn_s_shape_slot(
                    "reverse_point",
                    "reverse_heading",
                    feed=self.feed,
                    autodrive_params=self._autodrive_params,
                    world_hud=self._world_hud,
                    field_location_notifier=self._field_location_notifier,
                    heading_threshold_deg=reverse_heading_threshold_deg / 2,
                    position_threshold=reverse_position_threshold_m / 2,
                    reversing=True,
                    near_eorta_edge_distance_m_crops=crops_distance,
                ),
            ),
            stop(reason="rev -> fwd", feed=self.feed),
            set_forward(),
            set_target_speed(target_speed_mph=REENTER_FURROW_SPEED_MPH),
            align_straight(),
        ]
