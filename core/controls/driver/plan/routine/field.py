from abc import ABC
from typing import Any, List

from core.controls.commander.notification import NOTIFY_ENTERING_ROW, NOTIFY_EXITING_ROW
from core.controls.driver.constants import DRIVE_FURROW_SPEED_MPH
from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.op.gear import notify_commander, set_forward, set_paused
from core.controls.driver.plan.routine.furrows import DriveFurrow
from core.controls.driver.plan.routine.turnaround import GPSEndOfRowTurnAround
from core.controls.plan.ops.condition import forever
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op
from lib.common.angle_direction import AngleDirection


class FieldRoutine(DriverRoutine, ABC):
    pass


class FurrowAndTurn(FieldRoutine):
    def __init__(
        self,
        direction: AngleDirection,
        gps_assist: bool = False,
        no_gps: bool = False,
        speed: float = DRIVE_FURROW_SPEED_MPH,
        **kwargs: Any
    ):
        self._direction = direction
        self._gps_assist = gps_assist
        self._speed = speed
        self._no_gps = no_gps
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        turn_around = GPSEndOfRowTurnAround
        return [
            notify_commander(NOTIFY_ENTERING_ROW, "Entering Row"),
            set_forward(),
            DriveFurrow(speed=self._speed, no_gps=self._no_gps, **self.kwargs),
            notify_commander(NOTIFY_EXITING_ROW, "Exiting Row"),
            set_paused("Waiting for confirmation before starting eorta"),
            turn_around(direction=self._direction, **self.kwargs),
        ]


class FurrowAndTurnLeft(FurrowAndTurn):
    def __init__(self, **kwargs: Any):
        super().__init__(AngleDirection.LEFT, **kwargs)


class FurrowAndTurnRight(FurrowAndTurn):
    def __init__(self, **kwargs: Any):
        super().__init__(AngleDirection.RIGHT, **kwargs)


class GPSField(FieldRoutine):
    def __init__(self, direction: AngleDirection, speed: float, no_gps: bool = False, **kwargs: Any):
        self._direction = direction
        self._speed = speed
        self._no_gps = no_gps
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        loop_body: List[Op] = []

        if self._direction == AngleDirection.LEFT:
            loop_body.append(FurrowAndTurnLeft(speed=self._speed, no_gps=self._no_gps, **self.kwargs))
            loop_body.append(FurrowAndTurnRight(speed=self._speed, no_gps=self._no_gps, **self.kwargs))
        elif self._direction == AngleDirection.RIGHT:
            loop_body.append(FurrowAndTurnRight(speed=self._speed, no_gps=self._no_gps, **self.kwargs))
            loop_body.append(FurrowAndTurnLeft(speed=self._speed, no_gps=self._no_gps, **self.kwargs))
        else:
            assert "No Auto Detect Field Direction"

        return [loop(forever(), *loop_body)]
