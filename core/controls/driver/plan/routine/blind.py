from abc import ABC
from typing import Any, List

import numpy as np

from core.controls.driver.plan.base.routine import DriverRoutine, Reversible
from core.controls.driver.plan.op.distance import until_inches_traveled
from core.controls.driver.plan.op.gear import set_forward, set_reverse
from core.controls.driver.plan.op.heading import drive_straight
from core.controls.driver.plan.op.speed import stop
from core.controls.plan.ops.condition import forever
from core.controls.plan.ops.loop import loop
from core.controls.plan.ops.op import Op


class BlindRoutine(DriverRoutine, ABC):
    pass


class ReversibleBlindRoutine(BlindRoutine, Reversible, ABC):
    def __init__(self, reverse: bool = False, **kwargs: Any):
        Reversible.__init__(self, reverse=reverse)
        BlindRoutine.__init__(self, **kwargs)


class DriveStraightInches(ReversibleBlindRoutine):
    def __init__(self, inches: float, **kwargs: Any):
        self._inches: float = inches
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        gear_op: Op = set_forward() if np.sign(self._inches) >= 0 else set_reverse()

        return [
            gear_op,
            loop(
                until_inches_traveled(feed=self.feed, inches=abs(self._inches)),
                drive_straight(feed=self.feed, autodrive_params=self._autodrive_params),
            ),
            stop(reason=f"drove {self._inches}in", feed=self.feed),
        ]


class DriveBacknForthInches(ReversibleBlindRoutine):
    def __init__(self, inches: float, **kwargs: Any):
        self._inches: float = inches
        super().__init__(**kwargs)

    def make_ops(self) -> List[Op]:
        main_loop = loop(
            forever(),
            set_forward(),
            loop(
                until_inches_traveled(feed=self.feed, inches=abs(self._inches)),
                drive_straight(feed=self.feed, autodrive_params=self._autodrive_params),
            ),
            stop(reason=f"drove {self._inches}in", feed=self.feed),
            set_reverse(),
            loop(
                until_inches_traveled(feed=self.feed, inches=abs(self._inches)),
                drive_straight(feed=self.feed, autodrive_params=self._autodrive_params),
            ),
            stop(reason=f"drove {self._inches}in", feed=self.feed),
        )

        return [main_loop]
