import abc
from typing import Dict

import numpy as np

from lib.common.geo.boundary import Boundary
from lib.common.geo.ecef_utils import EcefVector


class FieldLocationNotifier(abc.ABC):
    @abc.abstractmethod
    def notify_eorta(self, vector: EcefVector) -> None:
        pass

    @abc.abstractmethod
    def notify_furrow(self, vector: EcefVector) -> None:
        pass

    @abc.abstractmethod
    def set_field_geofence(self, field: Dict[str, Boundary]) -> None:
        pass

    @abc.abstractmethod
    def set_turn_around_geofence(self, turn_around: Dict[str, Boundary]) -> None:
        pass

    @abc.abstractmethod
    def clear_geofences(self) -> None:
        pass

    @abc.abstractmethod
    def add_geo_ball(self, ball: np.ndarray) -> None:
        pass

    @abc.abstractmethod
    def add_path_vector(self, vector: EcefVector) -> None:
        pass

    @abc.abstractmethod
    def clear_path_vectors(self) -> None:
        pass

    @abc.abstractmethod
    def clear_route_vectors(self) -> None:
        pass
