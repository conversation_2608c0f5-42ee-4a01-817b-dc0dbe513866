from typing import Optional

from core.controls.driver.gear import Gear
from core.controls.plan.instruction import NoOpInstruction, PlanInstruction, TerminateInstruction
from lib.common.angle_direction import AngleDirection


class DriverInstruction(PlanInstruction):
    pass


class AlignStraightInstruction(DriverInstruction):
    def __init__(self) -> None:
        super().__init__(msg_type="align_straight", msg={})


class DriveAngleInstruction(DriverInstruction):
    def __init__(
        self, *, error_angle_rad: float, error_velocity_mph: float, actual_velocity_mph: float,
    ):
        super().__init__(
            msg_type="drive_angle",
            msg={
                "error_angle_rad": error_angle_rad,
                "error_velocity_mph": error_velocity_mph,
                "actual_velocity_mph": actual_velocity_mph,
            },
        )
        self._error_angle_rad = error_angle_rad
        self._error_velocity_mph = error_velocity_mph
        self._actual_velocity_mph = actual_velocity_mph

    @property
    def error_angle_rad(self) -> float:
        return self._error_angle_rad

    @property
    def error_velocity_mph(self) -> float:
        return self._error_velocity_mph

    @property
    def actual_velocity_mph(self) -> float:
        return self._actual_velocity_mph


class DriveInstruction(DriverInstruction):
    def __init__(self, *, forward: float, actual_velocity_mph: Optional[float], steer: float = 0):
        super().__init__(
            msg_type="drive", msg={"forward": forward, "steer": steer, "actual_velocity_mph": actual_velocity_mph},
        )
        self._forward = forward
        self._steer = steer
        self._actual_velocity_mph = actual_velocity_mph

    @property
    def forward(self) -> float:
        return self._forward

    @property
    def steer(self) -> float:
        return self._steer

    @property
    def actual_velocity_mph(self) -> Optional[float]:
        return self._actual_velocity_mph


class NoOpDriverInstruction(NoOpInstruction, DriverInstruction):
    """
    Driver-specific no-op instruction, mainly so APIs can be typed with DriverInstruction.
    """

    pass


class SetGearInstruction(DriverInstruction):
    def __init__(self, gear: Gear) -> None:
        super().__init__(msg_type="set_gear", msg={"gear": gear})
        self._gear = gear

    @property
    def gear(self) -> Gear:
        return self._gear


class StopInstruction(DriverInstruction):
    def __init__(self, reason: str) -> None:
        super().__init__(msg_type="stop", msg={"reason": reason})
        self._reason = reason

    @property
    def reason(self) -> str:
        return self._reason


class TargetAngleInstruction(DriverInstruction):
    def __init__(
        self,
        *,
        error_angle_rad: float,
        error_velocity_mph: float,
        actual_velocity_mph: float,
        direction: AngleDirection = AngleDirection.DIRECT,
    ):
        super().__init__(
            msg_type="target_angle",
            msg={
                "error_angle_rad": error_angle_rad,
                "error_velocity_mph": error_velocity_mph,
                "actual_velocity_mph": actual_velocity_mph,
                "direction": direction,
            },
        )
        self._error_angle_rad = error_angle_rad
        self._error_velocity_mph = error_velocity_mph
        self._actual_velocity_mph = actual_velocity_mph
        self._direction = direction

    @property
    def error_angle_rad(self) -> float:
        return self._error_angle_rad

    @property
    def error_velocity_mph(self) -> float:
        return self._error_velocity_mph

    @property
    def actual_velocity_mph(self) -> float:
        return self._actual_velocity_mph

    @property
    def direction(self) -> AngleDirection:
        return self._direction


class TerminateDriverInstruction(TerminateInstruction, DriverInstruction):
    """
    Driver-specific terminate instruction, mainly so APIs can be typed with DriverInstruction.
    """

    pass


class PauseDriverInstruction(DriverInstruction):
    def __init__(self, reason: str):
        super().__init__(msg_type="pause_driver", msg={"reason": reason})


class NotifyCommanderInstruction(DriverInstruction):
    def __init__(self, topic: str, reason: str):
        super().__init__(msg_type="notify_commander", msg={"topic": topic, "reason": reason})
        self._topic = topic

    @property
    def topic(self) -> str:
        return self._topic


class UpdateTargetSpeedInstruction(DriverInstruction):
    def __init__(self, *, target_speed_mph: float):
        assert 0 <= target_speed_mph <= 10
        super().__init__(msg_type="target_speed_mph", msg={"target_speed_mph": target_speed_mph})
        self._target_speed_mph = target_speed_mph

    @property
    def target_speed_mph(self) -> float:
        return self._target_speed_mph


class UpdateSpeedLimitPctInstruction(DriverInstruction):
    def __init__(self, pct: float):
        assert 0 <= pct <= 1
        super().__init__(msg_type="speed_limit", msg={"pct": pct})

        self._pct = pct

    @property
    def pct(self) -> float:
        return self._pct
