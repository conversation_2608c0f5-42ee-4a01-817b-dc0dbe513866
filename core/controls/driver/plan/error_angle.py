import math
from typing import Callable, Dict, List, Optional, Sequence

import numpy as np

from core.controls.driver.constants import (
    DEFAULT_FILTER_EXPIRATION_MS,
    DEFAULT_FILTER_MAX_DELTA_ERROR_ANGLE_DEG,
    DEFAULT_FILTER_MAX_ERROR_ANGLE_DEG,
    DEFAULT_FILTER_MAX_ERROR_X_PCT,
)
from core.controls.driver.driver_error import Driver<PERSON><PERSON><PERSON>, DriverErrorChannel, VelocityError
from core.controls.frame.mechanical import DriveCamMechanicalConfig
from core.cv.visual_cortex.model.ungenerated import FurrowSlope
from lib.common.logging import get_logger
from lib.common.math import deg2rad, rad2deg
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.time import iso8601_timestamp, maka_control_timestamp_ms
from lib.common.time.sleep import get_sleep_detune

LOG = get_logger(__name__)

# TODO these magic numbers are from ArUco days. Revisit from first principles
W_MARKER_WIDTH = 8.25  # how big is our markers as printed on full page
MEASURED_DISTANCE_FACTOR = 7039  # scale factor for distance to marker size

VELOCITY_TOO_OLD_MS = 500


class ComputeError(SubscriberChannel[DriverError]):
    """
    Transform a FurrowSlope channel to an error channel
    """

    def __init__(
        self,
        target_furrow_subscription: SubscriberChannel[FurrowSlope],
        error_velocity_mph_subscription: SubscriberChannel[VelocityError],
        drive_cam_mech_cfg_subscription: SubscriberChannel[DriveCamMechanicalConfig],
        target_speed_mph: Callable[[], float],
    ):
        super().__init__(topic=target_furrow_subscription.topic.sub("angle_error"))
        self._target_furrow_subscription = target_furrow_subscription
        self._error_velocity_mph_subscription = error_velocity_mph_subscription
        self._drive_cam_mech_cfg_subscription = drive_cam_mech_cfg_subscription
        self._target_speed_mph: Callable[[], float] = target_speed_mph
        self._last_output: Optional[DriverError] = None

    @staticmethod
    def _compute_error_angle_rad(
        slope: FurrowSlope,
        drive_cam_mech_cfg: DriveCamMechanicalConfig,
        actual_velocity_mph: float,
        error_velocity_mph: float,
    ) -> DriverError:
        angle_offset_deg_tan = math.tan(drive_cam_mech_cfg.angle_offset_deg / 180 * math.pi)
        x_error_px = slope.midpoint[0] - (
            slope.image_center[0] + drive_cam_mech_cfg.x_offset_px + angle_offset_deg_tan * slope.image_height / 2
        )
        x_error_pct = x_error_px / slope.image_width
        # TODO: seems incorrect, switch to actual angle
        error_angle_rad = math.atan2(x_error_px * W_MARKER_WIDTH, MEASURED_DISTANCE_FACTOR)
        return DriverError(
            timestamp_ms=slope.timestamp_ms,
            error_angle_rad=error_angle_rad,
            error_x_pct=x_error_pct,
            error_velocity_mph=error_velocity_mph,
            actual_velocity_mph=actual_velocity_mph,
        )

    def read(self) -> Optional[DriverError]:
        slope_optional: Optional[FurrowSlope] = self._target_furrow_subscription.read()
        if slope_optional is None:
            return self._last_output
        slope: FurrowSlope = slope_optional

        drive_cam_mech_cfg = self._drive_cam_mech_cfg_subscription.read()
        assert drive_cam_mech_cfg is not None

        error_velocity_mph = self._error_velocity_mph_subscription.read()
        if error_velocity_mph is None:
            return None

        age = maka_control_timestamp_ms() - error_velocity_mph.timestamp_ms
        if age > VELOCITY_TOO_OLD_MS:
            LOG.warning(f"velocity error too old to use: {age}ms > {VELOCITY_TOO_OLD_MS}ms")
            self._last_output = None
            return self._last_output

        self._last_output = ComputeError._compute_error_angle_rad(
            slope=slope,
            drive_cam_mech_cfg=drive_cam_mech_cfg,
            error_velocity_mph=error_velocity_mph.error_velocity_mph,
            actual_velocity_mph=error_velocity_mph.actual_velocity_mph,
        )
        return self._last_output


class Filter(DriverErrorChannel):
    """
    A filter for an error channel.
    Filtering by 1) the error angle and 2) the delta of errors between current and previous furrow.
    """

    def __init__(
        self,
        input_subscription: SubscriberChannel[DriverError],
        filter_expiration_ms: Callable[[], float] = lambda: DEFAULT_FILTER_EXPIRATION_MS,
        filter_max_delta_error_angle_deg: Callable[[], float] = lambda: DEFAULT_FILTER_MAX_DELTA_ERROR_ANGLE_DEG,
        filter_max_error_angle_deg: Callable[[], float] = lambda: DEFAULT_FILTER_MAX_ERROR_ANGLE_DEG,
        filter_max_error_x_pct: Callable[[], float] = lambda: DEFAULT_FILTER_MAX_ERROR_X_PCT,
    ):
        super().__init__(topic=input_subscription.topic.sub("filter"))
        self._input_subscription: SubscriberChannel[DriverError] = input_subscription
        self._last_output: Optional[DriverError] = None
        self._callback_expire_threshold_ms: Callable[[], float] = filter_expiration_ms
        self._callback_max_delta_error_angle_deg: Callable[[], float] = filter_max_delta_error_angle_deg
        self._callback_max_error_angle_deg: Callable[[], float] = filter_max_error_angle_deg
        self._callback_max_error_x_pct: Callable[[], float] = filter_max_error_x_pct

        self._status = ""

    def status(self) -> str:
        return self._status

    def _expire(self) -> None:
        """
        Check timestamp and expire last output if too old.
        """
        if self._last_output is not None:
            now = maka_control_timestamp_ms()
            age = now - self._last_output.timestamp_ms
            expire_threshold_ms = self._callback_expire_threshold_ms()
            if age > expire_threshold_ms * get_sleep_detune():
                self._status = f"age {age}ms > {expire_threshold_ms}ms"
                self._last_output = None

    def _filter_max_delta_error_angle(self, candidate: DriverError) -> Optional[DriverError]:
        """
        Filter the candidate error if too large of delta from lats output
        """
        if self._last_output is None:
            return candidate

        # FILTER if delta between last and current output is too large
        delta_threshold_deg = self._callback_max_delta_error_angle_deg()
        delta_threshold_rad = np.deg2rad(delta_threshold_deg)
        delta = self._last_output.error_angle_rad - candidate.error_angle_rad
        if abs(delta) > delta_threshold_rad:
            self._status = f"delta_error_angle_deg = |{round(delta, 2)}| > {delta_threshold_deg}"
            LOG.warning(
                f"{self.topic} Filtering delta_error_angle_deg = abs({delta}) > {delta_threshold_deg} "
                f"because delta too large from last non-expired result"
            )
            return None
        return candidate

    def _filter_max_error_angle(self, candidate: DriverError) -> Optional[DriverError]:
        """
        Filter the candidate error if too large
        """
        threshold_deg = self._callback_max_error_angle_deg()
        threshold_rad = deg2rad(threshold_deg)
        if abs(candidate.error_angle_rad) > threshold_rad:
            error_angle_deg = rad2deg(candidate.error_angle_rad)
            self._status = f"error_angle_deg = |{round(error_angle_deg, 2)}| > {threshold_deg}"
            LOG.debug(
                f"{self.topic} Filtering error_angle_deg = {error_angle_deg} > {threshold_deg} " f"because too large"
            )
            return None
        return candidate

    def _filter_max_error_x_pct(self, candidate: DriverError) -> Optional[DriverError]:
        """
        Filter the candidate error if too large
        """
        threshold_x_pct = self._callback_max_error_x_pct()
        if abs(candidate.error_x_pct) > threshold_x_pct:
            self._status = f"error_x_pct = |{round(candidate.error_x_pct, 2)}| > {threshold_x_pct}"
            LOG.debug(
                f"{self.topic} Filtering error_x_pct = {candidate.error_x_pct} > {threshold_x_pct} "
                f"because too large"
            )
            return None
        return candidate

    def read(self) -> Optional[DriverError]:
        # check if we should expire old furrow
        self._expire()

        # check for latest incoming furrow
        candidate: Optional[DriverError] = self._input_subscription.read()
        if candidate is None:
            return self._last_output

        candidate = self._filter_max_error_angle(candidate)
        if candidate is None:
            return self._last_output

        candidate = self._filter_max_delta_error_angle(candidate)
        if candidate is None:
            return self._last_output

        candidate = self._filter_max_error_x_pct(candidate)
        if candidate is None:
            return self._last_output

        self._last_output = candidate
        self._expire()
        return self._last_output


class Fuse(DriverErrorChannel):
    """
    Fuse 1+ error channels together into a single error.
    """

    def __init__(
        self,
        topic: Topic,
        input_channels: Sequence[DriverErrorChannel],
        filter_expiration_ms: Callable[[], int] = lambda: DEFAULT_FILTER_EXPIRATION_MS,
    ):
        super().__init__(topic=topic)
        assert len(input_channels) > 0
        self._input_channels: Sequence[DriverErrorChannel] = input_channels
        self._callback_filter_expiration_ms: Callable[[], int] = filter_expiration_ms

        # variable tracked to reduce excessive logging
        self._last_warn_expired_timestamp_ms: int = 0
        self._last_warn_missing_timestamp_ms: int = 0

        # possibly expired entries
        self._latest_by_input_topic: Dict[Topic, DriverError] = {}

    def status(self) -> str:
        return ", ".join([c.status() for c in self._input_channels])

    def read_channel(self, now: int, channel: DriverErrorChannel, expiration_ms: int) -> Optional[DriverError]:
        latest_optional: Optional[DriverError] = channel.read()
        if latest_optional is None:
            if now > self._last_warn_missing_timestamp_ms + 1000:
                most_recent: Optional[DriverError] = self._latest_by_input_topic.get(channel.topic)
                if most_recent is None:
                    LOG.warning(
                        f"Cannot fuse {channel.topic}. Never seen input from that channel. Seen: {self._latest_by_input_topic}"
                    )
                else:
                    LOG.warning(
                        f"No result from {channel.topic} since {iso8601_timestamp(seconds=most_recent.timestamp_ms / 1000)}. Cannot fuse."
                    )
                self._last_warn_missing_timestamp_ms = now
            return None
        latest: DriverError = latest_optional

        age = now - latest.timestamp_ms
        if age > expiration_ms:
            if latest.timestamp_ms > self._last_warn_expired_timestamp_ms:
                LOG.warning(f"{self.topic} Ignoring {channel.topic} due to age_ms = {age} > {expiration_ms}")
                self._last_warn_expired_timestamp_ms = latest.timestamp_ms
            return None

        self._latest_by_input_topic[channel.topic] = latest

        return latest

    def read_inputs(self, now: int, expiration_ms: int) -> Dict[Topic, Optional[DriverError]]:
        return {c.topic: self.read_channel(now, c, expiration_ms) for c in self._input_channels}

    def fuse(self, inputs: List[DriverError]) -> DriverError:
        min_angle_index = 0
        min_error_angle_rad = abs(inputs[0].error_angle_rad) if len(inputs) > 0 else 0
        for index, t in enumerate(inputs):
            if abs(t.error_angle_rad) < min_error_angle_rad:
                min_angle_index = index
                min_error_angle_rad = abs(t.error_angle_rad)

        avg_actual_velocity_mph = inputs[min_angle_index].actual_velocity_mph
        avg_error_velocity_mph = inputs[min_angle_index].error_velocity_mph
        avg_error_angle_rad = inputs[min_angle_index].error_angle_rad
        avg_error_x_pct = inputs[min_angle_index].error_x_pct
        latest_timestamp_ms = inputs[min_angle_index].timestamp_ms
        LOG.debug(
            f"{self.topic} Fused {len(inputs)}/{len(self._input_channels)} inputs amongst "
            f"{', '.join([c.topic for c in self._input_channels])}"
        )

        return DriverError(
            timestamp_ms=latest_timestamp_ms,
            error_angle_rad=avg_error_angle_rad,
            error_x_pct=avg_error_x_pct,
            error_velocity_mph=avg_error_velocity_mph,
            actual_velocity_mph=avg_actual_velocity_mph,
        )

    def read(self) -> Optional[DriverError]:
        now = maka_control_timestamp_ms()
        expiration_ms = self._callback_filter_expiration_ms()

        available_inputs: Dict[Topic, DriverError] = {
            k: v for k, v in self.read_inputs(now=now, expiration_ms=expiration_ms).items() if v is not None
        }

        if len(available_inputs) == 0:
            return None

        return self.fuse(list(available_inputs.values()))
