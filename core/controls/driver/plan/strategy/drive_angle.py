from typing import Optional

from core.controls.driver.driver_error import DriverError
from core.controls.driver.plan.instruction import DriveAngleInstruction, DriverInstruction, TerminateDriverInstruction
from core.controls.driver.plan.strategy.base import DriverErrorStrategy, StrategyType
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class DriveAngleStrategy(DriverErrorStrategy):
    """
    Emit DriveAngleInstructions
    """

    def __init__(self) -> None:
        super().__init__()
        self._last_driver_error: Optional[DriverError] = None
        # TODO time based and do in based class so works for target_angle too?
        # is this the right spot for this...?
        self._consecutive_no_change_velocity_error: int = 0
        self._fail_consecutive_no_change_velocity_error: int = 500

    @classmethod
    def strategy(cls) -> StrategyType:
        return StrategyType.DRIVE_ANGLE

    def evaluate(self, driver_error: DriverError) -> DriverInstruction:
        instruction: DriverInstruction = DriveAngleInstruction(
            error_angle_rad=driver_error.error_angle_rad,
            error_velocity_mph=driver_error.error_velocity_mph,
            actual_velocity_mph=driver_error.actual_velocity_mph,
        )

        if self._last_driver_error is None:
            self._last_driver_error = driver_error
            return instruction

        if driver_error.timestamp_ms > self._last_driver_error.timestamp_ms:
            if (
                driver_error.error_velocity_mph != 0
                and driver_error.error_velocity_mph == self._last_driver_error.error_velocity_mph
            ):
                self._consecutive_no_change_velocity_error += 1
            else:
                self._consecutive_no_change_velocity_error = 0

            if self._consecutive_no_change_velocity_error >= self._fail_consecutive_no_change_velocity_error:
                LOG.error(
                    f"Driver not responding to DriveAngleInstructions after {self._consecutive_no_change_velocity_error} ticks. Abort!"
                )
                instruction = TerminateDriverInstruction(
                    reason=f"unresponsive {self._consecutive_no_change_velocity_error} ticks"
                )

        return instruction
