from core.controls.driver.driver_error import DriverError
from core.controls.driver.plan.instruction import DriverInstruction, StopInstruction, TargetAngleInstruction
from core.controls.driver.plan.strategy.base import DriverErrorStrategy, StrategyType

ALIGNED_ERROR_ANGLE_RAD = 0.05


class TargetAngleStrategy(DriverErrorStrategy):
    """
    Emit TargetAngleInstructions until finally StopInstruction when close enough
    """

    @classmethod
    def strategy(cls) -> StrategyType:
        return StrategyType.TARGET_ANGLE

    def __init__(self, aligned_threshold_rad: float = ALIGNED_ERROR_ANGLE_RAD):
        self._aligned_threshold_rad = aligned_threshold_rad

    def evaluate(self, driver_error: DriverError) -> DriverInstruction:
        if abs(driver_error.error_angle_rad) <= self._aligned_threshold_rad:
            return StopInstruction(reason="target aligned")
        else:
            return TargetAngleInstruction(
                error_angle_rad=driver_error.error_angle_rad,
                error_velocity_mph=driver_error.error_velocity_mph,
                actual_velocity_mph=driver_error.actual_velocity_mph,
            )
