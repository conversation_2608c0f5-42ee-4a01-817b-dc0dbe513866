from abc import ABC, abstractmethod
from enum import Enum

from core.controls.driver.driver_error import DriverError
from core.controls.driver.plan.instruction import DriverInstruction


class StrategyType(Enum):
    """
    An enumeration of the available strategies.
    """

    TARGET_ANGLE = "target_angle"
    DRIVE_ANGLE = "drive_angle"


class BaseInstructionStrategy(ABC):
    """
    The bare base class that ensures everybody is connected with the enumeration
    """

    @classmethod
    @abstractmethod
    def strategy(cls) -> StrategyType:
        pass


class DriverErrorStrategy(BaseInstructionStrategy):
    """
    A strategy based on incoming heading angular error
    """

    @abstractmethod
    def evaluate(self, driver_error: DriverError) -> DriverInstruction:
        pass
