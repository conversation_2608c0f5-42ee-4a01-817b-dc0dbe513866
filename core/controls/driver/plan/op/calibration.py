from abc import ABC, abstractmethod
from typing import Any, Callable, Optional, cast

from core.controls.driver.parameters import DriverParameter, DriverParameters
from core.controls.driver.plan.instruction import DriveInstruction
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from core.model.topics import Topics
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.feed.base import Feed
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

DEFAULT_SLOTNAME_FORWARD = "calibrate_forward"
DEFAULT_SLOTNAME_STEER = "calibrate_steer"


def max_velocity_vector(v: VelocityMessage) -> float:
    return cast(float, max(abs(v.x), abs(v.y), abs(v.z)))


def is_nonzero(v: VelocityMessage) -> bool:
    return max_velocity_vector(v) > 0


def is_zero(v: VelocityMessage) -> bool:
    return not is_nonzero(v)


class DriverCalibrationException(Exception):
    pass


class BaseCalibrationDriveOp(Op, ABC):
    """
    Base shared data slots
    """

    def __init__(
        self,
        *,
        slotname_forward: str = DEFAULT_SLOTNAME_FORWARD,
        slotname_steer: str = DEFAULT_SLOTNAME_STEER,
        velocity_mph_subscription: SubscriberChannel[VelocityMessage],
    ):

        self._slotname_forward = slotname_forward
        self._slotname_steer = slotname_steer

        self._velocity_mph_subscription = velocity_mph_subscription


class BaseDriveUntilVelocityCondition(BaseCalibrationDriveOp, ABC):
    """
    Drive straight while ramping forward increment until a given velocity condition is met.
    """

    def __init__(
        self,
        *,
        ramp_forward_increment: Callable[[], float],
        ramp_time_ms: Callable[[], int],
        velocity_mph_subscription: SubscriberChannel[VelocityMessage],
        slotname_forward: str = DEFAULT_SLOTNAME_FORWARD,
        slotname_steer: str = DEFAULT_SLOTNAME_STEER,
    ):
        super().__init__(
            slotname_forward=slotname_forward,
            slotname_steer=slotname_steer,
            velocity_mph_subscription=velocity_mph_subscription,
        )

        self._ramp_forward: float = 0
        self._ramp_forward_increment: Callable[[], float] = ramp_forward_increment
        assert 0 < abs(self._ramp_forward_increment()) <= 1
        self._ramp_time_ms: Callable[[], int] = ramp_time_ms
        self._last_ramp_instruction: Optional[DriveInstruction] = None
        self._last_drive_ramp_time_ms: int = 0

    @property
    @abstractmethod
    def ramp_direction(self) -> int:
        pass

    @property
    @abstractmethod
    def velocity_condition_description(self) -> str:
        pass

    @abstractmethod
    def _velocity_condition_met(self, v: VelocityMessage) -> bool:
        pass

    def tick(self, data: RunData) -> OpTickGen:
        v = self._velocity_mph_subscription.read()
        if v is None:
            LOG.warning("current velocity subscription failure for drive calibration (will retry)")
            yield Again()
        if self._velocity_condition_met(v):
            raise DriverCalibrationException(
                f"Velocity condition: {self.velocity_condition_description} is already satisfied: {v}"
            )

        # start with appropriate forward value if already in a calibration sequence
        if self._slotname_forward in data:
            self._ramp_forward = cast(float, data[self._slotname_forward])
            assert isinstance(self._ramp_forward, float)

        last_log_ms: int = 0
        while True:
            v = self._velocity_mph_subscription.read()
            if v is None:
                LOG.warning("current velocity subscription failure for drive calibration (will retry)")
                yield Again()
            if self._velocity_condition_met(v):
                LOG.info(f"Velocity condition: {self.velocity_condition_description} is satisfied: {v}")
                yield Done()
                return

            # still zero, keeping ramping?
            now = maka_control_timestamp_ms()
            if now >= self._last_drive_ramp_time_ms + self._ramp_time_ms():
                before = self._ramp_forward
                self._last_drive_ramp_time_ms = now
                self._ramp_forward = self._ramp_forward + self.ramp_direction * self._ramp_forward_increment()

                if (before > 0 and self._ramp_forward < 0) or (before < 0 and self._ramp_forward > 0):
                    LOG.warning("Ramp direction would cross 0. Snap to 0.")
                    self._ramp_forward = 0

                # floating point error accumulates
                precision = len(str(str(self._ramp_forward_increment).split(".")[-1]))
                self._ramp_forward = round(self._ramp_forward, precision)

                LOG.info(f"Ramp forward: {before} --> {self._ramp_forward}")
                last_log_ms = now
            elif now >= last_log_ms + 1000:
                LOG.info(f"Ramp forward: {self._ramp_forward}")
                last_log_ms = now

            data[self._slotname_forward] = self._ramp_forward
            data[self._slotname_steer] = 0
            yield Again(instruction=DriveInstruction(forward=self._ramp_forward, steer=0, actual_velocity_mph=None))


class DriveUntilNonZeroVelocity(BaseDriveUntilVelocityCondition):
    @property
    def ramp_direction(self) -> int:
        return 1

    @property
    def velocity_condition_description(self) -> str:
        return "is_nonzero"

    def _velocity_condition_met(self, v: VelocityMessage) -> bool:
        return is_nonzero(v)


class DriveUntilZeroVelocity(BaseDriveUntilVelocityCondition):
    @property
    def ramp_direction(self) -> int:
        return -1

    @property
    def velocity_condition_description(self) -> str:
        return "is_zero"

    def _velocity_condition_met(self, v: VelocityMessage) -> bool:
        return is_zero(v)


class DriveUntilVelocity(BaseDriveUntilVelocityCondition):
    def __init__(self, velocity_mph: float, **kwargs: Any):
        super().__init__(**kwargs)
        self._velocity_mph = velocity_mph
        assert self._velocity_mph > 0

    @property
    def ramp_direction(self) -> int:
        return 1

    @property
    def velocity_condition_description(self) -> str:
        return f">={self._velocity_mph}mph"

    def _velocity_condition_met(self, v: VelocityMessage) -> bool:
        return max_velocity_vector(v) >= self._velocity_mph


class KeepDriving(BaseCalibrationDriveOp):
    """
    Keep driving according to the forward/steer in the data slots
    """

    def tick(self, data: RunData) -> OpTickGen:
        last_log_ms: int = 0
        while True:
            instruction = DriveInstruction(
                forward=cast(float, data[self._slotname_forward]),
                steer=cast(float, data[self._slotname_steer]),
                actual_velocity_mph=None,
            )

            now = maka_control_timestamp_ms()
            if now >= last_log_ms + 1000:
                LOG.info(f"Keep Driving forward: {instruction.forward}")
                last_log_ms = now

            yield Done(instruction=instruction)


def drive_until_nonzero_velocity(autodrive_params: DriverParameters, feed: Feed) -> DriveUntilNonZeroVelocity:
    return DriveUntilNonZeroVelocity(
        ramp_forward_increment=lambda: autodrive_params[
            DriverParameter.CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED
        ].float_value,
        ramp_time_ms=lambda: autodrive_params[DriverParameter.CALIBRATE_RAMP_UP_MS].int_value,
        velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage),
    )


def drive_until_velocity(velocity_mph: float, autodrive_params: DriverParameters, feed: Feed) -> DriveUntilVelocity:
    return DriveUntilVelocity(
        velocity_mph=velocity_mph,
        ramp_forward_increment=lambda: autodrive_params[
            DriverParameter.CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED
        ].float_value,
        ramp_time_ms=lambda: autodrive_params[DriverParameter.CALIBRATE_RAMP_UP_MS].int_value,
        velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage),
    )


def drive_until_zero_velocity(autodrive_params: DriverParameters, feed: Feed) -> DriveUntilZeroVelocity:
    return DriveUntilZeroVelocity(
        ramp_forward_increment=lambda: autodrive_params[
            DriverParameter.CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED
        ].float_value,
        ramp_time_ms=lambda: autodrive_params[DriverParameter.CALIBRATE_RAMP_UP_MS].int_value,
        velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage),
    )


def keep_driving(feed: Feed) -> KeepDriving:
    return KeepDriving(velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage))
