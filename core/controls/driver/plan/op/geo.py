import math
from typing import Dict, List, Optional, Set, Tuple, Union, cast

import navpy
import numpy as np

from core.controls.driver.driver_error import VelocityError, error_velocity_mph_channel
from core.controls.driver.localization.geofence import GeoFenceMatch
from core.controls.driver.parameters import DriverParameters
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.instruction import DriveAngleInstruction
from core.controls.driver.plan.subscription_types import HeadingSubscription, current_position
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.frame.pose.body import RobotBody
from core.controls.plan.ops.condition import AlwaysFalse, AlwaysTrue, ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from core.model.topics import Topics
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.geo.boundary import BoundaryType
from lib.common.geo.ecef_utils import (
    Ecef<PERSON><PERSON><PERSON>rid<PERSON><PERSON>vert,
    <PERSON>cef<PERSON><PERSON>,
    EcefVector,
    angle2heading,
    ecef_distance,
    heading2angle,
    inches2meters,
    lla2ecef_iterable,
)
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.feed.base import Feed
from lib.common.time.time import maka_control_timestamp_ms

LOG = get_logger(__name__)

GEOFENCE_MATCH_RETRY_MS = 1000
METERS_TO_MILES = 1609.34


class GeoMatchType:
    def __init__(self, boundary_type: BoundaryType, keep_out: bool):
        self.boundary_type = boundary_type
        self.keep_out = keep_out


class EortaFencer:
    def __init__(self, feed: Feed, field_convert: EcefFieldGridConvert):
        self._feed = feed
        self._field_convert = field_convert
        self._ecef_sub = feed.subscribe(Topics.BODY_POSITION.topic, RobotBody)
        self._geofence_sub = feed.subscribe(Topics.GEOFENCE.topic, GeopositionEcefMessage)
        self._match: Optional[GeoFenceMatch] = None
        self._match_polys: Optional[Dict[str, EcefPoly]] = None

    def eorta_edge_meters(
        self,
        heading: float,
        match_types: Set[BoundaryType] = {BoundaryType.CROPS, BoundaryType.FIELD, BoundaryType.TURN_AROUND},
    ) -> Optional[float]:
        if self._match is None:
            self._match = self._geofence_sub.read()
        position_ecef = current_position(self._ecef_sub)
        if self._match is None or position_ecef is None:
            return None

        if self._match.available is None:
            LOG.warn("no available geofences")
            return None

        if self._match_polys is None:
            self._match_polys = {}
            for id, boundary in self._match.available:
                self._match_polys[id] = EcefPoly(boundary)

        best_distance_meters: Optional[float] = None

        # Project out in front of us - nearest thing we intersect with is either
        # 1) edge of FIELD
        # 2) edge of TURN_AROUND
        # 3) edge of CROPS
        # Use the shortest of any of these
        field_position = self._field_convert.point_convert(position_ecef)
        for id, boundary in self._match.available:
            if boundary.type not in match_types:
                continue
            # Convert to field convert
            field_poly = self._field_convert.poly_convert(self._match_polys[id])

            # Track one mile forward of our current position on current heading
            angle = heading2angle(heading)
            one_mile = field_position + (np.array([math.cos(angle), math.sin(angle)]) * METERS_TO_MILES)
            drive_projection = EcefVector(field_position, field_position + one_mile)

            # Find where this intersects that geofence
            entry_point = field_poly.entry_point_xy(drive_projection)

            # If it doesn't intersect then keep looking
            if entry_point is None:
                continue

            entry_point_ecef = self._field_convert.point_revert(entry_point, field_alt=0.4)
            distance_meters = ecef_distance(position_ecef, entry_point_ecef)
            if best_distance_meters is None:
                best_distance_meters = distance_meters
            elif best_distance_meters > distance_meters:
                best_distance_meters = distance_meters

        return best_distance_meters

    def eorta_edge_meters_project(
        self, match_types: Set[BoundaryType] = {BoundaryType.CROPS, BoundaryType.FIELD, BoundaryType.TURN_AROUND}
    ) -> Tuple[Optional[float], Optional[np.ndarray]]:
        if self._match is None:
            self._match = self._geofence_sub.read()
        position_ecef = current_position(self._ecef_sub)
        if self._match is None or position_ecef is None:
            return None, None

        if self._match.available is None:
            LOG.warn("no available geofences")
            return None, None

        best_distance_meters: Optional[float] = None
        best_distance_point: Optional[np.ndarray] = None

        # Project current position onto geofence boundary
        # 1) edge of FIELD
        # 2) edge of TURN_AROUND
        # 3) edge of CROPS
        # Use the shortest of any of these
        field_position = self._field_convert.point_convert(position_ecef)
        for _, boundary in self._match.available:
            if boundary.type not in match_types:
                continue
            # Convert to field convert
            field_poly = self._field_convert.poly_convert(EcefPoly(boundary))

            # Find where this projects onto that geofence
            distance_meters, hit_position = field_poly.project(field_position)
            if distance_meters is None or hit_position is None:
                continue

            if best_distance_meters is None:
                best_distance_meters = distance_meters
                best_distance_point = self._field_convert.point_revert(hit_position, field_alt=0.4)
            elif best_distance_meters > distance_meters:
                best_distance_meters = distance_meters
                best_distance_point = self._field_convert.point_revert(hit_position, field_alt=0.4)

        return best_distance_meters, best_distance_point


class GeoFenceUntil(ConditionOp):
    def __init__(
        self,
        match_types: List[GeoMatchType],
        geofence_subscription: SubscriberChannel[GeoFenceMatch],
        multi_check: bool = False,
    ):
        super().__init__()
        self._match_types = match_types
        self._geofence_subscription = geofence_subscription
        self._multi_check = multi_check

    def condition(self, data: RunData) -> ConditionGenType:
        first_match_ms: Optional[int] = None
        last_in_match: Optional[GeoFenceMatch] = None
        keep_out: bool = False
        while True:
            ok = True
            match = self._geofence_subscription.read()
            if match is None:
                yield ok, Again()
                continue
            for mt in self._match_types:
                # Filter for boundaries that actually exist. This is so that we can operate in TURN_AROUND
                # mode or CROPS mode.
                if mt.boundary_type not in match.available_types:
                    continue
                not_in: bool = len([m for m in match.matched if m.type == mt.boundary_type]) == 0
                if mt.keep_out:
                    keep_out = True
                    ok = not_in
                else:
                    keep_out = False
                    ok = not not_in
                    # if we are continuing while IN then keep remembering the last thing we were IN because the
                    # plan will exit when we are OUT
                    if ok:
                        last_in_match = match

                # If we need multiple yes examples ...
                if self._multi_check:
                    # If we haven't matched this time then don't keep tracking ...
                    if ok:
                        first_match_ms = None
                    else:
                        # Reset to "not matched" and validate time ....
                        ok = True
                        if first_match_ms is None:
                            first_match_ms = maka_control_timestamp_ms()
                        else:
                            now = maka_control_timestamp_ms()
                            if now - first_match_ms > GEOFENCE_MATCH_RETRY_MS:
                                ok = False
            if not ok:
                if keep_out:
                    data["geofence_until_exit_match"] = match
                else:
                    if last_in_match is not None:
                        data["geofence_until_exit_match"] = last_in_match
            yield ok, Done(local_data={"ok": ok})


class ApproachEcefPoint(Op):
    def __init__(
        self,
        feed: Feed,
        error_velocity_mph_subscription: SubscriberChannel[VelocityError],
        heading_subscription: HeadingSubscription,
        lla_path_slotname: str,
        target_point_slotname: str,
        reverse: bool,
        world_hud: Optional[WorldHUD],
    ):
        super().__init__()
        self._feed = feed
        self._ecef_sub = feed.subscribe(Topics.BODY_POSITION.topic, RobotBody)
        self._error_velocity_mph_subscription = error_velocity_mph_subscription
        self._heading_subscription = heading_subscription
        self._lla_path_slotname = lla_path_slotname
        self._target_point_slotname = target_point_slotname
        self._backwards = reverse
        self._world_hud = world_hud
        self._eorta_fencer: Optional[EortaFencer] = None

    def tick(self, data: RunData) -> OpTickGen:
        lla_path: List[np.ndarray] = cast(List[np.ndarray], data[self._lla_path_slotname])
        start_point: np.ndarray = lla2ecef_iterable(lla_path)[0]
        field_convert = EcefFieldGridConvert.from_surface_normal(start_point)
        self._eorta_fencer = EortaFencer(self._feed, field_convert)

        while True:
            # All the reasons we might want to try again
            if self._target_point_slotname not in data:
                yield Done()
                continue
            error_velocity_mph = self._error_velocity_mph_subscription.read()
            if error_velocity_mph is None:
                yield Again()
                continue
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield Again()
                continue
            heading = heading_optional.heading
            if self._backwards:
                heading = (heading + 180) % 360  # get our reverse heading

            position = current_position(self._ecef_sub)
            if position is None:
                yield Again()
                continue

            # Grab our target point and position
            target_point = cast(np.ndarray, data[self._target_point_slotname])
            target_point = navpy.lla2ecef(target_point[0], target_point[1], target_point[2], "deg")

            # heading will be aligned with field_convert
            heading_angle = heading2angle(heading)
            # Convert to field grid
            position = field_convert.point_convert(position)
            target_point = field_convert.point_convert(target_point)
            angle: float = 0

            # Get error angle between heading and destination
            target_vec = EcefVector(position, target_point)

            basis_vec = EcefVector(np.array([0, 0]), np.array([1, 0]))
            target_basis_angle = basis_vec.angle_between(target_vec)

            angle = (target_basis_angle - heading_angle) % (math.pi * 2)

            if abs(angle) > math.pi:
                angle = -(2 * math.pi - angle)

            angle = -angle

            if self._world_hud is not None:
                basis_vec = EcefVector(np.array([0, 0]), np.array([1, 0]))
                target_basis_angle = target_vec.angle_between(basis_vec)
                target_heading = angle2heading(target_basis_angle)
                angle_heading = angle2heading(angle)
                self._world_hud.set_text(f"h: {heading}\nt: {target_heading}\nah: {angle_heading}\n:a: {angle}")
            instruction = DriveAngleInstruction(
                error_angle_rad=angle,
                error_velocity_mph=error_velocity_mph.error_velocity_mph,
                actual_velocity_mph=error_velocity_mph.actual_velocity_mph,
            )
            eim = self._eorta_fencer.eorta_edge_meters(heading)
            data["eorta inside meters"] = eim or 0
            yield Done(instruction=instruction)


class UntilDistanceEorta(ConditionOp):
    def __init__(
        self,
        feed: Feed,
        match_types: List[GeoMatchType],
        reverse: bool,
        distance_inches: float,
        field_location_notifier: Optional[FieldLocationNotifier] = None,
    ):
        super().__init__()
        self._feed = feed
        self._match_types = match_types
        self._geofence_sub = feed.subscribe(Topics.GEOFENCE.topic, GeoFenceMatch)
        self._heading_sub = feed.subscribe(Topics.HEADING.topic, HeadingMessage)
        self._ecef_sub = feed.subscribe(Topics.BODY_POSITION.topic, RobotBody)
        self._reverse = reverse
        self._distance_inches = distance_inches
        self._field_location_notifier = field_location_notifier

    def condition(self, data: RunData) -> ConditionGenType:
        field_convert: Optional[EcefFieldGridConvert] = None
        eorta_fencer: Optional[EortaFencer] = None
        while True:
            heading = self._heading_sub.read()
            position_ecef = current_position(self._ecef_sub)
            if heading is None or position_ecef is None:
                yield True, Again()
                continue
            heading = heading.heading
            if self._reverse:
                heading = (heading + 180) % 360

            if field_convert is None:
                field_convert = EcefFieldGridConvert.from_surface_normal(position_ecef)
            if eorta_fencer is None:
                eorta_fencer = EortaFencer(self._feed, field_convert)

            distance_meters: float = 20 * METERS_TO_MILES  # High sentinal value meaning "not yet"
            there: bool = False

            calc_distance_meters = eorta_fencer.eorta_edge_meters(heading)
            if calc_distance_meters is not None:
                distance_meters = calc_distance_meters

            data["meters until TA"] = distance_meters  # helpul ui output to drive cam
            if distance_meters < inches2meters(self._distance_inches):
                if self._field_location_notifier is not None:
                    self._field_location_notifier.clear_route_vectors()
                there = True

            yield not there, Done()


def until_turn_around(feed: Feed, no_gps: bool = False) -> Union[GeoFenceUntil, AlwaysTrue]:
    # match while either:
    #     1) not in TURN_AROUND
    #     2) in CROPS
    if no_gps:
        return AlwaysTrue()

    return GeoFenceUntil(
        [
            GeoMatchType(boundary_type=BoundaryType.TURN_AROUND, keep_out=True),
            GeoMatchType(boundary_type=BoundaryType.CROPS, keep_out=False),
        ],
        geofence_subscription=feed.subscribe(Topics.GEOFENCE.topic, GeoFenceMatch),
        multi_check=True,
    )


def until_distance_to_eorta(
    feed: Feed,
    reverse: bool,
    distance_inches: float,
    no_gps: bool = False,
    field_location_notifier: Optional[FieldLocationNotifier] = None,
) -> Union[UntilDistanceEorta, AlwaysTrue]:
    if no_gps:
        return AlwaysTrue()

    return UntilDistanceEorta(
        feed=feed,
        match_types=[
            GeoMatchType(boundary_type=BoundaryType.TURN_AROUND, keep_out=True),
            GeoMatchType(boundary_type=BoundaryType.CROPS, keep_out=False),
        ],
        reverse=reverse,
        distance_inches=distance_inches,
        field_location_notifier=field_location_notifier,
    )


def until_not_turn_around(feed: Feed, no_gps: bool = False) -> Union[GeoFenceUntil, AlwaysFalse]:
    # match while either:
    #     1) in TURN_AROUND
    #     2) not in CROPS
    if no_gps:
        return AlwaysFalse()

    return GeoFenceUntil(
        [
            GeoMatchType(boundary_type=BoundaryType.TURN_AROUND, keep_out=False),
            GeoMatchType(boundary_type=BoundaryType.CROPS, keep_out=True),
        ],
        geofence_subscription=feed.subscribe(Topics.GEOFENCE.topic, GeoFenceMatch),
    )


def in_field(feed: Feed) -> GeoFenceUntil:
    return GeoFenceUntil(
        [GeoMatchType(boundary_type=BoundaryType.FIELD, keep_out=False)],
        geofence_subscription=feed.subscribe(Topics.GEOFENCE.topic, GeoFenceMatch),
        multi_check=True,
    )


def approach_ecef_point(
    feed: Feed,
    lla_path_slotname: str,
    target_point_slotname: str,
    world_hud: Optional[WorldHUD],
    autodrive_params: DriverParameters,
    reverse: Optional[bool] = None,
) -> ApproachEcefPoint:
    if reverse is None:
        reverse = False
    return ApproachEcefPoint(
        feed=feed,
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        lla_path_slotname=lla_path_slotname,
        target_point_slotname=target_point_slotname,
        reverse=reverse,
        world_hud=world_hud,
    )
