from typing import List, Optional

from core.controls.driver.driver_error import Driver<PERSON><PERSON><PERSON>, DriverErrorChannel, error_velocity_mph_channel
from core.controls.driver.parameters import DriverParameter, DriverParameters
from core.controls.driver.plan.error_angle import ComputeError, Filter, Fuse
from core.controls.driver.plan.op.driver_error import Aligned, Drive, DriverErrorStrategyEventGen
from core.controls.driver.plan.strategy.base import DriverErrorStrategy
from core.controls.driver.plan.strategy.drive_angle import DriveAngleStrategy
from core.controls.driver.plan.strategy.target_angle import TargetAngleStrategy
from core.controls.frame.mechanical import DriveCamMechanicalConfig
from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Done
from core.controls.plan.state.run_data import RunData
from core.cv.visual_cortex.graph.output import FurrowsOutput
from core.cv.visual_cortex.processor.furrows import Closest
from core.model.topics import Topics
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.feed.base import Feed

LOG = get_logger(__name__)


class FoundFurrows(ConditionOp):
    def __init__(self, slopes: DriverErrorStrategyEventGen):
        super().__init__()
        self._slopes = slopes
        self._error_subscription = slopes.error_subscription

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            error: Optional[DriverError] = self._error_subscription.read()
            ok: bool = error is not None
            if not ok:
                LOG.warning("Cannot find furrows")
            yield ok, Done()


#
# Easy Creators
#


def furrows_aligned(feed: Feed, autodrive_params: DriverParameters) -> Aligned:
    furrows_subscription: SubscriberChannel[FurrowsOutput] = feed.subscribe(
        Topics.FURROWS_FRONT_RIGHT.topic, FurrowsOutput
    )
    error_subscription = ComputeError(
        target_furrow_subscription=Closest(furrows_subscription),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        drive_cam_mech_cfg_subscription=feed.subscribe(
            Topics.DRIVE_CAM_MECH_CFG_FRONT_RIGHT.topic, DriveCamMechanicalConfig
        ),
        target_speed_mph=lambda: autodrive_params[DriverParameter.TARGET_SPEED_MPH].float_value,
    )
    return Aligned(error_subscription=error_subscription)


def _base_get_furrow_subscription(
    ignored: bool, feed: Feed, furrow_topic: Topic, mech_cfg_topic: Topic, autodrive_params: DriverParameters,
) -> Optional[Filter]:

    filter_args = {
        DriverParameter.FILTER_EXPIRATION_MS.lower(): lambda: autodrive_params[
            DriverParameter.FILTER_EXPIRATION_MS
        ].int_value,
        DriverParameter.FILTER_MAX_DELTA_ERROR_ANGLE_DEG.lower(): lambda: autodrive_params[
            DriverParameter.FILTER_MAX_DELTA_ERROR_ANGLE_DEG
        ].float_value,
        DriverParameter.FILTER_MAX_ERROR_ANGLE_DEG.lower(): lambda: autodrive_params[
            DriverParameter.FILTER_MAX_ERROR_ANGLE_DEG
        ].float_value,
        DriverParameter.FILTER_MAX_ERROR_X_PCT.lower(): lambda: autodrive_params[
            DriverParameter.FILTER_MAX_ERROR_X_PCT
        ].float_value,
    }

    if not ignored and furrow_topic in feed.topics:
        error_subscription = Filter(
            ComputeError(
                target_furrow_subscription=Closest(feed.subscribe(furrow_topic, FurrowsOutput)),
                error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
                drive_cam_mech_cfg_subscription=feed.subscribe(mech_cfg_topic, DriveCamMechanicalConfig),
                target_speed_mph=lambda: autodrive_params[DriverParameter.TARGET_SPEED_MPH].float_value,
            ),
            **filter_args,
        )

        LOG.info(f"Using {furrow_topic} camera for following furrows")
        return error_subscription

    elif ignored:
        LOG.debug(f"Ignoring {furrow_topic}")
    elif "LEFT" not in str(furrow_topic).upper():
        # only log warning level for right cameras
        LOG.warning(f"{furrow_topic} unavailable")
    else:
        LOG.debug(f"{furrow_topic} unavailable")

    return None


def _base_furrows(
    feed: Feed,
    strategy: DriverErrorStrategy,
    autodrive_params: DriverParameters,
    ignore_back_cameras: bool = False,
    ignore_front_cameras: bool = False,
) -> Drive:
    assert not ignore_back_cameras or not ignore_front_cameras

    optional_channels: List[Optional[DriverErrorChannel]] = [
        _base_get_furrow_subscription(
            ignore_front_cameras,
            feed,
            Topics.FURROWS_FRONT_LEFT.topic,
            Topics.DRIVE_CAM_MECH_CFG_FRONT_LEFT.topic,
            autodrive_params,
        ),
        _base_get_furrow_subscription(
            ignore_front_cameras,
            feed,
            Topics.FURROWS_FRONT_RIGHT.topic,
            Topics.DRIVE_CAM_MECH_CFG_FRONT_RIGHT.topic,
            autodrive_params,
        ),
        _base_get_furrow_subscription(
            ignore_back_cameras,
            feed,
            Topics.FURROWS_BACK_LEFT.topic,
            Topics.DRIVE_CAM_MECH_CFG_BACK_LEFT.topic,
            autodrive_params,
        ),
        _base_get_furrow_subscription(
            ignore_back_cameras,
            feed,
            Topics.FURROWS_BACK_RIGHT.topic,
            Topics.DRIVE_CAM_MECH_CFG_BACK_RIGHT.topic,
            autodrive_params,
        ),
    ]
    fuse_inputs: List[DriverErrorChannel] = [c for c in optional_channels if c is not None]

    assert len(fuse_inputs) > 0, "No furrow camera available"

    LOG.info(f"Using fusion of {len(fuse_inputs)} cameras for following furrows")
    fused_error_subscription = Fuse(
        topic=Topic("/driver/error_fusion"),
        input_channels=fuse_inputs,
        filter_expiration_ms=lambda: autodrive_params[DriverParameter.FILTER_EXPIRATION_MS].int_value,
    )

    return Drive(error_subscription=fused_error_subscription, strategy=strategy)


def target_furrows(
    feed: Feed,
    autodrive_params: DriverParameters,
    ignore_back_cameras: bool = False,
    ignore_front_cameras: bool = False,
) -> Drive:
    return _base_furrows(
        feed=feed,
        autodrive_params=autodrive_params,
        strategy=TargetAngleStrategy(),
        ignore_back_cameras=ignore_back_cameras,
        ignore_front_cameras=ignore_front_cameras,
    )


def drive_down_furrows(
    feed: Feed,
    autodrive_params: DriverParameters,
    ignore_back_cameras: bool = False,
    ignore_front_cameras: bool = False,
) -> Drive:
    return _base_furrows(
        feed=feed,
        autodrive_params=autodrive_params,
        strategy=DriveAngleStrategy(),
        ignore_back_cameras=ignore_back_cameras,
        ignore_front_cameras=ignore_front_cameras,
    )


def found_furrows(
    feed: Feed,
    autodrive_params: DriverParameters,
    ignore_back_cameras: bool = False,
    ignore_front_cameras: bool = False,
) -> FoundFurrows:
    slopes = _base_furrows(
        feed=feed,
        autodrive_params=autodrive_params,
        strategy=DriveAngleStrategy(),
        ignore_back_cameras=ignore_back_cameras,
        ignore_front_cameras=ignore_front_cameras,
    )
    return FoundFurrows(slopes)
