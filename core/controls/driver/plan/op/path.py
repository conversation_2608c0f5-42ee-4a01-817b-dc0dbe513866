from typing import List, Op<PERSON>, Tuple, cast

import numpy as np

from core.controls.driver.constants import MACHINE_ROW_WIDTH_INCHES, MACHINE_TURN_RADIUS_INCHES
from core.controls.driver.localization.subscription_types import GeoFenceMatch
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.subscription_types import (
    GeopositionEcefSubscription,
    GPSHistoryVectorSubscription,
    current_position,
)
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from core.model.topics import Topics
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from lib.common.angle_direction import AngleDirection
from lib.common.geo.boundary import Boundary, BoundaryType
from lib.common.geo.ecef_utils import (
    EcefFieldGridConvert,
    <PERSON><PERSON>fPoly,
    EcefVector,
    angle2heading,
    heading2vector,
    inches2meters,
    lla2ecef_iterable,
    point_distance,
    point_norm,
    translate_vector,
)
from lib.common.logging import get_logger
from lib.common.protocol.feed.base import Feed

LOG = get_logger(__name__)

ALIGN_ENTRY_POINTS = 4
MACHINE_TURN_PLOT_DEGREES = 10
MACHINE_TURN_OFFSET_INCHES = 10 * 12


def geoposition_ecef_message_convert(field_convert: EcefFieldGridConvert, geop: GeopositionEcefMessage) -> np.ndarray:
    point = np.array([geop.x, geop.y, geop.z])
    return field_convert.point_convert(point)


class UntilPathComplete(ConditionOp):
    def __init__(
        self,
        ecef_subscription: GeopositionEcefSubscription,
        path_slotname: str,
        target_point_slotname: str,
        field_location_notifier: Optional[FieldLocationNotifier],
        world_hud: Optional[WorldHUD],
    ):
        self._ecef_sub = ecef_subscription
        self._path_slotname = path_slotname
        self._field_location_notifier = field_location_notifier
        self._world_hud = world_hud
        self._target_point_slotname = target_point_slotname

    def condition(self, data: RunData) -> ConditionGenType:
        lla_path: List[np.ndarray] = cast(List[np.ndarray], data[self._path_slotname])
        path = lla2ecef_iterable(lla_path)
        index = cast(int, data["ecef_path_index"])
        field_convert: Optional[EcefFieldGridConvert] = None

        last_distance: Optional[float] = None
        index += 1
        while index < len(path):
            while True:
                position = current_position(self._ecef_sub)
                if position is None:
                    yield True, Again()
                    continue

                if field_convert is None:
                    field_convert = EcefFieldGridConvert.from_surface_normal(position)

                # Get our target and update the state
                target_point = path[index]
                data[self._target_point_slotname] = lla_path[index]  # type: ignore

                # convert to field grid
                position = field_convert.point_convert(position)
                target_point = field_convert.point_convert(target_point)

                # Evaluate if we are there
                distance = point_distance(position, target_point)
                if last_distance is not None:
                    if distance < inches2meters(MACHINE_ROW_WIDTH_INCHES / 2) and distance > last_distance:
                        last_distance = None
                        break

                # Annotate the field
                if self._field_location_notifier is not None:
                    self._field_location_notifier.clear_path_vectors()
                    self._field_location_notifier.add_path_vector(
                        EcefVector(
                            field_convert.point_revert(position, 0.4), field_convert.point_revert(target_point, 0.4)
                        )
                    )

                last_distance = distance
                yield True, Done()
            index += 1

        if self._field_location_notifier is not None:
            self._field_location_notifier.clear_path_vectors()
            self._field_location_notifier.clear_route_vectors()
        if self._world_hud is not None:
            self._world_hud.set_text("")
        yield False, Done()


class UntilPathJoined(ConditionOp):
    def __init__(
        self,
        ecef_subscription: GeopositionEcefSubscription,
        path_slotname: str,
        target_point_slotname: str,
        field_location_notifier: Optional[FieldLocationNotifier],
    ):
        self._ecef_sub = ecef_subscription
        self._path_slotname = path_slotname
        self._field_location_notifier = field_location_notifier
        self._target_point_slotname = target_point_slotname

    def _get_target_point(self, field_path: List[np.ndarray], position: np.ndarray) -> Tuple[Optional[np.ndarray], int]:
        best_index = 0
        return field_path[best_index], best_index

    def condition(self, data: RunData) -> ConditionGenType:
        lla_path: List[np.ndarray] = cast(List[np.ndarray], data[self._path_slotname])
        path = lla2ecef_iterable(lla_path)

        # Pull first point and calculate field convert from normal
        start_point: np.ndarray = path[0]
        last_distance: Optional[float] = None
        field_convert = EcefFieldGridConvert.from_surface_normal(start_point)
        path = field_convert.path_convert(path)

        target_point: Optional[np.ndarray] = None
        target_point_index: int = 0
        line_up: bool = False  # first point will be just for lining up

        while True:
            try:
                assert self._ecef_sub
                position = geoposition_ecef_message_convert(field_convert, self._ecef_sub.read())
            except Exception:
                yield True, Again()
                continue

            if target_point is None:
                target_point, target_point_index = self._get_target_point(path, position)

            if target_point is None:
                yield True, Again()
                continue

            # Store the target and annotate
            data[self._target_point_slotname] = lla_path[target_point_index]  # type: ignore
            data["ecef_path_index"] = target_point_index

            # Annotate the field
            if self._field_location_notifier is not None:
                self._field_location_notifier.clear_path_vectors()
                self._field_location_notifier.add_path_vector(
                    EcefVector(field_convert.point_revert(position, 0.4), field_convert.point_revert(target_point, 0.4))
                )

            # Evaluate if we are there
            distance = point_distance(position, target_point)
            result: bool = True

            if line_up:
                if distance < inches2meters(1.5 * MACHINE_TURN_RADIUS_INCHES):
                    target_point_index = 0
                    target_point = path[target_point_index]
                    line_up = False
            else:
                if last_distance is not None:
                    done_distance = inches2meters(MACHINE_ROW_WIDTH_INCHES)
                    if distance < done_distance and distance > last_distance:
                        result = False
                last_distance = distance

            yield result, Done()


class GenerateEortaPath(Op):
    def __init__(
        self,
        gps_history_vector: GPSHistoryVectorSubscription,
        direction: AngleDirection,
        field_location_notifier: Optional[FieldLocationNotifier],
        world_hud: Optional[WorldHUD],
        reentry_point_offset_meters: float = 3,
        reverse_distance_m: float = 3,
    ):
        super().__init__()
        self._gps_history_vector = gps_history_vector
        self._direction = direction
        self._field_location_notifier = field_location_notifier
        self._world_hud = world_hud
        self._reentry_point_offset_meters = reentry_point_offset_meters
        self._reverse_distance_m = reverse_distance_m

    def tick(self, data: RunData) -> OpTickGen:
        while True:

            # Exit vector regression from gps history
            exit_vector: Optional[EcefVector] = self._gps_history_vector.read()
            if exit_vector is None:
                LOG.warning("GenerateEortaPath has no gps vector subscription")
                yield Done()
                continue
            if exit_vector.is_empty():
                LOG.warning("GenerateEortaPath has no gps history to regress")
                yield Done()
                continue

            # Direction of the next furrow vector
            vector_direction: Optional[EcefVector] = translate_vector(exit_vector, self._direction)

            # If we have no vector it means we didn't have a turn direction (ie we were DIRECT)
            if not vector_direction:
                LOG.warning("GenerateEortaPath failed to find next row vector")
                yield Done()
                continue

            # This marks out our basis for an x, y, z grid at the end point of the exit vector
            xs = point_norm(vector_direction.end)
            ys = point_norm(exit_vector.scalar)
            zs = point_norm(exit_vector.end)

            # get grid world converter
            grid_convert = EcefFieldGridConvert(xs, ys, zs, exit_vector.end)

            # Calculate re-entry vector
            reentry_vector = exit_vector + vector_direction * inches2meters(MACHINE_ROW_WIDTH_INCHES)

            # Find the intersection of the entry and exit vectors with the geofence
            geo_match: Optional[GeoFenceMatch] = cast(GeoFenceMatch, data.get("geofence_until_exit_match"))
            if geo_match is None:
                LOG.warning("GenerateEortaPath failed to geofence exit match")
                yield Done()
                continue

            match: List[Boundary] = []
            if BoundaryType.TURN_AROUND in geo_match.available_types:
                match = [x for x in geo_match.matched if x.type == BoundaryType.TURN_AROUND]
            elif BoundaryType.CROPS in geo_match.available_types:
                match = [x for x in geo_match.matched if x.type == BoundaryType.CROPS]
            if len(match) == 0:
                LOG.warning("GenerateEortaPath found 0 geofence exit matches")
                yield Done()
                continue

            grid_poly = grid_convert.poly_convert(EcefPoly(match[0]))
            grid_reentry_vector = grid_convert.vector_convert(reentry_vector)
            grid_exit_vector = grid_convert.vector_convert(exit_vector)

            local_reentry_point = grid_poly.entry_point_xy(grid_reentry_vector)
            if local_reentry_point is None:
                LOG.warning("GenerateEortaPath could not intersect exit vector with boundary")
                yield Done()
                continue
            reentry_point = grid_convert.point_revert(point=local_reentry_point, field_alt=0.4)
            local_exit_point = grid_poly.entry_point_xy(grid_exit_vector)
            if local_exit_point is None:
                LOG.warning("GenerateEortaPath could not intersect exit vector with boundary")
                yield Done()
                continue
            exit_point = grid_convert.point_revert(point=local_exit_point, field_alt=0.4)

            grid_convert = EcefFieldGridConvert.from_surface_normal(reentry_point)
            entry_angle = grid_convert.vector_convert(exit_vector).angle
            target_heading = (angle2heading(entry_angle) + 180) % 360
            target_point = grid_convert.point_revert(
                grid_convert.point_convert(reentry_point)
                - self._reentry_point_offset_meters * heading2vector(target_heading),
                0.4,
            )
            reverse_heading = target_heading
            reverse_point = grid_convert.point_revert(
                grid_convert.point_convert(target_point) - self._reverse_distance_m * heading2vector(target_heading),
                0.4,
            )

            # Place first turn point when exiting furrow in the direction of our reentry point from our exit point
            first_turn_target_heading = angle2heading(
                grid_convert.vector_convert(EcefVector(exit_point, reentry_point)).angle
            )
            first_turn_target_point = grid_convert.point_revert(
                grid_convert.point_convert(reentry_point)
                - 2 * heading2vector(target_heading)
                + inches2meters(MACHINE_TURN_RADIUS_INCHES) * heading2vector(first_turn_target_heading),
                0.4,
            )
            first_turn_reverse_heading = first_turn_target_heading
            first_turn_reverse_point = grid_convert.point_revert(
                grid_convert.point_convert(target_point)
                - self._reverse_distance_m * heading2vector(first_turn_target_heading),
                0.4,
            )

            # Do world annotations (unity)
            if self._field_location_notifier is not None:
                self._field_location_notifier.clear_path_vectors()
                self._field_location_notifier.clear_route_vectors()
                self._field_location_notifier.notify_eorta(exit_vector)
                self._field_location_notifier.notify_eorta(reentry_vector)
                self._field_location_notifier.add_geo_ball(target_point)
                self._field_location_notifier.add_geo_ball(reverse_point)
                self._field_location_notifier.add_geo_ball(first_turn_target_point)
                self._field_location_notifier.add_geo_ball(first_turn_reverse_point)

            data["target_heading"] = target_heading
            data["target_point"] = target_point  # type: ignore
            data["reverse_heading"] = reverse_heading
            data["reverse_point"] = reverse_point  # type: ignore
            data["first_turn_target_heading"] = first_turn_target_heading
            data["first_turn_target_point"] = first_turn_target_point  # type: ignore
            data["first_turn_reverse_heading"] = first_turn_reverse_heading
            data["first_turn_reverse_point"] = first_turn_reverse_point  # type: ignore

            yield Done()


def until_path_joined(
    feed: Feed,
    path_slotname: str,
    target_point_slotname: str,
    field_location_notifier: Optional[FieldLocationNotifier] = None,
) -> UntilPathJoined:
    return UntilPathJoined(
        feed.subscribe(Topics.GEOPOSITION_ECEF.topic, GeopositionEcefMessage),
        path_slotname,
        target_point_slotname,
        field_location_notifier,
    )


def until_path_complete(
    feed: Feed,
    path_slotname: str,
    target_point_slotname: str,
    field_location_notifier: Optional[FieldLocationNotifier] = None,
    world_hud: Optional[WorldHUD] = None,
) -> UntilPathComplete:
    return UntilPathComplete(
        feed.subscribe(Topics.GEOPOSITION_ECEF.topic, GeopositionEcefMessage),
        path_slotname,
        target_point_slotname,
        field_location_notifier,
        world_hud=world_hud,
    )


def generate_eorta_path(
    feed: Feed,
    direction: AngleDirection,
    field_location_notifier: Optional[FieldLocationNotifier],
    world_hud: Optional[WorldHUD],
) -> GenerateEortaPath:
    return GenerateEortaPath(
        feed.subscribe(Topics.GPS_HISTORY_VECTOR.topic, EcefVector), direction, field_location_notifier, world_hud,
    )
