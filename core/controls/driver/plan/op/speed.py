from typing import Optional

import lib.common.logging
from core.controls.driver.plan.instruction import StopInstruction, UpdateTargetSpeedInstruction
from core.controls.exterminator.controllers.aimbot.process.client.aimbot_client import AimbotClient
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from core.model.topics import Topics
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.pid.pid import PIDControl
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.feed.base import Feed

LOG = lib.common.logging.get_logger(__name__)


class SetTargetSpeed(Op):
    def __init__(self, *, target_speed_mph: float):
        super().__init__()
        self._target_speed_mph = target_speed_mph

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            yield Done(instruction=UpdateTargetSpeedInstruction(target_speed_mph=self._target_speed_mph),)


class SetDynamicTargetSpeed(SetTargetSpeed):
    def __init__(self, *, min_speed: float, max_speed: float):
        super().__init__(target_speed_mph=max_speed)
        self._min_speed = min_speed
        self._max_speed = max_speed
        self._current_speed = min_speed
        self._aimbot_client = AimbotClient()
        self._pid = PIDControl(0.25, 0, 0)

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            try:
                current_load, target_load = self._aimbot_client.get_estimated_load_from_sync()
                error = target_load - current_load
                step_size = max(self._current_speed, self._min_speed, 0.1)
                self._current_speed += self._pid.output(error) * step_size
                self._current_speed = min(max(self._current_speed, self._min_speed), self._max_speed)
                LOG.debug(f"Load: {current_load}, Error: {error}, Current Speed {self._current_speed}")
                yield Done(instruction=UpdateTargetSpeedInstruction(target_speed_mph=self._current_speed),)
            except Exception as e:
                LOG.exception(e)
                yield Done(instruction=UpdateTargetSpeedInstruction(target_speed_mph=self._target_speed_mph),)


class Stop(Op):
    def __init__(self, reason: str, velocity_mph_subscription: SubscriberChannel[VelocityMessage]):
        super().__init__()
        self._reason = reason
        self._velocity_mph_subscription = velocity_mph_subscription

    def tick(self, data: RunData) -> OpTickGen:
        instruction = StopInstruction(reason=self._reason)

        yield Again(instruction=instruction)

        velocity_mph: Optional[VelocityMessage] = self._velocity_mph_subscription.read()
        while velocity_mph is None or max(velocity_mph.x, velocity_mph.y, velocity_mph.z) > 0:
            yield Again(instruction=instruction)
            velocity_mph = self._velocity_mph_subscription.read()

        yield Done(instruction=instruction)


def set_target_speed(*, target_speed_mph: float) -> SetTargetSpeed:
    return SetTargetSpeed(target_speed_mph=target_speed_mph)


def set_dynamic_target_speed(*, min_speed: float, max_speed: float) -> SetTargetSpeed:
    return SetDynamicTargetSpeed(min_speed=min_speed, max_speed=max_speed)


def stop(*, reason: str, feed: Feed) -> Stop:
    return Stop(reason=reason, velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage))
