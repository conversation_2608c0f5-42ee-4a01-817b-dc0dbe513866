from typing import Callable, Optional, cast

import numpy as np

from core.controls.frame.heading_distance import HeadingDistanceAccumulator
from core.controls.frame.rotary_encoders_snapshot import RotaryEncodersSnapshot
from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from core.model.topics import Topics
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.protocol.feed.base import Feed

LOG = get_logger(__name__)


class UntilHeadingInchesTraveled(ConditionOp):
    """
    Keep going until cummulative inches traveled down a heading.
    """

    def __init__(
        self,
        slotname: str,
        heading_distance_subscription: SubscriberChannel[HeadingDistanceAccumulator],
        inches: Optional[float] = None,
        distance_slotname: Optional[str] = None,
        distance_modifier: Optional[Callable[[float], float]] = None,
        heading_modifier: Optional[Callable[[float], float]] = None,
    ):
        super().__init__()
        self._inches: Optional[float] = inches
        self._distance_slotname = distance_slotname
        self._heading_distance_subscription = heading_distance_subscription
        self._slotname = slotname
        self._distance_modifier = distance_modifier
        self._heading_modifier = heading_modifier

    def condition(self, data: RunData) -> ConditionGenType:
        hda = self._heading_distance_subscription.read()
        assert hda is not None  # This should always complete

        heading: float = cast(float, data[self._slotname])

        inches: Optional[float] = self._inches
        if self._distance_slotname is not None:
            inches = cast(float, data[self._distance_slotname])

        assert inches is not None, "inches not passed and not found in run data"

        if self._distance_modifier is not None:
            inches = self._distance_modifier(inches)
        if self._heading_modifier is not None:
            heading = self._heading_modifier(heading)
        LOG.debug(f"Need to travel: {heading} -> {inches}inches")
        while True:
            # Traveled since inception
            traveled_in: float = hda.heading_distance_inches(heading, expect_at_least_one_sample=True)
            LOG.debug(f"Traveled: {traveled_in}inches")
            data["heading_traveled_in"] = traveled_in  # only for humans to see in UI

            # Direction we should be achieving
            diff_sign: int = int(np.sign(inches))

            # How much is "left" (left means approaching zero from whichever sign)
            remaining_in: float = inches - traveled_in
            data["heading_remaining_in"] = remaining_in  # only for humans to see in UI

            if remaining_in * diff_sign <= 0:
                hda.close()
                yield False, Done()
            else:
                LOG.debug(f"Remaining {remaining_in}inches")
                yield True, Done()


class UntilInchesTraveled(ConditionOp):
    """
    Keep going until inches traveled (in any direction).
    """

    def __init__(
        self,
        rotary_encoders_snapshot_subscription: SubscriberChannel[RotaryEncodersSnapshot],
        inches: Optional[float],
        inches_slotname: Optional[str],
        distance_modifier: Optional[Callable[[float], float]] = None,
    ):
        self._inches = inches
        self._inches_slotname = inches_slotname
        self._distance_modifier = distance_modifier
        self._rotary_encoders_snapshot_subscription = rotary_encoders_snapshot_subscription

    def condition(self, data: RunData) -> ConditionGenType:
        assert self._rotary_encoders_snapshot_subscription
        if self._inches_slotname is not None:
            self._inches = cast(float, data[self._inches_slotname])

        # Inches must be assigned by now
        assert self._inches is not None

        if self._distance_modifier is not None:
            self._inches = self._distance_modifier(self._inches)
        start_snapshot: Optional[RotaryEncodersSnapshot] = self._rotary_encoders_snapshot_subscription.read()
        while True:
            snapshot: Optional[RotaryEncodersSnapshot] = self._rotary_encoders_snapshot_subscription.read()
            assert start_snapshot
            assert snapshot

            inches_traveled = snapshot.inches_since(start_snapshot)
            data["until_traveled_in"] = self._inches  # only for humans to see in UI
            data["traveled_in"] = inches_traveled  # only for humans to see in UI
            if inches_traveled >= self._inches:
                yield False, Done()
            else:
                yield True, Done()


class HeadingDistanceTracker(Op):
    def __init__(
        self, distance_slotname: str, heading_distance_subscription: SubscriberChannel[HeadingDistanceAccumulator]
    ):
        super().__init__()
        self._distance_slotname = distance_slotname
        self._heading_distance_subscription = heading_distance_subscription

    def tick(self, data: RunData) -> OpTickGen:
        # Kick off distance tracker and move on
        hda = self._heading_distance_subscription.read()
        assert hda is not None
        # TODO re-model HDA so it can be serializable. Need to better separate data from on-close behavior
        data[self._distance_slotname] = hda  # type: ignore

        # always done
        yield Done()


class DistanceTracker(Op):
    def __init__(
        self, distance_slotname: str, rotary_encoders_snapshot_subscription: SubscriberChannel[RotaryEncodersSnapshot],
    ):
        super().__init__()
        self._distance_slotname = distance_slotname
        self._rotary_encoders_snapshot_subscription = rotary_encoders_snapshot_subscription

    def tick(self, data: RunData) -> OpTickGen:
        assert self._rotary_encoders_snapshot_subscription
        start_snapshot: Optional[RotaryEncodersSnapshot] = self._rotary_encoders_snapshot_subscription.read()
        assert isinstance(start_snapshot, RotaryEncodersSnapshot)
        data[self._distance_slotname] = start_snapshot

        yield Done()


class StoreDistanceTraveled(Op):
    def __init__(
        self, distance_slotname: str, rotary_encoders_snapshot_subscription: SubscriberChannel[RotaryEncodersSnapshot],
    ):
        super().__init__()
        self._distance_slotname = distance_slotname
        self._rotary_encoders_snapshot_subscription = rotary_encoders_snapshot_subscription

    def tick(self, data: RunData) -> OpTickGen:
        assert self._rotary_encoders_snapshot_subscription
        start_snapshot = cast(RotaryEncodersSnapshot, data[self._distance_slotname])
        assert start_snapshot is not None
        end_snapshot: Optional[RotaryEncodersSnapshot] = self._rotary_encoders_snapshot_subscription.read()
        assert end_snapshot
        inches_traveled = end_snapshot.inches_since(start_snapshot)
        data[self._distance_slotname] = inches_traveled
        yield Done()


class HeadingDistanceTrackerComplete(Op):
    def __init__(self, distance_slotname: str, heading_slotname: str, heading_modifier: Callable[[float], float]):
        super().__init__()
        self._distance_slotname = distance_slotname
        self._heading_slotname = heading_slotname
        self._heading_modifier = heading_modifier

    def tick(self, data: RunData) -> OpTickGen:
        # Close out the heading tracker and save the value

        # pull live tracker
        hda: HeadingDistanceAccumulator = cast(HeadingDistanceAccumulator, data[self._distance_slotname])
        assert isinstance(hda, HeadingDistanceAccumulator)

        # calculate our comparison heading
        original_heading: float = cast(float, data[self._heading_slotname])
        assert isinstance(original_heading, float)
        target_heading: float = (self._heading_modifier(original_heading) + 360) % 360

        # get traveled in that direction and save it back
        traveled_in: float = hda.heading_distance_inches(target_heading)
        hda.close()
        data[self._distance_slotname] = traveled_in
        LOG.info("complete traveled: {} -> {}".format(target_heading, traveled_in))

        # always done
        yield Done()


def until_heading_inches_traveled(
    feed: Feed,
    slotname: str,
    inches: float = 0,
    distance_slotname: Optional[str] = None,
    distance_modifier: Optional[Callable[[float], float]] = None,
    heading_modifier: Optional[Callable[[float], float]] = None,
) -> UntilHeadingInchesTraveled:
    return UntilHeadingInchesTraveled(
        slotname=slotname,
        heading_distance_subscription=feed.subscribe(Topics.HEADING_DISTANCE.topic, HeadingDistanceAccumulator),
        inches=inches,
        distance_slotname=distance_slotname,
        distance_modifier=distance_modifier,
        heading_modifier=heading_modifier,
    )


def until_inches_traveled(
    feed: Feed,
    inches: Optional[float] = None,
    inches_slotname: Optional[str] = None,
    distance_modifier: Optional[Callable[[float], float]] = None,
) -> UntilInchesTraveled:
    return UntilInchesTraveled(
        rotary_encoders_snapshot_subscription=feed.subscribe(
            Topics.ROTARY_ENCODERS_SNAPSHOT.topic, RotaryEncodersSnapshot
        ),
        inches=inches,
        inches_slotname=inches_slotname,
        distance_modifier=distance_modifier,
    )


def track_distance_start(feed: Feed, distance_slotname: str) -> DistanceTracker:
    return DistanceTracker(
        distance_slotname=distance_slotname,
        rotary_encoders_snapshot_subscription=feed.subscribe(
            Topics.ROTARY_ENCODERS_SNAPSHOT.topic, RotaryEncodersSnapshot
        ),
    )


def store_distance_traveled(feed: Feed, distance_slotname: str) -> StoreDistanceTraveled:
    return StoreDistanceTraveled(
        distance_slotname=distance_slotname,
        rotary_encoders_snapshot_subscription=feed.subscribe(
            Topics.ROTARY_ENCODERS_SNAPSHOT.topic, RotaryEncodersSnapshot
        ),
    )


# distance tracking
def track_heading_distance_start(feed: Feed, distance_slotname: str) -> HeadingDistanceTracker:
    return HeadingDistanceTracker(
        distance_slotname=distance_slotname,
        heading_distance_subscription=feed.subscribe(Topics.HEADING_DISTANCE.topic, HeadingDistanceAccumulator),
    )


def track_heading_distance_stop(
    distance_slotname: str, heading_slotname: str, heading_modifier: Callable[[float], float]
) -> HeadingDistanceTrackerComplete:
    return HeadingDistanceTrackerComplete(
        distance_slotname=distance_slotname, heading_slotname=heading_slotname, heading_modifier=heading_modifier
    )
