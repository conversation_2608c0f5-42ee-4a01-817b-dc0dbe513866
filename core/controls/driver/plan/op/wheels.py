from core.controls.driver.plan.instruction import AlignStraightInstruction
from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData


class AlignStraight(Op):
    def tick(self, data: RunData) -> OpTickGen:
        instruction = AlignStraightInstruction()
        yield Done(instruction=instruction)


def align_straight() -> AlignStraight:
    return AlignStraight()
