from core.controls.driver.gear import Gear
from core.controls.driver.plan.instruction import NotifyCommanderInstruction, PauseDriverInstruction, SetGearInstruction
from core.controls.plan.ops.op import Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData


class Forward(Op):
    def tick(self, data: RunData) -> OpTickGen:
        while True:
            yield Done(instruction=SetGearInstruction(gear=Gear.FWD),)


class Reverse(Op):
    def tick(self, data: RunData) -> OpTickGen:
        while True:
            yield Done(instruction=SetGearInstruction(gear=Gear.REV),)


def set_forward() -> Forward:
    return Forward()


def set_reverse() -> Reverse:
    return Reverse()


class Pause(Op):
    def __init__(self, reason: str):
        self._reason = reason

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            yield Done(instruction=PauseDriverInstruction(self._reason))


def set_paused(reason: str) -> Pause:
    return Pause(reason=reason)


class Notify(Op):
    def __init__(self, topic: str, reason: str):
        self._reason = reason
        self._topic = topic

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            yield Done(instruction=NotifyCommanderInstruction(self._topic, self._reason))


def notify_commander(topic: str, reason: str) -> Notify:
    return Notify(topic=topic, reason=reason)
