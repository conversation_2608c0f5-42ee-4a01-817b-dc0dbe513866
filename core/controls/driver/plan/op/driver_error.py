from typing import Optional

from core.controls.driver.driver_error import DriverError, DriverErrorChannel
from core.controls.driver.plan.instruction import DriverInstruction, NoOpDriverInstruction, StopInstruction
from core.controls.driver.plan.op.heading import TargetOffset
from core.controls.driver.plan.strategy.base import DriverErrorStrategy
from core.controls.driver.plan.strategy.target_angle import ALIGNED_ERROR_ANGLE_RAD
from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunData
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


WARN_LOG_RATE_LIMIT_MS = 1000


class DriverErrorStrategyEventGen(Op):
    """
    An event gen which facilitates evaluating a strategy based on an incoming error channel
    """

    def __init__(self, error_subscription: DriverErrorChannel, strategy: DriverErrorStrategy):
        super().__init__()
        self._strategy = strategy
        self._error_subscription = error_subscription

        self._warn_log_rate_limit_ms = 0

    @property
    def error_subscription(self) -> SubscriberChannel[DriverError]:
        return self._error_subscription

    def _log_missing(self) -> None:
        now = maka_control_timestamp_ms()
        if now >= self._warn_log_rate_limit_ms + WARN_LOG_RATE_LIMIT_MS:
            LOG.warning(f"Missing driver error from {self._error_subscription.topic}")
            self._warn_log_rate_limit_ms = now

    def tick(self, data: RunData) -> OpTickGen:
        was_missing = False
        instruction: DriverInstruction
        while True:
            # read latest error
            driver_error_optional: Optional[DriverError] = self._error_subscription.read()
            if driver_error_optional is None:
                if not was_missing:
                    instruction = StopInstruction(reason="No error input")
                else:
                    instruction = NoOpDriverInstruction()

                self._log_missing()

                yield Again(instruction=instruction)
                was_missing = True
                continue
            driver_error: DriverError = driver_error_optional
            was_missing = False

            instruction = self._strategy.evaluate(driver_error=driver_error)
            data["driver_error"] = driver_error
            yield Done(instruction=instruction)


class Drive(DriverErrorStrategyEventGen):
    pass


class DriverErrorAlignedConditionGen(ConditionOp):
    """
    A conditional generator for when the triangular error is aligned,
    passing information about the target offset to the next node.
    """

    def __init__(
        self,
        error_subscription: SubscriberChannel[DriverError],
        aligned_threshold_rad: float = ALIGNED_ERROR_ANGLE_RAD,
    ):
        super().__init__()
        self._error_subscription: SubscriberChannel[DriverError] = error_subscription
        self._aligned_threshold_rad: float = aligned_threshold_rad

    def condition(self, data: RunData) -> ConditionGenType:
        while True:
            # read latest error
            driver_error_optional: Optional[DriverError] = self._error_subscription.read()
            assert driver_error_optional is not None
            driver_error: DriverError = driver_error_optional
            data["driver_error"] = driver_error

            # Pass the xdiff off to the next node
            assert isinstance(data["target_offset"], TargetOffset)
            data["target_offset"].offset_px = driver_error.error_x_pct

            if abs(driver_error.error_angle_rad) <= self._aligned_threshold_rad:
                yield True, Again()
            else:
                yield False, Done()


class Aligned(DriverErrorAlignedConditionGen):
    pass
