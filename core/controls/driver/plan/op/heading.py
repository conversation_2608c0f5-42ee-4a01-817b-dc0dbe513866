import abc
import math
from typing import Any, Callable, Dict, Optional, Tuple, cast

import numpy as np

from core.controls.driver.constants import DEFAULT_TURN_HEADING_ALIGNED_DEG
from core.controls.driver.driver_error import VelocityError, error_velocity_mph_channel
from core.controls.driver.parameters import DriverParameter, DriverParameters
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.instruction import (
    DriveAngleInstruction,
    DriverInstruction,
    StopInstruction,
    TargetAngleInstruction,
)
from core.controls.driver.plan.op.geo import EortaFencer
from core.controls.driver.plan.subscription_types import RobotBodySubscription, current_position
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.frame.pose.body import RobotBody
from core.controls.plan.instruction import NoOpInstruction
from core.controls.plan.ops.condition import ConditionGenType, ConditionOp
from core.controls.plan.ops.op import Again, Done, Op, OpTickGen
from core.controls.plan.state.run_data import RunD<PERSON>
from core.model.topics import Topics
from generated.core.controls.frame.model.heading_message import HeadingMessage
from lib.common.angle_direction import AngleDirection
from lib.common.geo.boundary import BoundaryType
from lib.common.geo.ecef_utils import EcefFieldGridConvert, EcefVector, heading2angle, heading2vector, point_distance
from lib.common.logging import get_logger
from lib.common.math import heading_error_angle_rad, rad2deg
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.protocol.feed.base import Feed
from lib.common.serialization.json import JsonSerializable
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class TargetOffset(JsonSerializable):
    """Container class for information to pass between target finding operations."""

    def __init__(self, offset_px: Optional[float] = None, perpendicular_heading: Optional[float] = None):
        self.offset_px: Optional[float] = offset_px  # How far from target are we in x pixels
        self.perpendicular_heading: Optional[float] = perpendicular_heading

    def to_json(self) -> Dict[str, Any]:
        return {"offset_px": self.offset_px, "perpendicular_heading": self.perpendicular_heading}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "TargetOffset":
        return TargetOffset(offset_px=data["offset_px"], perpendicular_heading=data["perpendicular_heading"])


#
# Directional
#
class TurnRelative(Op, abc.ABC):
    def __init__(
        self,
        heading_subscription: SubscriberChannel[HeadingMessage],
        error_velocity_mph_subscription: SubscriberChannel[VelocityError],
        turn_heading_aligned_deg: Callable[[], float] = lambda: DEFAULT_TURN_HEADING_ALIGNED_DEG,
        offset_heading_deg: Optional[float] = None,
        offset_heading_angle_slotname: Optional[str] = None,
        complete_on_sign_flip: bool = True,  # default is done when we blow through the angle
        world_hud: Optional[WorldHUD] = None,
    ):
        super().__init__()
        self._degrees: Optional[float] = offset_heading_deg
        self._offset_heading_angle_slotname: Optional[str] = offset_heading_angle_slotname
        self._heading_subscription = heading_subscription
        self._error_velocity_mph_subscription = error_velocity_mph_subscription
        self._callback_turn_heading_aligned_deg: Callable[[], float] = turn_heading_aligned_deg
        self._complete_on_sign_flip = complete_on_sign_flip
        self._world_hud = world_hud

    @abc.abstractmethod
    def target_heading(self, data: RunData, heading: float, degrees: float) -> Tuple[float, AngleDirection]:
        pass

    def tick(self, data: RunData) -> OpTickGen:
        # Find our current heading and how far off we are
        heading = self._heading_subscription.read()
        if heading is None:
            yield Again()
            return
        heading = heading.heading

        if self._offset_heading_angle_slotname is not None:
            self._degrees = np.rad2deg(cast(float, data[self._offset_heading_angle_slotname]))
        assert self._degrees
        target_heading, direction = self.target_heading(data, heading, self._degrees)

        error_angle_rad = heading_error_angle_rad(heading=heading, target=target_heading)
        offset_sign = np.sign(error_angle_rad)
        latest_turn_heading_aligned_deg = self._callback_turn_heading_aligned_deg()
        while abs(error_angle_rad) >= np.deg2rad(latest_turn_heading_aligned_deg):
            if self._world_hud is not None:
                self._world_hud.set_text(f"th: {target_heading:.2f}\nh: {heading:.2f}")

            error_velocity_mph = self._error_velocity_mph_subscription.read()
            instruction: DriverInstruction
            if error_velocity_mph is None:
                LOG.warning("Stopping due to unknown velocity error")
                instruction = StopInstruction(reason="unknown velocity error")
            else:
                instruction = TargetAngleInstruction(
                    error_angle_rad=error_angle_rad,
                    error_velocity_mph=error_velocity_mph.error_velocity_mph,
                    actual_velocity_mph=error_velocity_mph.actual_velocity_mph,
                    direction=direction,
                )

            local_data = {
                "heading": heading,
                "direction": direction,
                "offset_sign": offset_sign,
                "error_angle_deg": np.rad2deg(error_angle_rad),
                "turn_heading_aligned_deg": latest_turn_heading_aligned_deg,
            }

            # If we have blown past our target we need to switch to angle DIRECT, otherwise we'll go do another 360
            if np.sign(error_angle_rad) != offset_sign:
                if self._complete_on_sign_flip:
                    if self._world_hud is not None:
                        self._world_hud.set_text("heading matched.")
                    break
                direction = AngleDirection.DIRECT

            yield Again(
                instruction=instruction, local_data=local_data,
            )
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield Again(instruction=StopInstruction(reason="lost heading"))

                heading_optional = self._heading_subscription.read()
                while heading_optional is None:
                    yield Again(instruction=NoOpInstruction())
                    heading_optional = self._heading_subscription.read()

            heading = heading_optional.heading
            error_angle_rad = heading_error_angle_rad(heading=heading, target=target_heading)
            latest_turn_heading_aligned_deg = self._callback_turn_heading_aligned_deg()

        yield Done(
            local_data={
                "heading": heading,
                "direction": direction,
                "error_angle_deg": np.rad2deg(error_angle_rad),
                "offset_sign": offset_sign,
            }
        )


class TurnRight(TurnRelative):
    def target_heading(self, data: RunData, heading: float, degrees: float) -> Tuple[float, AngleDirection]:
        return (heading + degrees) % 360, AngleDirection.DIRECT


class TurnLeft(TurnRelative):
    def target_heading(self, data: RunData, heading: float, degrees: float) -> Tuple[float, AngleDirection]:
        return (heading - degrees) % 360, AngleDirection.DIRECT


class TurnHeadingOffset(TurnRelative):
    """
    Turn this many degrees in the direction of our xoff.
    """

    def __init__(
        self, *, slotname: str, direction: AngleDirection, **kwargs: Any,
    ):
        super().__init__(**kwargs)
        self._slotname = slotname
        self._direction = direction

    def target_heading(self, data: RunData, heading: float, _: float) -> Tuple[float, AngleDirection]:
        # This is built to work with LockOriginHeading
        offset_px: Optional[float] = None
        target_offset: Optional[TargetOffset] = None
        if "target_offset" in data:
            target_offset = cast(TargetOffset, data["target_offset"])
            assert isinstance(target_offset, TargetOffset)
            offset_px = target_offset.offset_px
        original_heading: float = cast(float, data[self._slotname])

        # If we are not tracking offset from a target then the raw offset_heading is used
        assert self._degrees
        if offset_px is None:
            heading = (original_heading + self._degrees) % 360
        else:
            if offset_px > 0:
                heading = (original_heading + self._degrees) % 360
            else:
                heading = (original_heading - self._degrees) % 360
            assert target_offset is not None  # mypy can't tell this is always set
            target_offset.perpendicular_heading = heading
            data["target_offset"] = target_offset
        return heading, self._direction


class FollowOriginHeading(TurnRelative):
    """
    Move back to original_heading.
    """

    # explicitly define args instead of using kwargs so we can pass degrees=0
    def __init__(
        self,
        heading_subscription: SubscriberChannel[HeadingMessage],
        error_velocity_mph_subscription: SubscriberChannel[VelocityError],
    ):
        super().__init__(
            offset_heading_deg=0,
            heading_subscription=heading_subscription,
            error_velocity_mph_subscription=error_velocity_mph_subscription,
        )

    def target_heading(self, data: RunData, heading: float, _: float) -> Tuple[float, AngleDirection]:
        original_heading: float = cast(float, data["original_heading"])
        return original_heading, AngleDirection.DIRECT


class LockHeading(Op):
    """
    Remember what our current heading is as "original_heading".
    """

    def __init__(self, slotname: str, heading_subscription: SubscriberChannel[HeadingMessage]):
        super().__init__()
        self._heading_subscription = heading_subscription
        self._slotname = slotname

    def tick(self, data: RunData) -> OpTickGen:
        while True:
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield Again()
                continue
            data[self._slotname] = heading_optional.heading

            LOG.debug("Lock {} at {}".format(self._slotname, data[self._slotname]))
            yield Done()


class UntilAligned(ConditionOp):
    def __init__(
        self,
        ecef_subscription: RobotBodySubscription,
        heading_subscription: SubscriberChannel[HeadingMessage],
        target_position: Optional[np.ndarray] = None,
        target_heading: Optional[float] = None,
        target_position_slot: Optional[str] = None,
        target_heading_slot: Optional[str] = None,
        position_threshold: float = 0.75,
        heading_threshold_deg: float = 2.0,
    ):
        self._ecef_sub = ecef_subscription
        self._heading_subscription = heading_subscription
        self._target_position = target_position
        self._target_heading = target_heading
        self._target_position_slot = target_position_slot
        self._target_heading_slot = target_heading_slot
        self._position_threshold = position_threshold
        self._heading_threshold_deg = heading_threshold_deg

    def condition(self, data: RunData) -> ConditionGenType:
        if self._target_heading_slot is not None and self._target_position_slot is not None:
            self._target_position = cast(np.ndarray, data[self._target_position_slot])
            self._target_heading = cast(float, data[self._target_heading_slot])

        assert self._target_heading is not None
        assert self._target_position is not None
        field_convert = None
        while True:
            position = current_position(self._ecef_sub)
            if position is None:
                yield True, Again()
                continue

            if field_convert is None:
                field_convert = EcefFieldGridConvert.from_surface_normal(position)

            position_grid = field_convert.point_convert(position)
            target_position_grid = field_convert.point_convert(self._target_position)

            distance = point_distance(position_grid, target_position_grid)
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield True, Again()
                continue
            heading = heading_optional.heading
            heading_angle = heading_error_angle_rad(target=self._target_heading, heading=heading)

            if distance < self._position_threshold and abs(np.rad2deg(heading_angle)) < self._heading_threshold_deg:
                break

            LOG.info(f"distance: {distance}, heading error: {abs(np.rad2deg(heading_angle))}")

            yield True, Done()

        yield False, Done()


class TurnSShape(Op):
    def __init__(
        self,
        feed: Feed,
        autodrive_params: DriverParameters,
        field_location_notifier: Optional[FieldLocationNotifier],
        target_position: Optional[np.ndarray] = None,
        target_heading: Optional[float] = None,
        target_point_slotname: Optional[str] = None,
        target_heading_slotname: Optional[str] = None,
        world_hud: Optional[WorldHUD] = None,
        reversing: bool = False,
        position_threshold: float = 0.25,
        heading_threshold_deg: float = 2.0,
        near_eorta_edge_distance_m_crops: float = 2,
        near_eorta_edge_distance_m_field: float = 2.5,
    ):
        super().__init__()
        self._feed = feed
        self._ecef_sub = self._feed.subscribe(Topics.BODY_POSITION.topic, RobotBody)
        self._heading_subscription = self._feed.subscribe(Topics.HEADING.topic, HeadingMessage)
        self._error_velocity_mph_subscription = error_velocity_mph_channel(self._feed, autodrive_params)
        self._world_hud = world_hud
        self._field_location_notifier = field_location_notifier
        self._target_point_slotname = target_point_slotname
        self._target_heading_slotname = target_heading_slotname
        self._target_position_global = target_position
        self._target_position: Optional[np.ndarray] = None
        self._target_heading = target_heading
        self._reversing = reversing
        self._position_threshold = position_threshold
        self._heading_threshold_deg = heading_threshold_deg
        self._near_eorta_edge_distance_m_crops = near_eorta_edge_distance_m_crops
        self._near_eorta_edge_distance_m_field = near_eorta_edge_distance_m_field

    def _get_heading(self) -> OpTickGen:
        heading_optional = self._heading_subscription.read()
        while heading_optional is None:
            yield Again(instruction=NoOpInstruction())
            heading_optional = self._heading_subscription.read()
        self._heading = heading_optional.heading
        if self._reversing:
            # Simplifies logic when reversing
            self._heading = (self._heading + 180) % 360

    def tick(self, data: RunData) -> OpTickGen:
        yield from self._get_heading()
        global_ecef_position = current_position(self._ecef_sub)
        assert global_ecef_position is not None
        ecef_field_grid_converter = EcefFieldGridConvert.from_surface_normal(global_ecef_position)
        position = ecef_field_grid_converter.point_convert(global_ecef_position)
        initial_position = position
        eorta_fencer = EortaFencer(self._feed, ecef_field_grid_converter)

        if self._target_heading_slotname is not None and self._target_point_slotname is not None:
            self._target_position = ecef_field_grid_converter.point_convert(
                cast(np.ndarray, data[self._target_point_slotname])
            )
            self._target_heading = cast(float, data[self._target_heading_slotname])
        elif self._target_position_global is not None:
            self._target_position = ecef_field_grid_converter.point_convert(self._target_position_global)

        assert self._target_heading is not None
        assert self._target_position is not None

        if self._reversing:
            self._target_heading = (self._target_heading + 180) % 360

        local_data = {}

        initial_target_vector = self._target_position - position
        initial_target_vector /= np.linalg.norm(initial_target_vector)
        initial_target_position_distance = point_distance(self._target_position, position)

        target_position_distance = initial_target_position_distance
        heading_error_angle = heading_error_angle_rad(target=self._target_heading, heading=self._heading)
        while (
            target_position_distance > self._position_threshold
            or abs(np.rad2deg(heading_error_angle)) > self._heading_threshold_deg
        ):
            if self._world_hud is not None:
                self._world_hud.set_text(f"th: {self._target_heading:.2f}\nh: {self._heading:.2f}")

            error_velocity_mph = self._error_velocity_mph_subscription.read()
            instruction: DriverInstruction
            if error_velocity_mph is None:
                yield Again()
                continue

            yield from self._get_heading()
            global_ecef_position = current_position(self._ecef_sub)
            assert global_ecef_position is not None
            position = ecef_field_grid_converter.point_convert(global_ecef_position)
            target_position_distance = point_distance(self._target_position, position)

            target_position_vector = position - initial_position
            distance_towards_target = initial_target_vector.dot(target_position_vector)

            if distance_towards_target - self._position_threshold > initial_target_position_distance:
                break

            # If we are within our position threshold, just target heading vs. using bezier curve.
            if (
                target_position_distance < self._position_threshold
                or (distance_towards_target + self._position_threshold) / initial_target_position_distance > 1.0
            ):
                angle = heading_error_angle_rad(target=self._target_heading, heading=self._heading)
            else:
                basis_vec = EcefVector(position, self._target_position)
                target_heading_vec = heading2vector(self._target_heading)

                target_heading_relative_angle = abs(
                    basis_vec.angle_between(EcefVector(np.array([0, 0]), target_heading_vec))
                )

                midpoint_distance = target_position_distance / 2

                L2 = midpoint_distance * math.sin(target_heading_relative_angle)

                control_point_first = position
                control_point_second = self._target_position - target_heading_vec * L2

                # Use a fixed a distance into the bezier curve since we recompute the curve every time.
                # Nice bezier visualization: https://www.geogebra.org/m/WPHQ9rUt
                distance_ratio = 0.25
                point_p6 = (1 - distance_ratio) * control_point_first + distance_ratio * control_point_second
                point_p7 = (1 - distance_ratio) * control_point_second + distance_ratio * self._target_position
                point_p9 = (1 - distance_ratio) * point_p6 + distance_ratio * point_p7

                target_vec = EcefVector(position, point_p9)

                basis_vec = EcefVector(np.array([0, 0]), np.array([1, 0]))
                target_basis_angle = basis_vec.angle_between(target_vec)

                heading_angle = heading2angle(self._heading)
                angle = (target_basis_angle - heading_angle) % (math.pi * 2)

                if abs(angle) > math.pi:
                    angle = -(2 * math.pi - angle)

                angle = -angle

            error_angle_rad = angle

            eorta_distance_m_crops, eorta_point_crops = eorta_fencer.eorta_edge_meters_project({BoundaryType.CROPS})
            eorta_distance_m_field, eorta_point_field = eorta_fencer.eorta_edge_meters_project({BoundaryType.FIELD})

            turning_offset_deg = max(-45, min(45, rad2deg(error_angle_rad)))
            turning_offset_deg = -turning_offset_deg if self._reversing else turning_offset_deg
            if eorta_point_crops is not None and eorta_distance_m_crops is not None:
                eorta_point_grid = ecef_field_grid_converter.point_convert(eorta_point_crops)
                eorta_point_vector = eorta_point_grid - position
                eorta_point_vector_norm = eorta_point_vector / np.linalg.norm(eorta_point_vector)
                if (
                    np.dot(eorta_point_vector_norm, heading2vector(self._heading + turning_offset_deg)) > 0.25
                    and eorta_distance_m_crops < self._near_eorta_edge_distance_m_crops
                ):
                    break

            if eorta_point_field is not None and eorta_distance_m_field is not None:
                eorta_point_grid = ecef_field_grid_converter.point_convert(eorta_point_field)
                eorta_point_vector = eorta_point_grid - position
                eorta_point_vector_norm = eorta_point_vector / np.linalg.norm(eorta_point_vector)
                if (
                    np.dot(eorta_point_vector_norm, heading2vector(self._heading + turning_offset_deg)) > 0.25
                    and eorta_distance_m_field < self._near_eorta_edge_distance_m_field
                ):
                    break

            instruction = DriveAngleInstruction(
                error_angle_rad=error_angle_rad,
                error_velocity_mph=error_velocity_mph.error_velocity_mph,
                actual_velocity_mph=error_velocity_mph.actual_velocity_mph,
            )

            local_data = {
                "heading": self._heading,
                "target_heading": self._target_heading,
                "target_position_distance": target_position_distance,
                "error_angle_deg": np.rad2deg(error_angle_rad),
            }

            yield Again(
                instruction=instruction, local_data=local_data,
            )

            yield from self._get_heading()
            heading_error_angle = heading_error_angle_rad(target=self._target_heading, heading=self._heading)

        yield Done(local_data=local_data)


#
# Driving
#


class DriveHeading(Op):
    def __init__(
        self,
        heading_subscription: SubscriberChannel[HeadingMessage],
        error_velocity_mph_subscription: SubscriberChannel[VelocityError],
        target_heading_slotname: Optional[str] = None,
        target_heading: Optional[float] = None,
    ):
        super().__init__()
        self._heading_subscription = heading_subscription
        self._error_velocity_mph_subscription: SubscriberChannel[VelocityError] = error_velocity_mph_subscription

        self._target_heading: Optional[float] = target_heading
        self._target_heading_slotname: Optional[str] = target_heading_slotname

        if self._target_heading is not None:
            assert self._target_heading_slotname is None

    def tick(self, data: RunData) -> OpTickGen:
        if self._target_heading is not None:
            target_heading = self._target_heading
        elif self._target_heading_slotname is not None:
            target_heading = cast(float, data[self._target_heading_slotname])
        else:
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield Again()
                return
            target_heading = heading_optional.heading

        while True:
            # angular error
            heading_optional = self._heading_subscription.read()
            if heading_optional is None:
                yield Again(instruction=StopInstruction(reason="lost heading"))

                heading_optional = self._heading_subscription.read()
                while heading_optional is None:
                    yield Again(instruction=NoOpInstruction())
                    heading_optional = self._heading_subscription.read()
            heading = heading_optional.heading

            error_angle_rad = heading_error_angle_rad(heading=heading, target=target_heading)

            error_velocity_mph = self._error_velocity_mph_subscription.read()
            instruction: DriverInstruction
            if error_velocity_mph is None:
                LOG.warning("Stopping due to unknown velocity error")
                instruction = StopInstruction(reason="unknown velocity error")
            else:
                assert error_velocity_mph.timestamp_ms > maka_control_timestamp_ms() - 500

                instruction = DriveAngleInstruction(
                    error_angle_rad=error_angle_rad,
                    error_velocity_mph=error_velocity_mph.error_velocity_mph,
                    actual_velocity_mph=error_velocity_mph.actual_velocity_mph,
                )
            LOG.debug(f"DriveHeading slot={self._target_heading_slotname} -> {target_heading}")
            yield Done(
                instruction=instruction, local_data={"heading": heading, "error_angle_rad": error_angle_rad},
            )


def _always_zero_heading_subscription() -> SubscriberChannel[HeadingMessage]:
    return CallbackSubscriberChannel(
        topic=Topic("/heading/always_zero"),
        callback=lambda: HeadingMessage(timestamp_ms=maka_control_timestamp_ms(), heading=0),
    )


def lock_heading(feed: Feed, slotname: str) -> LockHeading:
    return LockHeading(slotname=slotname, heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage))


def turn_right(feed: Feed, degrees: float, autodrive_params: DriverParameters) -> TurnRight:
    return TurnRight(
        offset_heading_deg=degrees,
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        turn_heading_aligned_deg=lambda: autodrive_params[DriverParameter.TURN_HEADING_ALIGNED_DEG].float_value,
    )


def turn_left(feed: Feed, degrees: float, autodrive_params: DriverParameters) -> TurnLeft:
    return TurnLeft(
        offset_heading_deg=degrees,
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        turn_heading_aligned_deg=lambda: autodrive_params[DriverParameter.TURN_HEADING_ALIGNED_DEG].float_value,
    )


def turn_s_shape(
    target_position: np.ndarray,
    target_heading: float,
    feed: Feed,
    autodrive_params: DriverParameters,
    field_location_notifier: Optional[FieldLocationNotifier] = None,
    world_hud: Optional[WorldHUD] = None,
    reversing: bool = False,
    position_threshold: float = 0.75,
    heading_threshold_deg: float = 2.0,
    near_eorta_edge_distance_m_crops: float = 2,
    near_eorta_edge_distance_m_field: float = 2.5,
) -> TurnSShape:
    return TurnSShape(
        target_position=target_position,
        target_heading=target_heading,
        feed=feed,
        autodrive_params=autodrive_params,
        field_location_notifier=field_location_notifier,
        world_hud=world_hud,
        reversing=reversing,
        position_threshold=position_threshold,
        heading_threshold_deg=heading_threshold_deg,
        near_eorta_edge_distance_m_crops=near_eorta_edge_distance_m_crops,
        near_eorta_edge_distance_m_field=near_eorta_edge_distance_m_field,
    )


def turn_s_shape_slot(
    target_point_slotname: str,
    target_heading_slotname: str,
    feed: Feed,
    autodrive_params: DriverParameters,
    field_location_notifier: Optional[FieldLocationNotifier] = None,
    world_hud: Optional[WorldHUD] = None,
    reversing: bool = False,
    position_threshold: float = 0.75,
    heading_threshold_deg: float = 2.0,
    near_eorta_edge_distance_m_crops: float = 2,
    near_eorta_edge_distance_m_field: float = 2.5,
) -> TurnSShape:
    return TurnSShape(
        target_point_slotname=target_point_slotname,
        target_heading_slotname=target_heading_slotname,
        feed=feed,
        autodrive_params=autodrive_params,
        field_location_notifier=field_location_notifier,
        world_hud=world_hud,
        reversing=reversing,
        position_threshold=position_threshold,
        heading_threshold_deg=heading_threshold_deg,
        near_eorta_edge_distance_m_crops=near_eorta_edge_distance_m_crops,
        near_eorta_edge_distance_m_field=near_eorta_edge_distance_m_field,
    )


def until_aligned(
    feed: Feed,
    target_point: np.ndarray,
    target_heading: float,
    position_threshold: float = 0.75,
    heading_threshold_deg: float = 2.0,
) -> UntilAligned:
    return UntilAligned(
        feed.subscribe(Topics.BODY_POSITION.topic, RobotBody),
        feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        target_position=target_point,
        target_heading=target_heading,
        position_threshold=position_threshold,
        heading_threshold_deg=heading_threshold_deg,
    )


def until_aligned_slot(
    feed: Feed,
    target_point_slot: str,
    target_heading_slot: str,
    position_threshold: float = 0.75,
    heading_threshold_deg: float = 2.0,
) -> UntilAligned:
    return UntilAligned(
        feed.subscribe(Topics.BODY_POSITION.topic, RobotBody),
        feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        target_position_slot=target_point_slot,
        target_heading_slot=target_heading_slot,
        position_threshold=position_threshold,
        heading_threshold_deg=heading_threshold_deg,
    )


def turn_heading_offset(
    feed: Feed,
    slotname: str,
    direction: AngleDirection,
    autodrive_params: DriverParameters,
    offset_heading_deg: Optional[float] = None,
    offset_heading_angle_slotname: Optional[str] = None,
    world_hud: Optional[WorldHUD] = None,
) -> TurnHeadingOffset:
    return TurnHeadingOffset(
        feed=feed,
        slotname=slotname,
        offset_heading_deg=offset_heading_deg,
        offset_heading_angle_slotname=offset_heading_angle_slotname,
        direction=direction,
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        turn_heading_aligned_deg=lambda: autodrive_params[DriverParameter.TURN_HEADING_ALIGNED_DEG].float_value,
        world_hud=world_hud,
    )


def follow_origin_heading(feed: Feed, autodrive_params: DriverParameters) -> FollowOriginHeading:
    return FollowOriginHeading(
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
    )


def drive_current_heading(feed: Feed, autodrive_params: DriverParameters) -> DriveHeading:
    return DriveHeading(
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
    )


def drive_heading_slot(
    feed: Feed, autodrive_params: DriverParameters, slotname: str, reversing: bool = False
) -> DriveHeading:
    return DriveHeading(
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        target_heading_slotname=slotname,
    )


def drive_heading(
    feed: Feed, autodrive_params: DriverParameters, target_heading: float, reversing: bool = False
) -> DriveHeading:
    return DriveHeading(
        heading_subscription=feed.subscribe(Topics.HEADING.topic, HeadingMessage),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
        target_heading=target_heading,
    )


def drive_straight(feed: Feed, autodrive_params: DriverParameters) -> DriveHeading:
    return DriveHeading(
        heading_subscription=_always_zero_heading_subscription(),
        error_velocity_mph_subscription=error_velocity_mph_channel(feed, autodrive_params),
    )
