from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple

from core.controls.driver.location_state import DriverLoc<PERSON><PERSON>tate
from core.controls.driver.parameters import DriverParameters
from core.controls.driver.plan.base.routine import DriverRoutine
from core.controls.driver.plan.base.runner import <PERSON><PERSON><PERSON><PERSON>un<PERSON>
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.instruction import DriverInstruction, StopInstruction
from core.controls.driver.plan.routine.blind import DriveBacknForthInches, DriveStraightInches
from core.controls.driver.plan.routine.calibration import (
    CalibrateDriveRampUp,
    CalibrateMinDrivableSpeed,
    CalibrateStaticFriction,
)
from core.controls.driver.plan.routine.field import <PERSON><PERSON><PERSON>
from core.controls.driver.plan.routine.furrows import DriveFurrow
from core.controls.driver.plan.routine.heading import (
    FollowHeading,
    ForwardTurnLeft,
    ForwardTurnRight,
    HeadingInches,
    HeadingTarget,
    TurnDegrees,
)
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.plan.state.execution_state import PlanExecutionState
from core.controls.plan.state.info import PlanInfo
from generated.core.controls.driver.model.auto_drive_request import Auto<PERSON>riveRequest
from lib.common.angle_direction import AngleDirection
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.base import Feed
from lib.common.time import maka_control_timestamp_ms

LOG = get_logger(__name__)


class DriverPlan(str, Enum):
    CALIBRATE_DRIVE_RAMP_UP = "calibrate_drive_ramp_up"
    CALIBRATE_MIN_DRIVABLE_SPEED = "calibrate_min_drivable_speed"
    CALIBRATE_STATIC_FRICTION_THRESHOLD = "calibrate_static_friction_threshold"

    DRIVE_FURROW = "drive_furrow"
    DRIVE_FURROW_REVERSE = "drive_furrow_reverse"
    DRIVE_FURROW_REVERSE_FRONT_CAMS = "drive_furrow_reverse_front_cams"
    DRIVE_FURROW_BACK_CAMS = "drive_furrow_back_cams"
    DRIVE_STRAIGHT_INCHES = "drive_straight_inches"
    DRIVE_STRAIGHT_BACK_N_FORTH_INCHES = "drive_straight_back_n_forth_inches"

    HEADING_TARGET = "heading_target"
    HEADING_INCHES = "heading_inches"
    HEADING_UNTIL_END_OF_ROW = "heading_until_end_of_row"

    TURN_DEGREES = "turn_degrees"
    TURN_LEFT = "turn_left"
    TURN_RIGHT = "turn_right"

    GPS_FIELD_LEFT = "gps_field_left"
    GPS_FIELD_RIGHT = "gps_field_right"


class DrivePlan:
    def __init__(
        self,
        *,
        topic: Topic,
        feed: Feed,
        autodrive_parameters: DriverParameters,
        cancel_callback: Callable[[], None],
        no_gps: bool = False,
        field_location_notifier: Optional[FieldLocationNotifier] = None,
        world_hud: Optional[WorldHUD] = None,
    ):
        self._no_gps = no_gps
        # Any channels created here are related to the drive
        self._topic = topic

        self._cancel_callback = cancel_callback

        # publishers
        self._last_log_time_ms: int = 0

        self._autodrive_params = autodrive_parameters

        # World Annotators
        self._field_location_notifier = field_location_notifier
        self._world_hud = world_hud

        # subscriptions
        self._feed = feed

        # General Planning
        self._plan_graph = DriverOpRunner(feed=feed)

        # Plans
        SAFE_SPEED_MPH = 1

        def safe_speed(speed: Optional[float] = None) -> float:
            return min(speed or SAFE_SPEED_MPH, SAFE_SPEED_MPH)

        self._plans: Dict[DriverPlan, Callable[[int], DriverRoutine]] = {
            DriverPlan.GPS_FIELD_LEFT: lambda speed: GPSField(
                AngleDirection.LEFT, speed=safe_speed(speed), no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.GPS_FIELD_RIGHT: lambda speed: GPSField(
                AngleDirection.RIGHT, speed=safe_speed(speed), no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.DRIVE_FURROW: lambda speed: DriveFurrow(
                speed=safe_speed(speed), no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.DRIVE_FURROW_BACK_CAMS: lambda speed: DriveFurrow(
                speed=safe_speed(speed), opposite_cams=True, no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.DRIVE_FURROW_REVERSE: lambda speed: DriveFurrow(
                speed=safe_speed(speed), reverse=True, no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.DRIVE_FURROW_REVERSE_FRONT_CAMS: lambda speed: DriveFurrow(
                speed=safe_speed(speed), opposite_cams=True, reverse=True, no_gps=self._no_gps, **self.kwargs
            ),
            DriverPlan.TURN_LEFT: lambda _: ForwardTurnLeft(**self.kwargs),
            DriverPlan.TURN_RIGHT: lambda _: ForwardTurnRight(**self.kwargs),
            DriverPlan.CALIBRATE_DRIVE_RAMP_UP: lambda v: CalibrateDriveRampUp(until_velocity_mph=v, **self.kwargs),
            DriverPlan.CALIBRATE_MIN_DRIVABLE_SPEED: lambda _: CalibrateMinDrivableSpeed(**self.kwargs),
            DriverPlan.CALIBRATE_STATIC_FRICTION_THRESHOLD: lambda _: CalibrateStaticFriction(**self.kwargs),
            DriverPlan.DRIVE_STRAIGHT_INCHES: lambda inches: DriveStraightInches(inches=inches, **self.kwargs),
            DriverPlan.DRIVE_STRAIGHT_BACK_N_FORTH_INCHES: lambda inches: DriveBacknForthInches(
                inches=inches, **self.kwargs
            ),
            DriverPlan.HEADING_TARGET: lambda target_heading: HeadingTarget(
                target_heading=target_heading, **self.kwargs
            ),
            DriverPlan.HEADING_INCHES: lambda inches: HeadingInches(inches=inches, **self.kwargs),
            DriverPlan.HEADING_UNTIL_END_OF_ROW: lambda _: FollowHeading(**self.kwargs),
            DriverPlan.TURN_DEGREES: lambda degrees: TurnDegrees(degrees=degrees, **self.kwargs),
        }

        self._is_safety_paused: bool = False
        self._to_be_paused: bool = False
        self._to_be_unpaused: bool = False

    @property
    def autodrive_parameters(self) -> DriverParameters:
        return self._autodrive_params

    @property
    def execution_state(self) -> PlanExecutionState:
        return self._plan_graph.execution_state

    @property
    def program_name(self) -> str:
        return self._plan_graph.program_name

    @property
    def active(self) -> bool:
        return self._plan_graph.active

    @property
    def paused(self) -> bool:
        return self._plan_graph.paused

    def pause(self) -> None:
        self._to_be_paused = True

    def unpause(self) -> None:
        self._to_be_unpaused = True

    def _pause(self) -> None:
        self._plan_graph.pause()

    def _unpause(self) -> None:
        self._plan_graph.unpause()

    @property
    def available_plans(self) -> List[str]:
        return list(self._plans.keys())

    #
    # General state
    #
    def driver_cancel_plan(self, reason: str) -> None:
        self._plan_graph.destroy(reason=reason)

    def cancel_plan(self) -> None:
        self._cancel_callback()

    @property
    def kwargs(self) -> Dict[str, Any]:
        """
        Reflection helper method for children
        """
        return {
            "feed": self._feed,
            "autodrive_params": self._autodrive_params,
            "field_location_notifier": self._field_location_notifier,
            "world_hud": self._world_hud,
        }

    def reset(self) -> None:
        self._plan_graph = DriverOpRunner(feed=self._feed)

    # EVENTS

    async def handle_tick(
        self, location_state: DriverLocationState
    ) -> Tuple[Optional[PlanInfo], Optional[DriverInstruction]]:
        """
        Run one drive plan tick. Update any drive state world publishers, check our safety geofence, and tick
        the plan_graph (if instructed).
        """
        if not location_state.is_safe() and not self.paused:
            instruction: Optional[DriverInstruction] = None

            if not self._is_safety_paused:
                self._pause()
                self._is_safety_paused = True

                instruction = StopInstruction(reason=location_state)

            elif self.execution_state.in_progress() and maka_control_timestamp_ms() - self._last_log_time_ms >= 1000:
                self._last_log_time_ms = maka_control_timestamp_ms()
                LOG.warning(f"{location_state} Awaiting safe geofence to continue plan")

            return None, instruction

        # previously was lost
        elif self._is_safety_paused:
            self.unpause()
            self._is_safety_paused = False
            return None, None

        else:
            self._is_safety_paused = False

            if self._to_be_unpaused:
                self._unpause()
                self._to_be_unpaused = False

            if self._to_be_paused:
                self._pause()
                self._to_be_paused = False
                return None, StopInstruction(reason=location_state)

            if self.active and not self.paused:
                return self._plan_graph.tick()
            else:
                return None, None

    # HEADING

    def handle_auto_drive_request(self, req: AutoDriveRequest) -> None:
        """
        Toggle predict driving - if toggling on then figure what is the priority order live prediction and
        use it as target
        """
        self.cancel_plan()
        # vision pathway following requests
        LOG.info(f"Auto driving. plan = {req.plan}, datum = {req.datum}")
        routine: DriverRoutine = self._plans[req.plan](req.datum)
        self._plan_graph.build(routine)
