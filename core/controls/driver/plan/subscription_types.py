from typing import Optional, TypeVar

import numpy as np

from core.controls.frame.heading_distance import HeadingDistanceAccumulator
from core.controls.frame.pose.body import RobotBody
from core.controls.frame.rotary_encoders_snapshot import RotaryEncodersSnapshot
from core.cv.visual_cortex.graph.output import FurrowsOutput, OpticalFlowOutput
from generated.core.controls.driver.model.pixel_distance_message import PixelDistanceMessage
from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from generated.core.controls.frame.model.heading_message import HeadingMessage
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.geo.ecef_utils import EcefVector
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel
from lib.common.time import maka_control_timestamp_ms

FurrowsSubscription = SubscriberChannel[FurrowsOutput]
OpticalFlowSubscription = SubscriberChannel[OpticalFlowOutput]
HeadingSubscription = SubscriberChannel[HeadingMessage]
GeopositionLatLonAltSubscription = SubscriberChannel[GeopositionLatLonAltMessage]
GeopositionEcefSubscription = SubscriberChannel[GeopositionEcefMessage]
TraveledInchesSubscription = SubscriberChannel[float]
HeadingDistanceSubscription = SubscriberChannel[HeadingDistanceAccumulator]
RotaryEncodersSnapshotSubscription = SubscriberChannel[RotaryEncodersSnapshot]
VelocityMphSubscription = SubscriberChannel[VelocityMessage]
GPSHistoryVectorSubscription = SubscriberChannel[EcefVector]
RobotBodySubscription = SubscriberChannel[RobotBody]


LOG = get_logger(__name__)


class PlanSubscriptionException(Exception):
    pass


T = TypeVar("T")


class DistanceAccumulator(SubscriberChannel[PixelDistanceMessage]):
    """
    Accumulate relative pixel distance updates and publish the absolute total distance.
    """

    def __init__(self) -> None:
        self._total: int = 0
        self._update_time_ms = 0

    def add_distance(self, addition: int) -> None:
        self._total += addition
        self._update_time_ms = maka_control_timestamp_ms()

    def read(self) -> Optional[PixelDistanceMessage]:
        return PixelDistanceMessage(timestamp_ms=self._update_time_ms, pixels=self._total)


def current_position(ecef_subscription: RobotBodySubscription) -> Optional[np.ndarray]:
    robot_body: Optional[RobotBody] = ecef_subscription.read()
    if robot_body is None:
        return None
    robot_body_ecef = robot_body.wheel_centroid_ecef
    return np.array([robot_body_ecef.x, robot_body_ecef.y, robot_body_ecef.z])
