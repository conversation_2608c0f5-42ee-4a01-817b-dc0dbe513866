import numpy as np

DEFAULT_TARGET_SPEED_MPH: float = 0.5

########################################################################################################################

DEFAULT_CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED = 0.01
DEFAULT_CALIBRATE_RAMP_UP_MS: int = 2000

DEFAULT_DRIVE_RAMP_UP_MS: int = 5000
DEFAULT_DRIVE_RAMP_DOWN_MS: int = 2000

DEFAULT_FILTER_EXPIRATION_MS: int = 5000
DEFAULT_FILTER_MAX_DELTA_ERROR_ANGLE_DEG: float = 5
# Do not increase! <PERSON> tried this, and then velocity estimator died (why?) and we drove full speed into furrows
# It's a bit hard for humans to line up at 5; found 6 to be better, more robust for starting the follow routine
DEFAULT_FILTER_MAX_ERROR_ANGLE_DEG: float = 6
DEFAULT_FILTER_MAX_ERROR_X_PCT: float = 0.2

DEFAULT_PID_TARGET_GAIN_P = 2
DEFAULT_PID_TARGET_GAIN_I = 0
DEFAULT_PID_TARGET_GAIN_D = 0

DEFAULT_PID_STEERING_GAIN_P = 0.0125
DEFAULT_PID_STEERING_GAIN_I = 0
DEFAULT_PID_STEERING_GAIN_D = 0
DEFAULT_PID_STEERING_DEADBAND_ANGLE = 0.5

DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG = 0.0
DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_SCALAR = 0.3

DEFAULT_TURN_HEADING_ALIGNED_DEG: float = 1

########################################################################################################################

DRIVE_FURROW_SPEED_MPH = 0.6
REENTER_FURROW_SPEED_MPH = 0.2
EORTA_TURN_SPEED_MPH = 0.5

MACHINE_ROW_WIDTH_IN = 80

# physical machine
MACHINE_ROW_WIDTH_INCHES = 80
MACHINE_TURN_RADIUS_INCHES = 12 * 20

# Legacy
# TODO connect this with AutoDriveParameters and show in Ui
PID_MIN_ERROR_ANGLE_DEGREES: float = 0.2
PID_MIN_ERROR_ANGLE_RADIANS: float = np.deg2rad(PID_MIN_ERROR_ANGLE_DEGREES)
