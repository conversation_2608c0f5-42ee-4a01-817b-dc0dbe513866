import asyncio
import math
from enum import Enum
from typing import Any, Callable, Coroutine, List, Optional, Tuple

import numpy as np

from lib.common.logging import get_logger
from lib.common.pid.pid import PIDControl
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.nanopb.nofx_board.nofx_board_connector import NoFXBoardConnector
from lib.drivers.spencer_fluids.hfx_interface import HfxInterface

LOG = get_logger(__name__)


class State(Enum):
    STOPPED = 0
    STOPPING = 1
    DRIVING = 2


class DrivebotException(Exception):
    pass


class Drivebot:
    def __init__(
        self,
        *,
        hfx_interface: HfxInterface,
        nofx_board: NoFXBoardConnector,
        deadman_timeout_ms: float = 1000,
        drive_controller_tick_time_ms: float = 50.0,
        velocity_estimator_tick_time_ms: float = 100.0,
        watchdog_tick_time_ms: float = 1000.0,
        status_tick_time_ms: float = 1000.0,
        deadman_tick_time_ms: float = 100.0,
        velocity_pid_kp: float = 15.0,
        velocity_pid_ki: float = 0,
        velocity_pid_kd: float = 4.0,
        steering_pid_kp: float = 0.015,
        steering_pid_ki: float = 0.0,
        steering_pid_kd: float = 0.0,
        steering_deadband_angle_deg: float = 0.5,
        stop_ma_per_second: float = 1000.0,
        one_rev_distance: float = 97.0,
        tpr: int = 20000,
        start_drive_ma: int = 500,
        min_drive_ma: int = 200,
        max_drive_ma: int = 800,
        stop_callback: Callable[[], Coroutine[Any, Any, None]],
    ) -> None:
        self._hfx_interface = hfx_interface
        self._nofx_board = nofx_board
        self._velocity_pid = PIDControl(Kp=velocity_pid_kp, Ki=velocity_pid_ki, Kd=velocity_pid_kd)
        self._hfx_interface.set_steering_pid_p(steering_pid_kp)
        self._hfx_interface.set_steering_pid_i(steering_pid_ki)
        self._hfx_interface.set_steering_pid_d(steering_pid_kd)
        self._hfx_interface.set_steering_deadband_angle(steering_deadband_angle_deg)
        self._state: State = State.STOPPED
        self._target_velocity_mph = 0.0
        self._actual_velocity_mph = 0.0
        self._start_drive_ma = start_drive_ma
        self._min_drive_ma = min_drive_ma
        self._max_drive_ma = max_drive_ma
        self._deadman_timeout_ms = deadman_timeout_ms
        self._one_rev_distance = one_rev_distance
        self._tpr = tpr
        self._last_positive_control_time_ms = 0
        self._positive_controller = "nobody"
        self._drive_ma = 0.0
        self._stop_ma_per_second = stop_ma_per_second
        self._last_rotary_ticks: Optional[int] = None
        self._last_rotary_timestamp_ms: Optional[float] = None
        self._stop_callback = stop_callback
        self._tasks = []
        loop = asyncio.get_event_loop()
        self._tasks.append(loop.create_task(self._hfx_interface.run_async()))
        self._tasks.append(
            loop.create_task(
                self._dispatch("drive controller", self._drive_controller_tick, drive_controller_tick_time_ms)
            )
        )
        self._tasks.append(
            loop.create_task(
                self._dispatch("velocity estimator", self._velocity_estimator_tick, velocity_estimator_tick_time_ms)
            )
        )
        loop.create_task(self._dispatch("watchdog", self._watchdog_tick, watchdog_tick_time_ms))
        self._tasks.append(loop.create_task(self._dispatch("status", self._status_tick, status_tick_time_ms)))
        self._tasks.append(loop.create_task(self._dispatch("deadman", self._deadman_tick, deadman_tick_time_ms)))

    def notify_positive_control(self, caller_id: str) -> None:
        self._last_positive_control_time_ms = maka_control_timestamp_ms()
        self._positive_controller = caller_id

    def verify_positive_control(self, caller_id: str) -> None:
        if self._positive_controller == "nobody":
            return
        if maka_control_timestamp_ms() - self._last_positive_control_time_ms > self._deadman_timeout_ms:
            # Deadman expired, allow positive control first come first serve.
            return
        if self._positive_controller != caller_id:
            raise DrivebotException(f"Positive control lost by {caller_id} to {self._positive_controller}")

    @property
    def state(self) -> State:
        return self._state

    @property
    def pc_control(self) -> bool:
        return self._hfx_interface.pc_control

    @property
    def actual_left_wheel_angle_deg(self) -> float:
        return self._hfx_interface.actual_left_wheel_angle_deg

    @property
    def actual_right_wheel_angle_deg(self) -> float:
        return self._hfx_interface.actual_right_wheel_angle_deg

    @property
    def target_left_wheel_angle_deg(self) -> float:
        return self._hfx_interface.target_left_wheel_angle_deg

    @property
    def target_right_wheel_angle_deg(self) -> float:
        return self._hfx_interface.target_right_wheel_angle_deg

    @property
    def target_velocity_mph(self) -> float:
        return self._target_velocity_mph

    @property
    def actual_velocity_mph(self) -> float:
        return self._actual_velocity_mph

    def set_velocity_mph(self, velocity_mph: float) -> None:
        if self._state == State.STOPPED:
            if not math.isclose(velocity_mph, 0):
                self._target_velocity_mph = velocity_mph
                self._state = State.DRIVING
        elif self._state in [State.STOPPING, State.DRIVING]:
            if math.isclose(velocity_mph, 0):
                self._target_velocity_mph = 0.0
                self._state = State.STOPPING
            else:
                if np.sign(velocity_mph) == np.sign(self._drive_ma) or int(self._drive_ma) == 0:
                    self._target_velocity_mph = velocity_mph
                    self._state = State.DRIVING
                else:
                    raise DrivebotException(
                        f"Need to stop before driving in opposite direction. Requested: {velocity_mph}, previous target: {self._target_velocity_mph}, drive milliamps: {self._drive_ma}"
                    )
        else:
            raise DrivebotException(f"Unknown state: {self._state}")

    def set_left_wheel_angle_deg(self, angle_deg: float) -> None:
        self._hfx_interface.set_left_steering_angle(angle_deg)

    def set_right_wheel_angle_deg(self, angle_deg: float) -> None:
        self._hfx_interface.set_right_steering_angle(angle_deg)

    def stop_motors(self, caller_id: str) -> None:
        # stop motors as quickly as we can
        self._hfx_interface.set_ground_speed(0)
        self._target_velocity_mph = 0.0
        self._drive_ma = 0.0
        self._state = State.STOPPED
        self._velocity_pid.reset()
        self.notify_positive_control(caller_id)

    @property
    def button_presses(self) -> List[Tuple[int, str]]:
        if self.pc_control:
            return self._hfx_interface.fort_button_presses
        else:
            return []

    async def _watchdog_tick(self, tick_time_ms: float) -> None:
        shutdown = False
        for task in self._tasks:
            if task.done():
                shutdown = True
        if shutdown:
            for task in self._tasks:
                task.cancel()
            await self._stop_callback()

    async def _drive_controller_tick(self, tick_time_ms: float) -> None:
        if not self._hfx_interface.pc_control:
            self.stop_motors("operator")
            return

        if self._state == State.STOPPING:
            speed_decrease_delta = self._stop_ma_per_second * tick_time_ms / 1000.0
            if abs(self._drive_ma) > speed_decrease_delta:
                self._drive_ma -= np.sign(self._drive_ma) * speed_decrease_delta
            else:
                self._drive_ma = 0.0
            self._hfx_interface.set_ground_speed(int(self._drive_ma))
            if int(self._drive_ma) == 0:
                self._state = State.STOPPED
        elif self._state == State.DRIVING:
            error = self._target_velocity_mph - self._actual_velocity_mph
            if abs(self._drive_ma) < self._min_drive_ma:
                # starting up
                self._drive_ma = np.sign(self._target_velocity_mph) * self._start_drive_ma
            self._drive_ma += self._velocity_pid.output(error)
            self._drive_ma = np.clip(
                self._drive_ma,
                self._min_drive_ma if self.target_velocity_mph > 0 else -self._max_drive_ma,
                self._max_drive_ma if self.target_velocity_mph > 0 else -self._min_drive_ma,
            )
            self._hfx_interface.set_ground_speed(int(self._drive_ma))

    async def _velocity_estimator_tick(self, tick_time_ms: float) -> None:
        rotary = await self._nofx_board.rotary()
        ticks = int((rotary.back_left_ticks + (-rotary.back_right_ticks)) / 2)
        # TODO(asergeev): nofx time sometimes goes crazy, use computer time instead for now
        timestamp_ms = maka_control_timestamp_ms()
        # timestamp_ms = rotary.timestamp.seconds * 1000 + rotary.timestamp.micros / 1000

        if self._last_rotary_ticks is None or self._last_rotary_timestamp_ms is None:
            self._last_rotary_ticks = ticks
            self._last_rotary_timestamp_ms = timestamp_ms
            return

        if int(timestamp_ms) == int(self._last_rotary_timestamp_ms):
            LOG.info(
                f"Discarding old rotary value - current: {timestamp_ms} vs. last: {self._last_rotary_timestamp_ms}"
            )
            return

        duration_s = (timestamp_ms - self._last_rotary_timestamp_ms) / 1000
        delta_ticks_per_second = (ticks - self._last_rotary_ticks) / duration_s

        ticks_to_inches = self._one_rev_distance / self._tpr
        inches_per_second_to_mph = 1 / 17.6
        ticks_per_second_to_mph = ticks_to_inches * inches_per_second_to_mph
        self._actual_velocity_mph = delta_ticks_per_second * ticks_per_second_to_mph

        self._last_rotary_ticks = ticks
        self._last_rotary_timestamp_ms = timestamp_ms

    async def _status_tick(self, tick_time_ms: float) -> None:
        LOG.info(
            f"{self._state}: Actual velocity: {self._actual_velocity_mph:.2f}. Target velocity: {self._target_velocity_mph:.2f}. PC control: {self._hfx_interface.pc_control}. Drive milliamps: {self._drive_ma:.0f}."
        )

    async def _deadman_tick(self, tick_time_ms: float) -> None:
        elapsed_ms = maka_control_timestamp_ms() - self._last_positive_control_time_ms
        if elapsed_ms >= self._deadman_timeout_ms and self._state != State.STOPPED:
            self.stop_motors("nobody")
            LOG.info(f"DEADMAN TIMEOUT! STOP MOTORS! Elapsed: {elapsed_ms}, timeout: {self._deadman_timeout_ms}")

    async def _dispatch(
        self, name: str, callback: Callable[[float], Coroutine[Any, Any, None]], tick_time_ms: float
    ) -> None:
        while True:
            start_time = maka_control_timestamp_ms()
            try:
                await callback(tick_time_ms)
            except asyncio.CancelledError:
                LOG.exception(f"{name} FAILED. STOP MOTORS!")
                self.stop_motors("nobody")
                break
            except:  # noqa
                LOG.exception(f"{name} FAILED. STOP MOTORS!")
                self.stop_motors("nobody")
            finish_time = maka_control_timestamp_ms()
            duration = finish_time - start_time
            sleep_for_ms = max(tick_time_ms - duration, 0)
            await asyncio.sleep(sleep_for_ms / 1000.0)

    def set_velocity_pid(
        self, kp: float, ki: float, kd: float, start_drive_ma: int, min_drive_ma: int, max_drive_ma: int
    ) -> None:
        self._velocity_pid.set_gain_p(kp)
        self._velocity_pid.set_gain_i(ki)
        self._velocity_pid.set_gain_d(kd)
        self._start_drive_ma = start_drive_ma
        self._min_drive_ma = min_drive_ma
        self._max_drive_ma = max_drive_ma

    def set_steer_pid(self, kp: float, ki: float, kd: float, steering_deadband_angle_deg: float) -> None:
        self._hfx_interface.set_steering_pid_p(kp)
        self._hfx_interface.set_steering_pid_i(ki)
        self._hfx_interface.set_steering_pid_d(kd)
        self._hfx_interface.set_steering_deadband_angle(steering_deadband_angle_deg)
