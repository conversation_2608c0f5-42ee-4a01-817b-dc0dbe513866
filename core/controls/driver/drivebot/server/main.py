import asyncio
import logging
import sys
from typing import Any

import can
import grpc

from core.controls.driver.drivebot.server.drivebot import Drivebot, DrivebotException, State
from generated.core.controls.driver.drivebot.proto import drivebot_pb2, drivebot_pb2_grpc
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import Topic
from lib.common.protocol.feed.inproc import InprocFeed
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.time import maka_control_timestamp_ms
from lib.drivers.nanopb.nofx_board.nofx_board_connector import NoFXBoardConnector
from lib.drivers.spencer_fluids.hfx_interface import HfxInterface

LOG = get_logger(__name__)


def to_proto_state(state: State) -> drivebot_pb2.State:
    if state == State.STOPPED:
        return drivebot_pb2.State.STOPPED
    elif state == State.STOPPING:
        return drivebot_pb2.State.STOPPING
    elif state == State.DRIVING:
        return drivebot_pb2.State.DRIVING
    else:
        raise DrivebotException("Unknown state")


class DriveServiceServicer(drivebot_pb2_grpc.DriveServiceServicer):
    def __init__(self, drivebot: Drivebot):
        self._drivebot = drivebot

    async def Drive(self, req: drivebot_pb2.DriveRequest, context: Any) -> drivebot_pb2.DriveResponse:
        try:
            self._drivebot.verify_positive_control(req.caller_id)
            if req.HasField("velocity_mph"):
                self._drivebot.set_velocity_mph(req.velocity_mph)
            if req.HasField("left_wheel_angle_deg"):
                self._drivebot.set_left_wheel_angle_deg(req.left_wheel_angle_deg)
            if req.HasField("right_wheel_angle_deg"):
                self._drivebot.set_right_wheel_angle_deg(req.right_wheel_angle_deg)
            self._drivebot.notify_positive_control(req.caller_id)
        except:  # noqa
            self._drivebot.stop_motors("nobody")
            LOG.exception("Drive() failed")
            raise
        return drivebot_pb2.DriveResponse()

    async def GetStatus(self, req: drivebot_pb2.GetStatusRequest, context: Any) -> drivebot_pb2.GetStatusResponse:
        status_response = drivebot_pb2.GetStatusResponse()
        status_response.actual_left_wheel_angle_deg = self._drivebot.actual_left_wheel_angle_deg
        status_response.actual_right_wheel_angle_deg = self._drivebot.actual_right_wheel_angle_deg
        status_response.actual_velocity_mph = self._drivebot.actual_velocity_mph
        status_response.odometer_in = 0  # TODO(evanbro) Hook up to a velocity accumulator
        status_response.pc_control = self._drivebot.pc_control
        status_response.state = to_proto_state(self._drivebot.state)
        status_response.target_velocity_mph = self._drivebot.target_velocity_mph
        status_response.target_left_wheel_angle_deg = self._drivebot.target_left_wheel_angle_deg
        status_response.target_right_wheel_angle_deg = self._drivebot.target_right_wheel_angle_deg
        status_response.timestamp_ms = maka_control_timestamp_ms()

        return status_response

    async def SetVelocityPid(
        self, req: drivebot_pb2.SetVelocityPidRequest, context: Any
    ) -> drivebot_pb2.SetVelocityPidResponse:
        try:
            self._drivebot.verify_positive_control(req.caller_id)
            self._drivebot.set_velocity_pid(
                req.kp, req.ki, req.kd, req.start_drive_ma, req.min_drive_ma, req.max_drive_ma
            )
        except:  # noqa
            self._drivebot.stop_motors("nobody")
            LOG.exception("SetVelocityPid() failed")
            raise
        return drivebot_pb2.SetVelocityPidResponse()

    async def SetSteerPid(self, req: drivebot_pb2.SetSteerPidRequest, context: Any) -> drivebot_pb2.SetSteerPidResponse:
        try:
            self._drivebot.verify_positive_control(req.caller_id)
            self._drivebot.set_steer_pid(req.kp, req.ki, req.kd, req.steering_deadband_angle_deg)
        except:  # noqa
            self._drivebot.stop_motors("nobody")
            LOG.exception("SetSteerPid() failed")
            raise
        return drivebot_pb2.SetSteerPidResponse()

    async def GetButtonPresses(
        self, req: drivebot_pb2.GetButtonPressesRequest, context: Any
    ) -> drivebot_pb2.GetButtonPressesResponse:
        try:
            button_presses = [
                drivebot_pb2.ButtonPress(timestamp_ms=x[0], button=x[1]) for x in self._drivebot.button_presses
            ]
        except:  # noqa
            LOG.exception("GetButtonPresses() failed")
            raise
        return drivebot_pb2.GetButtonPressesResponse(button_presses=button_presses)


async def serve(port: int = 6969) -> None:
    hfx_interface = HfxInterface(
        topic=Topic("/hfx"), can_bus=can.interface.Bus("can0", bustype="socketcan"), feed=InprocFeed()  # type: ignore
    )
    nofx_board = NoFXBoardConnector()
    server = grpc.aio.server()

    async def stop_callback() -> None:
        await server.stop(50)

    drivebot = Drivebot(hfx_interface=hfx_interface, nofx_board=nofx_board, stop_callback=stop_callback)

    servicer = DriveServiceServicer(drivebot)
    drivebot_pb2_grpc.add_DriveServiceServicer_to_server(servicer, server)
    server.add_insecure_port(f"[::]:{port}")
    await server.start()
    LOG.info(f"Started Drivebot at 0.0.0.0:{port}")
    await server.wait_for_termination()
    sys.exit(1)


if __name__ == "__main__":
    logging.basicConfig(level="INFO")
    asyncio.run_coroutine_threadsafe(serve(), get_event_loop_by_name()).result()
