from typing import Optional

import grpc

from generated.core.controls.driver.drivebot.proto import drivebot_pb2, drivebot_pb2_grpc
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class DrivebotClient:
    def __init__(self, hostname: str = "localhost", port: int = 6969) -> None:
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[drivebot_pb2_grpc.DriveServiceStub] = None

    def _maybe_connect(self) -> drivebot_pb2_grpc.DriveServiceStub:
        if self._stub is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = drivebot_pb2_grpc.DriveServiceStub(self._channel)

    async def drive(
        self,
        *,
        caller_id: str,
        velocity_mph: Optional[float] = None,
        left_wheel_angle_deg: Optional[float] = None,
        right_wheel_angle_deg: Optional[float] = None,
    ) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.DriveRequest(
            caller_id=caller_id,
            velocity_mph=velocity_mph,
            left_wheel_angle_deg=left_wheel_angle_deg,
            right_wheel_angle_deg=right_wheel_angle_deg,
        )
        await self._stub.Drive(req)

    async def get_status(self) -> drivebot_pb2.GetStatusResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.GetStatusRequest()

        status = await self._stub.GetStatus(req)

        return status

    async def get_button_presses(self) -> drivebot_pb2.GetButtonPressesResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.GetButtonPressesRequest()

        button_presses = await self._stub.GetButtonPresses(req)

        return button_presses


class DrivebotClientSynchronous:
    def __init__(self, hostname: str = "localhost", port: int = 6969) -> None:
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[drivebot_pb2_grpc.DriveServiceStub] = None

    def _maybe_connect(self) -> drivebot_pb2_grpc.DriveServiceStub:
        if self._stub is None:
            self._channel = grpc.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = drivebot_pb2_grpc.DriveServiceStub(self._channel)

    def drive(
        self,
        *,
        caller_id: str,
        velocity_mph: Optional[float] = None,
        left_wheel_angle_deg: Optional[float] = None,
        right_wheel_angle_deg: Optional[float] = None,
    ) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.DriveRequest(
            caller_id=caller_id,
            velocity_mph=velocity_mph,
            left_wheel_angle_deg=left_wheel_angle_deg,
            right_wheel_angle_deg=right_wheel_angle_deg,
        )
        self._stub.Drive(req)

    def get_status(self) -> drivebot_pb2.GetStatusResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.GetStatusRequest()

        status = self._stub.GetStatus(req)

        return status

    def get_button_presses(self) -> drivebot_pb2.GetButtonPressesResponse:
        self._maybe_connect()
        assert self._stub is not None
        req = drivebot_pb2.GetButtonPressesRequest()

        button_presses = self._stub.GetButtonPresses(req)

        return button_presses

    # TODO(asergeev): set_velocity_pid(), set_steer_pid()
