syntax = "proto3";

message DriveRequest {
  string caller_id = 1;
  optional float velocity_mph = 2;
  optional float left_wheel_angle_deg = 3;
  optional float right_wheel_angle_deg = 4;
}

message DriveResponse {}

enum State {
  STOPPED = 0;
  STOPPING = 1;
  DRIVING = 2;
}

message GetStatusRequest {}

message GetStatusResponse {
  int64 timestamp_ms = 1;
  State state = 2;
  bool pc_control = 3;
  float target_left_wheel_angle_deg = 4;
  float actual_left_wheel_angle_deg = 5;
  float target_right_wheel_angle_deg = 6;
  float actual_right_wheel_angle_deg = 7;
  float target_velocity_mph = 8;
  float actual_velocity_mph = 9;
  float odometer_in = 10;
}

message ButtonPress {
  int64 timestamp_ms = 1;
  string button = 2;
}

message GetButtonPressesRequest {}

message GetButtonPressesResponse {
  repeated ButtonPress button_presses = 1;
}

message SetVelocityPidRequest {
  string caller_id = 1;
  float kp = 2;
  float ki = 3;
  float kd = 4;
  float start_drive_ma = 5;
  float min_drive_ma = 6;
  float max_drive_ma = 7;
}

message SetVelocityPidResponse {}

message SetSteerPidRequest {
  string caller_id = 1;
  float kp = 2;
  float ki = 3;
  float kd = 4;
  float steering_deadband_angle_deg = 5;
}

message SetSteerPidResponse {}

service DriveService {
  rpc Drive(DriveRequest) returns (DriveResponse) {}
  rpc GetStatus(GetStatusRequest) returns (GetStatusResponse) {}
  rpc SetVelocityPid(SetVelocityPidRequest) returns (SetVelocityPidResponse) {}
  rpc SetSteerPid(SetSteerPidRequest) returns (SetSteerPidResponse) {}
  rpc GetButtonPresses(GetButtonPressesRequest) returns (GetButtonPressesResponse) {}
}