from enum import Enum
from typing import Dict

from core.controls.driver.constants import (
    DEFAULT_CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED,
    DEFAULT_CALIBRATE_RAMP_UP_MS,
    DEFAULT_DRIVE_RAMP_DOWN_MS,
    DEFAULT_DRIVE_RAMP_UP_MS,
    DEFAULT_FILTER_EXPIRATION_MS,
    DEFAULT_FILTER_MAX_DELTA_ERROR_ANGLE_DEG,
    DEFAULT_FILTER_MAX_ERROR_ANGLE_DEG,
    DEFAULT_FILTER_MAX_ERROR_X_PCT,
    DEFAULT_PID_STEERING_DEADBAND_ANGLE,
    DEFAULT_PID_STEERING_GAIN_D,
    DEFAULT_PID_STEERING_GAIN_I,
    DEFAULT_PID_STEERING_GAIN_P,
    DEFAULT_PID_TARGET_GAIN_D,
    DEFAULT_PID_TARGET_GAIN_I,
    DEFAULT_PID_TARGET_GAIN_P,
    DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG,
    DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_SCALAR,
    DEFAULT_TARGET_SPEED_MPH,
    DEFAULT_TURN_HEADING_ALIGNED_DEG,
)
from core.model.parameter import FloatParameter, IntParameter, Parameter

DriverParameters = Dict[str, Parameter]


class DriverParameter(str, Enum):

    CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED = "calibrate_ramp_forward_increment_unsigned"
    CALIBRATE_RAMP_UP_MS = "calibrate_ramp_up_ms"

    DRIVE_RAMP_DOWN_MS = "drive_ramp_down_ms"
    DRIVE_RAMP_UP_MS = "drive_ramp_up_ms"

    FILTER_EXPIRATION_MS = "filter_expiration_ms"
    FILTER_MAX_DELTA_ERROR_ANGLE_DEG = "filter_max_delta_error_angle_deg"
    FILTER_MAX_ERROR_ANGLE_DEG = "filter_max_error_angle_deg"
    FILTER_MAX_ERROR_X_PCT = "filter_max_error_x_pct"

    PID_TARGET_GAIN_P = "pid_target_gain_p"
    PID_TARGET_GAIN_I = "pid_target_gain_i"
    PID_TARGET_GAIN_D = "pid_target_gain_d"

    PID_STEERING_GAIN_P = "pid_steering_gain_p"
    PID_STEERING_GAIN_I = "pid_steering_gain_i"
    PID_STEERING_GAIN_D = "pid_steering_gain_d"
    PID_STEERING_DEADBAND_ANGLE = "pid_steering_deadband_angle"

    SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG = "speed_limit_angular_error_effective_zero_deg"
    SPEED_LIMIT_ANGULAR_ERROR_SCALAR = "speed_limit_angular_error_scalar"

    TARGET_SPEED_MPH = "target_speed_mph"

    TURN_HEADING_ALIGNED_DEG = "turn_heading_aligned_deg"

    def __str__(self) -> str:
        return str(self.value)

    @property
    def cmdline_arg(self) -> str:
        return f"--{self.replace('_', '-')}"


DEFAULT_DRIVER_PARAMETERS: DriverParameters = {
    #
    # Set Points (at top alphabetically for UI render)
    #
    DriverParameter.TARGET_SPEED_MPH: FloatParameter(
        name=DriverParameter.TARGET_SPEED_MPH, value=DEFAULT_TARGET_SPEED_MPH, min=0, max=10, step=0.01,
    ),
    #
    # Calibration
    #
    DriverParameter.CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED: FloatParameter(
        name=DriverParameter.CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED,
        value=DEFAULT_CALIBRATE_RAMP_FORWARD_INCREMENT_UNSIGNED,
        min=0,
        max=1,
        step=0.001,
    ),
    DriverParameter.CALIBRATE_RAMP_UP_MS: IntParameter(
        name=DriverParameter.CALIBRATE_RAMP_UP_MS, value=DEFAULT_CALIBRATE_RAMP_UP_MS, min=0, max=1000000, step=10,
    ),
    #
    # Acceleration
    #
    DriverParameter.DRIVE_RAMP_DOWN_MS: IntParameter(
        name=DriverParameter.DRIVE_RAMP_DOWN_MS,
        description="Rate limit minimum number of milliseconds to decelerate from 100% to 0",
        value=DEFAULT_DRIVE_RAMP_DOWN_MS,
        min=0,
        max=2000,
        step=10,
    ),
    DriverParameter.DRIVE_RAMP_UP_MS: IntParameter(
        name=DriverParameter.DRIVE_RAMP_UP_MS,
        description="Rate limit minimum number of milliseconds to accelerate from 0 to 100%",
        value=DEFAULT_DRIVE_RAMP_UP_MS,
        min=0,
        max=1000000,
        step=10,
    ),
    #
    # Filter
    #
    DriverParameter.FILTER_EXPIRATION_MS: IntParameter(
        name=DriverParameter.FILTER_EXPIRATION_MS, value=DEFAULT_FILTER_EXPIRATION_MS, min=0, max=1000000, step=10,
    ),
    DriverParameter.FILTER_MAX_DELTA_ERROR_ANGLE_DEG: FloatParameter(
        name=DriverParameter.FILTER_MAX_DELTA_ERROR_ANGLE_DEG,
        value=DEFAULT_FILTER_MAX_DELTA_ERROR_ANGLE_DEG,
        min=0,
        max=90,
        step=0.1,
    ),
    DriverParameter.FILTER_MAX_ERROR_ANGLE_DEG: FloatParameter(
        name=DriverParameter.FILTER_MAX_ERROR_ANGLE_DEG,
        value=DEFAULT_FILTER_MAX_ERROR_ANGLE_DEG,
        min=0,
        max=90,
        step=0.1,
    ),
    DriverParameter.FILTER_MAX_ERROR_X_PCT: FloatParameter(
        name=DriverParameter.FILTER_MAX_ERROR_X_PCT, value=DEFAULT_FILTER_MAX_ERROR_X_PCT, min=0, max=0.5, step=0.01,
    ),
    #
    # Target PID
    #
    DriverParameter.PID_TARGET_GAIN_P: FloatParameter(
        name=DriverParameter.PID_TARGET_GAIN_P, value=DEFAULT_PID_TARGET_GAIN_P, min=-100, max=100, step=0.01,
    ),
    DriverParameter.PID_TARGET_GAIN_I: FloatParameter(
        name=DriverParameter.PID_TARGET_GAIN_I, value=DEFAULT_PID_TARGET_GAIN_I, min=-100, max=100, step=0.01,
    ),
    DriverParameter.PID_TARGET_GAIN_D: FloatParameter(
        name=DriverParameter.PID_TARGET_GAIN_D, value=DEFAULT_PID_TARGET_GAIN_D, min=-100, max=100, step=0.01,
    ),
    #
    # Steering PID
    #
    DriverParameter.PID_STEERING_GAIN_P: FloatParameter(
        name=DriverParameter.PID_STEERING_GAIN_P, value=DEFAULT_PID_STEERING_GAIN_P, min=0, max=100, step=0.001,
    ),
    DriverParameter.PID_STEERING_GAIN_I: FloatParameter(
        name=DriverParameter.PID_STEERING_GAIN_I, value=DEFAULT_PID_STEERING_GAIN_I, min=0, max=100, step=0.001,
    ),
    DriverParameter.PID_STEERING_GAIN_D: FloatParameter(
        name=DriverParameter.PID_STEERING_GAIN_D, value=DEFAULT_PID_STEERING_GAIN_D, min=0, max=100, step=0.001,
    ),
    DriverParameter.PID_STEERING_DEADBAND_ANGLE: FloatParameter(
        name=DriverParameter.PID_STEERING_DEADBAND_ANGLE,
        value=DEFAULT_PID_STEERING_DEADBAND_ANGLE,
        min=0,
        max=100,
        step=0.001,
    ),
    #
    # Speed Limit
    #
    DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG: FloatParameter(
        name=DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG,
        value=DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_EFFECTIVE_ZERO_DEG,
        min=0,
        max=180,
        step=0.05,
    ),
    DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_SCALAR: FloatParameter(
        name=DriverParameter.SPEED_LIMIT_ANGULAR_ERROR_SCALAR,
        value=DEFAULT_SPEED_LIMIT_ANGULAR_ERROR_SCALAR,
        min=0,
        max=1,
        step=0.01,
    ),
    #
    # Turning
    #
    DriverParameter.TURN_HEADING_ALIGNED_DEG: FloatParameter(
        name=DriverParameter.TURN_HEADING_ALIGNED_DEG, value=DEFAULT_TURN_HEADING_ALIGNED_DEG, min=0, max=90, step=0.1,
    ),
}

DEFAULT_DRIVER_PARAMETERS_UNITY: DriverParameters = {}
for k in DriverParameter:
    p = DEFAULT_DRIVER_PARAMETERS.get(k, DEFAULT_DRIVER_PARAMETERS_UNITY.get(k))
    assert p is not None, f"Missing default parameter for: {k}"
    assert p.name == k
