from typing import Any, Dict, List, cast

from core.controls.driver.driver import Driver
from core.controls.driver.parameters import DriverParameter
from core.model.node.type import NodeType
from core.model.parameter import ParameterValueType
from generated.core.controls.driver.model.auto_drive_request import AutoDriveRequest
from generated.core.controls.driver.model.auto_drive_response import AutoDriveResponse
from generated.core.controls.driver.model.update_drive_controller_request import UpdateDriveControllerRequest
from generated.core.controls.driver.model.update_drive_controller_response import UpdateDriveControllerResponse
from lib.common.logging import get_logger
from lib.common.web.http.router import ComponentRouterBuilder, RouterBuilder
from lib.common.web.http.router_utils import merge_add, optional

LOG = get_logger(__name__)


class DriverRouterBuilder(ComponentRouterBuilder):
    def __init__(self, driver: Driver):
        super().__init__(base_route=f"/{NodeType.DRIVER.serialize()}")
        self._driver: Driver = driver

    def _handle_get_drive_controllers(self, bot: Any) -> List[str]:
        return [c for c in self._driver.controllers]

    def _handle_set_drive_controller(self, bot: Any, **kwargs: Any) -> Dict[str, Any]:
        request: UpdateDriveControllerRequest = UpdateDriveControllerRequest.from_json(kwargs)
        response: UpdateDriveControllerResponse = self._driver.handle_update_drive_controller_request(request)
        # TODO why does mypy need help below?
        return response.to_json()  # type: ignore

    def _handle_get_parameters(self, bot: Any) -> Dict[str, Dict[str, ParameterValueType]]:
        return self._driver.parameters_json

    def _handle_save_parameter(self, bot: Any, parameter: str) -> None:
        self._driver.save_parameter(parameter)

    def _handle_set_parameters(self, bot: Any, **param_to_values: float) -> None:
        assert len(param_to_values) > 0, "No parameters passed"
        self._driver.set_parameters(**{k: v for k, v in param_to_values.items() if v is not None})

    def _handle_plan_cancel(self, bot: Any) -> None:
        self._driver.cancel_plan()

    def _handle_plan_continue(self, bot: Any) -> None:
        self._driver.plan_continue_threadsafe()

    def _handle_plan_pause(self, bot: Any) -> None:
        self._driver.plan_pause_threadsafe()

    def _handle_plan_reset(self, bot: Any) -> None:
        self._driver.reset_plan()

    def _handle_autodrive(self, bot: Any, **kwargs: Any) -> Dict[str, Any]:
        dreq = AutoDriveRequest.from_json(kwargs)
        resp: AutoDriveResponse = cast(AutoDriveResponse, self._driver.handle_auto_drive_request(dreq))
        # TODO(coryg) figure out why reveal_type(resp) still thinks its Any
        return cast(Dict[str, Any], resp.to_json())

    def add_onto(self, bot_routes: RouterBuilder) -> None:
        bot_routes.add(
            "POST",
            "/maka/autodrive",
            body_desc={"datum": optional(float), "plan": str, "source": str, "timestamp_ms": int},
        )(merge_add(self._handle_autodrive))

        bot_routes.add("POST", self.suburl("/plan/cancel"))(merge_add(self._handle_plan_cancel))
        bot_routes.add("POST", self.suburl("/plan/continue"))(merge_add(self._handle_plan_continue))
        bot_routes.add("POST", self.suburl("/plan/pause"))(merge_add(self._handle_plan_pause))
        bot_routes.add("POST", self.suburl("/plan/reset"))(merge_add(self._handle_plan_reset))

        bot_routes.add("GET", self.suburl("/controllers"))(merge_add(self._handle_get_drive_controllers))

        # TODO remove injection of "bot" parameter which we don't need now
        bot_routes.add("POST", self.suburl("/controller"), body_desc={"timestamp_ms": int, "controller": str})(
            merge_add(self._handle_set_drive_controller)
        )

        bot_routes.add("GET", self.suburl("/parameters"))(merge_add(self._handle_get_parameters))
        params_body_desc = {str(k): optional(float) for k in DriverParameter}
        bot_routes.add("POST", self.suburl("/parameter"), body_desc=params_body_desc)(
            merge_add(self._handle_set_parameters)
        )
        bot_routes.add("POST", self.suburl("/parameter/save"), body_desc={"parameter": str})(
            merge_add(self._handle_save_parameter)
        )
