from typing import Any, Optional

import lib.common.logging
from core.controls.driver.parking_brake import ParkingBrakeNode
from core.controls.driver.rotary_encoder import RotaryEncoderNode
from core.controls.frame.sensor.fuel_sensor.fuel_sensor import FuelSensorNode
from lib.common.devices.boards.nofx.nofx_board_device import NoFXBoardDevice
from lib.common.devices.registry import DeviceRegistry

LOG = lib.common.logging.get_logger(__name__)


class NoFXRotaryEncoder(RotaryEncoderNode):
    def __init__(
        self,
        *args: Any,
        device_id: str,
        device_path: str,
        TPR: int,
        directional: Optional[bool] = None,
        device_registry: DeviceRegistry,
        **kwargs: Any,
    ):
        super().__init__(*args, device_path=device_path, **kwargs)

        self._device_registry = device_registry
        self._device_id = device_id
        self._position_id = device_path.instance_str
        self._ticks_per_revolution = TPR
        self._board = device_registry.get_device_from_sync(NoFXBoardDevice, device_id)
        if directional is None:
            self._directional = False
        else:
            self._directional = directional

    @property
    async def ticks(self) -> int:
        """
        Get the ticks for this rotary encoder position - needs to be called from a something
        on an async loop.
        """
        return await self._board.ticks_for_rotary(self._position_id)

    @property
    def timestamp_ms(self) -> int:
        return self._board.timestamp_ms()

    @property
    def TPR(self) -> int:
        return self._ticks_per_revolution

    @property
    def directional(self) -> bool:
        return self._directional


class NoFXParkingBrake(ParkingBrakeNode):
    def __init__(self, *args: Any, device_id: str, device_path: str, device_registry: DeviceRegistry, **kwargs: Any):
        super().__init__(*args, device_path=device_path, **kwargs)

        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(NoFXBoardDevice, device_id)

    async def on(self) -> None:
        await self._board.park_brake_on()

    async def off(self) -> None:
        await self._board.park_brake_off()

    async def query(self) -> bool:
        return await self._board.park_brake_query()


class NoFXFuelSensor(FuelSensorNode):
    def __init__(
        self,
        *args: Any,
        device_id: str,
        device_path: str,
        device_registry: DeviceRegistry,
        min_lvl: float,
        max_lvl: float,
        **kwargs: Any,
    ):
        super().__init__(*args, device_path=device_path, **kwargs)

        self._min_lvl = min_lvl
        self._max_lvl = max_lvl
        self._device_registry = device_registry
        self._device_id = device_id
        self._board = device_registry.get_device_from_sync(NoFXBoardDevice, device_id)

    @property
    async def level(self) -> float:
        level = await self._board.fuel_lvl()
        # depending on settings, these values could scale from min to max, OR from max to min. We're converting
        # from voltage level via the opamp to the ADC directly.
        if self._min_lvl < self._max_lvl:
            if level < self._min_lvl:
                level = 0
            elif level > self._max_lvl:
                level = 100
            else:
                level = 100 * ((level - self._min_lvl) / (self._max_lvl - self._min_lvl))
        else:
            if level < self._max_lvl:
                level = 100
            elif level > self._min_lvl:
                level = 0
            else:
                level = 100 * ((self._min_lvl - level) / (self._min_lvl - self._max_lvl))
        return level
