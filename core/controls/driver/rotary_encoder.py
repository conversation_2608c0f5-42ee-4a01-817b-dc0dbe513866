import abc
from typing import Any, Dict

from core.model.sensor import Sensor
from lib.common.time import maka_control_timestamp_ms


class RotaryEncoder(abc.ABC):
    def status_callback(self) -> Dict[str, Any]:
        return {"ticks": self.ticks}

    @property
    @abc.abstractmethod
    async def ticks(self) -> int:
        pass

    @property
    def timestamp_ms(self) -> int:
        return maka_control_timestamp_ms()

    @property
    @abc.abstractmethod
    def TPR(self) -> int:
        pass

    @property
    def directional(self) -> bool:
        return False


class RotaryEncoderNode(Sensor, RotaryEncoder):
    pass
