import abc
from typing import Any, cast

import numpy as np

from core.model.actuator import Actuator


class DriveMotor(Actuator, abc.ABC):
    def __init__(self, *argv: Any, min_speed: float = 0, max_speed: float = 1, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        assert 0 <= min_speed < 1
        assert 0 < max_speed <= 1
        assert min_speed < max_speed
        self._min_speed: float = min_speed
        self._max_speed: float = max_speed

    @property
    @abc.abstractmethod
    def speed_mph(self) -> float:
        pass

    @abc.abstractmethod
    def set_speed_mph(self, speed_mph: float) -> None:
        pass

    @property
    def forward(self) -> float:
        """
        Normalized value mapping

        [-max, -min] U [min, max] --> [-1, 1]
        """
        return (self.speed_mph - cast(float, np.sign(self.speed_mph)) * self._min_speed) / self._max_speed

    @property
    def min_speed(self) -> float:
        """
        The unsigned value the motor should be set at in order to drive at the minimum speed

        Likely 0 (haven't encountered a nonzero case yet - usually we would solve friction issues in the PID/software)
        """
        return self._min_speed

    @property
    def max_speed(self) -> float:
        """
        The unsigned value the motor should be set at in order to drive at the maximum speed

        Likely 1.0, or less when we have too much power (e.g. electric drive motors on red robot)
        """
        return self._max_speed
