import asyncio
import threading
from collections import deque
from typing import Any, Callable, Deque, Dict, List, Optional, cast

from core.controls.commander.notification import NOTIFY_DRIVE_PLAN_PAUSE, NOTIFY_DRIVE_PLAN_RESUME
from core.controls.driver.annotate import DriverAnnotator
from core.controls.driver.config import DriverConfigFile
from core.controls.driver.controller.controller import Drive<PERSON>ontroller, DriveControllerTick
from core.controls.driver.controller.hydraulics import FRONT_WHEEL_STEER, FrontWheelSteerHydraulicsDriveController
from core.controls.driver.drive_system import DriveSystem
from core.controls.driver.gear import Gear
from core.controls.driver.limiter.speed_limiter import AutoSpeedLimiter
from core.controls.driver.localization.geofence import (
    GeoFenceMatch,
    GeoFenceProducer,
    RobotBodyGeo,
    RobotBodyGeoProducer,
)
from core.controls.driver.location_state import DriverLocationState, DriverLocationStateChannel
from core.controls.driver.parameters import DriverParameter, DriverParameters
from core.controls.driver.parking_brake import Parking<PERSON>rake, ParkingBrakeRequest
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.instruction import (
    AlignStraightInstruction,
    DriveAngleInstruction,
    DriveInstruction,
    DriverInstruction,
    NoOpDriverInstruction,
    NotifyCommanderInstruction,
    PauseDriverInstruction,
    SetGearInstruction,
    StopInstruction,
    TargetAngleInstruction,
    TerminateDriverInstruction,
    UpdateSpeedLimitPctInstruction,
    UpdateTargetSpeedInstruction,
)
from core.controls.driver.plan.plan import DrivePlan
from core.controls.driver.plan.world_hud import WorldHUD
from core.controls.frame.frame import Frame
from core.controls.frame.pose.body import RobotBody
from core.controls.plan.state.info import PlanInfo
from core.cv.retina.camera.node import Cam
from core.cv.retina.retina import Retina
from core.cv.visual_cortex.visual_cortex import VisualCortex
from core.drivers.hydraulics.veggiedrive import Veggiedrive
from core.model.actuator import Actuator
from core.model.configuration import ServiceConfiguration
from core.model.node.type import NodeType
from core.model.parameter import ParameterValueType
from core.web.websocket.path.drive import DriveWebSocket
from generated.core.controls.driver.model.auto_drive_request import AutoDriveRequest
from generated.core.controls.driver.model.auto_drive_response import AutoDriveResponse
from generated.core.controls.driver.model.manual_drive_request import ManualDriveRequest
from generated.core.controls.driver.model.manual_drive_response import ManualDriveResponse
from generated.core.controls.driver.model.update_drive_controller_request import UpdateDriveControllerRequest
from generated.core.controls.driver.model.update_drive_controller_response import UpdateDriveControllerResponse
from generated.core.controls.driver.model.update_speed_limit_pct_request import UpdateSpeedLimitPctRequest
from generated.core.controls.driver.model.update_speed_limit_pct_response import UpdateSpeedLimitPctResponse
from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
from lib.common.angle_direction import AngleDirection
from lib.common.concurrent import LockedObject
from lib.common.geo.boundary import Boundaries, BoundaryType
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.channel.callback import CallbackSubscriberChannel
from lib.common.tasks.manager import get_event_loop_by_name
from lib.common.tasks.scheduler import dispatch
from lib.common.time.time import maka_control_timestamp_ms
from lib.drivers.spencer_fluids.messages.ecu import ECUFeedback

LOG = get_logger(__name__)

# Generic Values
DRIVE_POLL_MS = 250

# It's not mypy possible to specify first arg is Driver, then *args, then **kwargs
AutoDriveType = Callable[..., Any]

F = Callable[..., Any]


def _no_auto_protect(func: F) -> F:
    """
    Decorator to short-circuit a autonomous driving function if it is accidentally invoked
    when safety parameter --no-auto-drive is set
    """

    def check(d: "Driver", *argv: Any, **kwargs: Any) -> Any:
        if d.no_auto_drive:
            LOG.error(f"no auto drive, ignoring {func}")
            return
        return func(d, *argv, **kwargs)

    return check


class DriverConfiguration(ServiceConfiguration[DriverConfigFile, DriverParameters]):
    pass


class Driver(Actuator):
    """
    Driver object handles control of the drive motors and watches the drive cameras.
    """

    def __init__(
        self,
        *argv: Any,
        boundaries: Boundaries,
        config: DriverConfiguration,
        disable_auto_drive: bool = False,
        no_gps: bool = False,
        field_location_notifier: Optional[FieldLocationNotifier] = None,
        world_hud: Optional[WorldHUD] = None,
        **kwargs: Any,
    ):
        super().__init__(*argv, **kwargs)

        self.no_auto_drive: bool = disable_auto_drive
        self._no_gps = no_gps
        self._config = config

        # Commander Notification
        self.notify_commander_callback: Optional[Callable[[str], None]] = None

        # Plan
        self._controllers: Dict[str, DriveController] = self._create_controllers()
        self._controller: DriveController = self._controllers[FRONT_WHEEL_STEER]
        self.feed.publish_callback(topic=self.topic.sub("controller_tick"), callback=lambda: self.controller.last_tick)

        self._boundaries: Boundaries = boundaries
        self._publish_body_geo_subscription()
        self._publish_geofence_subscription(field_location_notifier)
        self._publish_location_state_subscription()
        location_state = self.feed.subscribe(topic=Topic("/driver/location_state"), msg_type=DriverLocationState).read()
        assert location_state is not None
        self._location_state: DriverLocationState = location_state

        # Fuel Level
        self._fuel_level_subscription = self.feed.subscribe_if_published(topic=Topic("/frame/fuel_lvl"), msg_type=float)

        # scoot_complete stream
        self._plan: DrivePlan = DrivePlan(
            topic=self.topic,
            feed=self.feed,
            autodrive_parameters=self.config.parameters,
            cancel_callback=self.cancel_plan,
            no_gps=self._no_gps,
            field_location_notifier=field_location_notifier,
            world_hud=world_hud,
        )
        self._plan_instruction_lock = threading.Lock()
        self._last_instructions: Deque[DriverInstruction] = deque(maxlen=5)

        # Tell the visual cortex where the wheels are
        #    foreach drive cam ...

        # Driver will need to eventually set this to something real
        if self._visual_cortex is not None:
            self._visual_cortex.set_front_right_wheel_image_location(0, 0)

        self._plan_info: Optional[PlanInfo] = None
        self.feed.publish_callback(topic=self.topic.sub("/plan_info"), callback=lambda: self.plan_info)

        # Threaded worker
        self._outstanding_manual_drive_request: LockedObject[ManualDriveRequest] = LockedObject()
        self._outstanding_parking_brake_request: LockedObject[ParkingBrakeRequest] = LockedObject()
        # Set initial request to turning the brake off. If somebody else decides it needs to be on before tick then
        # no problem.
        self._outstanding_parking_brake_request.set(ParkingBrakeRequest.OFF)
        self._last_manual_dreq: Optional[ManualDriveRequest] = None

        self._websocket: Optional[DriveWebSocket] = None

        # Attach annotations
        self._annotator: DriverAnnotator = DriverAnnotator(self.feed, self)
        self._annotator.attach()

    ###########################################################################
    # make channels
    ###########################################################################

    def _publish_geofence_subscription(self, field_location_notifier: Optional[FieldLocationNotifier] = None) -> None:
        topic = self.topic.sub("geofence")

        body_subscription = self.feed.subscribe_if_published(topic=Topic("/frame/body"), msg_type=RobotBody)

        geofence_subscription: SubscriberChannel[GeoFenceMatch]
        if body_subscription is not None:
            geofence_subscription = GeoFenceProducer(
                topic=topic,
                body_subscription=body_subscription,
                boundaries=self._boundaries,
                field_location_notifier=field_location_notifier,
            )
        else:
            geofence_subscription = CallbackSubscriberChannel(topic=topic, callback=lambda: GeoFenceMatch([]))

        self.feed.publish(topic=geofence_subscription.topic, subscription=geofence_subscription)

    def _publish_body_geo_subscription(self) -> None:
        topic = self.topic.sub("body_geo")

        body_subscription = self.feed.subscribe_if_published(topic=Topic("/frame/body"), msg_type=RobotBody)
        body_geo_subscription: SubscriberChannel[RobotBodyGeo]
        if body_subscription is not None:
            body_geo_subscription = RobotBodyGeoProducer(
                topic=topic, body_subscription=body_subscription, boundaries=self._boundaries
            )
        else:
            body_geo_subscription = CallbackSubscriberChannel(
                topic=topic, callback=lambda: cast(Optional[RobotBodyGeo], None)
            )

        self.feed.publish(topic=body_geo_subscription.topic, subscription=body_geo_subscription)

    def _publish_location_state_subscription(self) -> None:
        topic = self.topic.sub("location_state")

        geoposition_lla_subscription = self.feed.subscribe_if_published(
            topic=Topic("/frame/geoposition_lla"), msg_type=GeopositionLatLonAltMessage
        )

        location_state_channel: SubscriberChannel[DriverLocationState]
        if geoposition_lla_subscription is not None:
            location_state_channel = DriverLocationStateChannel(
                topic=topic,
                geoposition_lla_subscription=geoposition_lla_subscription,
                body_geo_subscription=self.feed.subscribe(topic=Topic("/driver/body_geo"), msg_type=RobotBodyGeo),
                no_gps=self._no_gps,
            )
        else:
            location_state_channel = CallbackSubscriberChannel(
                topic=topic, callback=lambda: DriverLocationState.LOST_GPS
            )

        self.feed.publish(topic=location_state_channel.topic, subscription=location_state_channel)

    ###########################################################################
    # Properties
    ###########################################################################

    @property
    def config(self) -> DriverConfiguration:
        return self._config

    @property
    def boundaries(self) -> Boundaries:
        return self._boundaries

    @property
    def controller(self) -> DriveController:
        return self._controller

    @property
    def controllers(self) -> Dict[str, DriveController]:
        return self._controllers

    @property
    def drive_system(self) -> DriveSystem:
        return self._drive_system

    @property
    def back_left_camera(self) -> Optional[Cam]:
        return self._cam_back_left

    @property
    def back_right_camera(self) -> Optional[Cam]:
        return self._cam_back_right

    @property
    def front_left_camera(self) -> Optional[Cam]:
        return self._cam_front_left

    @property
    def front_right_camera(self) -> Optional[Cam]:
        return self._cam_front_right

    @property
    def plan(self) -> DrivePlan:
        return self._plan

    @property
    def plan_info(self) -> Optional[PlanInfo]:
        return self._plan_info

    @property
    def last_instructions(self) -> Deque[DriverInstruction]:
        return self._last_instructions

    ###########################################################################
    # INIT helper functions
    ###########################################################################

    def _cache_named_references(self) -> None:
        # Drive System
        self._drive_system: DriveSystem = cast(DriveSystem, self.get_node(NodeType.DRIVE_SYSTEM, required=True))

        # Frame
        self._frame: Optional[Frame] = cast(Optional[Frame], self.get_node(NodeType.FRAME))
        if self._frame is None:
            LOG.warning(f"{self.device_path} Initializing with no frame!")

        # Visual Cortex
        self._visual_cortex: Optional[VisualCortex] = cast(
            Optional[VisualCortex], self.get_node(NodeType.VISUAL_CORTEX)
        )

        if self._visual_cortex is None:
            LOG.warning(f"{self.device_path} Initializing with no visual cortex!")

        # Cameras
        retina: Optional[Retina] = cast(Optional[Retina], self.get_node(NodeType.RETINA))
        self._cam_front_left: Optional[Cam] = retina.front_left_cam if retina is not None else None
        if self._cam_front_left is None:
            LOG.warning(f"{self.device_path} Initializing with no front left drive cam!")

        self._cam_front_right: Optional[Cam] = retina.front_right_cam if retina is not None else None
        if self._cam_front_right is None:
            LOG.warning(f"{self.device_path} Initializing with no front right drive cam!")

        self._cam_back_left: Optional[Cam] = retina.back_left_cam if retina is not None else None
        if self._cam_back_left is None:
            LOG.warning(f"{self.device_path} Initializing with no back left drive cam!")

        self._cam_back_right: Optional[Cam] = retina.back_right_cam if retina is not None else None
        if self._cam_back_right is None:
            LOG.warning(f"{self.device_path} Initializing with no back right drive cam!")

        # Parking Brake
        self._parking_brake: Optional[ParkingBrake] = cast(
            Optional[ParkingBrake], self.get_node(NodeType.PARKING_BRAKE)
        )

    def _create_controllers(self, gear: Gear = Gear.NEUTRAL) -> Dict[str, DriveController]:
        controllers: Dict[str, DriveController] = {}

        speed_limiter = AutoSpeedLimiter()

        if self.drive_system.hydraulics is not None and self._frame is not None:
            controllers[FRONT_WHEEL_STEER] = FrontWheelSteerHydraulicsDriveController(
                name=FRONT_WHEEL_STEER,
                hydraulics=self.drive_system.hydraulics,
                gear=gear,
                speed_limiter=speed_limiter,
                autodrive_params=self.config.parameters,
                mechanical_config=self._frame.config.mechanical,
            )

        assert len(controllers) > 0, "No drive controllers work???"

        return controllers

    ###########################################################################
    # UI Helper functions - looking to improve with better discovery in UI
    ###########################################################################

    @property
    def gear(self) -> Gear:
        return self.controller.gear

    @property
    def location_state(self) -> DriverLocationState:
        # TODO compute, store this in driver directly - Driver should make plan changes not the plan
        return self._location_state

    @property
    def json_geofences(self) -> Dict[str, Any]:
        return {
            "geofences": self.boundaries.to_json()["boundaries"],
            "types": [t for t in sorted(BoundaryType)],
            "buffer": self.json_geofence_point_buffer,
        }

    @property
    def json_geofence_point_buffer(self) -> Dict[str, Any]:
        return {
            "points": self.boundaries.point_buffer,
            "num_buildable_points": len(self.boundaries.buildable_point_buffer),
        }

    @property
    def parameters(self) -> DriverParameters:
        return self.config.parameters

    @property
    def parameters_json(self) -> Dict[str, Dict[str, ParameterValueType]]:
        return {k: v.to_json() for k, v in self.parameters.items()}

    def dump_callback(self) -> Dict[str, Any]:
        return {
            "geofences": self.json_geofences,
            "parameters": self.parameters_json,
            "plans": self.plan.available_plans,
            "status": self.status_callback(),
        }

    # TODO generate status type from protobuf
    def status_callback(self) -> Dict[str, Any]:
        # TODO this string lookup will break
        engine_percent_torque = 0
        ecu_feedback_channel = self.feed.subscribe_if_published(
            Topic("/driver/drive_system/hydraulics/e_c_u_feedback"), ECUFeedback
        )
        if ecu_feedback_channel is not None:
            ecu_feedback: Optional[ECUFeedback] = ecu_feedback_channel.read()
            if ecu_feedback is not None:
                engine_percent_torque = ecu_feedback.engine_percent_torque
        result = {
            "controller": self.controller.name,
            "pc_control": self.controller.pc_control,
            "engine_percent_torque": engine_percent_torque,
            "gear": self.gear,
            "plan_execution_state": self.plan.execution_state,
            "plan_program_type": self.plan.program_name,
            "location_state": self.location_state,
            "speed_limit_pct": self.controller.speed_limiter.limit_pct,
            "target_speed_mph": self.config.parameters[DriverParameter.TARGET_SPEED_MPH].float_value,
            "fuel_lvl": self._fuel_level_subscription.read() if self._fuel_level_subscription is not None else 0,
        }
        return result

    def get_cameras_for_ui(self) -> List[str]:
        result: List[str] = []
        if self._cam_front_right is not None:
            result.append(self._cam_front_right.id)
        return result

    def save_parameter(self, parameter: str) -> None:
        self.config.save_parameter(parameter)

    def set_parameters(self, **kwargs: float) -> None:
        assert len(kwargs) > 0, "No parameters passed"
        for k, v in kwargs.items():
            if k not in self.parameters:
                LOG.warning(f"Attempt to set unknown parameter: {k} -> {v}")
            else:
                previous_value = self.parameters[k].value
                LOG.info(f"Update: {k}: {previous_value} -> {v}")
                self.parameters[k].set_value(v)

                if self.drive_system.hydraulics is not None and isinstance(self.drive_system.hydraulics, Veggiedrive):
                    pass
                    # TODO add pid setting to Drive bot
                    # if DriverParameter.PID_STEERING_GAIN_P in k:
                    #     self.drive_system.hydraulics._controller.set_steering_pid_p(v)
                    # elif DriverParameter.PID_STEERING_GAIN_I in k:
                    #     self.drive_system.hydraulics._controller.set_steering_pid_i(v)
                    # elif DriverParameter.PID_STEERING_GAIN_D in k:
                    #     self.drive_system.hydraulics._controller.set_steering_pid_d(v)
                    # elif DriverParameter.PID_STEERING_DEADBAND_ANGLE in k:
                    #     self.drive_system.hydraulics._controller.set_steering_deadband_angle(v)

    ###########################################################################
    # Core API
    # TODO we can look to generate much of this boilerplate and build abstractions
    #  - separate routing
    #  - separate handling
    #  - piecewise mocking
    ###########################################################################

    def handle_manual_drive_request(self, manual_drive_request: ManualDriveRequest) -> ManualDriveResponse:
        """
        Live joystick updates from the user happen immediately cancel existing plan
        """
        positive_control = False
        is_new_manual_dreq = (
            self._last_manual_dreq is None or manual_drive_request.timestamp_ms > self._last_manual_dreq.timestamp_ms
        )
        if is_new_manual_dreq:
            if self.plan.active:
                self.plan.cancel_plan()
            self._outstanding_manual_drive_request.set(manual_drive_request)
            positive_control = True
        else:
            LOG.warning(
                f"Ignoring {manual_drive_request} due since it is older than previously processed drive request: "
                f"{self._last_manual_dreq.timestamp_ms if self._last_manual_dreq is not None else ''}"
            )
        return ManualDriveResponse(positive_control=positive_control)

    @_no_auto_protect
    def handle_auto_drive_request(self, auto_drive_request: AutoDriveRequest) -> AutoDriveResponse:
        now = maka_control_timestamp_ms()
        self.plan.handle_auto_drive_request(auto_drive_request)
        return AutoDriveResponse(timestamp_ms=now)

    def reset_plan(self) -> None:
        self.cancel_plan()
        self.plan.reset()

    def cancel_plan(self, reason: str = "plan cancelled") -> None:
        self.plan.driver_cancel_plan(reason=reason)
        self.controller.set_gear(Gear.NEUTRAL)
        self.controller.stop_vehicle()
        LOG.debug("Park Brake State: cancel plan requesting to set OFF")
        self._outstanding_parking_brake_request.set(ParkingBrakeRequest.OFF)
        self._plan_info = None
        LOG.debug("Cancel Plan")

    def handle_update_drive_controller_request(
        self, update_drive_controller_request: UpdateDriveControllerRequest
    ) -> UpdateDriveControllerResponse:
        now = maka_control_timestamp_ms()

        controller: DriveController = self.controllers[update_drive_controller_request.controller]
        if self.controller != controller:
            LOG.info(f"{self.device_path} Activating drive controller {controller.name}")
            self.controller.align_straight()
            self.controller.stop_vehicle()
            self._controller = controller
        else:
            LOG.info(f"{self.device_path} Drive controller {controller.name} is already active.")

        return UpdateDriveControllerResponse(timestamp_ms=now)

    @_no_auto_protect
    def handle_update_speed_limit_pct_request(
        self, update_speed_limiter_request: UpdateSpeedLimitPctRequest
    ) -> UpdateSpeedLimitPctResponse:
        now = maka_control_timestamp_ms()
        before = self.controller.speed_limiter.limit_pct
        new = round(update_speed_limiter_request.speed_limit_pct, 2)
        self.controller.speed_limiter.set_limit_pct(new)
        LOG.info(f"Updated speed limit from {before} to {self.controller.speed_limiter.limit_pct}")
        return UpdateSpeedLimitPctResponse(timestamp_ms=now)

    def handle_interrupts(self) -> bool:
        positive_control = False

        # Drive request
        manual_dreq: Optional[ManualDriveRequest] = self._outstanding_manual_drive_request.pop()
        if manual_dreq is not None:
            # require in-order Drive requests
            self.controller.handle_manual_drive_request(manual_dreq)
            positive_control = True
            self._last_manual_dreq = manual_dreq
        return positive_control

    def handle_plan_drive_instruction(self, drive_instruction: DriverInstruction) -> None:
        """
        Handle messages from the planner - protect against race condition with commander asking us to pause.
        """
        with self._plan_instruction_lock:
            # TODO turn this into a proper generated message (requires modeling OneOf / Union concept)
            # we can use request routers, etc. Lots to improve as we build this out

            if isinstance(drive_instruction, AlignStraightInstruction):
                self.controller.align_straight()

            elif isinstance(drive_instruction, DriveAngleInstruction):
                self.controller.drive_angle(
                    error_angle_rad=drive_instruction.error_angle_rad,
                    direction=AngleDirection.DIRECT,
                    error_velocity_mph=drive_instruction.error_velocity_mph,
                    actual_velocity_mph=drive_instruction.actual_velocity_mph,
                )

            elif isinstance(drive_instruction, DriveInstruction):
                self.controller.drive(
                    forward=drive_instruction.forward,
                    steer=drive_instruction.steer,
                    actual_velocity_mph=drive_instruction.actual_velocity_mph,
                )

            elif isinstance(drive_instruction, NoOpDriverInstruction):
                LOG.debug("No op")

            elif isinstance(drive_instruction, SetGearInstruction):
                self.controller.set_gear(drive_instruction.gear)

            elif isinstance(drive_instruction, StopInstruction):
                self.controller.stop_vehicle()

            elif isinstance(drive_instruction, TargetAngleInstruction):
                self.controller.target_angle(
                    error_angle_rad=drive_instruction.error_angle_rad,
                    direction=drive_instruction.direction,
                    error_velocity_mph=drive_instruction.error_velocity_mph,
                    actual_velocity_mph=drive_instruction.actual_velocity_mph,
                )

            elif isinstance(drive_instruction, TerminateDriverInstruction):
                self.cancel_plan()

            elif isinstance(drive_instruction, PauseDriverInstruction):
                self.plan_pause()

            elif isinstance(drive_instruction, NotifyCommanderInstruction):
                self.notify_commander(drive_instruction.topic)

            elif isinstance(drive_instruction, UpdateSpeedLimitPctInstruction):
                self.controller.speed_limiter.set_limit_pct(drive_instruction.pct)

            elif isinstance(drive_instruction, UpdateTargetSpeedInstruction):
                self.parameters[DriverParameter.TARGET_SPEED_MPH].set_value(drive_instruction.target_speed_mph)

            else:
                assert False, f"Unexpected instruction: {drive_instruction}"

            self._last_instructions.appendleft(drive_instruction)

    # for UI
    def plan_continue_threadsafe(self) -> None:
        self.plan_continue()

    # for UI
    def plan_pause_threadsafe(self) -> None:
        self.plan_pause()

    def notify_commander(self, topic: str) -> None:
        if self.notify_commander_callback is not None:
            self.notify_commander_callback(topic)

    def plan_pause(self) -> None:
        """
        Commander may ask us to pause planner - do this locked.
        """
        self._plan.pause()

        self.controller.stop_vehicle()
        LOG.info("Park Brake State: pause plan requesting to set ON")
        self._outstanding_parking_brake_request.set(ParkingBrakeRequest.ON)

        self.notify_commander(NOTIFY_DRIVE_PLAN_PAUSE)
        LOG.info("Plan paused")

    def plan_continue(self) -> None:
        """
        Commander may ask us to continue planner - do this locked.
        """
        self.notify_commander(NOTIFY_DRIVE_PLAN_RESUME)
        self._plan.unpause()

        LOG.info("Park Brake State: plan continue requesting to set OFF")
        self._outstanding_parking_brake_request.set(ParkingBrakeRequest.OFF)

        LOG.info("Plan continued")

    async def query_parking_brake(self) -> bool:
        if self._parking_brake is None:
            return False

        future = asyncio.run_coroutine_threadsafe(self._parking_brake.query(), get_event_loop_by_name())
        return await asyncio.wrap_future(future)

    ###########################################################################
    # Publishing
    ###########################################################################

    def publish(self, plan_info: Optional[PlanInfo], drive_controller_tick: Optional[DriveControllerTick]) -> None:
        self._plan_info = plan_info

    ###########################################################################
    # Execution Tasks below - should grow smaller over time as we build abstractions
    ###########################################################################

    def setup_async(self, loop: asyncio.BaseEventLoop) -> None:
        assert self._websocket is None
        self._websocket = DriveWebSocket(manual_drive_callback=self.handle_manual_drive_request)
        self._websocket.setup_async(loop)

    async def tick(self) -> None:
        """
        Tick the Driver once
        """
        try:
            positive_control = self.handle_interrupts()
            if not positive_control:
                if self.plan.active and not self.controller.pc_control:
                    self.cancel_plan(reason="PC not in control")
                    LOG.warning("Canceled plan due to pc_control: false")

                # Tick location check (safety)
                location_state = self.feed.subscribe(
                    topic=Topic("/driver/location_state"), msg_type=DriverLocationState
                ).read()
                assert location_state is not None
                self._location_state = location_state

                # Timed tick for plan
                plan_info, drive_instruction = await self.plan.handle_tick(self._location_state)
                drive_controller_tick: Optional[DriveControllerTick] = None
                if drive_instruction is not None:
                    self.handle_plan_drive_instruction(drive_instruction)
                    drive_controller_tick = self.controller.last_tick

                # Timed tick for controls
                brake_request: Optional[ParkingBrakeRequest] = self._outstanding_parking_brake_request.pop()
                if brake_request is not None and self._parking_brake is not None:
                    if brake_request == ParkingBrakeRequest.OFF:
                        LOG.debug("Park Brake State: driver tick reads OFF request, turning off")
                        future = asyncio.run_coroutine_threadsafe(self._parking_brake.off(), get_event_loop_by_name())
                    else:
                        LOG.debug("Park Brake State: driver tick reads ON request, turning on")
                        future = asyncio.run_coroutine_threadsafe(self._parking_brake.on(), get_event_loop_by_name())
                    await asyncio.wrap_future(future)

                # Publish plan info
                if plan_info is not None:
                    self.publish(plan_info=plan_info, drive_controller_tick=drive_controller_tick)

        except Exception as e:
            self.cancel_plan()
            self._drive_system.stop_motors(log_prefix=f"[e: {e}] ")
            LOG.exception(f"{self.device_path} Unexpected exception")

    def stop(self) -> None:
        self.cancel_plan()
        super().stop()

    def _control_loop(self) -> None:
        """
        Main driver loop - never terminates, operates on incoming images.
        """
        LOG.debug(f"{self.device_path} Starting Driver Thread...")
        asyncio.run_coroutine_threadsafe(
            dispatch(name=str(self.device_path), callback=self.tick, poll_ms=DRIVE_POLL_MS), get_event_loop_by_name()
        ).result()
        assert False, "Drive Loop Should Not Exit"
