from abc import ABC, abstractmethod
from typing import Any


class SpeedLimiter(ABC):
    """
    A speed limiter regulates the speed of the Driver.

    https://en.wikipedia.org/wiki/Governor_(device)
        A governor, or speed limiter or controller, is a device used to measure
        and regulate the speed of a machine, such as an engine.

        A classic example is the centrifugal governor, also known as the Watt
        or fly-ball governor on a reciprocating steam engine, which uses the
        effect of inertial force on rotating weights driven by the machine
        output shaft to regulate its speed by altering the input flow of steam.
    """

    def __init__(self, initial_value: float = 1) -> None:
        self._validate_speed_limit(initial_value)
        self._pct: float = initial_value

    @property
    def limit_pct(self) -> float:
        return self._pct

    def set_limit_pct(self, pct: float) -> None:
        assert 0 <= pct <= 1
        self._pct = pct

    def __call__(self, *args: Any, **kwargs: Any) -> float:
        """
        Output the speed limiter value.

        [0, 1] inclusive
        """
        # do validation in base class
        value = self._evaluate()
        SpeedLimiter._validate_speed_limit(value)
        return value

    @staticmethod
    def _validate_speed_limit(value: float) -> None:
        # Fail fast on bug - likely a UI bug
        assert 0 <= value <= 1, f"Invalid speed limit value: {value}"

    @abstractmethod
    def _evaluate(self) -> float:
        pass


class AutoSpeedLimiter(SpeedLimiter):
    """
    A simple base implementation to build out the concept of governing speed.

    Initial implementation is a thin facade for a human-set value from the UI.

    These could become very smart if we choose in the future. A governor in
    a mechanical system doesn't need to be told what to do. It just does it.
    """

    def __init__(self, value: float = 1):
        super().__init__()
        SpeedLimiter._validate_speed_limit(value)
        self._value = value

    def update_value(self, value: float) -> None:
        SpeedLimiter._validate_speed_limit(value)
        self._value = value

    def _evaluate(self) -> float:
        return self._value * self._pct
