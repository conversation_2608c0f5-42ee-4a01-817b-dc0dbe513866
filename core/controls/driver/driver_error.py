from abc import abstractmethod
from typing import Any, Callable, Dict, Optional

from attr import dataclass

from core.controls.driver.parameters import Driver<PERSON>arameter, DriverParameters
from core.exceptions import RestartProcessException
from core.model.topics import Topics
from generated.core.controls.frame.model.velocity_message import VelocityMessage
from lib.common.logging import get_logger
from lib.common.protocol.channel.base import SubscriberChannel, Topic
from lib.common.protocol.feed.base import Feed
from lib.common.serialization.json import JsonSerializable
from lib.common.time.time import Timestamp, maka_control_timestamp_ms

LOG = get_logger(__name__)


class DriverError(JsonSerializable, Timestamp):
    def __init__(
        self,
        *,
        timestamp_ms: int,
        error_angle_rad: float,
        error_x_pct: float,
        actual_velocity_mph: float,
        error_velocity_mph: float,
    ):
        # timestamp_ms
        self._timestamp_ms = timestamp_ms

        # error_angle_rad
        self._error_angle_rad: float = error_angle_rad

        # error_x_pct
        assert -0.5 <= error_x_pct <= 0.5, f"Invalid error_x_pct: {error_x_pct}"
        self._error_x_pct: float = error_x_pct

        # error_velocity_mph
        self._error_velocity_mph: float = error_velocity_mph

        # actual_velocity_mph
        self._actual_velocity_mph: float = actual_velocity_mph

    @property
    def timestamp_ms(self) -> int:
        return self._timestamp_ms

    @property
    def error_angle_rad(self) -> float:
        return self._error_angle_rad

    @property
    def error_x_pct(self) -> float:
        return self._error_x_pct

    @property
    def error_velocity_mph(self) -> float:
        return self._error_velocity_mph

    @property
    def actual_velocity_mph(self) -> float:
        return self._actual_velocity_mph

    def to_json(self) -> Dict[str, Any]:
        return {
            "timestamp_ms": self.timestamp_ms,
            "error_angle_rad": self.error_angle_rad,
            "error_x_pct": self.error_x_pct,
            "error_velocity_mph": self.error_velocity_mph,
            "actual_velocity_mph": self.actual_velocity_mph,
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "DriverError":
        return DriverError(
            timestamp_ms=data["timestamp_ms"],
            error_angle_rad=data["error_angle_rad"],
            error_x_pct=data["error_x_pct"],
            error_velocity_mph=data["error_velocity_mph"],
            actual_velocity_mph=data["actual_velocity_mph"],
        )


class DriverErrorChannel(SubscriberChannel[DriverError]):
    @abstractmethod
    def status(self) -> str:
        pass


TOO_OLD_MS = 500
# Shooting While Moving frequently see this tick up to 20-30
TOO_MANY_CONSECUTIVE_NO_NEW_VELOCITY_LOG = 50
TOO_MANY_CONSECUTIVE_NO_NEW_VELOCITY_HARD_FAIL = 20000  # Hubbling can block us for a long time.


@dataclass
class VelocityError:
    timestamp_ms: int
    actual_velocity_mph: float
    error_velocity_mph: float


# :( still chasing this nasty nasty bug
HARD_FAILURE_MESSAGE = "Velocity Estimator Died. Please restart the controls process."
_velocity_estimator_dead: bool = False


class ErrorVelocityChannel(SubscriberChannel[VelocityError]):
    def __init__(
        self,
        topic: Topic,
        velocity_mph_subscription: SubscriberChannel[VelocityMessage],
        target_speed_mph: Callable[[], float],
    ):
        super().__init__(topic=topic)
        self._target_speed_mph: Callable[[], float] = target_speed_mph
        self._velocity_mph_subscription = velocity_mph_subscription

        # can't use maka_control_timestamp_ms() because could come from firmware board with out of sync time
        self._previous_timestamp_ms: Optional[int] = None
        self._consecutive_no_new_velocity_mph: int = 0

    def read(self) -> Optional[VelocityError]:

        target_velocity_mph: float = self._target_speed_mph()
        actual_velocity_mph: Optional[VelocityMessage] = self._velocity_mph_subscription.read()
        assert actual_velocity_mph is not None

        # I don't think this can trigger here because the plan will exit already, but being extra cautious
        global _velocity_estimator_dead
        if _velocity_estimator_dead:
            raise RestartProcessException(HARD_FAILURE_MESSAGE)

        error_velocity_mph = actual_velocity_mph.y - target_velocity_mph
        self._previous_timestamp_ms = actual_velocity_mph.timestamp_ms

        return VelocityError(
            timestamp_ms=maka_control_timestamp_ms(),
            error_velocity_mph=error_velocity_mph,
            actual_velocity_mph=actual_velocity_mph.y,
        )


def error_velocity_mph_channel(feed: Feed, autodrive_params: DriverParameters) -> ErrorVelocityChannel:
    # TODO need mechanism to filter old values?
    return ErrorVelocityChannel(
        topic=Topics.VELOCITY_ERROR_MPH.topic,
        target_speed_mph=lambda: autodrive_params[DriverParameter.TARGET_SPEED_MPH].float_value,
        velocity_mph_subscription=feed.subscribe(Topics.VELOCITY_MPH.topic, VelocityMessage),
    )
