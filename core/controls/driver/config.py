from typing import Any, Dict

import yaml

from core.fs.defaults import CONFIG_DIR
from core.model.node.type import NodeType
from lib.common.config_file import ConfigFile


class DriverConfig(Dict[str, Any]):
    pass


class DriverConfigFile(ConfigFile):
    FILENAME = "driver.yaml"

    def __init__(self, config_dir: str = CONFIG_DIR, filename: str = FILENAME) -> None:
        super().__init__(
            config_dir=config_dir,
            filename=filename,
            serialize=yaml.dump,
            deserialize=yaml.safe_load,
            root_element=str(NodeType.DRIVER).lower(),
        )
