import abc
from enum import Enum, auto

from core.model.actuator import Actuator


class ParkingBrakeRequest(Enum):
    OFF = auto()
    ON = auto()


class ParkingBrake(abc.ABC):
    @abc.abstractmethod
    async def on(self) -> None:
        pass

    @abc.abstractmethod
    async def off(self) -> None:
        pass

    @abc.abstractmethod
    async def query(self) -> bool:
        pass


class ParkingBrakeNode(Actuator, ParkingBrake):
    pass
