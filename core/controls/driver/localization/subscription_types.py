from typing import Any, Dict, List, Optional

from lib.common.geo.boundary import Boundaries, Boundary, BoundaryType
from lib.common.logging import get_logger
from lib.common.serialization.json import JsonSerializable

LOG = get_logger(__name__)


class GeoFenceMatch(JsonSerializable):
    def __init__(self, matched: List[Boundary], available: Optional[Boundaries] = None):
        self.matched = matched
        self.available: Optional[Boundaries] = available
        self.available_types: List[BoundaryType] = []
        if self.available is not None:
            self.available_types = self.available.available_types

    def _boundaries_str(self) -> str:
        return ", ".join([b.type for b in self.matched])

    def __str__(self) -> str:
        return "GeoFenceMatch({} boundaries with {} available)".format(self._boundaries_str(), self.available)

    def to_json(self) -> Dict[str, Any]:
        return {"geofences": [g.to_json() for g in self.matched]}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "GeoFenceMatch":
        raise NotImplementedError
