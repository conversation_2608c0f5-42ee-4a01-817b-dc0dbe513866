import os
from tempfile import NamedTemporaryFile

import pytest
import shapely.wkt as wkt
from shapely import Polygon
from shapely.affinity import translate

from core.controls.driver.localization.geofence import GeoFence
from lib.common.geo.boundary import PRECISION, Boundaries, Boundary, BoundaryType, GeofencesConfigFile

# This is a trivial square starting at the origin (0,0) and extending 1 unit up and right to (1,1)
DEFAULT_POLYGON = Polygon([(0, 0), (0, 1), (1, 1), (1, 0), (0, 0)])
DEFAULT_WKT = wkt.dumps(DEFAULT_POLYGON, rounding_precision=PRECISION)


@pytest.fixture(scope="function")
def temporary_config_file() -> GeofencesConfigFile:
    test_config_file = NamedTemporaryFile()
    test_config_dir, config_filename = os.path.split(test_config_file.name)

    return GeofencesConfigFile(config_dir=test_config_dir, filename=config_filename)


def test_single_lat_lon(temporary_config_file: GeofencesConfigFile) -> None:
    """
    Test that we can do geofencing tests correctly with only one boundary defined.
    """
    boundaries = Boundaries(temporary_config_file)
    test_boundary = Boundary.from_wkt(id="test1", coordinates_wkt=DEFAULT_WKT, boundary_type=BoundaryType.TURN_AROUND)
    boundaries.add(test_boundary)
    geofence = GeoFence(boundaries)

    def check_lat_lon_is_true(latitude_deg: float, longitude_deg: float) -> None:
        matching_regions = geofence.check(latitude_deg, longitude_deg)
        assert len(matching_regions.matched) == 1
        assert matching_regions.matched[0] == test_boundary

    def check_no_matches(latitude_deg: float, longitude_deg: float) -> None:
        matching_regions = geofence.check(latitude_deg, longitude_deg)
        assert len(matching_regions.matched) == 0

    matching_coords = [(0, 0), (0.5, 0.5), (1, 1), (0, 1), (1, 0)]
    for lat, lon in matching_coords:
        check_lat_lon_is_true(lat, lon)

    non_matching_coords = [
        (-0.5, -0.5),
        (-1, -1),
        (0, -90),
        (0, 90),
        (0, 179),
        (0, -179),
        (10, -90),
        (10, 90),
        (10, 179),
        (10, -179),
        (-10, -90),
        (-10, 90),
        (-10, 179),
        (-10, -179),
    ]
    for lat, lon in non_matching_coords:
        check_no_matches(lat, lon)

    bad_coordinates = [
        (66.5, 0),
        (-66.5, 0),
        (80, 0),
        (-80, 0),
        (90, 0),
        (-90, 0),
        (0, -180),
        (0, 180),
        (0, -1000),
        (0, 1000),
    ]
    # Verify that we reject checks at crazy high/low latitudes and non-standard longitudes
    for lat_deg, lon_deg in bad_coordinates:
        with pytest.raises(AssertionError):
            geofence.check(lat_deg, lon_deg)


def test_multiple_regions(temporary_config_file: GeofencesConfigFile) -> None:
    """
    Test that multiple matching regions can be detected if boundaries intersect each other.
    """
    boundaries = Boundaries(temporary_config_file)
    test_boundary = Boundary.from_wkt(id="test1", coordinates_wkt=DEFAULT_WKT, boundary_type=BoundaryType.TURN_AROUND)
    boundaries.add(test_boundary)

    # Similar to the test region
    second_region = translate(DEFAULT_POLYGON, -0.5, -0.5)
    second_test_boundary = Boundary.from_wkt(
        id="test2", coordinates_wkt=wkt.dumps(second_region), boundary_type=BoundaryType.TURN_AROUND
    )
    boundaries.add(second_test_boundary)

    geofence = GeoFence(boundaries)

    def check_only_one_region_matches(latitude_deg: float, longitude_deg: float, expected_region: Boundary) -> None:
        matching_regions = geofence.check(latitude_deg, longitude_deg)
        assert len(matching_regions.matched) == 1
        assert matching_regions.matched[0] == expected_region

    def check_both_regions_match(latitude_deg: float, longitude_deg: float) -> None:
        matching_regions = geofence.check(latitude_deg, longitude_deg)
        assert len(matching_regions.matched) == 2

    check_only_one_region_matches(0.75, 0.75, test_boundary)
    check_only_one_region_matches(0.51, 0.51, test_boundary)
    check_only_one_region_matches(0.51, 0.50, test_boundary)
    check_only_one_region_matches(0.50, 0.51, test_boundary)
    check_only_one_region_matches(1, 1, test_boundary)
    check_only_one_region_matches(-0.5, -0.5, second_test_boundary)
    check_only_one_region_matches(-0.25, -0.25, second_test_boundary)
    check_only_one_region_matches(-0.01, -0.01, second_test_boundary)
    check_only_one_region_matches(0.01, -0.01, second_test_boundary)
    check_only_one_region_matches(-0.01, 0.01, second_test_boundary)
    check_both_regions_match(0, 0)
    check_both_regions_match(0.25, 0.25)
    check_both_regions_match(0.5, 0.5)
    check_both_regions_match(0, 0.5)
    check_both_regions_match(0.5, 0)
