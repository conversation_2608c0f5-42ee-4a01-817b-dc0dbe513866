import os
from tempfile import NamedTemporaryFile
from uuid import uuid4

import pytest
import shapely.wkt as wkt
from shapely import Polygon

from lib.common.geo.boundary import PRECISION, Boundaries, Boundary, BoundaryType, GeofencesConfigFile

DEFAULT_POLYGON = Polygon([(-121, 47), (-121, 48), (-120, 48), (-120, 47), (-121, 47)])
DEFAULT_WKT = wkt.dumps(DEFAULT_POLYGON, rounding_precision=PRECISION)


def test_boundary() -> None:
    """
    Test some basics of boundaries, including things like properties being recorded correctly,
    unique IDs generated, and most importantly that boundaries can be serialized and deserialized
    back to the same boundary definition.
    """
    polygon = DEFAULT_POLYGON
    coordinates = DEFAULT_WKT

    for i, t in enumerate(BoundaryType):
        boundary = Boundary.from_wkt(id=f"test{i}", coordinates_wkt=coordinates, boundary_type=t)

        assert boundary.type == t
        assert boundary.coordinates_wkt == coordinates
        assert boundary.geometry == polygon

        json_dict = boundary.to_json()
        boundary_from_dict = Boundary.from_json(json_dict)
        assert boundary_from_dict.type == t
        assert boundary_from_dict.coordinates_wkt == coordinates
        assert boundary_from_dict.geometry == polygon
        assert boundary_from_dict.id == boundary.id


def test_boundary_with_id() -> None:
    """
    Verify that we can create a boundary with a specific ID.
    """
    polygon = DEFAULT_POLYGON
    coordinates = DEFAULT_WKT
    test_type = BoundaryType.TURN_AROUND
    test_id = str(uuid4())

    boundary = Boundary.from_wkt(coordinates_wkt=coordinates, boundary_type=test_type, id=test_id)

    assert boundary.type == test_type
    assert boundary.coordinates_wkt == coordinates
    assert boundary.geometry == polygon
    assert boundary.id == str(test_id)

    json_dict = boundary.to_json()
    boundary_from_dict = Boundary.from_json(json_dict)

    assert boundary_from_dict.type == test_type
    assert boundary_from_dict.coordinates_wkt == coordinates
    assert boundary_from_dict.geometry == polygon
    assert boundary_from_dict.id == str(test_id)

    boundary_with_string_id = Boundary.from_wkt(coordinates_wkt=coordinates, boundary_type=test_type, id=str(test_id))
    assert boundary_with_string_id.id == str(test_id)

    non_uuid_ids = ["abc-123", str(uuid4())[:-1], "something completely not looking like a UUID"]
    for _id in non_uuid_ids:
        Boundary.from_wkt(coordinates_wkt=coordinates, boundary_type=test_type, id=_id)

    with pytest.raises(TypeError):
        bad_wkt = "POINT (0.000 0.000)"
        Boundary.from_wkt(coordinates_wkt=bad_wkt, boundary_type=test_type, id=str(test_id))


def test_boundaries_from_file() -> None:
    """
    Verify that we can load boundaries from a configuration file.
    """
    test_config_file = NamedTemporaryFile()
    test_config_dir, config_filename = os.path.split(test_config_file.name)

    boundaries_config_file = GeofencesConfigFile(config_dir=test_config_dir, filename=config_filename)

    boundaries = Boundaries(boundaries_config_file)

    coordinates = DEFAULT_WKT
    test_type = BoundaryType.TURN_AROUND

    boundary = Boundary.from_wkt(id="test1", coordinates_wkt=coordinates, boundary_type=test_type)
    boundaries.add(boundary)
    assert len(boundaries) == 1
    for id, bound in boundaries:
        assert str(id) == boundary.id
        assert bound == boundary

    # Now try to read the boundaries that should have persisted to disk back.
    boundaries_from_file = Boundaries(boundaries_config_file)
    assert len(boundaries_from_file) == 1
    for id, bound in boundaries_from_file:
        assert str(id) == boundary.id
        assert bound == boundary

    # Remove the boundary from the boundaries and confirm that the change persists.
    assert boundaries.remove(boundary.id) is True
    assert len(boundaries) == 0
    boundaries_from_file = Boundaries(boundaries_config_file)
    assert len(boundaries_from_file) == 0

    assert boundaries.remove(boundary.id) is False

    # Verify that we can store multiple boundaries
    boundaries.add(boundary)
    # second boundary is the same as the first except with a new id
    second_boundary = Boundary.from_wkt(id="test2", coordinates_wkt=coordinates, boundary_type=test_type)
    assert boundary.id != second_boundary.id
    boundaries.add(second_boundary)
    assert len(boundaries) == 2
    boundaries_from_file = Boundaries(boundaries_config_file)
    assert len(boundaries_from_file) == 2

    test_config_file.close()


def test_boundaries_by_points() -> None:
    """
    Verify that we can construct boundaries one point at a time using the Boundaries API.
    """
    test_config_file = NamedTemporaryFile()
    test_config_dir, config_filename = os.path.split(test_config_file.name)

    boundaries_config_file = GeofencesConfigFile(config_dir=test_config_dir, filename=config_filename)

    boundaries = Boundaries(boundaries_config_file)

    test_boundary_name = "abcd123"
    test_boundary_type = BoundaryType.TURN_AROUND
    # Try to add the known geometry buffer one point at a time
    for point in zip(*DEFAULT_POLYGON.exterior.coords.xy):
        assert boundaries.add_point_to_buffer(longitude=point[0], latitude=point[1])

    assert len(boundaries.point_buffer) == len(DEFAULT_POLYGON.exterior.coords.xy[0])
    assert boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name)
    assert len(boundaries.point_buffer) == 0
    assert len(boundaries) == 1
    for id, boundary in boundaries:
        assert id == test_boundary_name
        assert boundary.id == test_boundary_name
        assert boundary.type == test_boundary_type
        assert boundary.coordinates_wkt == DEFAULT_WKT

    # Remove the boundary that was added to start from a clean slate again.
    assert boundaries.remove(test_boundary_name)
    assert len(boundaries) == 0

    # Now try to add the same good buffer of points but don't commit between rounds
    for i in range(2):
        # Try to add the known geometry buffer one point at a time
        for point in zip(*DEFAULT_POLYGON.exterior.coords.xy):
            assert boundaries.add_point_to_buffer(longitude=point[0], latitude=point[1])
        assert len(boundaries.point_buffer) == len(DEFAULT_POLYGON.exterior.coords.xy[0])

        # After the first round, clear the buffer of points before committing. If not, then we'll
        # create an invalid geometry due to self-intersections.
        if i == 0:
            boundaries.clear_buffer()
            assert len(boundaries.point_buffer) == 0
    assert len(boundaries.point_buffer) == len(DEFAULT_POLYGON.exterior.coords.xy[0])
    assert boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name)

    # Try to add some points that have bogus coordinates:
    assert not boundaries.add_point_to_buffer(-71, 0)
    assert len(boundaries.point_buffer) == 0
    assert not boundaries.add_point_to_buffer(71, 0)
    assert len(boundaries.point_buffer) == 0
    assert not boundaries.add_point_to_buffer(0, -181)
    assert len(boundaries.point_buffer) == 0
    assert not boundaries.add_point_to_buffer(0, 181)
    assert len(boundaries.point_buffer) == 0

    # Try to commit boundaries with not enough points
    assert not boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name)
    assert boundaries.add_point_to_buffer(0, 0)
    assert len(boundaries.point_buffer) == 1
    assert not boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name)
    assert boundaries.add_point_to_buffer(0, 1)
    assert len(boundaries.point_buffer) == 2
    assert not boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name)

    # Now that we add a 3rd point, this becomes a valid geometry which should be add-able.
    assert boundaries.add_point_to_buffer(1, 1)
    assert len(boundaries.point_buffer) == 3
    assert boundaries.commit_buffer(boundary_type=test_boundary_type, name=test_boundary_name + "again")
    assert len(boundaries.point_buffer) == 0
