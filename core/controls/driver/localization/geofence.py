from typing import Any, List, Optional

from shapely import Point, Polygon

from core.controls.driver.localization.subscription_types import GeoFenceMatch
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.frame.pose.body import RobotBody
from lib.common.geo.boundary import Boundaries, Boundary, BoundaryType
from lib.common.protocol.channel.base import SubscriberChannel


class GeoFence:
    def __init__(self, boundaries: Boundaries, field_location_notifier: Optional[FieldLocationNotifier] = None):
        """
        Constructor.

        Parameters:
            boundaries (Boundaries): The boundaries that this GeoFence object is managing and will perform checks over.
        """
        self._boundaries = boundaries

        if field_location_notifier is not None:
            # Tell field location notifier about the FIELD
            field_location_notifier.clear_geofences()
            field = boundaries.filter(BoundaryType.FIELD)
            field_location_notifier.set_field_geofence(field)
            turn_around = boundaries.filter(BoundaryType.TURN_AROUND)
            field_location_notifier.set_turn_around_geofence(turn_around)
            crops = boundaries.filter(BoundaryType.CROPS)
            field_location_notifier.set_turn_around_geofence(crops)

    def check(self, latitude_deg: float, longitude_deg: float) -> GeoFenceMatch:
        """
        Check if the (optionally given or queried from frame) location is within any known boundaries.

        Parameters:
            latitude_deg (float): The latitude (in degrees) to check. Latitudes are between -66.5 (S)
                                  and +66.5 (N) degrees (arctic circle - no farming there).
            longitude_deg (float): The longitude (in degrees) to check. Longitudes are between
                                   -180 (W) and +180 (E) degrees.

        Returns:
            The collection (list) of boundaries that this point falls within. If none, the list will be empty.
        """
        assert -66.5 < latitude_deg < 66.5
        assert -180.0 < longitude_deg < 180.0

        # Note that points are in X,Y order, which means longitude is the X-axis and latitude is the
        # Y-axis.
        point = Point(longitude_deg, latitude_deg)

        # Iteratively check every region we know about for which boundaries we're in.
        matches: List[Boundary] = []
        boundaries = self._boundaries  # store this for iteration in case this changes underneath us
        for _, boundary in boundaries:
            region: Polygon = boundary.geometry
            if region.intersects(point):
                matches.append(boundary)
        return GeoFenceMatch(matched=matches, available=self._boundaries)


# TODO delete after merge with paul. we should be able to just use RobotBodyGeo
class GeoFenceProducer(SubscriberChannel[GeoFenceMatch]):
    def __init__(
        self,
        body_subscription: SubscriberChannel[RobotBody],
        boundaries: Boundaries,
        field_location_notifier: Optional[FieldLocationNotifier] = None,
        **kwargs: Any
    ):
        super().__init__(**kwargs)
        self._body_subscription = body_subscription
        self._geofence = GeoFence(boundaries, field_location_notifier)

    def read(self) -> Optional[GeoFenceMatch]:
        body: Optional[RobotBody] = self._body_subscription.read()
        if body is None:
            return None
        return self._geofence.check(body.wheel_centroid_lla.lat, body.wheel_centroid_lla.lon)


class RobotBodyGeo:
    def __init__(self, body: RobotBody, geofence: GeoFence):
        self._body = body
        self._geofence = geofence

        self._robot_geomatch = self._geofence.check(body.wheel_centroid_lla.lat, body.wheel_centroid_lla.lon)

        self._force_field_geomatches: List[GeoFenceMatch] = []
        for ll in self._body.force_field:
            self._force_field_geomatches.append(self._geofence.check(ll.lat, ll.lon))

    @property
    def body(self) -> RobotBody:
        return self._body

    @property
    def robot_geomatch(self) -> GeoFenceMatch:
        return self._robot_geomatch

    @property
    def force_field_geomatches(self) -> List[GeoFenceMatch]:
        return self._force_field_geomatches


class RobotBodyGeoProducer(SubscriberChannel[RobotBodyGeo]):
    def __init__(self, body_subscription: SubscriberChannel[RobotBody], boundaries: Boundaries, **kwargs: Any):
        super().__init__(**kwargs)
        self._body_subscription = body_subscription
        self._geofence = GeoFence(boundaries)

    def read(self) -> Optional[RobotBodyGeo]:
        body: Optional[RobotBody] = self._body_subscription.read()
        if body is None:
            return None
        return RobotBodyGeo(body=body, geofence=self._geofence)
