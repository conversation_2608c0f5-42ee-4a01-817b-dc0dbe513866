from typing import List, Optional, Tuple, cast

import numpy as np

import lib.common.tasks
import lib.common.time
from core.controls.exterminator.model.gimbal import Gimbal2D
from core.controls.exterminator.model.scanner import Scanner
from core.cv.retina.camera.node import Cam
from core.model.node.type import NodeType
from lib.common.error import MakaException
from lib.common.logging import get_logger

LOG = get_logger(__name__)


def do_sequence_capture(
    scanner: Scanner,
    current: bool = False,
    corners: bool = False,
    center: bool = False,
    cardinals: bool = False,
    random: bool = False,
    num_random: int = 10,
    random_servo_max: int = 25,
    annotated: bool = False,
    ui_capture_dir: Optional[str] = None,
) -> None:
    if not ui_capture_dir:
        raise MakaException("Sequence capture needs a destination directory.")

    LOG.debug("{} Capturing sequence...".format(scanner.id))
    if scanner.target_cam is None:
        raise RuntimeError(f"{scanner.device_path} Cannot capture target sequence with no target cam")
    tcam: Cam = scanner.target_cam
    gimbal: Gimbal2D = cast(Gimbal2D, scanner.get_node(NodeType.GIMBAL, required=True))

    # generate points
    og_pan, og_tilt = gimbal.get_position()
    assert og_pan is not None
    assert og_tilt is not None
    points = gimbal.points(corners=corners, center=center, cardinals=cardinals, random=0)
    if current:
        ret, current_servos = gimbal.get_position()
        assert ret == 0
        points = cast(List[Tuple[int, int]], [current_servos]) + points
    pan_min, tilt_min = gimbal.min
    pan_max, tilt_max = gimbal.max
    if random:
        rand_pans = np.random.randint(
            max(pan_min, og_pan - random_servo_max), min(pan_max, og_pan + random_servo_max), num_random
        )
        rand_tilts = np.random.randint(
            max(tilt_min, og_tilt - random_servo_max), min(tilt_max, og_tilt + random_servo_max), num_random
        )
        points += zip(rand_pans, rand_tilts)

    # do a single predict capture
    assert scanner.predict_space is not None
    for pcam in scanner.predict_space.list_cameras():
        pcam.copy_settings_to(ui_capture_dir)

        LOG.debug("Taking predict image...")
        img = pcam.capture(annotated=annotated)
        filepath = pcam.save(filename_suffix="ui_capture.png", image=img, subdir=ui_capture_dir)
        LOG.debug("{} Saved image at {}".format(pcam.id, filepath))

    # do target capture sequence
    tcam.copy_settings_to(ui_capture_dir)
    LOG.debug("Visiting points: {}".format(points))
    for servos in points:
        lib.common.tasks.get_current().raise_if_cancelled()
        LOG.debug("GOTO {}".format(servos))
        scanner.goto_servos(servos)
        arrived_at = gimbal.get_position()
        scanner.settle_delay()
        LOG.debug("{} Capturing...".format(tcam.id))
        img = tcam.capture(annotated=annotated)
        timestamp = lib.common.time.iso8601_timestamp(ms=True, replace_colon=True)

        filename_fmt_no_ext = get_filename_fmt_no_ext(timestamp, gimbal, servos, arrived_at)
        filepath = tcam.save(filename_suffix=filename_fmt_no_ext + ".png", image=img, subdir=ui_capture_dir)
        LOG.debug("{} Saved image at {}".format(tcam.id, filepath))


def get_filename_fmt_no_ext(
    timestamp: str, gimbal: Gimbal2D, servos: Tuple[int, int], arrived_at: Tuple[Optional[int], Optional[int]]
) -> str:
    filename_fmt_no_ext = "{}_servos_{}_{}".format(timestamp, *arrived_at)
    if servos == gimbal.center:
        filename_fmt_no_ext += "_center"
    if servos == gimbal.top_left:
        filename_fmt_no_ext += "_top_left"
    if servos == gimbal.top_right:
        filename_fmt_no_ext += "_top_right"
    if servos == gimbal.bottom_right:
        filename_fmt_no_ext += "_bottom_right"
    if servos == gimbal.bottom_left:
        filename_fmt_no_ext += "_bottom_left"
    if servos == gimbal.top_mid:
        filename_fmt_no_ext += "_top_mid"
    if servos == gimbal.right_mid:
        filename_fmt_no_ext += "_right_mid"
    if servos == gimbal.bottom_mid:
        filename_fmt_no_ext += "_bottom_mid"
    if servos == gimbal.left_mid:
        filename_fmt_no_ext += "_left_mid"
    return filename_fmt_no_ext
