import os
from typing import Optional

import lib.common.logging
import lib.common.time
from core.fs.filesystem import FileSystem

LOG = lib.common.logging.get_logger(__name__)

UNTITLED_SESSION = "untitled_session"


def resolve_subdirectory(
    fs: FileSystem,
    operation: str,
    device: str,
    is_annotated: bool = False,
    session: Optional[str] = None,
    fixup: Optional[bool] = True,
) -> str:
    timestamp = lib.common.time.iso8601_timestamp(ms=False, replace_colon=True)
    session_dir = session
    if session_dir is None:
        session_dir = timestamp + UNTITLED_SESSION
    assert ":" not in session_dir, 'Please avoid ":" in directory names to avoid cross-platform issues.'

    operation_dir = operation
    if fixup is True:
        operation_dir = f"{operation}_{'annotated_' if is_annotated else ''}{device.replace(':', '-')}"

    full_path = os.path.join(fs.abs_media_subdir_ui_capture, session_dir, operation_dir)
    os.makedirs(full_path, exist_ok=True)
    return full_path
