import logging
from typing import List, Tuple, cast

import lib.common.logging
from core.controls.capture.p2p_save import make_p2p_prefix, write_p2p_to_disk
from core.controls.exterminator.model.scanner import Scanner
from core.cv.retina.camera.node import Cam
from core.cv.retina.space.predict_space import PredictSpace
from lib.common.math import polygon_contains_point
from lib.common.time import iso8601_timestamp

LOG = lib.common.logging.get_logger(__name__)


def do_p2p_capture(predict: Cam, scanners: List[Scanner], pcoord: Tuple[float, float], destination_path: str) -> None:
    timestamp = iso8601_timestamp(replace_colon=True)

    predict_indices = [
        x.predict_space.get_index_by_id(predict.id) if x.predict_space is not None else None for x in scanners
    ]
    refined_scanners = [
        (i, s)
        for i, s in zip(predict_indices, scanners)
        if s.pcam_limits is not None
        and i is not None
        and s.pcam_limits[i] is not None
        and polygon_contains_point(cast(List[Tuple[float, float]], s.pcam_limits[i]), pcoord)
    ]

    for i, s in refined_scanners:
        cam = cast(Cam, cast(PredictSpace, s.predict_space).get_cam_by_index(i))
        s.goto_predict_geometric(cam, pcoord)

    predict_img = predict.next()
    if predict_img.ppi is None:
        logging.error(f"Predict camera {predict.id} does not have ppi defined.")
        return

    for _, scanner in refined_scanners:
        assert scanner.target_cam is not None
        # take two pictures, first picture may be stale if servo movement is much faster than imaging time
        # TODO(asergeev): remove this after we fix RollingOrderedBuffer.next()
        scanner.target_cam.next()
        target_img = scanner.target_cam.next()
        if target_img.ppi is None:
            logging.error(f"Target camera of {scanner.id} does not have ppi defined.")
            continue
        write_p2p_to_disk(
            make_p2p_prefix(predict.id, scanner.id, timestamp), destination_path, pcoord, predict_img, target_img
        )

    logging.info(f"Captured p2p in {destination_path} with {refined_scanners}")
