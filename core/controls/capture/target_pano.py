import json
import os
from typing import Any, Dict, List, Optional, Tuple, cast

import numpy as np

import lib.common.tasks
import lib.common.time
from core.controls.exterminator.model.scanner import Scanner
from lib.common.logging import get_logger
from lib.common.types import GimbalPosition

LOG = get_logger(__name__)


def _safe_tuple(val: Optional[Any]) -> Optional[Tuple[Any, ...]]:
    if val is None:
        return None

    return tuple(val)


class TargetPanoMeta:
    def __init__(self) -> None:
        self._dict: Dict[str, Any] = {
            "timestamp": None,
            "target": {"dims": (0, 0)},
            "pano": {"file": None},
            "predict": {"file": None, "stitch_command": None},
            "servo_tdims": None,
        }

    def set_timestamp(self, timestamp: str) -> None:
        self._dict["timestamp"] = timestamp

    @property
    def timestamp(self) -> Optional[str]:
        return cast(Optional[str], self._dict["timestamp"])

    def set_target_dims(self, cols: int, rows: int) -> None:
        self._dict["target"]["dims"] = (cols, rows)
        # data is stored in row-major format
        for row in range(rows):
            self._dict["target"][str(row)] = dict()
            for col in range(cols):
                self._dict["target"][str(row)][str(col)] = {
                    "pcoords": None,
                    "tcoords": None,
                    "panocoords": None,
                    "pano_corner_coords": None,
                    "servo_coords": None,
                    "file": None,
                }

    @property
    def target_dims(self) -> Optional[Tuple[int, int]]:
        return cast(Optional[Tuple[int, int]], _safe_tuple(self._dict["target"]["dims"]))

    def set_predict_file(self, file: str) -> None:
        self._dict["predict"]["file"] = file

    @property
    def predict_file(self) -> Optional[str]:
        return cast(Optional[str], self._dict["predict"]["file"])

    def set_target_pcoords(self, col: int, row: int, coords: Tuple[float, float]) -> None:
        self._dict["target"][str(row)][str(col)]["pcoords"] = coords

    def get_target_pcoords(self, col: int, row: int) -> Optional[Tuple[float, float]]:
        return cast(Optional[Tuple[float, float]], _safe_tuple(self._dict["target"][str(row)][str(col)]["pcoords"]))

    def set_target_tcoords(self, col: int, row: int, coords: Tuple[int, int]) -> None:
        self._dict["target"][str(row)][str(col)]["tcoords"] = coords

    def get_target_tcoords(self, col: int, row: int) -> Optional[Tuple[int, int]]:
        return cast(Optional[Tuple[int, int]], _safe_tuple(self._dict["target"][str(row)][str(col)]["tcoords"]))

    def set_target_pano_coords(self, col: int, row: int, coords: Optional[Tuple[float, float]]) -> None:
        self._dict["target"][str(row)][str(col)]["panocoords"] = coords

    def get_target_pano_coords(self, col: int, row: int) -> Optional[Tuple[float, float]]:
        return cast(Optional[Tuple[float, float]], _safe_tuple(self._dict["target"][str(row)][str(col)]["panocoords"]))

    def set_target_pano_corner_coords(self, col: int, row: int, coords: Optional[List[Tuple[float, float]]]) -> None:
        self._dict["target"][str(row)][str(col)]["pano_corner_coords"] = coords

    def get_target_pano_corner_coords(self, col: int, row: int) -> Optional[List[Tuple[float, float]]]:
        return cast(
            Optional[List[Tuple[float, float]]],
            _safe_tuple(self._dict["target"][str(row)][str(col)]["pano_corner_coords"]),
        )

    def set_target_servo_coords(self, col: int, row: int, coords: Optional[GimbalPosition]) -> None:
        self._dict["target"][str(row)][str(col)]["servo_coords"] = tuple(coords) if coords is not None else None

    def get_target_servo_coords(self, col: int, row: int) -> Optional[Tuple[int, int]]:
        return cast(Optional[Tuple[int, int]], _safe_tuple(self._dict["target"][str(row)][str(col)]["servo_coords"]))

    def set_target_file(self, col: int, row: int, file: str) -> None:
        self._dict["target"][str(row)][str(col)]["file"] = file

    def get_target_file(self, col: int, row: int) -> Optional[str]:
        return cast(Optional[str], self._dict["target"][str(row)][str(col)]["file"])

    @property
    def pano_file(self) -> Optional[str]:
        return cast(Optional[str], self._dict["pano"]["file"])

    def set_pano_file(self, file: str) -> None:
        self._dict["pano"]["file"] = file

    @property
    def stitch_command(self) -> Optional[List[str]]:
        return cast(Optional[List[str]], self._dict["pano"]["stitch_command"])

    def set_stitch_command(self, command: List[str]) -> None:
        self._dict["pano"]["stitch_command"] = command

    @property
    def servo_tdims(self) -> Optional[Tuple[float, float]]:
        return cast(Tuple[float, float], self._dict["servo_tdmis"]) if self._dict["servo_tdmis"] else None

    def set_servo_tdims(self, servo_tdims: Tuple[float, float]) -> None:
        self._dict["servo_tdims"] = servo_tdims

    def save(self, pano_dir: str) -> None:
        meta_json = os.path.join(pano_dir, "meta.json")
        with open(meta_json, "w") as f:
            json.dump(self._dict, f, indent=4)
        LOG.debug("Updated {}".format(meta_json))

    @staticmethod
    def load(pano_dir: str) -> "TargetPanoMeta":
        meta_json = os.path.join(pano_dir, "meta.json")
        ret = TargetPanoMeta()
        with open(meta_json, "r") as f:
            ret._dict = json.load(f)
        return ret


def do_pano_capture(scanner: Scanner, cols: int, rows: int, destination_path: str) -> None:
    LOG.debug("{} Capturing target panorama {}x{}...".format(scanner.id, cols, rows))

    timestamp_str = lib.common.time.iso8601_timestamp(ms=True)
    timestamp_no_colons = timestamp_str.replace(":", "-")

    # capture predict image
    tcam = scanner.target_cam
    assert scanner.predict_space is not None
    assert tcam is not None, "No target cam!"
    for pcam in scanner.predict_space.list_cameras():
        # save Pylon configuration
        pcam.copy_settings_to(destination_path)

        # metadata about the capture
        meta = TargetPanoMeta()
        meta.set_timestamp(timestamp_str)
        meta.set_target_dims(cols, rows)
        # We assume that pan is X and tilt is Y
        assert scanner.servo_tdims is not None, "No servo tdims!"
        meta.set_servo_tdims((scanner.servo_tdims.pan.xy, scanner.servo_tdims.tilt.xy))

        LOG.debug("Taking a predict image...")
        img = pcam.capture()
        filepath = pcam.save(
            filename_suffix="{}_capture.png".format(timestamp_no_colons), image=img, subdir=destination_path
        )
        assert filepath is not None, "Could not save predict image."
        LOG.debug("{} Saved image at {}".format(pcam.id, filepath))
        _, filename = os.path.split(filepath)
        meta.set_predict_file(filename)

        # capture target images
        tcam.copy_settings_to(destination_path)
        assert scanner.pcam_limits is not None, "No pcam limits!"
        for yidx, py in enumerate(
            np.linspace(
                min(p[1] for p in scanner.pcam_limits if p is not None),
                max(p[1] for p in scanner.pcam_limits if p is not None),
                num=rows,
            )
        ):
            for xidx, px in enumerate(
                np.linspace(
                    min(p[0] for p in scanner.pcam_limits if p is not None),
                    max(p[0] for p in scanner.pcam_limits if p is not None),
                    num=cols,
                )
            ):
                lib.common.tasks.get_current().raise_if_cancelled()
                ret = scanner.goto_predict_geometric(pcam, (px, py))
                if ret != 0:
                    continue
                scanner.settle_delay()

                assert scanner.gimbal is not None, "No gimbal!"
                servo_coords = scanner.gimbal.get_position()

                LOG.debug("{} Capturing...".format(tcam.id))
                img = tcam.capture()
                now = lib.common.time.iso8601_timestamp(ms=True, replace_colon=True)
                filepath = tcam.save(
                    filename_suffix="{}_panorama_{}_{}.png".format(now, yidx, xidx), image=img, subdir=destination_path,
                )
                assert filepath is not None, "Could not save target image."
                LOG.debug("{} Saved image at {}".format(tcam.id, filepath))
                _, filename = os.path.split(filepath)

                meta.set_target_pcoords(xidx, yidx, (px, py))
                assert scanner.target is not None, "No target coords!"
                meta.set_target_tcoords(xidx, yidx, scanner.target)
                meta.set_target_servo_coords(xidx, yidx, servo_coords)
                meta.set_target_file(xidx, yidx, filename)

        # add meta.json
        meta.save(destination_path)
