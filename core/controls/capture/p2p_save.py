import os
from typing import <PERSON><PERSON>, cast

import cv2
import numpy as np

import lib.common.logging
from lib.common.image import CamImage
from lib.common.math import round_tuple

LOG = lib.common.logging.get_logger(__name__)


def make_p2p_prefix(predict_id: str, scanner_id: str, timestamp: str) -> str:
    return f"{predict_id.replace(':', '-')}_{scanner_id.replace(':', '-')}_{timestamp}"


def write_p2p_to_disk(
    prefix: str, destination_path: str, pcoord: Tuple[float, float], predict_img: CamImage, target_img: CamImage
) -> None:
    # make a perspective with the same field of view as predict, centered around pcoord
    if predict_img.ppi is None or target_img.ppi is None:
        LOG.warning("Camera PPI unavailable")
        return
    px, py = cast(Tuple[int, int], round_tuple(pcoord, digits=0))
    perspective_half_width = int(target_img.width / target_img.ppi * predict_img.ppi / 2)
    perspective_half_height = int(target_img.height / target_img.ppi * predict_img.ppi / 2)
    perspective_bgr = np.pad(
        predict_img.image_bgr,
        ((perspective_half_height, perspective_half_height), (perspective_half_width, perspective_half_width), (0, 0),),
        mode="constant",
    )[py : py + 2 * perspective_half_height, px : px + 2 * perspective_half_width]
    cv2.imwrite(os.path.join(destination_path, f"{prefix}.perspective.png"), perspective_bgr)

    # save target image
    cv2.imwrite(os.path.join(destination_path, f"{prefix}.image.png"), target_img.image_bgr)

    # save annotated image
    perspective_at_target_ppi = cv2.resize(perspective_bgr, (target_img.width, target_img.height))
    cv2.drawMarker(
        perspective_at_target_ppi,
        (int(target_img.width / 2), int(target_img.height / 2)),
        (0, 0, 255),
        markerType=cv2.MARKER_TILTED_CROSS,
        markerSize=15,
        thickness=2,
    )

    perspective_at_target_ppi_inner_20pct = perspective_at_target_ppi[
        int(target_img.height * 0.3) : int(target_img.height * 0.7),
        int(target_img.width * 0.3) : int(target_img.width * 0.7),
    ]

    perspective_at_target_ppi_inner_20pct = cv2.resize(
        perspective_at_target_ppi_inner_20pct, (int(target_img.width * 0.2), int(target_img.height * 0.2))
    )

    image_ann = target_img.image_bgr.copy()
    image_ann[
        : perspective_at_target_ppi_inner_20pct.shape[0], : perspective_at_target_ppi_inner_20pct.shape[1]
    ] = perspective_at_target_ppi_inner_20pct
    LOG.info(f"Saving P2P at {destination_path}")
    cv2.imwrite(os.path.join(destination_path, f"{prefix}.image_annotated.png"), image_ann)
