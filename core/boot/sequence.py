from aenum import OrderedEnum, auto


class BootSequence(OrderedEnum):
    """
    A boot sequence is the initial set of operations that the computer performs when it is switched on.
    Every computer has a boot sequence. The average computer doesn’t understand the boot sequence
    but is important to know for customizing and troubleshooting your computer.
    """

    START = auto()  # Started
    BOOT_ARGS = auto()  # Parsing boot arguments
    CONFIG = auto()  # Read config files
    DEVICES = auto()  # Boot registry devices
    LEAF_PLAN = auto()  # Plan individual device bootloaders
    DEVICE_POOL = auto()  # Boot devices
    TREE_PLAN = auto()  # Plan device tree bootloaders
    NODE_TREE = auto()  # Boot device tree
    ROOT = auto()  # Boot final robot
    BOOTED = auto()  # Done

    def __str__(self) -> str:
        return str(self.name)
