from typing import Any

from core.boot.context import Shared<PERSON>ontext
from core.boot.node import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oader
from core.controls.exterminator.model.gimbal import Gimbal2D
from core.controls.exterminator.model.scanner import Scanner
from core.model.path import DevicePath
from core.robot import Robot
from cv.utils.cupy import set_cupy_memory_allocator


class Robot<PERSON>odeBootLoader(RootNodeBootLoader[Robot]):
    def __init__(self, root: DevicePath = DevicePath.robot(), **kwargs: Any):
        assert root == DevicePath.robot()
        super().__init__(root=root, **kwargs)

        # TODO (scochrane) This is a workaround for now due to the way we currently link cams to scanners.
        # This is also going to change soon due to process separation
        # and exterminator model changes, hence the workaround
        Scanner.NEXT_ID = 0
        Gimbal2D.NEXT_ID = 0

        set_cupy_memory_allocator()

    def _boot_root_from_context(self, context: SharedContext) -> Robot:
        return Robot(
            args=context.args,
            subtree=context.tree,
            filesystem=context.fs,
            feed=context.feed,
            subscriptions={},
            device_registry=context.device_registry,
        )
