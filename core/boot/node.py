import os
from abc import ABC, abstractmethod
from argparse import Namespace
from typing import Any, Callable, Dict, Generic, Optional, Tuple, TypeVar, cast

import core.unity.unity as unity
from core.boot.args import BootArgs, UnexpectedBootArgException, UnparsedBootArgsType, filter_boot_args, parse_boot_args
from core.boot.constants import BOOT_ARGS_FILENAME, BOOT_ARGS_FILEPATH
from core.boot.context import Shared<PERSON>ontext
from core.boot.loader import <PERSON><PERSON><PERSON><PERSON><PERSON>, Device<PERSON><PERSON>BootLoader
from core.boot.plan import LeavesBootPlan, TreeBootPlan
from core.boot.sequence import BootSequence
from core.config.config import (
    ConfigCategory,
    PhysicalRobotConfiguration,
    config_exists,
    get_geofences_dirname,
    load_config,
    override_robot_name,
)
from core.config.hardware import HardwareConfigFile
from core.controls.driver.config import DriverConfig, DriverConfigFile
from core.controls.exterminator.calibration.config import CalibrationConfigFile
from core.controls.frame.config import FrameConfig, FrameConfigFile
from core.controls.frame.mechanical import MechanicalConfig, MechanicalConfigFile
from core.cv.retina.config import RetinaConfigFile
from core.model.id import ReferenceId
from core.model.node.base import Node
from core.model.path import DevicePath
from core.virtual.world import VirtualRobotConfiguration, VirtualWorld, load_virtual_robot
from lib.common.config_file import ConfigDictType, ConfigFile
from lib.common.devices.registry import DeviceRegistryFile
from lib.common.file import getsize_formatted
from lib.common.geo.boundary import GeofencesConfigFile
from lib.common.logging import get_logger
from lib.common.tasks.manager import get_event_loop_by_name

LOG = get_logger(__name__)


N = TypeVar("N", bound=Node)
F = TypeVar("F", bound=ConfigFile)
C = TypeVar("C")


class RootNodeBootLoader(DeviceTreeBootLoader, Generic[N], ABC):
    """
    Master bootloader for Node singleton.

    The program that starts the “chain reaction” which ends with the entire operating system being loaded is known as
    the boot loader.py (or bootstrap loader.py). The term creatively came from early designers imagining that before a
    computer “runs” it must have it’s “boots strapped”. The boot loader.py’s only job is to load other software for the
    operating system to start. Often, multiple-stage boot loaders are used, in which several small programs of
    increasing complexity sequentially summon one after the other, until the last of them loads the operating system.
    """

    def __init__(
        self, root: DevicePath, ignore_bootfile: bool = False, ignore_calibration_file: bool = False, **kwargs: Any
    ):
        super().__init__(device_path=DevicePath.robot(), **kwargs)
        self._root = root

        # boot args
        self._boot_args: BootArgs = BootArgs()  # will be overwritten

        # configurations
        self._virtual_config: Optional[VirtualRobotConfiguration] = None
        self._unity_config: Optional[unity.UnityRobotConfiguration] = None
        self._physical_robot_config: Optional[PhysicalRobotConfiguration] = None

        # parameterization for testing
        self._ignore_bootfile: bool = ignore_bootfile
        self._ignore_calibration_file: bool = ignore_calibration_file

        # state vars
        self._boot_seq_complete: Optional[BootSequence] = None  # tracks boot sequence (completed)
        self._boot_seq_active: Optional[BootSequence] = None  # tracks boot sequence (in progress)

    def _init_func(self) -> Callable[..., Node]:
        # robot does not use standard init func but still extends same abstract function
        # we could model this better but its not important yet
        pass

    def _set_active_boot_seq(self, active_boot_seq: BootSequence, logging: bool = True) -> None:
        if self._boot_seq_complete is None:
            assert active_boot_seq == BootSequence.START, "Must start with {} not {}".format(
                BootSequence.START, active_boot_seq
            )
        else:
            assert active_boot_seq > self._boot_seq_complete, "Cannot go backwards from {} to {}".format(
                self._boot_seq_complete, active_boot_seq
            )
        assert active_boot_seq is not None
        self._boot_seq_active = active_boot_seq

        if logging:
            LOG.debug("{} Begin".format(self._boot_seq_active))

    def _parse_boot_args(self) -> None:
        self._set_active_boot_seq(BootSequence.BOOT_ARGS, logging=False)  # we will do our own log below
        LOG.info(f"{self._boot_seq_active} Parsing boot args...")

        is_unity = self._context.args.unity
        is_virtual = self._context.args.virtual is not None
        if is_unity and is_virtual:
            raise UnexpectedBootArgException("Unity / virtual mode not supported together")

        is_real_robot = config_exists()

        # parse boot args
        cmdline_args: Namespace = self._context.args
        boot_args_filepath: Optional[str] = BOOT_ARGS_FILEPATH if not self._ignore_bootfile else None
        extra_args: Optional[UnparsedBootArgsType] = None

        if is_unity:
            unity.initialize_connector(self._context.args.unity_host, self._context.args.unity_port)
            connector = unity.get_unity_connector()
            assert connector is not None
            self._unity_config = connector.get_config()
            extra_args = self._unity_config.unparsed_boot_args

        elif is_virtual:
            virtual_name = self._context.args.virtual
            override_robot_name(virtual_name)
            virtual_world = VirtualWorld(
                image=self._context.args.sim_image, treadkill=self._context.args.virtual_treadkill
            )
            self._physical_robot_config = load_config(virtual_name)
            self._virtual_config = load_virtual_robot(self._physical_robot_config, virtual_world, virtual_name)
            boot_args_filepath = self._physical_robot_config.get(ConfigCategory.BOOT, BOOT_ARGS_FILENAME)
            virtual_world.export_board_args(self._context.args)

        elif is_real_robot and not self._ignore_bootfile:
            self._physical_robot_config = load_config()
            boot_args_filepath = self._physical_robot_config.get(ConfigCategory.BOOT, BOOT_ARGS_FILENAME)

        parsed_boot_args = parse_boot_args(
            cmdline_boot_args=cmdline_args, boot_args_filepath=boot_args_filepath, extra_args=extra_args
        )
        # filter by root
        self._boot_args = filter_boot_args(parsed_boot_args, self._root)

        self._boot_seq_complete = self._boot_seq_active
        LOG.info(f"{self._boot_seq_complete} Parsed boot args.")

    def _read_retina_cfg_file(self) -> RetinaConfigFile:
        cfg_dir = self._context.fs.abs_config_dir
        if self._physical_robot_config is not None:
            cfg_path: Optional[str] = self._physical_robot_config.get_no_fail(
                ConfigCategory.BOOT, RetinaConfigFile.FILENAME
            )
            if cfg_path is not None:
                cfg_dir = os.path.dirname(
                    self._physical_robot_config.get(str(ConfigCategory.BOOT), RetinaConfigFile.FILENAME)
                )
        return RetinaConfigFile(config_dir=cfg_dir)

    def _read_geofences_cfg_file(self) -> Optional[GeofencesConfigFile]:
        geo_location = os.environ.get("MAKA_GEO_LOCATION")
        if geo_location is None:
            return None
        dirname = get_geofences_dirname(geo_location)
        if dirname is None:
            LOG.warning(f"geofences dirname for {geo_location} not available!")
            return None
        return GeofencesConfigFile(config_dir=dirname)

    def _read_config_type(
        self,
        cat: str,  # ConfigCategory
        filename: str,
        file_constructor: Callable[..., F],
        config_constructor: Callable[[ConfigDictType], C],
    ) -> Tuple[F, C]:
        cfg_dir = self._context.fs.abs_config_dir
        if self._physical_robot_config is not None:
            cfg_path: Optional[str] = self._physical_robot_config.get_no_fail(str(cat), filename)
            if cfg_path is not None:
                cfg_dir = os.path.dirname(self._physical_robot_config.get(str(cat), filename))

        cfg_file: F = file_constructor(config_dir=cfg_dir)
        if os.path.exists(cfg_file.filepath):
            LOG.info(
                f"{self._boot_seq_active} Loading {getsize_formatted(cfg_file.filepath)} config from {cfg_file.filepath}..."
            )
            cfg = config_constructor(cfg_file.load())
            LOG.info(f"{self._boot_seq_active} Successfully loaded config.")
        else:
            LOG.warning(f"{self._boot_seq_active} No config file found at {cfg_file.filepath}! Using default config.")
            cfg = config_constructor({})

        return cfg_file, cfg

    def _read_configs(self) -> None:
        """
        Run boot step CONFIG. At end, boot sequence will be in CONFIG state.

        This logic is the same for every boot, so it resides in the config.
        """
        self._set_active_boot_seq(BootSequence.CONFIG, logging=False)  # we will do our own log below
        LOG.info("{} Loading configs...".format(self._boot_seq_active))

        # TODO we are using comment blocks to segment the code.
        #  Let's write a better config parser instead now that we have most configs in the repo.
        ################################################################################################################
        # Calibration config
        calib_cfg_file = CalibrationConfigFile()
        self._context.calib_cfg = {}
        if self._ignore_calibration_file:
            LOG.info("{} Ignoring calibration config!".format(self._boot_seq_active))
        elif os.path.exists(calib_cfg_file.filepath):
            LOG.info(
                "{} Loading {} calibration config from {}...".format(
                    self._boot_seq_active, getsize_formatted(calib_cfg_file.filepath), calib_cfg_file.filepath
                )
            )
            self._context.calib_cfg = {DevicePath.parse(k): v for k, v in calib_cfg_file.load().items()} or {}
            LOG.info(
                "{} Successfully loaded calibration config with {} entries.".format(
                    self._boot_seq_active, len(self._context.calib_cfg)
                )
            )
        else:
            LOG.warning(
                "{} No calibration config file found at {}! Using default config.".format(
                    self._boot_seq_active, calib_cfg_file.filepath
                )
            )

        ################################################################################################################
        # Hardware config
        if self._unity_config is not None:
            self._context.hw_cfg = self._unity_config.hw_cfg
        else:
            _, self._context.hw_cfg = self._read_config_type(
                ConfigCategory.BOOT,
                HardwareConfigFile.FILENAME,
                HardwareConfigFile,
                # TODO make HardwareConfig type like the other configs
                lambda d: cast(Dict[ReferenceId, Dict[str, Any]], d if isinstance(d, dict) else dict(d)),
            )

        ################################################################################################################
        # Retina config
        self._context.retina_cfg = self._read_retina_cfg_file()

        # GeoFences config
        self._context.boundaries = None
        self._context.geofences_cfg_file = self._read_geofences_cfg_file()
        # This is fallthrough for default unity geofences file - overridable with config var
        if self._context.geofences_cfg_file is None and self._unity_config is not None:
            if self._unity_config.geofences_cfg_file is not None:
                self._context.geofences_cfg_file = self._unity_config.geofences_cfg_file
            else:
                self._context.boundaries = self._unity_config.boundaries
                self._context.geofences_cfg_file = None

        ################################################################################################################
        # Driver
        self._read_driver_cfgs()

        ################################################################################################################
        # frame config
        if self._unity_config is not None:
            self._context.frame_cfg_file = self._unity_config.frame_cfg_file
        else:
            self._context.frame_cfg_file, _ = self._read_config_type(
                ConfigCategory.CALIBRATION, FrameConfigFile.FILENAME, FrameConfigFile, FrameConfig
            )

        ################################################################################################################
        # mechanical config
        if self._unity_config is not None:
            self._context.mechanical_cfg = self._unity_config.mechanical_cfg
        else:
            _, self._context.mechanical_cfg = self._read_config_type(
                ConfigCategory.BOOT, MechanicalConfigFile.FILENAME, MechanicalConfigFile, MechanicalConfig
            )
        ################################################################################################################

        self._boot_seq_complete = self._boot_seq_active
        LOG.info(f"{self._boot_seq_complete} Loaded configs.")

    def _read_driver_cfgs(self) -> None:
        if self._unity_config is not None:
            self._context.driver_cfg_file = self._unity_config.driver_cfg_file
        else:
            self._context.driver_cfg_file, _ = self._read_config_type(
                ConfigCategory.CALIBRATION, DriverConfigFile.FILENAME, DriverConfigFile, DriverConfig
            )

    def _boot_device_registry(self) -> None:
        self._set_active_boot_seq(BootSequence.DEVICES)

        if self._physical_robot_config is not None:
            devices_file_path = self._physical_robot_config.get_no_fail(ConfigCategory.BOOT, DeviceRegistryFile.NAME)
            if devices_file_path is not None:
                self._context.device_registry.boot_devices_from_sync(
                    devices_file_path, get_event_loop_by_name(), skip_list=set(self._context.args.skip_device)
                )

        self._boot_seq_complete = self._boot_seq_active
        LOG.info("Devices Loaded into Device Registry")

    def _compute_leaves_boot_plan(self) -> LeavesBootPlan:
        self._set_active_boot_seq(BootSequence.LEAF_PLAN)

        leaves_boot_plan = LeavesBootPlan.build(self._boot_args, self._context.hw_cfg)

        self._boot_seq_complete = self._boot_seq_active
        LOG.info(f"{self._boot_seq_complete} Computed leaves boot plan.")

        return leaves_boot_plan

    def _boot_leaves(self, leaves_boot_plan: LeavesBootPlan) -> None:
        self._set_active_boot_seq(BootSequence.DEVICE_POOL)

        for ref_id in leaves_boot_plan.ref_ids:
            leaf_bootloader: BootLoader = leaves_boot_plan.init_bootloader(
                ref_id=ref_id,
                device_path=leaves_boot_plan.device_path_by_id[ref_id],
                context=self._context,
                log_prefix=f"{self._boot_seq_active} ",
            )
            _ = leaf_bootloader.boot()

        self._boot_seq_complete = self._boot_seq_active
        LOG.info("{} Booted {} devices.".format(self._boot_seq_complete, len(self._context.reference_pool)))

    def _compute_tree_boot_plan(self, leaves_boot_plan: LeavesBootPlan) -> TreeBootPlan:
        self._set_active_boot_seq(BootSequence.TREE_PLAN)

        # can prevent the frame to boot by passing a "beneath" parameter
        tree_boot_plan = TreeBootPlan(device_pool_boot_plan=leaves_boot_plan, root=self._root)

        self._boot_seq_complete = self._boot_seq_active
        LOG.info("{} Computed device tree boot plan.".format(self._boot_seq_complete))

        return tree_boot_plan

    def _boot_tree(self, tree_boot_plan: TreeBootPlan) -> None:
        """
        :type tree_boot_plan: TreeBootPlan
        """
        self._set_active_boot_seq(BootSequence.NODE_TREE)

        for device_path in tree_boot_plan.inorder_boot():
            node_bootloader: BootLoader = tree_boot_plan.init_bootloader(
                device_path, self._context, log_prefix="{} ".format(self._boot_seq_active)
            )
            _ = node_bootloader.boot()

        # assert
        assert len(self._context.tree) == len(self._context.reference_pool)
        assert len(self._context.tree) == len(self._context.actuators) + len(self._context.sensors)

        self._boot_seq_complete = self._boot_seq_active
        width = len(str(len(self._context.tree)))
        LOG.info("{} pool:      {}".format(self._boot_seq_complete, len(self._context.reference_pool)))
        LOG.info("{} actuators: {}".format(self._boot_seq_complete, str(len(self._context.actuators)).rjust(width)))
        LOG.info("{} sensors:   {}".format(self._boot_seq_complete, str(len(self._context.sensors)).rjust(width)))
        LOG.info("{} tree:      {}".format(self._boot_seq_complete, len(self._context.tree)))

    def _boot_root(self) -> N:
        self._set_active_boot_seq(BootSequence.ROOT)

        node = self._boot_root_from_context(context=self._context)

        self._boot_seq_complete = self._boot_seq_active
        return node

    @abstractmethod
    def _boot_root_from_context(self, context: SharedContext) -> N:
        pass

    def _boot(self) -> N:
        """
        Boot devices and return a booted Robot.
        """
        assert self is not None and isinstance(
            self, RootNodeBootLoader
        ), "Bad self argument to boot(). This is likely a code typo."

        # START
        self._set_active_boot_seq(BootSequence.START, logging=False)
        LOG.info(f"{self._boot_seq_active} Booting {self._root}...")
        self._boot_seq_complete = self._boot_seq_active

        # BOOT_ARGS
        self._parse_boot_args()

        # CONFIG
        self._read_configs()

        # DEVICES
        self._boot_device_registry()

        # DEVICE_POOL_PLAN
        device_pool_boot_plan = self._compute_leaves_boot_plan()

        # DEVICE_POOL
        self._boot_leaves(device_pool_boot_plan)

        # DEVICE_TREE_PLAN
        device_tree_boot_plan = self._compute_tree_boot_plan(device_pool_boot_plan)

        # DEVICE_TREE
        self._boot_tree(device_tree_boot_plan)

        # NODE
        root_node: N = self._boot_root()

        # BOOTED
        self._boot_seq_active = None
        self._boot_seq_complete = BootSequence.BOOTED
        LOG.info("{} Boot Complete.".format(self._boot_seq_complete))

        return root_node

    def boot(self) -> N:
        # do extra logging
        node = super().boot()
        assert isinstance(node, Node), f"Non-Node type detected: {type(node)}"
        assert self._root == node.device_path
        self._dump_context()
        return node  # type: ignore
