from argparse import Namespace
from typing import Any, Dict, Optional, Union

import lib.common.logging
from core.controls.driver.config import DriverConfigFile
from core.controls.frame.config import FrameConfigFile
from core.controls.frame.mechanical import MechanicalConfig
from core.cv.retina.config import RetinaConfigFile
from core.fs.filesystem import FileSystem
from core.model.actuator import Actuator
from core.model.id import ReferenceId
from core.model.node.base import Node
from core.model.path import DevicePath
from core.model.sensor import Sensor
from lib.common.devices.registry import DeviceRegistry
from lib.common.geo.boundary import Boundary, GeofencesConfigFile
from lib.common.protocol.feed.base import Feed

LOG = lib.common.logging.get_logger(__name__)


class SharedContext:
    """
    A shared container for organizing references during boot.
    """

    def __init__(self, args: Namespace, device_registry: DeviceRegistry, feed: Feed):
        assert args is not None and isinstance(args, Namespace)
        self._args: Namespace = args

        self._filesystem: FileSystem = FileSystem.from_args(args)
        self._device_registry: DeviceRegistry = device_registry
        self._feed: Feed = feed

        # Subsets of these will get preserved by the various device trees that get initialized
        # The robot will keep the full reference pool, while the subtrees will get their own subsets based on
        # what can be reached from them

        # leaf devices built in any order, then device trees added as they are booted
        self._reference_pool: Dict[ReferenceId, Node] = {}
        # leaves built first, then by depth, then by alphabetical
        self._tree: Dict[DevicePath, Node] = {}

        self._sensors: Dict[DevicePath, Sensor] = {}

        # actuators can only control devices in their subtree
        self._actuators: Dict[DevicePath, Actuator] = {}

        self._calib_cfg: Dict[DevicePath, Any] = {}
        self._hw_cfg: Dict[ReferenceId, Dict[str, Any]] = {}
        self.retina_cfg: RetinaConfigFile = RetinaConfigFile()
        self.frame_cfg_file: Optional[FrameConfigFile] = None
        self.driver_cfg_file: Optional[DriverConfigFile] = None
        self.geofences_cfg_file: Optional[GeofencesConfigFile] = None
        self.boundaries: Optional[Dict[str, Boundary]] = None
        self.mechanical_cfg: MechanicalConfig = MechanicalConfig()

    @property
    def args(self) -> Namespace:
        return self._args

    @property
    def fs(self) -> FileSystem:
        return self._filesystem

    @property
    def feed(self) -> Feed:
        return self._feed

    @property
    def device_registry(self) -> DeviceRegistry:
        return self._device_registry

    @property
    def actuators(self) -> Dict[DevicePath, Actuator]:
        return self._actuators

    @property
    def reference_pool(self) -> Dict[ReferenceId, Node]:
        return self._reference_pool

    @property
    def sensors(self) -> Dict[DevicePath, Sensor]:
        return self._sensors

    @property
    def tree(self) -> Dict[DevicePath, Node]:
        return self._tree

    @property
    def calib_cfg(self) -> Dict[DevicePath, Any]:
        return self._calib_cfg

    @calib_cfg.setter
    def calib_cfg(self, val: Dict[DevicePath, Any]) -> None:
        self._calib_cfg = val

    @property
    def hw_cfg(self) -> Dict[ReferenceId, Dict[str, Any]]:
        return self._hw_cfg

    @hw_cfg.setter
    def hw_cfg(self, val: Dict[ReferenceId, Dict[str, Any]]) -> None:
        self._hw_cfg = val

    @staticmethod
    def _register(key: Union[ReferenceId, DevicePath], obj: Any, registry: Dict[Any, Any], registry_name: str) -> None:
        assert key not in registry, "Duplicate key in {}: {}".format(registry_name, key)
        registry[key] = obj
        LOG.debug('{} += {{"{}": {}}}'.format(registry_name, key, obj))

    def register_actuators(self, device_path: DevicePath, obj: Any) -> None:
        SharedContext._register(device_path, obj, self.actuators, "actuators")

    def register_pool(self, device_path: DevicePath, obj: Any) -> None:
        SharedContext._register(device_path, obj, self.reference_pool, "pool")

    def register_sensors(self, device_path: DevicePath, obj: Any) -> None:
        SharedContext._register(device_path, obj, self.sensors, "sensors")

    def register_tree(self, device_path: DevicePath, obj: Any) -> None:
        SharedContext._register(device_path, obj, self.tree, "tree")
