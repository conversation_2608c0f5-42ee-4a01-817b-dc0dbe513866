import abc
import argparse
from typing import Any, Callable, Dict, List, Optional, Union

import lib.common.logging
from core.boot.context import SharedContext
from core.fs.filesystem import FileSystem
from core.model.actuator import Actuator
from core.model.id import ReferenceId
from core.model.node.base import Node, NodeAccessPolicy
from core.model.path import DevicePath
from core.model.sensor import Sensor
from lib.common.devices.registry import DeviceRegistry
from lib.common.protocol.feed.base import Feed
from lib.common.protocol.feed.inproc import InprocFeed

LOG = lib.common.logging.get_logger(__name__)


class BootLoader(abc.ABC):
    """
    Boot Loader base class. Implementations should return a particular type of object with a particular device path.

    e.g. GimbalBootLoader(...).boot() returns a Gimbal
    e.g. LaserBootLoader(...).boot() returns a Laser

    Each device will have an associated BootLoader.
    """

    def __init__(
        self, context: Optional[SharedContext] = None, args: Optional[Union[List[str], argparse.Namespace]] = None
    ):
        """
        Either args or context should be None but not both

        :type context: SharedContext
        :type args: either list (in which case it gets parsed--this facilitates easier testing) or argparse.Namespace
        """
        # XOR
        assert (args is None) ^ (
            context is None
        ), "Either args or context must be None, but not both. Got: ({}, {})".format(args, context)

        # parse args if needed
        if args is not None and isinstance(args, list):
            assert context is None, "Must not pass context if passing args"
            # TODO this exposes args to everyone; can we do better?
            from core.args import RobotArgumentParser

            args = RobotArgumentParser().parse_args(args=args)

        assert args is None or isinstance(args, argparse.Namespace)
        assert context is None or isinstance(context, SharedContext)
        context = (
            SharedContext(args, device_registry=DeviceRegistry(), feed=InprocFeed()) if args is not None else context
        )
        assert context is not None
        self._context: SharedContext = context

        self._object = None

    @property
    def feed(self) -> Feed:
        return self._context.feed

    @property
    def fs(self) -> FileSystem:
        return self._context.fs

    def boot(self) -> Any:
        """
        Boot the underlying object and add it to the global reference pool AND the tree.
        """
        self._object = self._boot()

        self._attach_to_context()

        return self._object

    def _attach_to_context(self) -> None:
        """
        Extensible: child classes are allowed to override this method, but must call super()._attach_to_context() first.
        """
        assert self._object is not None, "Must boot object before attaching to context"

        # register device by id in robot reference pool
        self._context.register_pool(self._object.id, self._object)

    def _dump_context(self, level: str = "INFO") -> None:
        """
        Debug method
        """
        if level == "DEBUG":
            log_f = LOG.debug
        elif level == "INFO":
            log_f = LOG.info
        else:
            assert False, "Unsupported log level here"

        max_id_width = max([len(k) for k in list(self._context.reference_pool.keys())])
        log_f("REFERENCE POOL:")
        for k, v in sorted(self._context.reference_pool.items()):
            log_f("{}: {}".format(str(k).ljust(max_id_width), type(v)))

        max_path_width = max([len(k) for k in list(self._context.tree.keys())])
        log_f("DEVICE TREE:")
        for x, v in sorted(self._context.tree.items()):
            log_f("{}: {}: {}".format(str(x).ljust(max_path_width), str(v.id).ljust(max_id_width), type(v)))

    @abc.abstractmethod
    def _boot(self) -> Any:
        """
        :return: obj
        """
        pass


class DeviceTreeBootLoader(BootLoader):
    def __init__(
        self,
        ref_id: Optional[ReferenceId] = None,
        device_path: Optional[DevicePath] = None,
        device_init_func: Optional[Callable[..., Node]] = None,
        **kwargs: Any
    ):
        super().__init__(**kwargs)

        assert device_path is not None
        self.device_path = device_path

        # for leaves (carryover from Device structure)
        self._ref_id = ref_id
        self._device_init_func = device_init_func

    @abc.abstractmethod
    def _init_func(self) -> Callable[..., Node]:
        """
        A function which accepts a device path, a dictionary of children, and global sensors
        """
        pass

    def _boot(self) -> Node:
        return self._init_func()(
            filesystem=self.fs,
            feed=self.feed,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            subscriptions=self._get_subscriptions(),
        )

    def _get_subscriptions(self) -> Dict[DevicePath, Node]:
        # Everybody gets PUBLIC nodes
        return {k: v for k, v in self._context.tree.items() if v.access_policy == NodeAccessPolicy.PUBLIC}

    def _attach_to_context(self) -> None:
        super()._attach_to_context()

        if self.device_path is not None:
            self._context.register_tree(self.device_path, self._object)

        if self.device_path is not None and self.device_path.depth != 0:
            if isinstance(self._object, Sensor):
                self._context.register_sensors(self.device_path, self._object)
            elif isinstance(self._object, Actuator):
                self._context.register_actuators(self.device_path, self._object)

    def _get_subtree(self) -> Dict[DevicePath, Node]:
        return {k: v for k, v in self._context.tree.items() if self.device_path.is_ancestor(k)}


class SimulatedDeviceBootLoader(DeviceTreeBootLoader):
    def _boot(self) -> Node:
        assert self._ref_id is not None
        assert self._device_init_func is not None
        return self._device_init_func(
            subtree=self._get_subtree(),
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
        )

    def _init_func(self) -> Callable[..., Node]:
        # devices still use there own init func pattern
        pass
