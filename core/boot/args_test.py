import os
import warnings
from typing import Dict, List

import pytest

from core.boot.args import ARG_SIM, BootArgs, parse_boot_args
from core.model.path import DevicePath

DASH_D = "-d"


FRAME_SHORTHAND = ["/frame"]
FRAME = ["/frame/gps", "/frame/imu", "/frame/ins", "/frame/fuel_sensor"]

GIMBAL_1_SHORTHAND = ["/exterminator/row_module:1/scanner:1/gimbal"]
GIMBAL_1 = [
    "/exterminator/row_module:1/scanner:1/gimbal",
    "/exterminator/row_module:1/scanner:1/gimbal/servo:pan",
    "/exterminator/row_module:1/scanner:1/gimbal/servo:tilt",
]
SCANNER_1_SHORTHAND = ["/exterminator/row_module:1/scanner:1"]

SCANNER_1 = sorted(["/exterminator/row_module:1/scanner:1/laser"] + GIMBAL_1)

SCANNER_2 = [
    "/exterminator/row_module:1/scanner:2/gimbal",
    "/exterminator/row_module:1/scanner:2/gimbal/servo:pan",
    "/exterminator/row_module:1/scanner:2/gimbal/servo:tilt",
    "/exterminator/row_module:1/scanner:2/laser",
]

DEVICE_TREE = SCANNER_1 + SCANNER_2

TEST_BOOT_ARGS_FILEPATH = "/tmp/test_boot.args"

ARG_FOO = "foo"
ARG_BAR = "bar"


def _to_args(device_paths: List[str], arg: str) -> List[str]:
    args = []
    for device_path_str in device_paths:
        args.extend([DASH_D, device_path_str, arg])
    return args


def _args_to_test_file(args: List[str]) -> None:
    """
    Serialize args to test file
    """
    if os.path.exists(TEST_BOOT_ARGS_FILEPATH):
        os.remove(TEST_BOOT_ARGS_FILEPATH)

    with open(TEST_BOOT_ARGS_FILEPATH, "w") as f:
        for i in range(0, len(args), 3):
            f.write(" ".join(args[i : i + 3]) + os.linesep)


def _test_tree(tree: List[str]) -> None:
    foo_args = _to_args(tree, ARG_FOO)

    def test(boot_args_: Dict[DevicePath, str], expected: str) -> None:
        assert len(boot_args_) == len(tree), "Expected {} boot arg parsed. Got: {}".format(len(tree), boot_args_)
        for dp, boot_arg in boot_args_.items():
            assert boot_arg == expected
            assert str(dp) in tree

    # parse as if from commandline
    boot_args: BootArgs = parse_boot_args(cmdline_boot_args=foo_args, boot_args_filepath=None)
    test(boot_args, ARG_FOO)

    # parse from boot.args file
    _args_to_test_file(foo_args)
    boot_args = parse_boot_args(cmdline_boot_args=[], boot_args_filepath=TEST_BOOT_ARGS_FILEPATH)
    test(boot_args, ARG_FOO)

    # parse from boot.args file but override with commandline
    bar_args = _to_args(tree, ARG_BAR)
    boot_args = parse_boot_args(cmdline_boot_args=bar_args, boot_args_filepath=TEST_BOOT_ARGS_FILEPATH)
    test(boot_args, ARG_BAR)


def test_single_device_boots() -> None:
    warnings.filterwarnings("ignore", category=pytest.PytestUnraisableExceptionWarning)
    for device_path_str in DEVICE_TREE:
        tree = [device_path_str]
        _test_tree(tree)


def test_full_scanner() -> None:
    _test_tree(SCANNER_1)


def _test_shorthand(shorthand_tree: List[str], expanded_tree: List[str]) -> None:
    foo_args = _to_args(shorthand_tree, ARG_SIM)

    def test(boot_args_: Dict[DevicePath, str], expected: str) -> None:
        assert len(boot_args_) == len(expanded_tree), "Expected {} boot arg parsed. Got: {}".format(
            len(expanded_tree), boot_args_
        )
        for dp, boot_arg in boot_args_.items():
            assert boot_arg == expected
            assert str(dp) in expanded_tree

    # parse as if from commandline
    boot_args: BootArgs = parse_boot_args(cmdline_boot_args=foo_args, boot_args_filepath=None)
    test(boot_args, ARG_SIM)

    # parse from boot.args file
    _args_to_test_file(foo_args)
    boot_args = parse_boot_args(cmdline_boot_args=[], boot_args_filepath=TEST_BOOT_ARGS_FILEPATH)
    test(boot_args, ARG_SIM)


def test_frame_shorthand() -> None:
    _test_shorthand(FRAME_SHORTHAND, FRAME)


def test_gimbal_shorthand() -> None:
    _test_shorthand(GIMBAL_1_SHORTHAND, GIMBAL_1)


def test_scanner_shorthand() -> None:
    _test_shorthand(SCANNER_1_SHORTHAND, SCANNER_1)
