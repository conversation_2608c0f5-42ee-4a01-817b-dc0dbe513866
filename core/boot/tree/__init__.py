import functools
from typing import Any, Callable, Dict, Optional, cast

from core.boot.args import is_simulator
from core.boot.loader import Device<PERSON>reeBootLoader
from core.controls.driver.plan.field_location_notifier import FieldLocationNotifier
from core.controls.driver.plan.world_hud import WorldHUD
from core.model.node.base import Node
from core.model.node.type import NodeType
from core.model.path import DevicePath
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class DriverBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.driver.driver import Driver

        return Driver

    def _boot(self) -> Node:
        from core.controls.driver.driver import DriverConfiguration
        from lib.common.geo.boundary import Boundaries, GeofencesConfigFile
        from core.controls.driver.parameters import DEFAULT_DRIVER_PARAMETERS

        field_location_notifier: Optional[FieldLocationNotifier] = None
        world_hud: Optional[WorldHUD] = None
        if self._context.args.unity:
            from core.unity.drivers.world_annotate import Unity<PERSON>ieldLocationNotifier
            from core.unity.drivers.world_hud import UnityWorldHUD

            field_location_notifier = UnityFieldLocationNotifier()
            world_hud = UnityWorldHUD()

        boundaries: Boundaries
        if self._context.geofences_cfg_file is not None:
            boundaries = Boundaries(self._context.geofences_cfg_file)
        elif self._context.boundaries is not None:
            boundaries = Boundaries(boundaries=self._context.boundaries)
        else:
            boundaries = Boundaries(GeofencesConfigFile(config_dir=self.fs.abs_config_dir))

        parameters = DEFAULT_DRIVER_PARAMETERS.copy()

        driver_cfg_file = self._context.driver_cfg_file
        assert driver_cfg_file is not None

        return self._init_func()(
            filesystem=self.fs,
            feed=self.feed,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            subscriptions=self._get_subscriptions(),
            boundaries=boundaries,
            config=DriverConfiguration(cfg_file=driver_cfg_file, parameters=parameters),
            disable_auto_drive=self._context.args.no_auto_drive,
            no_gps=self._context.args.no_gps,
            field_location_notifier=field_location_notifier,
            world_hud=world_hud,
        )


class DriveSystemBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.driver.drive_system import DriveSystem

        return DriveSystem


class ExterminatorBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.exterminator.model.exterminator import Exterminator

        calib_cfg = self._context.calib_cfg.get(self.device_path)
        init_func = functools.partial(Exterminator, args=self._context.args, calib_cfg=calib_cfg)
        return init_func


class FrameBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.frame.frame import Frame

        return Frame

    def _boot(self) -> Node:
        from core.controls.frame.frame import FrameConfiguration
        from core.controls.frame.parameter import (
            DEFAULT_FRAME_PARAMETERS,
            DEFAULT_FRAME_PARAMETERS_UNITY,
            FrameParameter,
        )

        unity = self._context.args.unity
        parameters = DEFAULT_FRAME_PARAMETERS.copy()
        if unity:
            parameters.update(DEFAULT_FRAME_PARAMETERS_UNITY)
            parameters[FrameParameter.UNITY_GPS_OUTAGE_FREQ_MS].set_value(self._context.args.unity_gps_outage_freq_ms)
            parameters[FrameParameter.UNITY_GPS_OUTAGE_LENGTH_MS].set_value(
                self._context.args.unity_gps_outage_length_ms
            )
            parameters[FrameParameter.UNITY_HEADING_STD_DEV].set_value(self._context.args.unity_heading_std_dev)

        return self._init_func()(
            filesystem=self.fs,
            feed=self.feed,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            subscriptions=self._get_subscriptions(),
            config=FrameConfiguration(
                mechanical_config=self._context.mechanical_cfg,
                cfg_file=self._context.frame_cfg_file,
                parameters=parameters,
            ),
            unity=unity,
        )


class Gimbal2DBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.exterminator.model.gimbal import Gimbal2D

        return Gimbal2D


class CommanderBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.commander.node import CommanderNode

        return CommanderNode

    def _boot(self) -> Node:
        return self._init_func()(
            filesystem=self._context.fs,
            feed=self.feed,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            subscriptions=self._get_subscriptions(),
        )

    def _get_subscriptions(self) -> Dict[DevicePath, Node]:
        root_nodes = {path: reference for path, reference in self._context.tree.items() if path.depth == 1}
        return {**super()._get_subscriptions(), **root_nodes}


class VisualCortexBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.cv.visual_cortex.visual_cortex import VisualCortex

        return VisualCortex

    def _boot(self) -> Node:
        from core.cv.visual_cortex.graph.pathway_name import PathwayName

        registry = self._context.device_registry

        return self._init_func()(
            filesystem=self._context.fs,
            feed=self.feed,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            subscriptions=self._get_subscriptions(),
            nofx_device_id="nofx_board_1",
            device_registry=registry,
            enable_pathways=[PathwayName(p) for p in self._context.args.pathway],
            unity=self._context.args.unity,
            is_virtual=self._context.args.virtual is not None,
        )


class RetinaBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.cv.retina.retina import Retina

        return Retina


class RowModuleBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.exterminator.model.row_module import RowModule

        calib_cfg = self._context.calib_cfg.get(self.device_path)
        init_func = functools.partial(RowModule, args=self._context.args, calib_cfg=calib_cfg)
        return init_func


class ScannerBootLoader(DeviceTreeBootLoader):
    def _init_func(self) -> Callable[..., Node]:
        from core.controls.exterminator.model.scanner import Scanner

        calib_cfg = self._get_calib_cfg()
        init_func = functools.partial(Scanner, args=self._context.args, calib_cfg=calib_cfg)
        return init_func

    def _get_calib_cfg(self) -> Optional[Dict[DevicePath, Any]]:
        # TODO we should organize this better. Maybe develop per scanner syntax for command-line
        from core.controls.exterminator.model.scanner import CalibrationConfig

        if self._context.args.calibrate == "load":
            # loaded file
            LOG.info("{} Using loaded calibration config".format(self.device_path))
            sc_config = self._context.calib_cfg.get(self.device_path)

        elif self._context.args.calibrate == "new":
            # brand new config
            LOG.info("{} Creating new calibration config".format(self.device_path))
            sc_config = {}

        elif is_simulator(self._context.args.calibrate):
            LOG.info("{} Using simulated calibration config".format(self.device_path))
            # simulated calibration config
            from core.coords.strata import BivariateStrata
            from core.simulator.calibration_samples import SimCalibrationSamples
            from core.simulator.dimensions import SimRealWorldDimensions, SimServoDimensions
            from core.simulator.interpolation import SimStratifiedInterpolation, SimInterpolation
            from core.simulator.utils import IMAGE_LIMITS
            from core.controls.exterminator.model.gimbal import Gimbal2D

            crosshair = (960, 540)
            calib_samples = SimCalibrationSamples()
            pcam_limits = IMAGE_LIMITS
            predict_to_servos = SimInterpolation("sim.P->S", len(calib_samples.predict_to_servos))

            gimbal: Gimbal2D = cast(Gimbal2D, self._context.tree.get(self.device_path.join(NodeType.GIMBAL)))
            delta_target_to_delta_servos = SimStratifiedInterpolation(
                "sim.dT->dS",
                len(calib_samples.delta_target_to_delta_servos),
                BivariateStrata.from_servo_limits(gimbal.min, gimbal.max, 1, 1),
            )
            servo_tdims = SimServoDimensions()
            real_world_dims = SimRealWorldDimensions(servo_tdims=servo_tdims)

            sc_config = {
                CalibrationConfig.CROSSHAIR: crosshair,
                CalibrationConfig.SAMPLES: "SimPath",
                CalibrationConfig.PREDICT_IMAGE_LIMITS: pcam_limits,
                CalibrationConfig.SERVO_DIMENSIONS: servo_tdims,
                CalibrationConfig.GOTO_PREDICT: predict_to_servos,
                CalibrationConfig.GOTO_TARGET: delta_target_to_delta_servos,
                CalibrationConfig.REAL_WORLD_DIMENSIONS: real_world_dims,
            }

        else:
            assert False, "Bad --calibrate argument: {}. This is a bug and should have been caught earlier".format(
                self._context.args.calibrate
            )

        assert sc_config is None or isinstance(sc_config, dict)
        return sc_config
