from typing import Any, Callable, Dict, Optional, cast

from core.boot.args import ARG_SIMULATOR, ARG_UNITY, ARG_VIRTUAL, is_simulator, is_unity, is_virtual
from core.boot.device import (
    AxisEthernetBootLoader,
    BufferCamBootLoader,
    EmergentCamBootLoader,
    ExpressBootLoader,
    ExpressOnlyFailBootLoader,
    HardwareAimbotProcessGimbalBootLoader,
    HardwareAimbotProcessLensBootLoader,
    HardwareDriveMotorCypressBootLoader,
    HardwareGPSBenjaminBootLoader,
    HardwareINSDuroBootLoader,
    HardwareLaserCypressBootLoader,
    HardwareLensCorningBootLoader,
    HardwareLensEmergentBootLoader,
    HardwareLensKayaBootLoader,
    HardwarePulczarBoardGimbalBootLoader,
    HardwarePulczarBoardLaserBootLoader,
    HardwarePulczarBoardLensBootLoader,
    HardwarePulczarBoardServoBootLoader,
    HardwareRowModuleBoardGimbalBootLoader,
    HardwareRowModuleBoardLaserBootLoader,
    HardwareRowModuleBoardServoBootLoader,
    KayaCamBootLoader,
    LatestBufferCamBootLoader,
    NoFXFuelSensorBootLoader,
    NoFXParkingBrakeBootLoader,
    NoFXRotaryEncoderBootLoader,
    PylonCamBootLoader,
    SimCamBootLoader,
    SimDriveMotorBootLoader,
    SimEngineControlUnitBootLoader,
    SimFuelSensorBootLoader,
    SimGPSBootLoader,
    SimHydraulicsBootLoader,
    SimIMUBootLoader,
    SimINSBootLoader,
    SimLaserBootLoader,
    SimLEDBootLoader,
    SimLensNodeBootLoader,
    SimParkingBrakeBootLoader,
    SimRotaryEncoderNodeBootLoader,
    SimRotaryNodeBootLoader,
    SimServoBootLoader,
    ThinkLucidCamBootLoader,
    UnityCameraBootLoader,
    UnityHydraulicsBootLoader,
    UnityIMUNodeBootLoader,
    UnityINSNodeBootLoader,
    UnityMotorBootLoader,
    UnityRotaryBootLoader,
    UnityRotaryEncoderBootLoader,
    VirtualCamBootLoader,
    VirtualLaserBootLoader,
    VirtualLensNodeBootLoader,
    VirtualServoBootLoader,
    ZedCamBootLoader,
)
from core.boot.device.subscriber_channel import ShmemProxyINSNodeBootLoader
from core.boot.tree import (
    CommanderBootLoader,
    DriverBootLoader,
    DriveSystemBootLoader,
    ExterminatorBootLoader,
    FrameBootLoader,
    Gimbal2DBootLoader,
    RetinaBootLoader,
    RowModuleBootLoader,
    ScannerBootLoader,
    VisualCortexBootLoader,
)
from core.config.hardware import (
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_AXIS_ETHERNET,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_BUFFER,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_EMERGENT,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_IDENTITY,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_KAYA,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_LATEST_BUFFER,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_PYLON,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_THINKLUCID,
    HW_CONFIG_KEY_CAMERA_BOOTLOADER_ZED,
    HW_CONFIG_KEY_DRIVE_MOTOR_BOOTLOADER_SERIAL_CYPRESS,
    HW_CONFIG_KEY_EXPRESS_ONLY,
    HW_CONFIG_KEY_FUEL_SENSOR_NOFX,
    HW_CONFIG_KEY_GIMBAL_BOOTLOADER_AIMBOT_PROCESS,
    HW_CONFIG_KEY_GIMBAL_BOOTLOADER_PULCZAR_BOARD,
    HW_CONFIG_KEY_GIMBAL_BOOTLOADER_ROW_MODULE_BOARD,
    HW_CONFIG_KEY_GPS_BOOTLOADER_BENJAMIN,
    HW_CONFIG_KEY_INS_BOOTLOADER_DURO,
    HW_CONFIG_KEY_LASER_BOOTLOADER_PULCZAR_BOARD,
    HW_CONFIG_KEY_LASER_BOOTLOADER_ROW_MODULE_BOARD,
    HW_CONFIG_KEY_LASER_BOOTLOADER_SERIAL_CYPRESS,
    HW_CONFIG_KEY_LENS_BOOTLOADER_AIMBOT_PROCESS,
    HW_CONFIG_KEY_LENS_BOOTLOADER_CORNING,
    HW_CONFIG_KEY_LENS_BOOTLOADER_EMERGENT,
    HW_CONFIG_KEY_LENS_BOOTLOADER_KAYA,
    HW_CONFIG_KEY_LENS_BOOTLOADER_PULCZAR_BOARD,
    HW_CONFIG_KEY_PARKING_BRAKE_NOFX,
    HW_CONFIG_KEY_ROTARY_ENCODER_NOFX,
    HW_CONFIG_KEY_SERVO_BOOTLOADER_PULCZAR_BOARD,
    HW_CONFIG_KEY_SERVO_BOOTLOADER_ROW_MODULE_BOARD,
)
from core.model.depth_class import NodeDepthClass
from core.model.node.base import Node
from core.model.node.type import NodeType

# Broadly speaking, there are four different ways to boot a leaf node:
#  - hardware
#  - simulator
#  - virtual
#  - unity
#
# And there is one way to boot internal nodes like Driver/Exterminator.


class LeafBootLoaderType:
    SUBSCRIBER_CHANNEL = "subscriber_channel"
    HARDWARE = "hardware"
    SIMULATOR = ARG_SIMULATOR
    VIRTUAL = ARG_VIRTUAL
    UNITY = ARG_UNITY


######################################################################################################
# Leaf Node: Subscriber Channel Bootloaders
# these are generally just proxies to a publisher channel node / process
######################################################################################################
SUBSCRIBER_CHANNEL_BOOTLOADER_REGISTRY: Dict[NodeType, Dict[str, Callable[..., Any]]] = {
    NodeType.INS: {"shmem": ShmemProxyINSNodeBootLoader}
}

######################################################################################################
# Leaf Node: Hardware Bootloaders
######################################################################################################
HARDWARE_BOOTLOADER_REGISTRY: Dict[NodeType, Dict[str, Callable[..., Any]]] = {
    NodeType.CAMERA: {
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_IDENTITY: PylonCamBootLoader,  # TODO remove (backwards compat)
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_PYLON: PylonCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_THINKLUCID: ThinkLucidCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_AXIS_ETHERNET: AxisEthernetBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_ZED: ZedCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_EMERGENT: EmergentCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_KAYA: KayaCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_BUFFER: BufferCamBootLoader,
        HW_CONFIG_KEY_CAMERA_BOOTLOADER_LATEST_BUFFER: LatestBufferCamBootLoader,
    },
    NodeType.DRIVE_MOTOR: {HW_CONFIG_KEY_DRIVE_MOTOR_BOOTLOADER_SERIAL_CYPRESS: HardwareDriveMotorCypressBootLoader},
    NodeType.HYDRAULICS: {HW_CONFIG_KEY_EXPRESS_ONLY: ExpressOnlyFailBootLoader},
    NodeType.ROTARY_ENCODER: {HW_CONFIG_KEY_ROTARY_ENCODER_NOFX: NoFXRotaryEncoderBootLoader},
    NodeType.FUEL_SENSOR: {HW_CONFIG_KEY_FUEL_SENSOR_NOFX: NoFXFuelSensorBootLoader},
    NodeType.PARKING_BRAKE: {HW_CONFIG_KEY_PARKING_BRAKE_NOFX: NoFXParkingBrakeBootLoader},
    NodeType.GPS: {HW_CONFIG_KEY_GPS_BOOTLOADER_BENJAMIN: HardwareGPSBenjaminBootLoader},
    NodeType.INS: {HW_CONFIG_KEY_INS_BOOTLOADER_DURO: HardwareINSDuroBootLoader},
    NodeType.LASER: {
        HW_CONFIG_KEY_LASER_BOOTLOADER_SERIAL_CYPRESS: HardwareLaserCypressBootLoader,
        HW_CONFIG_KEY_LASER_BOOTLOADER_ROW_MODULE_BOARD: HardwareRowModuleBoardLaserBootLoader,
        HW_CONFIG_KEY_LASER_BOOTLOADER_PULCZAR_BOARD: HardwarePulczarBoardLaserBootLoader,
    },
    NodeType.LENS: {
        HW_CONFIG_KEY_LENS_BOOTLOADER_CORNING: HardwareLensCorningBootLoader,
        HW_CONFIG_KEY_LENS_BOOTLOADER_EMERGENT: HardwareLensEmergentBootLoader,
        HW_CONFIG_KEY_LENS_BOOTLOADER_KAYA: HardwareLensKayaBootLoader,
        HW_CONFIG_KEY_LENS_BOOTLOADER_PULCZAR_BOARD: HardwarePulczarBoardLensBootLoader,
        HW_CONFIG_KEY_LENS_BOOTLOADER_AIMBOT_PROCESS: HardwareAimbotProcessLensBootLoader,
    },
    NodeType.SERVO: {
        HW_CONFIG_KEY_SERVO_BOOTLOADER_ROW_MODULE_BOARD: HardwareRowModuleBoardServoBootLoader,
        HW_CONFIG_KEY_SERVO_BOOTLOADER_PULCZAR_BOARD: HardwarePulczarBoardServoBootLoader,
    },
    NodeType.GIMBAL: {
        HW_CONFIG_KEY_GIMBAL_BOOTLOADER_ROW_MODULE_BOARD: HardwareRowModuleBoardGimbalBootLoader,
        HW_CONFIG_KEY_GIMBAL_BOOTLOADER_PULCZAR_BOARD: HardwarePulczarBoardGimbalBootLoader,
        HW_CONFIG_KEY_GIMBAL_BOOTLOADER_AIMBOT_PROCESS: HardwareAimbotProcessGimbalBootLoader,
    },
}


######################################################################################################
# Leaf Node: Simulator Bootloaders
######################################################################################################
SIMULATOR_BOOTLOADER_REGISTRY: Dict[NodeType, Callable[..., Any]] = {
    NodeType.CAMERA: SimCamBootLoader,
    NodeType.DRIVE_MOTOR: SimDriveMotorBootLoader,
    NodeType.ECU: SimEngineControlUnitBootLoader,
    NodeType.HYDRAULICS: SimHydraulicsBootLoader,
    NodeType.GPS: SimGPSBootLoader,
    NodeType.IMU: SimIMUBootLoader,
    NodeType.INS: SimINSBootLoader,
    NodeType.LASER: SimLaserBootLoader,
    NodeType.LED: SimLEDBootLoader,
    NodeType.LENS: SimLensNodeBootLoader,
    NodeType.SERVO: SimServoBootLoader,
    NodeType.GIMBAL: Gimbal2DBootLoader,
    NodeType.ROTARY: SimRotaryNodeBootLoader,
    NodeType.ROTARY_ENCODER: SimRotaryEncoderNodeBootLoader,
    NodeType.PARKING_BRAKE: SimParkingBrakeBootLoader,
    NodeType.FUEL_SENSOR: SimFuelSensorBootLoader,
}
# validation
for t in NodeType.leaves():
    assert t in SIMULATOR_BOOTLOADER_REGISTRY, f"Missing simulator bootloader entry: {t}"
for t in SIMULATOR_BOOTLOADER_REGISTRY:
    assert t.is_leaf or t.is_dynamic_internal


######################################################################################################
# Leaf Node: Virtual Bootloaders
######################################################################################################
VIRTUAL_BOOTLOADER_REGISTRY: Dict[NodeType, Callable[..., Any]] = {
    NodeType.CAMERA: VirtualCamBootLoader,
    NodeType.LASER: VirtualLaserBootLoader,
    NodeType.LENS: VirtualLensNodeBootLoader,
    NodeType.SERVO: VirtualServoBootLoader,
}
# validation
for t in VIRTUAL_BOOTLOADER_REGISTRY:
    assert t.is_leaf


######################################################################################################
# Leaf Node: Unity Bootloaders
######################################################################################################
UNITY_BOOTLOADER_REGISTRY: Dict[NodeType, Callable[..., Any]] = {
    NodeType.DRIVE_MOTOR: UnityMotorBootLoader,
    NodeType.CAMERA: UnityCameraBootLoader,
    NodeType.IMU: UnityIMUNodeBootLoader,
    NodeType.INS: UnityINSNodeBootLoader,
    NodeType.HYDRAULICS: UnityHydraulicsBootLoader,
    NodeType.ROTARY: UnityRotaryBootLoader,
    NodeType.ROTARY_ENCODER: UnityRotaryEncoderBootLoader,
}
# validation
for t in UNITY_BOOTLOADER_REGISTRY:
    assert t.is_leaf or t.is_dynamic_internal


######################################################################################################
# Internal Node: Bootloaders
######################################################################################################
INTERNAL_NODE_BOOTLOADER_REGISTRY: Dict[NodeType, Callable[..., Any]] = {
    NodeType.DRIVER: DriverBootLoader,
    NodeType.DRIVE_SYSTEM: DriveSystemBootLoader,
    NodeType.EXTERMINATOR: ExterminatorBootLoader,
    NodeType.FRAME: FrameBootLoader,
    NodeType.GIMBAL: Gimbal2DBootLoader,
    NodeType.COMMANDER: CommanderBootLoader,
    NodeType.RETINA: RetinaBootLoader,
    NodeType.ROW_MODULE: RowModuleBootLoader,
    NodeType.SCANNER: ScannerBootLoader,
    NodeType.VISUAL_CORTEX: VisualCortexBootLoader,
}
# validation
for t in NodeType.internal_nodes():
    assert t in INTERNAL_NODE_BOOTLOADER_REGISTRY or t.is_dynamic_internal, f"Missing simulator bootloader entry: {t}"
for t in INTERNAL_NODE_BOOTLOADER_REGISTRY:
    assert not t.is_leaf


######################################################################################################
# Master Bootloader list
######################################################################################################
MASTER_BOOTLOADER_REGISTRY: Dict[NodeDepthClass, Any] = {
    NodeDepthClass.INTERNAL: INTERNAL_NODE_BOOTLOADER_REGISTRY,
    NodeDepthClass.LEAF: {
        LeafBootLoaderType.SUBSCRIBER_CHANNEL: SUBSCRIBER_CHANNEL_BOOTLOADER_REGISTRY,
        LeafBootLoaderType.HARDWARE: HARDWARE_BOOTLOADER_REGISTRY,
        LeafBootLoaderType.SIMULATOR: SIMULATOR_BOOTLOADER_REGISTRY,
        LeafBootLoaderType.VIRTUAL: VIRTUAL_BOOTLOADER_REGISTRY,
        LeafBootLoaderType.UNITY: UNITY_BOOTLOADER_REGISTRY,
    },
}


def get_bootloader(node_type: NodeType, bootloader_reference_key: Optional[str] = None) -> Callable[..., Node]:
    """
    Return the bootloader for the given node type based on the given bootloader reference key (for leaves)
    """
    global MASTER_BOOTLOADER_REGISTRY

    assert node_type.depth_class in [NodeDepthClass.LEAF, NodeDepthClass.INTERNAL]
    depth_class_bootloaders = MASTER_BOOTLOADER_REGISTRY[node_type.depth_class]

    if node_type.depth_class == NodeDepthClass.INTERNAL or bootloader_reference_key is None:
        result = INTERNAL_NODE_BOOTLOADER_REGISTRY[node_type]
    elif is_simulator(bootloader_reference_key):
        result = depth_class_bootloaders[LeafBootLoaderType.SIMULATOR][node_type]
    elif is_virtual(bootloader_reference_key):
        result = depth_class_bootloaders[LeafBootLoaderType.VIRTUAL][node_type]
    elif is_unity(bootloader_reference_key):
        result = depth_class_bootloaders[LeafBootLoaderType.UNITY][node_type]
    elif bootloader_reference_key == "shmem":
        result = depth_class_bootloaders[LeafBootLoaderType.SUBSCRIBER_CHANNEL][node_type][bootloader_reference_key]
    elif bootloader_reference_key == "express":
        result = ExpressBootLoader
    else:
        result = depth_class_bootloaders[LeafBootLoaderType.HARDWARE][node_type][bootloader_reference_key]

    assert callable(result)
    return cast(Callable[..., Node], result)
