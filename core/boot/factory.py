from typing import Type

from core.boot.node import N, RootN<PERSON>BootLoader
from core.boot.robot import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oader
from core.boot.root_device import SingleNodeBootLoader
from core.model.node.type import NodeType
from core.model.path import DevicePath


class UnexpectedRootNodeException(Exception):
    pass


class RootNodeBootLoaderFactory:
    @staticmethod
    def get_boot_loader_init(root: DevicePath) -> Type[RootNodeBootLoader[N]]:
        if root == DevicePath.robot():
            return RobotNodeBootLoader  # type: ignore
        elif root == DevicePath.robot().join(NodeType.FRAME).join(NodeType.INS):
            return SingleNodeBootLoader  # type: ignore

        raise UnexpectedRootNodeException(f"Unexpected root node: {root}")
