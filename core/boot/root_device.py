from typing import Any

from core.boot.context import Shared<PERSON>ontext
from core.boot.node import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oader
from core.model.node.base import Node
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class MultipleNodesDetectedException(Exception):
    pass


class SingleNodeBootLoader(RootNodeBootLoader[Node]):
    """
    Yes that's right. The root is a leaf. a.k.a a single node.
    """

    def __init__(self, *argv: Any, **kwargs: Any):
        super().__init__(*argv, **kwargs)
        self._attached = False

    def _attach_to_context(self) -> None:
        # skip parent behavior if we've actually already booted
        # this will go away when we build a bit better graph model into the bootloader
        assert self._object is not None
        assert isinstance(self._object, Node)
        if self._object.device_path != self._root:
            raise MultipleNodesDetectedException(
                f"SingleNodeBootLoader should not be booting: {self._object.device_path} if root: {self._root}"
            )

        # workaround until a more complete refactor of the boot plan for the graph
        self._attached = self._root in self._context.tree
        if not self._attached:
            super()._attach_to_context()
            self._attached = self._root in self._context.tree
            assert self._attached  # sanity check
        else:
            # TODO this will fire off until we refactor the bootloader better
            LOG.warning("Duplicate attempt to attach: ")

    def _boot_root_from_context(self, context: SharedContext) -> Node:
        return context.tree[self._root]
