from typing import cast

from core.boot.device import Any, HardwareConfigEntryBootLoader
from core.controls.frame.model.topics import FrameTopic
from core.model.node.base import Node


class ShmemProxyINSNodeBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.frame.sensor.ins.node import SubscriptionProxyINSNode

        super().__init__(
            device_init_func=SubscriptionProxyINSNode, **kwargs,
        )

    def _boot(self) -> Node:
        from core.controls.frame.sensor.ins.node import SubscriptionProxyINSNode
        from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
        from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
        from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
        from generated.core.controls.frame.model.mag_message import MagMessage
        from generated.core.controls.frame.model.velocity_message import VelocityMessage
        from lib.common.protocol.channel.base import Topic
        from lib.common.protocol.channel.shmem import ShmemSubscriberChannel

        # TODO integrate fabric.yaml to abstract away type / format of channel

        base_topic: Topic = self.device_path.topic

        self.feed.publish(
            base_topic.sub(FrameTopic.ANGULAR_VELOCITY),
            ShmemSubscriberChannel(
                topic=base_topic.sub(FrameTopic.ANGULAR_VELOCITY),
                deserialize=lambda m: AngularVelocityMessage.from_bytes(m),
                file_suffix=".pb",
            ),
        )
        self.feed.publish(
            base_topic.sub(FrameTopic.GEOPOSITION_ECEF),
            ShmemSubscriberChannel(
                topic=base_topic.sub(FrameTopic.GEOPOSITION_ECEF),
                deserialize=lambda m: GeopositionEcefMessage.from_bytes(m),
                file_suffix=".pb",
            ),
        )
        self.feed.publish(
            base_topic.sub(FrameTopic.GEOPOSITION_LLA),
            ShmemSubscriberChannel(
                topic=base_topic.sub(FrameTopic.GEOPOSITION_LLA),
                deserialize=lambda m: GeopositionLatLonAltMessage.from_bytes(m),
                file_suffix=".pb",
            ),
        )
        self.feed.publish(
            base_topic.sub(FrameTopic.MAGNETOMETER),
            ShmemSubscriberChannel(
                topic=base_topic.sub(FrameTopic.MAGNETOMETER),
                deserialize=lambda m: MagMessage.from_bytes(m),
                file_suffix=".pb",
            ),
        )
        self.feed.publish(
            base_topic.sub(FrameTopic.VELOCITY_ECEF),
            ShmemSubscriberChannel(
                topic=base_topic.sub(FrameTopic.VELOCITY_ECEF),
                deserialize=lambda m: VelocityMessage.from_bytes(m),
                file_suffix=".pb",
            ),
        )

        assert self._device_init_func is not None
        ins_node: SubscriptionProxyINSNode = cast(
            SubscriptionProxyINSNode,
            self._device_init_func(
                filesystem=self.fs, feed=self.feed, ref_id=self._ref_id, device_path=self.device_path
            ),
        )
        return ins_node
