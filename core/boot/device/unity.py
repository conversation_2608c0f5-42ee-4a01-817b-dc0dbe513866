import functools
from typing import Any

from core.boot.device.hardware import CameraHardwareConfigEntryBootLoader, HardwareConfigEntryBootLoader
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class UnityMotorBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_motor import UnityMotor

        super().__init__(device_init_func=UnityMotor, **kwargs)


class UnityCameraBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_camera import UnityCamera

        super().__init__(device_init_func=UnityCamera, **kwargs)


class UnityIMUNodeBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_imu import UnityIMUNode

        super().__init__(device_init_func=UnityIMUNode, **kwargs)


class UnityINSNodeBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_ins import UnityINSNode

        super().__init__(device_init_func=UnityINSNode, **kwargs)
        self._device_init_func = functools.partial(
            UnityINSNode,
            gps_outage_freq_ms=self._context.args.unity_gps_outage_freq_ms,
            gps_outage_length_ms=self._context.args.unity_gps_outage_length_ms,
        )


class UnityHydraulicsBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_hydraulics import UnityHydraulics

        super().__init__(device_init_func=UnityHydraulics, **kwargs)


class UnityRotaryBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_helac import UnityHelac

        super().__init__(device_init_func=UnityHelac, **kwargs)


class UnityRotaryEncoderBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.unity.drivers.unity_rotary_encoder import UnityRotaryEncoder

        super().__init__(device_init_func=UnityRotaryEncoder, **kwargs)
