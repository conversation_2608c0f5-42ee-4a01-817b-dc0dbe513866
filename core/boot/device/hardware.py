import importlib
from abc import ABC
from typing import Any, Callable, Dict, List, cast

from core.boot.loader import Device<PERSON><PERSON><PERSON><PERSON>Loader
from core.model.id import ReferenceId
from core.model.node.base import Node
from lib.common.logging import get_logger

LOG = get_logger(__name__)

HardwareConfigType = Dict[ReferenceId, Dict[str, Any]]


class ExpressBootLoader(DeviceTreeBootLoader):
    def __init__(self, hw_cfg_entry: Dict[str, Any], **kwargs: Any):
        module_name = hw_cfg_entry["module"]
        func_name = hw_cfg_entry["func"]
        self._kwargs = hw_cfg_entry["kwargs"]
        LOG.info(f"Express bootloading {module_name} {func_name}")
        module = importlib.import_module(module_name)
        device_init_func = getattr(module, func_name)
        self._hw_cfg_entry = hw_cfg_entry
        super().__init__(device_init_func=device_init_func, **kwargs)

    def _boot(self) -> Node:
        assert self._device_init_func is not None  # pacify MyPy
        try:
            device = self._device_init_func(
                filesystem=self.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                subtree=self._get_subtree(),
                **self._kwargs,
            )
        except Exception:
            LOG.error(f"Failure booting: {self._device_init_func} with entry: {self._hw_cfg_entry}")
            raise
        return device

    def _init_func(self) -> Callable[..., Node]:
        pass


class HardwareConfigEntryBootLoader(DeviceTreeBootLoader, ABC):
    def __init__(self, hw_cfg_entry: Dict[str, Any], **kwargs: Any):
        super().__init__(**kwargs)

        assert hw_cfg_entry is not None and isinstance(hw_cfg_entry, dict)
        self._hw_cfg_entry = hw_cfg_entry

    def _boot(self) -> Node:
        assert self._device_init_func is not None  # leftover from when Devices were a thing
        device = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            **self._hw_cfg_entry,
        )
        return device

    def _init_func(self) -> Callable[..., Node]:
        # devices still use there own init func pattern
        pass


class CameraHardwareConfigEntryBootLoader(HardwareConfigEntryBootLoader):
    """
    Adds handing of image path and camera args to HardwareConfigEntryBootLoader
    """

    def _boot(self) -> Node:
        """Need to override _boot because default doesn't pass camera args"""
        cam_args = self._hw_cfg_entry.get("args", {})
        retina_args = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})
        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            **self._hw_cfg_entry,
            **cam_args,
            **retina_args,
        )
        return cam


##############################
# Camera
##############################
class PylonCamBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.pylon.node import PylonCam

        super().__init__(device_init_func=PylonCam, **kwargs)

    def _boot(self) -> Node:
        from lib.drivers.pylon.runner import pylonctl

        pylon_device = pylonctl.get_device_by_identity(self._hw_cfg_entry)
        assert pylon_device is not None, "Could not find pylon device matching: {}".format(self._hw_cfg_entry)
        cam_args = self._hw_cfg_entry.get("args", {})

        retina_args = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})

        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            pylon_device=pylon_device,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            shmem_enabled=self._context.args.shmem_buffer,
            **cam_args,
            **retina_args,
        )
        return cam


class BufferCamBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.buffer.node import BufferCam

        super().__init__(device_init_func=BufferCam, **kwargs)

    def _boot(self) -> Node:
        cam_args = self._hw_cfg_entry.get("args", {})
        retina_args = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})

        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            **cam_args,
            **retina_args,
        )
        return cam


class LatestBufferCamBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.buffer.node import LatestBufferCam

        super().__init__(device_init_func=LatestBufferCam, **kwargs)

    def _boot(self) -> Node:
        cam_args = self._hw_cfg_entry.get("args", {})
        retina_args = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})

        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            **cam_args,
            **retina_args,
        )
        return cam


class AxisEthernetBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.axis.ethernet import AxisEthernetCam

        super().__init__(device_init_func=AxisEthernetCam, **kwargs)


class ZedCamBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.zedcam.node import ZedCam

        super().__init__(device_init_func=ZedCam, **kwargs)

    def _boot(self) -> Node:
        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            shmem_enabled=self._context.args.shmem_buffer,
            **self._hw_cfg_entry,
        )
        return cam


class EmergentCamBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.emergent.node import EmergentCam

        super().__init__(device_init_func=EmergentCam, **kwargs)


class KayaCamBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.kaya.node import KayaCam

        super().__init__(device_init_func=KayaCam, **kwargs)


class ThinkLucidCamBootLoader(CameraHardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.thinklucid.node import ThinkLucidCam

        super().__init__(device_init_func=ThinkLucidCam, **kwargs)


##############################
# Drive Motor
##############################


class HardwareDriveMotorCypressBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.drive_motor.seedy import CypressDriveMotor

        super().__init__(device_init_func=CypressDriveMotor, **kwargs)


##############################
# GPS
##############################
class HardwareGPSBenjaminBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.gps.benjamin_gps.node import BenjaminGPSNode

        super().__init__(device_init_func=BenjaminGPSNode, **kwargs)


##############################
# INS
##############################
class HardwareINSDuroBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.ins.duro_inertial.node import DuroInertialINSNode

        super().__init__(device_init_func=DuroInertialINSNode, **kwargs)

    def _boot(self) -> Node:
        import functools
        from core.drivers.ins.duro_inertial.duro import DuroInertialNavigationSystem
        from core.drivers.ins.duro_inertial.node import DuroInertialINSNode
        from core.controls.frame.model.topics import FrameTopic
        from generated.core.controls.frame.model.acceleration_message import AccelerationMessage
        from generated.core.controls.frame.model.angular_velocity_message import AngularVelocityMessage
        from generated.core.controls.frame.model.geoposition_ecef_message import GeopositionEcefMessage
        from generated.core.controls.frame.model.geoposition_lat_lon_alt_message import GeopositionLatLonAltMessage
        from generated.core.controls.frame.model.mag_message import MagMessage
        from generated.core.controls.frame.model.velocity_message import VelocityMessage
        from lib.common.protocol.channel.base import PublisherChannel, Topic
        from lib.common.protocol.channel.shmem import ShmemPublisherChannel
        from lib.drivers.swift_nav.driver import DuroInertialTCPDriver
        from lib.drivers.swift_nav.message import SBPMessage

        # TODO integrate fabric.yaml to abstract away type / format of channel

        def msg_to_binary(msg: Any) -> bytes:
            return msg.to_bytes()  # type: ignore

        base_topic: Topic = self.device_path.topic
        shmem_channel = functools.partial(ShmemPublisherChannel, serialize=msg_to_binary, file_suffix=".pb")

        # INS
        accel_shmem_channel: PublisherChannel[AccelerationMessage] = shmem_channel(topic=base_topic.sub("acceleration"))
        angular_vel_shmem_channel: PublisherChannel[AngularVelocityMessage] = shmem_channel(
            topic=base_topic.sub(FrameTopic.ANGULAR_VELOCITY)
        )
        geoposition_ecef_shmem_channel: PublisherChannel[GeopositionEcefMessage] = shmem_channel(
            topic=base_topic.sub(FrameTopic.GEOPOSITION_ECEF)
        )
        geoposition_lla_shmem_channel: PublisherChannel[GeopositionLatLonAltMessage] = shmem_channel(
            topic=base_topic.sub(FrameTopic.GEOPOSITION_LLA)
        )
        mag_shmem_channel: PublisherChannel[MagMessage] = shmem_channel(topic=base_topic.sub(FrameTopic.MAGNETOMETER))
        vel_shmem_channel: PublisherChannel[VelocityMessage] = shmem_channel(
            topic=base_topic.sub(FrameTopic.VELOCITY_ECEF)
        )

        accel_publishers = [accel_shmem_channel]
        angular_vel_publishers = [angular_vel_shmem_channel]
        geoposition_ecef_publishers = [geoposition_ecef_shmem_channel]
        geoposition_lla_publishers = [geoposition_lla_shmem_channel]
        mag_publishers = [mag_shmem_channel]
        vel_publishers = [vel_shmem_channel]

        # Duro-specific (empty list for now - was originally used for shmem until INS topic messages took over)
        sbp_publishers: List[PublisherChannel[SBPMessage]] = []

        driver = DuroInertialTCPDriver(
            base_topic=self.device_path.topic,
            host=self._hw_cfg_entry["ip"],
            port=self._hw_cfg_entry["port"],
            sbp_publishers=sbp_publishers,
            accel_publishers=accel_publishers,
            angular_vel_publishers=angular_vel_publishers,
            geoposition_ecef_publishers=geoposition_ecef_publishers,
            geoposition_lla_publishers=geoposition_lla_publishers,
            mag_publishers=mag_publishers,
            vel_publishers=vel_publishers,
        )
        duro_ins = DuroInertialNavigationSystem(
            imu_raw_subscription=driver.get_imu_raw_subscription(),
            mag_raw_subscription=driver.get_mag_raw_subscription(),
            pos_ecef_subscription=driver.get_pos_ecef_subscription(),
            vel_ecef_subscription=driver.get_vel_ecef_subscription(),
        )
        assert self._device_init_func is not None
        duro_node: DuroInertialINSNode = cast(
            DuroInertialINSNode,
            self._device_init_func(
                filesystem=self.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                driver=driver,
                ins_interface=duro_ins,
            ),
        )
        return duro_node


##############################
# Laser
##############################
class HardwareLaserCypressBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.laser.seedy import CypressLaser

        super().__init__(device_init_func=CypressLaser, **kwargs)


class HardwareRowModuleBoardLaserBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardLaser import RowModuleBoardLaser

        super().__init__(device_init_func=RowModuleBoardLaser, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardLaser import RowModuleBoardLaser

        assert self._device_init_func is not None
        return cast(
            RowModuleBoardLaser,
            self._device_init_func(
                filesystem=self.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwarePulczarBoardLaserBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_laser import PulczarBoardLaser

        super().__init__(device_init_func=PulczarBoardLaser, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_laser import PulczarBoardLaser

        assert self._device_init_func is not None
        return cast(
            PulczarBoardLaser,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


##############################
# Lens
##############################
class HardwareLensCorningBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.lens.corning.node import LiquidLensNodeDriver

        super().__init__(device_init_func=LiquidLensNodeDriver, **kwargs)

    def _boot(self) -> Node:
        assert self._device_init_func is not None
        device = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            context=self._context,
            **self._hw_cfg_entry,
        )
        return device


class HardwareLensEmergentBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.emergent.node import EmergentLensNodeDriver

        super().__init__(device_init_func=EmergentLensNodeDriver, **kwargs)

    def _boot(self) -> Node:
        from core.drivers.camera.emergent.node import EmergentCam

        camera_id = self.device_path.leaf().instance
        camera = self._context.reference_pool.get(ReferenceId(camera_id))
        assert camera is not None, f"Cound not find camera {camera_id}"
        assert isinstance(camera, EmergentCam)

        assert self._device_init_func is not None  # leftover from when Devices were a thing
        device = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            camera=camera,
            context=self._context,
            **self._hw_cfg_entry,
        )
        return device


class HardwareLensKayaBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.camera.kaya.node import KayaLensNodeDriver

        super().__init__(device_init_func=KayaLensNodeDriver, **kwargs)

    def _boot(self) -> Node:
        from core.drivers.camera.kaya.node import KayaCam

        camera_id = self.device_path.leaf().instance
        camera = self._context.reference_pool.get(ReferenceId(camera_id))
        assert camera is not None, f"Cound not find camera {camera_id}"
        assert isinstance(camera, KayaCam)

        assert self._device_init_func is not None  # leftover from when Devices were a thing
        device = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            camera=camera,
            context=self._context,
            **self._hw_cfg_entry,
        )
        return device


class HardwarePulczarBoardLensBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.lens.pulczar_board.PulczarBoardLens import PulczarBoardLens

        super().__init__(device_init_func=PulczarBoardLens, **kwargs)

    def _boot(self) -> Node:
        from core.drivers.lens.pulczar_board.PulczarBoardLens import PulczarBoardLens

        assert self._device_init_func is not None

        return cast(
            PulczarBoardLens,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                device_registry=self._context.device_registry,
                subtree=self._get_subtree(),
                context=self._context,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwareAimbotProcessLensBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.drivers.lens.aimbot_process.aimbot_process_lens import AimbotProcessLens

        super().__init__(device_init_func=AimbotProcessLens, **kwargs)

    def _boot(self) -> Node:
        from core.drivers.lens.aimbot_process.aimbot_process_lens import AimbotProcessLens

        assert self._device_init_func is not None

        return cast(
            AimbotProcessLens,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                subtree=self._get_subtree(),
                context=self._context,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwareRowModuleBoardServoBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardServo import RowModuleBoardServo

        super().__init__(device_init_func=RowModuleBoardServo, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardServo import RowModuleBoardServo

        assert self._device_init_func is not None
        return cast(
            RowModuleBoardServo,
            self._device_init_func(
                filesystem=self.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwarePulczarBoardServoBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_servo import PulczarBoardServo

        super().__init__(device_init_func=PulczarBoardServo, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_servo import PulczarBoardServo

        assert self._device_init_func is not None
        return cast(
            PulczarBoardServo,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


##############################
# Gimbal
##############################


class HardwareRowModuleBoardGimbalBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardGimbal import RowModuleBoardGimbal

        super().__init__(device_init_func=RowModuleBoardGimbal, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.row_module_board.RowModuleBoardGimbal import RowModuleBoardGimbal

        assert self._device_init_func is not None
        return cast(
            RowModuleBoardGimbal,
            self._device_init_func(
                filesystem=self.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                subtree=self._get_subtree(),
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwarePulczarBoardGimbalBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_gimbal import PulczarBoardGimbal

        super().__init__(device_init_func=PulczarBoardGimbal, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.pulczar_board.pulczar_board_gimbal import PulczarBoardGimbal

        assert self._device_init_func is not None
        return cast(
            PulczarBoardGimbal,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                subtree=self._get_subtree(),
                device_registry=self._context.device_registry,
                **self._hw_cfg_entry["kwargs"],
            ),
        )


class HardwareAimbotProcessGimbalBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal import AimbotProcessGimbal

        super().__init__(device_init_func=AimbotProcessGimbal, **kwargs)

    def _boot(self) -> Node:
        from core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal import AimbotProcessGimbal

        assert self._device_init_func is not None
        return cast(
            AimbotProcessGimbal,
            self._device_init_func(
                filesystem=self._context.fs,
                feed=self.feed,
                ref_id=self._ref_id,
                device_path=self.device_path,
                subtree=self._get_subtree(),
                **self._hw_cfg_entry["kwargs"],
            ),
        )


##############################
# NoFX
##############################


class NoFXDeviceBootLoader(HardwareConfigEntryBootLoader):
    def _boot(self) -> Node:
        assert self._device_init_func is not None
        return self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            device_registry=self._context.device_registry,
            **self._hw_cfg_entry["kwargs"],
        )


class NoFXRotaryEncoderBootLoader(NoFXDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.driver.nofx.nofx_board import NoFXRotaryEncoder

        super().__init__(device_init_func=NoFXRotaryEncoder, **kwargs)


class NoFXParkingBrakeBootLoader(NoFXDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.driver.nofx.nofx_board import NoFXParkingBrake

        super().__init__(device_init_func=NoFXParkingBrake, **kwargs)


class NoFXFuelSensorBootLoader(NoFXDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.controls.driver.nofx.nofx_board import NoFXFuelSensor

        super().__init__(device_init_func=NoFXFuelSensor, **kwargs)


##############################
# Express only fail
##############################
class ExpressOnlyFailBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        raise Exception("This hardware device only supports express boot")
