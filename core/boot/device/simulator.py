import functools
from typing import Any, Dict, Optional

from core.boot.loader import SimulatedDeviceBootLoader
from core.model.node.base import Node
from core.model.path import RelativeInstance
from lib.common.logging import get_logger

LOG = get_logger(__name__)


class SimDriveMotorBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.drive_motor import SimDriveMotor

        super().__init__(device_init_func=SimDriveMotor, **kwargs)


class SimEngineControlUnitBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.engine_control_unit import SimEngineControlUnit

        super().__init__(device_init_func=SimEngineControlUnit, **kwargs)


class SimGPSBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        super().__init__(device_init_func=self._init_func, **kwargs)

    def _init_func(self, **kwargs: Any) -> Any:
        from core.simulator.gps import SimGPSNode

        return SimGPSNode(**kwargs)


class SimHydraulicsBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.hydraulics import SimHydraulics

        super().__init__(device_init_func=SimHydraulics, **kwargs)

    def _boot(self) -> Node:
        # TODO allow for customizing number of legs so we can try two legged hydraulics
        legs = RelativeInstance.quadrants()
        assert self._device_init_func is not None
        hydraulics = self._device_init_func(
            subtree=self._get_subtree(),
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            legs=legs,
        )
        return hydraulics


class SimIMUBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        super().__init__(device_init_func=self._init_func, **kwargs)

    def _init_func(self, **kwargs: Any) -> Any:
        from core.simulator.imu import SimIMUNode

        identity = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]
        return SimIMUNode(device_to_body_rotation=identity, **kwargs)


class SimINSBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        super().__init__(device_init_func=self._init_func, **kwargs)

    def _init_func(self, **kwargs: Any) -> Any:
        from core.simulator.ins import SimINSNode

        identity = [[1, 0, 0], [0, 1, 0], [0, 0, 1]]
        return SimINSNode(device_to_body_rotation=identity, **kwargs)


class SimLaserBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.laser import SimLaser

        super().__init__(device_init_func=SimLaser, **kwargs)


class SimLEDBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.led import SimLED

        super().__init__(device_init_func=SimLED, **kwargs)


class SimLensNodeBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.lens import SimLensNode

        super().__init__(device_init_func=SimLensNode, **kwargs)

    def _boot(self) -> Node:
        assert self._device_init_func is not None
        device = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            context=self._context,
        )
        return device


class SimParkingBrakeBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.parking_brake import SimParkingBrake

        super().__init__(device_init_func=SimParkingBrake, **kwargs)


class SimFuelSensorBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.fuel_sensor import SimFuelSensor

        super().__init__(device_init_func=SimFuelSensor, **kwargs)


class SimRotaryNodeBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.rotary import SimRotaryNode

        super().__init__(device_init_func=SimRotaryNode, **kwargs)


class SimRotaryEncoderNodeBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.rotary_encoder import SimRotaryEncoderNode

        super().__init__(device_init_func=SimRotaryEncoderNode, **kwargs)


# TODO we should build a standardized way to bring in hardware-type args into simulated devices
class SimServoBootLoader(SimulatedDeviceBootLoader):
    def __init__(self, **kwargs: Any):
        from core.simulator.servo import SimServo

        kwargs.update({"device_init_func": SimServo})  # keeps mypy happy
        super().__init__(**kwargs)

    def _boot(self) -> Node:
        from core.simulator.utils import SERVO_BOUNDS

        assert self._ref_id is not None and len(self._ref_id) > 0
        # pan will be even ==> evaluates to 0 ==> first index == pan
        is_odd = int(self._ref_id[-1]) % 2 == 1
        servo_bound = SERVO_BOUNDS[int(is_odd)]

        # TODO Can we get the total number of scanner to split for?
        MARGIN_SIZE = 0.05
        OFFSET_RATIO = 0.33  # Based on 2 Scanners
        servo_id = int(self._ref_id[-1])
        gimbal_id = int(servo_id / 2) % 2  # Only support 2 ranges for now, extras have the same as previous
        minimum = int((MARGIN_SIZE + gimbal_id * OFFSET_RATIO * (1 - (servo_id % 2))) * servo_bound)
        maximum = servo_bound - int((MARGIN_SIZE + (1 - gimbal_id) * OFFSET_RATIO * (1 - (servo_id % 2))) * servo_bound)

        # robust against positive/negative
        servo_min = min(minimum, servo_bound)
        servo_max = max(0, maximum)
        assert self._device_init_func is not None
        servo = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            min=servo_min,
            max=servo_max,
        )
        return servo


class SimCamBootLoader(SimulatedDeviceBootLoader):
    DEFAULT_POLL_MS = 100

    def __init__(self, *argv: Any, hw_cfg_entry: Optional[Dict[Any, Any]] = None, **kwargs: Any):
        from core.simulator.cam import SimCam

        self._hw_cfg_entry = hw_cfg_entry
        kwargs.update({"device_init_func": SimCam})
        super().__init__(*argv, **kwargs)

    def _boot(self) -> Node:
        from core.simulator.cam import (
            cached_simulated_drive_image,
            cached_simulated_predict_image,
            DRIVE_RESOLUTION,
            PREDICT_RESOLUTION,
            TARGET_RESOLUTION,
        )

        from core.model.path import CameraInstance

        assert self._ref_id is not None
        if CameraInstance.PREDICT in self._ref_id:
            grabframe_func = functools.partial(cached_simulated_predict_image, self._context.args)
            resolution = PREDICT_RESOLUTION
        elif CameraInstance.TARGET in self._ref_id:
            grabframe_func = functools.partial(cached_simulated_predict_image, self._context.args)
            resolution = TARGET_RESOLUTION
        else:
            grabframe_func = functools.partial(cached_simulated_drive_image, self._context.args, periodic_flash_sec=5)
            resolution = DRIVE_RESOLUTION

        assert self._hw_cfg_entry is None or isinstance(self._hw_cfg_entry, dict)

        retina_args: Dict[str, float] = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})

        assert self._device_init_func is not None

        # If we are running this for unity we can slow this way down
        poll_ms = max(
            SimCamBootLoader.DEFAULT_POLL_MS, SimCamBootLoader.DEFAULT_POLL_MS * 100 * int(self._context.args.unity)
        )
        cam = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            grabframe_func=grabframe_func,
            poll_ms=poll_ms,
            resolution=resolution,
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            **retina_args,
        )

        return cam
