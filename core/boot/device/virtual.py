from typing import Any

import lib.common.logging
from core.boot.device import Node
from core.boot.device.hardware import HardwareConfigEntryBootLoader

LOG = lib.common.logging.get_logger(__name__)


class VirtualCamBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.virtual.drivers.virtual_cam import VirtualCamDriver

        super().__init__(device_init_func=VirtualCamDriver, **kwargs)

    def _boot(self) -> Node:
        cam_args = self._hw_cfg_entry.get("args", {})
        retina_args = self._context.retina_cfg.load().get(str(self.device_path.leaf()), {})

        assert self._device_init_func is not None
        cam = self._device_init_func(
            filesystem=self.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            virtual_id=self._hw_cfg_entry["virtual_id"],
            preview_scale=self._context.args.preview_scale,
            preview_fps_limit=self._context.args.preview_fps,
            **cam_args,
            **retina_args,
        )
        return cam


class VirtualLaserBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.virtual.drivers.virtual_laser import VirtualLaserDriver

        super().__init__(device_init_func=VirtualLaserDriver, **kwargs)


class VirtualLensNodeBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.virtual.drivers.virtual_lens import VirtualLensNodeDriver

        super().__init__(device_init_func=VirtualLensNodeDriver, **kwargs)

    def _boot(self) -> Node:
        assert self._device_init_func is not None
        device = self._device_init_func(
            filesystem=self._context.fs,
            feed=self.feed,
            ref_id=self._ref_id,
            device_path=self.device_path,
            subtree=self._get_subtree(),
            context=self._context,
            **self._hw_cfg_entry,
        )
        return device


class VirtualServoBootLoader(HardwareConfigEntryBootLoader):
    def __init__(self, **kwargs: Any):
        from core.virtual.drivers.virtual_servo import VirtualServoDriver

        super().__init__(device_init_func=VirtualServoDriver, **kwargs)
