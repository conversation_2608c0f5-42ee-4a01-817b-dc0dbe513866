from typing import Any, Callable, Dict, List, Optional, Set

import lib.common.logging
from core.boot.args import ARG_SIM, is_simulator
from core.boot.context import Shared<PERSON>ontext
from core.boot.loader import <PERSON>ot<PERSON>oader
from core.boot.registry import INTERNAL_NODE_BOOTLOADER_REGISTRY, get_bootloader
from core.config.hardware import HW_CONFIG_KEY_DEVICE_BOOTLOADER
from core.model.id import ReferenceId
from core.model.node.base import Node
from core.model.node.type import NodeType
from core.model.path import DeviceExpression, DevicePath, Instance

LOG = lib.common.logging.get_logger(__name__)


class LeavesBootPlan:
    """
    A structure describing everything needed to boot all of the devices.

    The set of unique devices, including simulated devices, is called the Device Pool.

    The Device Pool, after boot, is representing by a map from device id to device reference.
    Hence, device ids should be unique.
    """

    _simulated_ref_id_namepool: Dict[NodeType, int] = {}

    def __init__(
        self,
        ref_ids_by_path: Dict[DevicePath, ReferenceId],
        bootloaders_by_ref_id: Dict[ReferenceId, Callable[..., Node]],
        hw_cfgs: Dict[ReferenceId, Dict[str, Any]],
    ):
        """
        :param ref_ids_by_path: a dict from DevicePath to DeviceId
        :param bootloaders_by_ref_id: A dict from device id to bootloader init.
        :param hw_cfgs: A dict from device id to hw_cfg entry (basically a flatten version of the dict to dict hw cfg)
        """
        assert ref_ids_by_path is not None
        assert bootloaders_by_ref_id is not None
        assert hw_cfgs is not None

        assert len(bootloaders_by_ref_id) >= len(hw_cfgs)
        for ref_id, hw_cfg_entry in hw_cfgs.items():
            assert ref_id in bootloaders_by_ref_id, "Missing bootloader for device id {}".format(ref_id)

        self.ref_ids_by_path = ref_ids_by_path
        self.device_path_by_id: Dict[ReferenceId, DevicePath] = {v: k for k, v in self.ref_ids_by_path.items()}
        assert len(self.ref_ids_by_path) == len(self.device_path_by_id), "Duplicate boot args detected"

        self._bootloaders = bootloaders_by_ref_id
        self._hw_cfgs = hw_cfgs  # not everything will have a hw cfg (simulated devices)

        self.ref_ids = list(
            map(lambda x: x[0], sorted(self.device_path_by_id.items(), key=lambda x: x[1].depth, reverse=True))
        )

    def init_bootloader(
        self, ref_id: ReferenceId, device_path: DevicePath, context: SharedContext, log_prefix: str = ""
    ) -> BootLoader:
        assert ref_id is not None
        assert context is not None

        leaf_bootloader_init = self._bootloaders[ref_id]
        LOG.info(f"{log_prefix}{device_path} -> {ref_id} Booting via {leaf_bootloader_init}")

        kwargs: Dict[str, Any] = {"context": context}
        if ref_id in self._hw_cfgs:
            # hardware config
            kwargs["hw_cfg_entry"] = self._hw_cfgs[ref_id]

        assert device_path is not None
        leaf_bootloader = leaf_bootloader_init(ref_id=ref_id, device_path=device_path, **kwargs)

        assert isinstance(leaf_bootloader, BootLoader)
        return leaf_bootloader

    @staticmethod
    def build(boot_args: Dict[DevicePath, str], hw_cfg: Dict[ReferenceId, Dict[str, Any]]) -> "LeavesBootPlan":  # noqa
        """
        INPUT HW_CFG IS MODIFIED IN THIS FUNCTION.

        Parses a fully specified boot args and hardware config into a device pool boot plan. e.g.

        boot_args:
            /scanner:1/laser --> sim
            /driver/camera:front_right --> camera0

        hw_cfg:
            'camera': {
                'camera0': {
                    bootloader: foo
                    bar: baz
                }
            },
            ...

        becomes:

        DevicePoolBootPlan:

            bootloaders: {
                sim_laser_id: SimLaser_init_func
                camera0: foo_init_func
            }

            device_hw_cfgs: {
                camera0: {
                    bar: baz
                }
            }

        The device_hw_cfgs end up being the kwargs for the bootloader init funcs.
        In this way, everything is ready for boot. Just loop through the dictionaries, and call the bootloaders.

        :param boot_args: dict of fully specified device_path to boot args
        :param hw_cfg: The full hardware config dictionary, dict to dicts
        """
        assert boot_args is not None, "Must pass boot_args"
        assert hw_cfg is not None, "Must pass hw_cfg"

        # this is the main information we need to determine:
        # bootloader init functions, by device path
        bootloaders_by_ref_id: Dict[ReferenceId, Callable[..., Any]] = {}
        # hw config dictionaries (storing bootloader init parameters), for non-simulated devices
        device_hw_cfgs: Dict[ReferenceId, Dict[str, Any]] = {}
        # device path to device id (either simulated id or specified via boot args)
        device_path_to_ref_id: Dict[DevicePath, ReferenceId] = {}

        # we can iterate in any order, so go alphabetical
        for device_path, boot_arg in sorted(boot_args.items()):
            leaf: DeviceExpression = device_path.leaf()  # the leaf is the fully specified device to be booted
            device_type = leaf.type
            device_instance: Optional[Instance] = leaf.instance

            ref_id = LeavesBootPlan.get_ref_id(device_type, device_instance, boot_arg)
            if ref_id in bootloaders_by_ref_id:
                bootloader_init = bootloaders_by_ref_id[ref_id]
                device_path_to_ref_id[device_path] = ref_id
                LOG.debug("{} Already planned bootloader for {}: {}".format(device_path, ref_id, bootloader_init))
            else:
                if is_simulator(boot_arg):
                    # no hw config for simulators
                    bootloader_ref_key = ARG_SIM
                else:
                    # Get, validate hardware config entry
                    hw_cfg_category: Optional[Dict[str, Any]] = hw_cfg.get(ReferenceId(device_type.serialize()))
                    assert hw_cfg_category is not None, "Missing hw_cfg_category {}".format(device_type.serialize())
                    hw_cfg_entry = hw_cfg_category.get(ref_id)
                    assert hw_cfg_entry is not None, "Missing hw_cfg_entry for {} {} {}".format(
                        device_type, ref_id, hw_cfg
                    )
                    assert HW_CONFIG_KEY_DEVICE_BOOTLOADER in hw_cfg_entry, "Missing key: {} in {}".format(
                        HW_CONFIG_KEY_DEVICE_BOOTLOADER, hw_cfg_entry
                    )

                    # Get bootloader reference key
                    bootloader_ref_key = hw_cfg_entry[HW_CONFIG_KEY_DEVICE_BOOTLOADER]
                    # delete reference key in underlying dict as it is no longer needed
                    # and we will pass along this dict
                    del hw_cfg_entry[HW_CONFIG_KEY_DEVICE_BOOTLOADER]
                    # store hw cfg entry for later
                    device_hw_cfgs[ref_id] = hw_cfg_entry

                bootloader_init = get_bootloader(device_type, bootloader_ref_key)
                bootloaders_by_ref_id[ref_id] = bootloader_init
                device_path_to_ref_id[device_path] = ref_id
                LOG.debug("{} booting {} with {}".format(device_path, ref_id, bootloader_init))

        return LeavesBootPlan(device_path_to_ref_id, bootloaders_by_ref_id, device_hw_cfgs)

    @staticmethod
    def get_ref_id(device_type: NodeType, device_instance: Optional[Instance], boot_arg: str) -> ReferenceId:
        ref_id: ReferenceId
        if is_simulator(boot_arg):
            # simulated device
            if device_type == NodeType.CAMERA:
                assert device_instance is not None
                ref_id = ReferenceId(device_instance)
            else:
                ref_id = LeavesBootPlan._next_simulated_ref_id(device_type)
        else:
            # hardware device
            ref_id = ReferenceId(boot_arg)
        return ref_id

    @classmethod
    def _next_simulated_ref_id(cls, device_type: NodeType) -> ReferenceId:
        next_id = cls._simulated_ref_id_namepool.get(device_type, -1) + 1
        cls._simulated_ref_id_namepool[device_type] = next_id
        return ReferenceId("{}-{}{}".format(ARG_SIM, str(device_type).lower(), next_id))


class TreeBootPlan:
    """
    A structure describing everything needed to boot all of the device trees.

    The Device Tree, after boot, is representing by a map from device path to device reference.
    Hence, device paths should be unique.
    """

    # TODO https://github.com/carbonrobotics/robot/issues/789
    # The nodes to boot should all be expressed in boot args, even if they're not hardware-backed
    # e.g. "-d /commander" with no second argument
    IMPLIED_NODES = {
        # if there is a /driver or /exterminator, then we should boot the /commander alongside it
        NodeType.DRIVER: NodeType.COMMANDER,
        NodeType.EXTERMINATOR: NodeType.COMMANDER,
        # if there is a /retina, then we should boot the /visual_cortex alongside it
        NodeType.RETINA: NodeType.VISUAL_CORTEX,
    }

    # relative boot orders, if necessary. default is 0. Lower values boot first.
    # TODO: Now that we have a generic notion of subscription, we can parse that graph to determine the boot orders
    BOOT_ORDER = {
        NodeType.COMMANDER: +1,
        NodeType.VISUAL_CORTEX: -2,
        NodeType.FRAME: -3,
        NodeType.RETINA: -4,
    }

    def __init__(self, *, device_pool_boot_plan: LeavesBootPlan, root: DevicePath):
        """
        :type device_boot_plan: LeavesBootPlan
        """
        # this is what the bootloader iterates over
        self._device_paths = TreeBootPlan._expand_all_parent_nodes(device_pool_boot_plan, root)
        self._bootloaders = {}

        for device_path in self._device_paths:
            leaf = device_path.leaf()  # leaves here are always device trees
            bootloader_init = get_bootloader(leaf.type)
            self._bootloaders[device_path] = bootloader_init

    def inorder_boot(self) -> List[DevicePath]:
        """
        :return: the ordered list of device paths to boot
        """
        # sort by override, then reverse depth, then alphabetically
        boot_order_func = lambda dp: (TreeBootPlan.BOOT_ORDER.get(dp.leaf().type, 0), -dp.depth, str(dp))
        result = list(sorted(self._device_paths, key=boot_order_func))

        return result

    @staticmethod
    def _expand_all_parent_nodes(
        device_boot_plan: LeavesBootPlan, exclude_above_or_equal_root: DevicePath
    ) -> Set[DevicePath]:
        """
        Expand all the parent nodes up to the root, which is excluded.
        """
        assert device_boot_plan is not None and isinstance(device_boot_plan, LeavesBootPlan)

        leaf_parents = set(
            [dp.trim_leaf() for dp in device_boot_plan.ref_ids_by_path if dp.trim_leaf() != DevicePath.robot()]
        )
        result = set()
        for path in leaf_parents:
            # If we are dynamic internal then don't include in list of static internal
            leaf = path.leaf()
            if leaf.type.is_dynamic_internal and (
                leaf.type not in INTERNAL_NODE_BOOTLOADER_REGISTRY or path in device_boot_plan.ref_ids_by_path
            ):
                continue

            # This is a static internal node
            result.add(path)
            while path.trim_leaf() != DevicePath.robot():
                path = path.trim_leaf()
                result.add(path)

        # check for implied nodes
        implied = set()
        for node_path in result:
            if node_path.type in TreeBootPlan.IMPLIED_NODES:
                implied_node_path = DevicePath.robot().join(TreeBootPlan.IMPLIED_NODES[node_path.type])
                LOG.debug(f"Booting {implied_node_path} after {node_path}")
                implied.add(implied_node_path)
        result.update(implied)

        # filter
        filtered = set([path for path in result if path not in exclude_above_or_equal_root])
        for r in sorted(result):
            if r not in filtered:
                # warning for now so its visible, but can be reduced after some time
                # or when the boot sequence is "better"
                LOG.warning(f"Filtered booting {r} based on root: {exclude_above_or_equal_root}")

        return filtered

    def init_bootloader(self, device_path: DevicePath, context: SharedContext, log_prefix: str = "") -> BootLoader:
        assert device_path is not None and isinstance(device_path, DevicePath)
        assert context is not None and isinstance(context, SharedContext)

        device_tree_bootloader_init = self._bootloaders[device_path]
        LOG.info("{}Booting {} using {}".format(log_prefix, device_path, device_tree_bootloader_init))

        kwargs = {"context": context}

        device_tree_bootloader = device_tree_bootloader_init(device_path=device_path, **kwargs)

        assert isinstance(device_tree_bootloader, BootLoader)
        return device_tree_bootloader
