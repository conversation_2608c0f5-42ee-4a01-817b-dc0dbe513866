import os
from argparse import ArgumentPars<PERSON>, Namespace
from typing import Any, Dict, List, Optional, Union

import lib.common.logging
from core.boot.constants import BOOT_ARGS_FILENAME, BOOT_ARGS_FILEPATH
from core.model.path import DevicePath
from core.model.schematic import Schematic, SubTreePaths

LOG = lib.common.logging.get_logger(__name__)

ARG_SIM = "sim"
ARG_SIMULATOR = "simulator"

UnparsedBootArgsType = List[str]


class BootArgs(Dict[DevicePath, str]):
    pass


def is_simulator(arg: Optional[str]) -> bool:
    """
    Matches:
     - sim
     - simulator
     - sim_*
    """
    if arg is None:
        return False
    arg = arg.lower()
    return arg.startswith(f"{ARG_SIM}_") or arg in [ARG_SIM, ARG_SIMULATOR]


ARG_VIRTUAL = "virtual"
ARG_UNITY = "unity"


def is_virtual(arg: Optional[str]) -> bool:
    if arg is None:
        return False
    arg = arg.lower()
    # Matches:
    # - virtual_*
    return arg.startswith(f"{ARG_VIRTUAL}_")


def is_unity(arg: Optional[str]) -> bool:
    if arg is None:
        return False
    arg = arg.lower()
    # Matches:
    # - unity_*
    return arg.startswith(f"{ARG_UNITY}_")


def add_boot_arguments(argument_parser: ArgumentParser) -> None:
    default: List[str] = []
    argument_parser.add_argument("-d", "--device", nargs=2, action="append", type=str, default=default)


def expand_shorthand(boot_args: BootArgs, log_prefix: str = "") -> BootArgs:
    # Expand boot args to full verbosity, in case any shorthand simulated devices were used
    full_boot_args = BootArgs()
    lazy_logs = []  # a little bit of indirection so we can nicely format a group of logs to be aligned
    for device_path, boot_arg in boot_args.items():
        node_type = device_path.type
        if is_simulator(boot_arg) and not node_type.is_leaf:
            if node_type.is_dynamic_internal:
                full_boot_args[device_path] = boot_arg
            std_subtree = SubTreePaths.expand(device_path)
            for sub_device_path in std_subtree:
                existing_boot_arg = boot_args.get(sub_device_path)
                chosen_boot_arg = existing_boot_arg or ARG_SIM
                if existing_boot_arg is None:
                    lazy_logs.append(
                        (
                            LOG.debug,
                            "{}Adding boot arg `-d {} {}` based on shorthand expansion of `-d {} {}`",
                            log_prefix,
                            sub_device_path,
                            chosen_boot_arg,
                            device_path,
                            boot_arg,
                        )
                    )
                else:
                    lazy_logs.append(
                        (
                            LOG.warning,
                            "{}Existing boot arg `-d {} {}` is more specific than `-d {} {}`",
                            log_prefix,
                            sub_device_path,
                            existing_boot_arg,
                            device_path,
                            boot_arg,
                        )
                    )
                full_boot_args[sub_device_path] = chosen_boot_arg
        else:
            full_boot_args[device_path] = boot_arg

    # logging
    if len(lazy_logs) > 0:
        log_align1 = max([len(log[3]) for log in lazy_logs])  # align first  `-d |foo | bar` vertically
        log_align2 = max([len(log[5]) for log in lazy_logs])  # align second `-d |foo2| baz` vertically
        for log in lazy_logs:
            f = log[0]
            fmt_str = log[1]
            fmt_args = log[2:]
            assert len(fmt_args) == 5, "Bad fmt_args: {}".format(fmt_args)
            log_line = fmt_str.format(
                fmt_args[0],
                str(fmt_args[1]).ljust(log_align1),
                fmt_args[2],
                str(fmt_args[3]).ljust(log_align2),
                fmt_args[4],
            )
            f(log_line)

    return full_boot_args


def _log_boot_args(
    bootfile_args_map: BootArgs, cmdline_boot_args_map: BootArgs, boot_args: BootArgs, log_prefix: str = "",
) -> None:
    """
    Helper function to wrap bulky logging logic
    """
    if len(boot_args) == 0:
        return
    log_device_path_align = max([len(k) for k, v in boot_args.items()])
    log_bootloader_reference_key_align = max([len(v) for k, v in boot_args.items()])
    d_str = "-d"
    log_prefix_align = max(len(s) for s in [d_str, BOOT_ARGS_FILENAME])

    for device_path, chosen in sorted(boot_args.items()):
        cmdline_arg = cmdline_boot_args_map.get(device_path)
        bootfile_arg = bootfile_args_map.get(device_path)
        override = matches = False
        log = log_prefix
        if cmdline_arg is None:
            # boot.arg only
            log += "[{}] ".format(BOOT_ARGS_FILENAME.ljust(log_prefix_align))
        elif bootfile_arg is None:
            # cmdline only
            log += "[{}] ".format(d_str.ljust(log_prefix_align))
        elif cmdline_arg == bootfile_arg:
            # cmdline matches boot.args
            log += "[{}] ".format(d_str.ljust(log_prefix_align))
            matches = True
        else:
            # cmdline overrides boot.args
            log += "[{}] ".format(d_str.ljust(log_prefix_align))
            override = True
        log += "{} {} {}".format(
            d_str, str(device_path).ljust(log_device_path_align), chosen.ljust(log_bootloader_reference_key_align)
        )
        if override:
            log += " (overrides `{}` from {})".format(bootfile_arg, BOOT_ARGS_FILENAME)
        if matches:
            log += " (same as boot.args)"

        if override:
            LOG.warning(log)
        else:
            LOG.info(log)


def _parse_and_expand_boot_args(boot_args_namespace: Namespace, log_prefix: str = "", log_hint: str = "") -> BootArgs:
    # parse into DevicePaths
    parsed_boot_args: BootArgs = BootArgs({DevicePath.parse(dp): val for dp, val in boot_args_namespace.device})

    # expand shorthand
    expanded_boot_args: BootArgs = expand_shorthand(parsed_boot_args, log_prefix=log_prefix)
    if len(expanded_boot_args) != len(parsed_boot_args):
        LOG.debug(
            "{}Expanded {} shorthand {} boot args into {} fully specified boot args".format(
                log_prefix, len(parsed_boot_args), log_hint, len(expanded_boot_args)
            )
        )

    return expanded_boot_args


class UnexpectedBootArgException(Exception):
    pass


def _validate_boot_args(boot_args: BootArgs) -> None:
    # validate
    for dp in boot_args:
        if dp not in Schematic.WHITELIST:
            if str(dp).startswith("/scanner"):
                assert not str(dp).startswith(
                    "/scanner"
                ), f'Bad device path: {dp}. "/scanner*" must be nested under "/exterminator".'
            if str(dp) == "/driver/drive_system/drive_motor:left":
                assert str(dp) == "/driver/drive_system/drive_motor:back_left", (
                    f'Bad device path: {dp}. Please use "back_left", not "left", for drive_motor. '
                    f"This assertion allows for easier compatibility with four-wheel machines."
                )
            if str(dp) == "/driver/drive_system/drive_motor:right":
                assert str(dp) == "/driver/drive_system/drive_motor:back_right", (
                    f'Bad device path: {dp}. Please use "back_right", not "right", for drive_motor. '
                    f"This assertion allows for easier compatibility with four-wheel machines."
                )
            if dp not in Schematic.WHITELIST:
                raise UnexpectedBootArgException(
                    f"Unsupported device path found in boot args: {dp}. Expected: {Schematic.WHITELIST}"
                )


# GOD METHOD!
def parse_boot_args(
    cmdline_boot_args: Optional[Union[UnparsedBootArgsType, Namespace]] = None,
    boot_args_filepath: Optional[str] = BOOT_ARGS_FILEPATH,
    extra_args: Optional[UnparsedBootArgsType] = None,
    log_prefix: str = "",
) -> BootArgs:
    try:
        return do_parse_boot_args(
            unparsed_cmdline_boot_args=cmdline_boot_args,
            boot_args_filepath=boot_args_filepath,
            extra_args=extra_args,
            log_prefix=log_prefix,
        )
    except UnexpectedBootArgException:
        LOG.error(f"{log_prefix} Unable to parse boot args")
        LOG.error(f"{log_prefix} commandline_boot_args: {cmdline_boot_args}")
        LOG.error(f"{log_prefix} boot_args_filepath: {boot_args_filepath}")
        LOG.error(f"{log_prefix} extra_args: {extra_args}")
        raise


def filter_boot_args(boot_args: BootArgs, filter: DevicePath) -> BootArgs:
    return BootArgs({k: v for k, v in boot_args.items() if filter in k})


def do_parse_boot_args(
    unparsed_cmdline_boot_args: Optional[Union[UnparsedBootArgsType, Namespace]] = None,
    boot_args_filepath: Optional[str] = BOOT_ARGS_FILEPATH,
    extra_args: Optional[UnparsedBootArgsType] = None,
    log_prefix: str = "",
) -> BootArgs:
    # Parse boot.args
    bootfile_args: BootArgs = BootArgs()
    if boot_args_filepath is None:
        LOG.debug("{}Skipping parsing {} since overridden with None".format(log_prefix, BOOT_ARGS_FILENAME))
    else:
        LOG.debug("{}Parsing boot args from {}...".format(log_prefix, boot_args_filepath))
        bootfile_args_namespace: Namespace = BootArgsFileArgumentParser(filepath=boot_args_filepath).parse_args()
        LOG.info(
            "{}Parsed {} boot args in {}".format(log_prefix, len(bootfile_args_namespace.device), boot_args_filepath)
        )
        bootfile_args = _parse_and_expand_boot_args(
            bootfile_args_namespace, log_prefix=log_prefix, log_hint=BOOT_ARGS_FILENAME
        )
    _validate_boot_args(bootfile_args)

    # Parse commandline boot args
    cmdline_boot_args_namespace: Namespace = Namespace()
    if unparsed_cmdline_boot_args is None or isinstance(unparsed_cmdline_boot_args, list):
        LOG.debug("{}Parsing boot args from commandline...".format(log_prefix))
        cmdline_boot_args_namespace, _ = BootArgumentParser().parse_known_args(args=unparsed_cmdline_boot_args)
        LOG.info("{}Parsed {} boot args from commandline".format(log_prefix, len(cmdline_boot_args_namespace.device)))
    else:
        LOG.info("{}Using given boot args as if from commandline".format(log_prefix))
        cmdline_boot_args_namespace = unparsed_cmdline_boot_args
    cmdline_boot_args: BootArgs = _parse_and_expand_boot_args(
        cmdline_boot_args_namespace, log_prefix=log_prefix, log_hint="cmdline"
    )
    _validate_boot_args(cmdline_boot_args)

    # Parse Extra Arguments
    extra_boot_args: BootArgs = BootArgs()
    if extra_args is not None:
        LOG.debug("{}Parsing extra boot args...".format(log_prefix))
        extra_boot_args_namespace, _ = BootArgumentParser().parse_known_args(args=extra_args)
        LOG.info("{}Parsed {} extra boot args".format(log_prefix, len(extra_boot_args_namespace.device)))
        extra_boot_args = _parse_and_expand_boot_args(
            extra_boot_args_namespace, log_prefix=log_prefix, log_hint="extra"
        )
    _validate_boot_args(extra_boot_args)

    # Compute chosen args
    boot_args: BootArgs = BootArgs({**bootfile_args, **extra_boot_args, **cmdline_boot_args})  # cmdline has precedence
    LOG.info(
        "{}Parsed {} unique device paths specified out of total {}".format(
            log_prefix, len(boot_args), len(bootfile_args) + len(cmdline_boot_args)
        )
    )

    # Logging
    _log_boot_args(bootfile_args, cmdline_boot_args, boot_args, log_prefix=log_prefix)

    return boot_args


class BootArgumentParser(ArgumentParser):
    """
    Customized argument parser which reads arguments from boot.args.
    """

    def __init__(self, *argv: Any, description: str = "Boot a robot", **kwargs: Any):
        kwargs.update({"description": description})  # avoid mypy error
        super().__init__(*argv, **kwargs)

        add_boot_arguments(self)


class BootArgsFileArgumentParser(ArgumentParser):
    """
    Customized argument parser which reads arguments from boot.args.
    """

    def __init__(
        self,
        *argv: Any,
        filepath: str = BOOT_ARGS_FILEPATH,
        description: str = "Boot a robot",
        fromfile_prefix_chars: str = "@",
        **kwargs: Any,
    ):
        kwargs.update({"description": description})  # avoid mypy error
        kwargs.update({"fromfile_prefix_chars": fromfile_prefix_chars})  # avoid mypy error
        super().__init__(*argv, **kwargs)

        self.filepath = filepath
        add_boot_arguments(self)

    def convert_arg_line_to_args(self, line: str) -> Any:  # parent is List[str]
        """
        Override to support comments in files that get parsed
        """
        for arg in line.split():
            if not arg.strip():
                continue
            if arg[0] == "#":
                break
            yield arg

    def parse_args(self, args: Any = None, namespace: Any = None) -> Any:  # untyped parent
        """
        Override to support default files getting added
        """
        assert args is None, "Cannot pass in args to {} as it only uses args from file".format(self)

        # pass in args from default file as first arg, if it exists
        if os.path.exists(self.filepath):
            args = ["@" + self.filepath]
        else:
            LOG.warning("Missing {}".format(self.filepath))
            args = []

        return super().parse_args(args=args, namespace=namespace)
