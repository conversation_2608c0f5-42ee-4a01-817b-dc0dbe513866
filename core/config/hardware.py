#!/usr/bin/env python3
from typing import Any, Dict, Optional, cast

import yaml

from core.fs.defaults import CONFIG_DIR
from lib.common.config_file import ConfigDictType, ConfigFile

# General
HW_CONFIG_KEY_DEVICE_BOOTLOADER = "bootloader"

# Laser
HW_CONFIG_KEY_LASER_BOOTLOADER_SERIAL_CYPRESS = "cypress_laser"
HW_CONFIG_KEY_LASER_BOOTLOADER_ROW_MODULE_BOARD = "row_module_board_laser"
HW_CONFIG_KEY_LASER_BOOTLOADER_PULCZAR_BOARD = "pulczar_board_laser"

# Lens
HW_CONFIG_KEY_LENS_BOOTLOADER_CORNING = "liquid_lens"
HW_CONFIG_KEY_LENS_BOOTLOADER_EMERGENT = "emergent"
HW_CONFIG_KEY_LENS_BOOTLOADER_KAYA = "kaya"
HW_CONFIG_KEY_LENS_BOOTLOADER_PULCZAR_BOARD = "pulczar_board_lens"
HW_CONFIG_KEY_LENS_BOOTLOADER_AIMBOT_PROCESS = "aimbot_process_lens"

# Servo
HW_CONFIG_KEY_SERVO_BOOTLOADER_ROW_MODULE_BOARD = "row_module_board_servo"
HW_CONFIG_KEY_SERVO_BOOTLOADER_PULCZAR_BOARD = "pulczar_board_servo"

# Gimbal
HW_CONFIG_KEY_GIMBAL_BOOTLOADER_ROW_MODULE_BOARD = "row_module_board_gimbal"
HW_CONFIG_KEY_GIMBAL_BOOTLOADER_PULCZAR_BOARD = "pulczar_board_gimbal"
HW_CONFIG_KEY_GIMBAL_BOOTLOADER_AIMBOT_PROCESS = "aimbot_process_gimbal"

# Camera
HW_CONFIG_KEY_CAMERA_BOOTLOADER_IDENTITY = "identity"  # TODO deprecate in favor of pylon after botcfg updated
HW_CONFIG_KEY_CAMERA_BOOTLOADER_PYLON = "pylon"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_THINKLUCID = "thinklucid"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_AXIS_ETHERNET = "axis_ethernet"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_ZED = "zed"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_EMERGENT = "emergent"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_KAYA = "kaya"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_BUFFER = "buffer"
HW_CONFIG_KEY_CAMERA_BOOTLOADER_LATEST_BUFFER = "latest_buffer"

# GPS
HW_CONFIG_KEY_GPS_BOOTLOADER_BENJAMIN = "benjamin_gps"

# INS
HW_CONFIG_KEY_INS_BOOTLOADER_DURO = "duro"

# Drive Motor
HW_CONFIG_KEY_DRIVE_MOTOR_BOOTLOADER_SERIAL_CYPRESS = "serial_cypress"

# Rotary Encoder
HW_CONFIG_KEY_ROTARY_ENCODER_NOFX = "nofx_rotary_encoder"

# Parking Brake
HW_CONFIG_KEY_PARKING_BRAKE_NOFX = "nofx_parking_brake"

# Fuel Sensor
HW_CONFIG_KEY_FUEL_SENSOR_NOFX = "nofx_fuel_sensor"

# This hardware type *only* supportes express bootloaders - what this means is that nobody ever wrote a non-
# express bootloader for this device.
HW_CONFIG_KEY_EXPRESS_ONLY = "express_only"


class HardwareConfigFile(ConfigFile):
    FILENAME = "hardware.yaml"

    def __init__(self, config_dir: str = CONFIG_DIR, filename: str = FILENAME) -> None:
        super().__init__(config_dir=config_dir, filename=filename, serialize=yaml.dump, deserialize=yaml.safe_load)


def get_hw_config(key: str) -> Optional[Dict[str, Any]]:
    config_file = HardwareConfigFile()
    config: ConfigDictType = config_file.load() or {}
    key = str(key)  # sometimes an enum gets passed in here
    # fallback to default or None
    return cast(Optional[Dict[str, Any]], config.get(key))
