{"geofences": {"boundaries": [{"coordinates": "POLY<PERSON><PERSON> ((-119.860805999999997 45.923765000000003, -119.860806600000004 45.923764700000000, -119.860806499999995 45.923765199999998, -119.860805499999998 45.923765199999998, -119.860802100000001 45.923763800000003, -119.860796300000004 45.923762300000000, -119.860781099999997 45.923759199999999, -119.860752599999998 45.923749200000003, -119.860695600000000 45.923704899999997, -119.860529099999994 45.923707499999999, -119.859668999999997 45.923827099999997, -119.858081900000002 45.924109100000003, -119.857459899999995 45.924167699999998, -119.857319300000000 45.924235899999999, -119.857293999999996 45.924304399999997, -119.857319099999998 45.924411100000000, -119.857320599999994 45.924503000000001, -119.857481600000000 45.924821600000001, -119.857717699999995 45.925114499999999, -119.857870199999994 45.925275700000000, -119.858180500000003 45.925523599999998, -119.858347100000003 45.925630499999997, -119.858530799999997 45.925711300000003, -119.859713099999993 45.926058599999998, -119.860439799999995 45.926250500000002, -119.860796199999996 45.926282999999998, -119.861162199999995 45.926274300000003, -119.861633400000002 45.926232800000001, -119.861993299999995 45.926152100000003, -119.862445600000001 45.926008199999998, -119.863071899999994 45.925716000000001, -119.863274399999995 45.925599599999998, -119.863530999999995 45.925394300000001, -119.863890600000005 45.925017500000003, -119.864085900000006 45.924772099999998, -119.864224300000004 45.924551999999998, -119.864348800000002 45.924209900000001, -119.864356099999995 45.924079200000001, -119.864406599999995 45.923783200000003, -119.864388800000000 45.923708099999999, -119.864335999999994 45.923668900000003, -119.864293000000004 45.923661799999998, -119.862892000000002 45.923679200000002, -119.862776600000004 45.923666799999999, -119.860946605522543 45.923645597365805, -119.860911700000003 45.923675600000003, -119.860876700000006 45.923677300000001, -119.860820399999994 45.923758399999997, -119.860800999999995 45.923763500000000, -119.860799600000007 45.923765600000003, -119.860755400000002 45.923753699999999, -119.860805999999997 45.923765000000003))", "id": "mercer50_field", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["FIELD"]}, null, null, null]}}, {"coordinates": "POLYGON ((-119.860731000000001 45.923783000000000, -119.860667000000007 45.923758800000002, -119.860610800000003 45.923759500000003, -119.857493700000006 45.924242700000001, -119.857462600000005 45.924248900000002, -119.857455400000006 45.924251200000000, -119.857452499999994 45.924252500000001, -119.857417499999997 45.924286700000003, -119.857338900000002 45.924308400000001, -119.857426700000005 45.924551500000000, -119.857485199999999 45.924663700000004, -119.857624799999996 45.924878399999997, -119.857780000000005 45.925065699999998, -119.858120299999996 45.925380300000000, -119.858328599999993 45.925530100000003, -119.858501799999999 45.925628000000003, -119.858856900000006 45.925768499999997, -119.859348100000005 45.925923099999999, -119.859725900000001 45.926029700000001, -119.860355799999994 45.926179699999999, -119.860635200000004 45.926220700000002, -119.861117899999996 45.926210400000002, -119.861760200000006 45.926124899999998, -119.862318900000005 45.925977199999998, -119.862672799999999 45.925842799999998, -119.862851300000003 45.925752299999999, -119.863054300000002 45.925643600000001, -119.863347000000005 45.925454199999997, -119.863622300000003 45.925227900000003, -119.863839499999997 45.925008400000003, -119.864030700000001 45.924741200000000, -119.864142999999999 45.924538300000002, -119.864255999999997 45.924263600000003, -119.864324400000001 45.923937500000001, -119.864330499999994 45.923791500000000, -119.864228600000004 45.923776199999999, -119.864169899999993 45.923736900000002, -119.863000900000003 45.923752299999997, -119.860939099999996 45.923732100000002, -119.860782900000004 45.923788199999997, -119.860710699999998 45.923766700000002, -119.860731000000001 45.923783000000000))", "id": "mercer50_crops", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["CROPS"]}, null, null, null]}}]}}