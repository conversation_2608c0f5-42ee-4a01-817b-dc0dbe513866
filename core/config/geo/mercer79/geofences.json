{"geofences": {"boundaries": [{"coordinates": "P<PERSON><PERSON><PERSON><PERSON>((-119.9076144 45.9320202,-119.90776836104507 45.931938847594616,-119.90790354662704 45.931859360582195,-119.90804677883605 45.931770545880504,-119.9081766 45.931678,-119.9081934 45.9316641,-119.9082022 45.9316572,-119.9082108 45.9316511,-119.9082455 45.9316239,-119.9084285 45.9314486,-119.9086079 45.931226,-119.908769 45.9309411,-119.9088548 45.9307277,-119.9088903 45.9305203,-119.9088766 45.9303851,-119.9089105 45.9302434,-119.9088944 45.9300497,-119.9088342 45.9298301,-119.9087277 45.929586,-119.9086039 45.929378,-119.9084742 45.9292207,-119.9081928 45.9289653,-119.9078092 45.9287069,-119.9073425 45.9284931,-119.9069256 45.9283695,-119.9066704 45.9283175,-119.9063639 45.9282758,-119.9060909 45.9282592,-119.9057672 45.9282636,-119.9052953 45.9283124,-119.904795 45.9284281,-119.9044513 45.9285514,-119.9041687 45.9286831,-119.903843 45.9288905,-119.9036686 45.9290285,-119.9034473 45.9292488,-119.9032408 45.9295247,-119.9030879 45.9298676,-119.9030375 45.9300722,-119.9030191 45.930235,-119.9030327 45.9305399,-119.9030823 45.9307529,-119.9031395 45.9309042,-119.9032146 45.931042,-119.9033336 45.9312053,-119.9035187 45.9314083,-119.904016 45.9318198,-119.9042901 45.9320021,-119.9044388 45.9320782,-119.9047274 45.9321846,-119.9052592 45.9323139,-119.905807 45.9323692,-119.9060825 45.9323729,-119.9063554 45.9323557,-119.9067745 45.9322961,-119.9071304 45.9322056,-119.9073591 45.9321289,-119.9075808 45.9320377,-119.907594 45.9320308,-119.9076079 45.9320239,-119.9076209 45.9320173,-119.9076144 45.9320202))", "id": "mercer79_crop", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["CROPS"]}, null, null, null]}}, {"coordinates": "POLYGON((-119.908273300000005 45.931700700000000, -119.908444900000006 45.931536000000001, -119.908681299999998 45.931244800000002, -119.908822700000002 45.930970600000002, -119.908886300000006 45.930806099999998, -119.908941700000000 45.930610299999998, -119.908937699999996 45.930399999999999, -119.908967300000000 45.930284700000001, -119.908959100000004 45.930114900000000, -119.908924999999996 45.929944900000002, -119.908845499999998 45.929708499999997, -119.908749499999999 45.929510399999998, -119.908604299999993 45.929298500000002, -119.908440999999996 45.929116000000000, -119.908267400000000 45.928961899999997, -119.908017799999996 45.928775100000003, -119.907698100000005 45.928592500000001, -119.907471999999999 45.928492599999998, -119.907136300000005 45.928383900000000, -119.906774200000001 45.928294500000000, -119.906388199999995 45.928239400000002, -119.906128499999994 45.928221600000001, -119.905822000000001 45.928223500000001, -119.905338499999999 45.928257000000002, -119.905179099999998 45.928287900000001, -119.904634099999996 45.928442099999998, -119.904535300000006 45.928498200000000, -119.904425099999997 45.928516500000001, -119.904153600000001 45.928648799999998, -119.903792100000004 45.928882600000001, -119.903543799999994 45.929087600000003, -119.903356200000005 45.929295400000001, -119.903223600000004 45.929476899999997, -119.903144999999995 45.929610199999999, -119.903035900000006 45.929876000000000, -119.902974900000004 45.930187699999998, -119.902975600000005 45.930484600000000, -119.903030900000005 45.930765500000000, -119.903074599999997 45.930890400000003, -119.903139100000004 45.931006199999999, -119.903386299999994 45.931339100000002, -119.903749000000005 45.931657500000000, -119.904294699999994 45.932050599999997, -119.904450400000002 45.932134900000001, -119.904620199999997 45.932201399999997, -119.905151500000002 45.932345200000000, -119.905670200000003 45.932405600000003, -119.906053200000002 45.932418900000002, -119.906624100000002 45.932369700000002, -119.907097899999997 45.932271800000002, -119.907502899999997 45.932133700000001, -119.907673200000005 45.932053300000000, -119.907729900000007 45.932009800000003, -119.907909099999998 45.931932300000000, -119.908273300000005 45.931700700000000))", "id": "mercer79_field", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["FIELD"]}, null, null, null]}}]}}