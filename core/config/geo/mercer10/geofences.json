{"geofences": {"boundaries": [{"coordinates": "POLYGON ((-119.926492100000004 45.943371300000003, -119.926431500000007 45.943459799999999, -119.926085200000003 45.943822200000000, -119.925994200000005 45.943979200000001, -119.925948800000000 45.944003299999999, -119.925906800000007 45.944056199999999, -119.925865299999998 45.944203600000002, -119.925850900000000 45.944380299999999, -119.925859900000006 45.944558200000003, -119.925889999999995 45.944695899999999, -119.925988700000005 45.944872400000001, -119.926100500000004 45.945030199999998, -119.926299299999997 45.945242899999997, -119.926549800000004 45.945416799999997, -119.926799500000001 45.945545099999997, -119.926964600000005 45.945607899999999, -119.927070099999995 45.945670900000003, -119.927394100000001 45.945756400000000, -119.927829000000003 45.945803800000000, -119.928411600000004 45.945754600000001, -119.928764999999999 45.945664499999999, -119.929114799999994 45.945507800000001, -119.929259999999999 45.945402799999997, -119.929353000000006 45.945361300000002, -119.929623800000002 45.945101500000000, -119.929715900000005 45.944988899999998, -119.929873700000002 45.944688499999998, -119.929896200000002 45.944482600000001, -119.929877300000001 45.943934700000000, -119.929835999999995 45.943828500000002, -119.929687400000006 45.943697100000001, -119.929556800000000 45.943550000000002, -119.929358800000003 45.943387500000000, -119.929071100000002 45.943205800000001, -119.928866999999997 45.943107900000001, -119.928382900000003 45.942976999999999, -119.927917699999995 45.942934500000000, -119.927762799999996 45.942945299999998, -119.927637599999997 45.942965700000002, -119.927243599999997 45.943076099999999, -119.926705200000001 45.943180499999997, -119.926533699999993 45.943281200000001, -119.926492100000004 45.943371300000003))", "id": "mercer10_field", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["FIELD"]}, null, null, null]}}, {"coordinates": "POLYG<PERSON> ((-119.926614900000004 45.943385700000000, -119.926481699999997 45.943512699999999, -119.926478700000004 45.943514100000002, -119.926477700000007 45.943514100000002, -119.926469800000007 45.943514200000003, -119.926465199999996 45.943514499999999, -119.926453100000003 45.943512499999997, -119.926473200000004 45.943513899999999, -119.926471699999993 45.943514700000001, -119.926469699999998 45.943516000000002, -119.926446299999995 45.943548499999999, -119.926432000000005 45.943562000000000, -119.926350099999993 45.943627900000003, -119.926133600000000 45.943869900000003, -119.926013499999996 45.944174500000003, -119.925991900000000 45.944316000000001, -119.925991600000003 45.944457100000001, -119.926017200000004 45.944616900000000, -119.926072700000006 45.944774500000001, -119.926134000000005 45.944897699999999, -119.926246100000000 45.945051800000002, -119.926369800000003 45.945182199999998, -119.926514900000001 45.945301899999997, -119.926689499999995 45.945414000000000, -119.926843500000004 45.945485800000000, -119.927223600000005 45.945620499999997, -119.927649099999996 45.945699099999999, -119.927922300000006 45.945711899999999, -119.928115099999999 45.945702300000001, -119.928590299999996 45.945621400000000, -119.928810400000003 45.945551500000001, -119.929013100000006 45.945462700000000, -119.929358399999998 45.945239100000002, -119.929565699999998 45.945034900000003, -119.929653999999999 45.944912500000001, -119.929734699999997 45.944752299999998, -119.929791800000004 45.944570100000000, -119.929788400000007 45.944207599999999, -119.929759599999997 45.944063900000003, -119.929682799999995 45.943887400000001, -119.929560600000002 45.943716299999998, -119.929396699999998 45.943541600000003, -119.929166300000006 45.943377300000002, -119.928921099999997 45.943245800000000, -119.928600299999999 45.943133199999998, -119.928433400000003 45.943092999999998, -119.928142899999997 45.943050700000001, -119.927721300000002 45.943033100000001, -119.927284700000001 45.943112900000003, -119.927067699999995 45.943175699999998, -119.926851999999997 45.943262199999999, -119.926614900000004 45.943385700000000))", "id": "mercer10_crops", "type": {"py/reduce": [{"py/type": "lib.common.geo.boundary.BoundaryType"}, {"py/tuple": ["CROPS"]}, null, null, null]}}]}}