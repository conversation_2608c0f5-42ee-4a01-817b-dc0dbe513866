# Row modules

-d /exterminator sim
#-d /exterminator/row_module:1/scanner:1/gimbal/servo:pan pan1
#-d /exterminator/row_module:1/scanner:1/gimbal/servo:tilt tilt1
#-d /exterminator/row_module:1/scanner:1/gimbal gimbal1
#-d /exterminator/row_module:1/scanner:1/laser laser1
#-d /exterminator/row_module:1/scanner:2/gimbal/servo:pan pan2
#-d /exterminator/row_module:1/scanner:2/gimbal/servo:tilt tilt2
#-d /exterminator/row_module:1/scanner:2/gimbal gimbal2
#-d /exterminator/row_module:1/scanner:2/laser laser2
#-d /exterminator/row_module:2/scanner:1/gimbal/servo:pan pan3
#-d /exterminator/row_module:2/scanner:1/gimbal/servo:tilt tilt3
#-d /exterminator/row_module:2/scanner:1/gimbal gimbal3
#-d /exterminator/row_module:2/scanner:1/laser laser3
#-d /exterminator/row_module:2/scanner:2/gimbal/servo:pan pan4
#-d /exterminator/row_module:2/scanner:2/gimbal/servo:tilt tilt4
#-d /exterminator/row_module:2/scanner:2/gimbal gimbal4
#-d /exterminator/row_module:2/scanner:2/laser laser4

# Cameras

-d /retina sim
-d /retina/camera:front_right front_right
-d /retina/camera:back_right back_right
#-d /retina/camera:predict1 predict1
#-d /retina/camera:predict2 predict2
#-d /retina/camera:predict3 predict3
#-d /retina/camera:predict4 predict4
#-d /retina/camera:target1 target1
#-d /retina/camera:target2 target2
#-d /retina/camera:target3 target3
#-d /retina/camera:target4 target4
#-d /retina/camera:hubble1 hubble1
#-d /retina/lens:target1 lens1
#-d /retina/lens:target2 lens2
#-d /retina/lens:target3 lens3
#-d /retina/lens:target4 lens4
#-d /retina/lens:hubble1 lens5

# Driving

-d /driver/drive_system/hydraulics veggiedrive

-d /driver/drive_system/rotary_encoder:front_left rotary_encoder1
-d /driver/drive_system/rotary_encoder:front_right rotary_encoder2
-d /driver/drive_system/rotary_encoder:back_left rotary_encoder3
-d /driver/drive_system/rotary_encoder:back_right rotary_encoder4
-d /driver/drive_system/parking_brake parking_brake1

-d /frame/gps benjamin_gps
