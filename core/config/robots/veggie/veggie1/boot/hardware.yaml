camera:
  predict1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict1
      looping: true
  predict2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict2
      looping: true
  predict3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict3
      looping: true
  predict4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict4
      looping: true
  target1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target1
      looping: true
  target2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target2
      looping: true
  target3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target3
      looping: true
  target4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target4
      looping: true
  front_right:
    bootloader: zed
    ip: **********
  back_right:
    bootloader: zed
    ip: **********
  hubble1:
    bootloader: emergent
    ip: **********
servo:
  pan1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 12000
      min: 0
      resolution: 262144
  pan2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 0
      resolution: 262144
  pan3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 0
      resolution: 262144
  pan4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 11500
      min: 0
      resolution: 262144
  tilt1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 1700
      resolution: 262144
  tilt2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 3000
      resolution: 262144
  tilt3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 1500
      resolution: 262144
  tilt4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 13000
      min: 2500
      resolution: 262144
laser:
  laser1:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser2:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser3:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
  laser4:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
gimbal:
  gimbal1:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal2:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal3:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
  gimbal4:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
gps:
  benjamin_gps:
    bootloader: benjamin_gps
    ip: **********
    heading_offset: 180
ins:
  duro:
    bootloader: duro
    ip: *************
    port: 55555
  shmem:
    bootloader: shmem
lens:
  lens1:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.1:1.0-port0
  lens2:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.2:1.0-port0
  lens3:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.3:1.0-port0
  lens4:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.4:1.0-port0
  lens5:
    bootloader: emergent
hydraulics:
  veggiedrive:
    bootloader: express
    module: core.drivers.hydraulics.veggiedrive
    func: Veggiedrive
    kwargs:
      can_device: can0
      rotary_limits_frame_angles_deg:
        front_left: [-45, 45]
        front_right: [-45, 45]
      min_drive_current_mA: 500
      max_drive_current_mA: 1000
rotary_encoder:
  rotary_encoder1:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 40
  rotary_encoder2:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 40
  rotary_encoder3:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 20000
      directional: true
  rotary_encoder4:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 20000
      directional: true
parking_brake:
  parking_brake1:
    bootloader: nofx_parking_brake
    kwargs:
      device_id: nofx_board_1
