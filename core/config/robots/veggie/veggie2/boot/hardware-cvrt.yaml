hydraulics:
  veggiedrive:
    bootloader: express
    module: core.drivers.hydraulics.veggiedrive
    func: Veggiedrive
    kwargs:
      can_device: can0
      rotary_offset_frame_angles_deg:
        front_left: 0
        front_right: 0
      rotary_limits_frame_angles_deg:
        front_left: [-45, 45]
        front_right: [-45, 45]
      max_drive_current_mA: 950
camera:
  predict1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict1
      looping: true
  predict2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict2
      looping: true
  predict3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict3
      looping: true
  predict4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict4
      looping: true
  target1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target1
      looping: true
  target2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target2
      looping: true
  target3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target3
      looping: true
  target4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target4
      looping: true
servo:
  pan1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 12000
      min: 0
      resolution: 262144
  pan2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 0
      resolution: 262144
  pan3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 0
      resolution: 262144
  pan4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 11500
      min: 0
      resolution: 262144
  tilt1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 12000
      min: 1700
      resolution: 262144
  tilt2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 3000
      resolution: 262144
  tilt3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 1500
      resolution: 262144
  tilt4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 13000
      min: 2500
      resolution: 262144
laser:
  laser1:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser2:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser3:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
  laser4:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
gimbal:
  gimbal1:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal2:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal3:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
  gimbal4:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
lens:
  lens1:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.1:1.0-port0
  lens2:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.2:1.0-port0
  lens3:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.4:1.0-port0
  lens4:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.3:1.0-port0
