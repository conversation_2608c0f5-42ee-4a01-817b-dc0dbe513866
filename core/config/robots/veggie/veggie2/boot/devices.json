{"devices": {"row_module_1": {"module": "lib.common.devices.boards.row_module.row_module_board_device", "boot_func": "RowModuleBoardDevice", "kwargs": {"serial_port": "/dev/serial/by-id/usb-Maka_ARS_M_USB_60000208160B0593-if00", "max_velocity_mrpm": 590000, "settle_timeout_ms": 250, "settle_window": 50, "home_offset": 1000, "home_step": 1000, "dawg_timeout_ms": 1000, "dawg_petting_interval_ms": 300, "scanners": [{"scanner_id": 1, "pan_id": 9, "tilt_id": 10, "intensity": 1}, {"scanner_id": 2, "pan_id": 12, "tilt_id": 11, "intensity": 1}]}}, "row_module_2": {"module": "lib.common.devices.boards.row_module.row_module_board_device", "boot_func": "RowModuleBoardDevice", "kwargs": {"serial_port": "/dev/serial/by-id/usb-Maka_ARS_M_USB_320002090B171893-if00", "max_velocity_mrpm": 590000, "settle_timeout_ms": 250, "settle_window": 50, "home_offset": 1000, "home_step": 1000, "dawg_timeout_ms": 1000, "dawg_petting_interval_ms": 300, "scanners": [{"scanner_id": 1, "pan_id": 13, "tilt_id": 14, "intensity": 1}, {"scanner_id": 2, "pan_id": 15, "tilt_id": 16, "intensity": 1}]}}}}