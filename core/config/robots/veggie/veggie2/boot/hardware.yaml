hydraulics:
  veggiedrive:
    bootloader: express
    module: core.drivers.hydraulics.veggiedrive
    func: Veggiedrive
    kwargs:
      can_device: can0
      rotary_limits_frame_angles_deg:
        front_left: [-45, 45]
        front_right: [-45, 45]
      min_drive_current_mA: 475
      max_drive_current_mA: 1000
camera:
  predict1:
    bootloader: pylon
    device: 267601CA266E
    type: acA4112-20uc
    args:
      transpose: true
      flip: true
      strobing: true
  predict2:
    bootloader: pylon
    device: 267601CA2671
    type: acA4112-20uc
    args:
      transpose: true
      mirror: true
      strobing: true
  predict3:
    bootloader: pylon
    device: 267601CA28DA
    type: acA4112-20uc
    args:
      transpose: true
      flip: true
      strobing: true
  predict4:
    bootloader: pylon
    device: 267601CA0CDE
    type: acA4112-20uc
    args:
      transpose: true
      mirror: true
      strobing: true
  target1:
    bootloader: pylon
    device: 26760165A936
    type: acA1300-200uc
    args:
      latency_ms: 20
      transpose: true
      strobing: true
  target2:
    bootloader: pylon
    device: 26760165A929
    type: acA1300-200uc
    args:
      latency_ms: 20
      transpose: true
      strobing: true
  target3:
    bootloader: pylon
    device: 26760165A928
    type: acA1300-200uc
    args:
      latency_ms: 20
      transpose: true
      strobing: true
  target4:
    bootloader: pylon
    device: 26760165A93B
    type: acA1300-200uc
    args:
      latency_ms: 20
      transpose: true
      strobing: true
servo:
  pan1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 12000
      min: 0
      resolution: 262144
  pan2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 0
      resolution: 262144
  pan3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 0
      resolution: 262144
  pan4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 11500
      min: 0
      resolution: 262144
  tilt1:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 12000
      min: 1700
      resolution: 262144
  tilt2:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_1
      max: 13000
      min: 3000
      resolution: 262144
  tilt3:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 12000
      min: 1500
      resolution: 262144
  tilt4:
    bootloader: row_module_board_servo
    kwargs:
      device_id: row_module_2
      max: 13000
      min: 2500
      resolution: 262144
laser:
  laser1:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser2:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_1
  laser3:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
  laser4:
    bootloader: row_module_board_laser
    kwargs:
      device_id: row_module_2
gimbal:
  gimbal1:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal2:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_1
  gimbal3:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
  gimbal4:
    bootloader: row_module_board_gimbal
    kwargs:
      device_id: row_module_2
lens:
  lens1:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.1:1.0-port0
  lens2:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.2:1.0-port0
  lens3:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.4:1.0-port0
  lens4:
    bootloader: liquid_lens
    serial_dev: /dev/serial/by-path/pci-0000:00:14.0-usb-0:11.3:1.0-port0
#rotary_encoder:
#    rotary_encoder_treadkill:
#        bootloader: nofx_rotary_encoder
#        kwargs:
#          device_id: nofx_board_1
#          TPR: 20000
#          directional: true

