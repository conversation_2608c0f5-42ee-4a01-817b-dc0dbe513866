{"devices": {"nofx_board_1": {"module": "lib.common.devices.boards.nofx.nofx_board_device", "boot_func": "NoFXBoardDevice", "kwargs": {"reversed_polarity": false, "firmware_name": "wheel_encoder_board", "bootloader_type": "mcuboot", "pps_enabled": false, "version_check": false, "use_broadcast": true}}, "gps_1": {"module": "lib.common.devices.boards.gps.gps_board_device", "boot_func": "GPSBoardDevice", "kwargs": {"bootloader_type": "mcuboot", "firmware_name": "gps_board", "pps_enabled": false, "version_check": false}}}}