{"devices": {"nofx_board_1": {"module": "lib.common.devices.boards.nofx.nofx_board_device", "boot_func": "NoFXBoardDevice", "kwargs": {"wheel_diameter_in": 4.096, "reversed_polarity": true, "firmware_name": "wheel_encoder_board", "bootloader_type": "mcuboot", "pps_enabled": false, "version_check": true, "use_broadcast": true}}, "pulczar1": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 1, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar2": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 2, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar3": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 3, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar4": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 4, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "flow_override": false, "stf_alt_override": false, "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar5": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 5, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar6": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 6, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar7": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 7, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar8": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 8, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar9": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 9, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}, "pulczar10": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 10, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "bootloader_type": "mcuboot", "firmware_name": "pulsar_board", "pps_enabled": false, "pan_limit_range": [13000, 14000], "tilt_limit_range": [11000, 12000], "max_raw_laser_intensity": 1000}}}}