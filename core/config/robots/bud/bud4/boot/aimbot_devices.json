{"devices": {"nofx_board_1": {"module": "lib.common.devices.boards.nofx.nofx_board_device", "boot_func": "NoFXBoardDevice", "kwargs": {"firmware_name": "NoFX_Bud", "version_check": false, "reversed_polarity": true, "wheel_diameter_in": 4.096}}, "pulczar1": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 1, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "flow_override": true, "stf_alt_override": true, "pps_enabled": true}}, "pulczar2": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 2, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}, "pulczar3": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 3, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}, "pulczar4": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 4, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "flow_override": true, "stf_alt_override": true, "pps_enabled": true}}, "pulczar5": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 5, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}, "pulczar6": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 6, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}, "pulczar7": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 7, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}, "pulczar8": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 8, "settle_timeout_ms": 1000, "settle_window": 50, "fail_on_bad_status": false, "pps_enabled": true}}}}