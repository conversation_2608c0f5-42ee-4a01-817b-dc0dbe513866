{"retina": {"camera:predict1": {"ppi": 137.0}, "camera:predict2": {"ppi": 137.0}, "camera:predict3": {"ppi": 137.0}, "camera:predict4": {"ppi": 137.0}, "camera:target1": {"ppi": 164.0}, "camera:target2": {"ppi": 164.0}, "camera:target3": {"ppi": 164.0}, "camera:target4": {"ppi": 164.0}, "camera:target5": {"ppi": 164.0}, "camera:target6": {"ppi": 164.0}, "camera:target7": {"ppi": 164.0}, "camera:target8": {"ppi": 164.0}, "lens1": {"focus": 75, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens1"]}}, "max": 255, "min": 0}, "lens2": {"focus": 85, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens2"]}}, "max": 255, "min": 0}, "lens3": {"focus": 90, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens3"]}}, "max": 255, "min": 0}, "lens4": {"focus": 85, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens4"]}}, "max": 255, "min": 0}, "lens5": {"focus": 91, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens5"]}}, "max": 255, "min": 0}, "lens6": {"focus": 85, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens6"]}}, "max": 255, "min": 0}, "lens7": {"focus": 82, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens7"]}}, "max": 255, "min": 0}, "lens8": {"focus": 89, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens8"]}}, "max": 255, "min": 0}}}