{"devices": {"nofx_board_1": {"module": "lib.common.devices.boards.nofx.nofx_board_device", "boot_func": "NoFXBoardDevice", "kwargs": {"wheel_diameter_in": 1.5, "reversed_polarity": true, "firmware_name": "wheel_encoder_board", "bootloader_type": "mcuboot", "version_check": false, "pps_enabled": false}}, "pulczar1": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 1, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}, "pulczar2": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 2, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}, "pulczar3": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 3, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}, "pulczar4": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 4, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}, "pulczar5": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 5, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}, "pulczar6": {"module": "lib.common.devices.boards.pulczar.pulczar_board_device", "boot_func": "PulczarBoardDevice", "kwargs": {"scanner_id": 6, "settle_timeout_ms": 250, "settle_window": 50, "fail_on_bad_status": false}}}}