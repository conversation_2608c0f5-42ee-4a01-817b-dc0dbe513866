camera:
  predict1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict1
      looping: true
  predict2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict2
      looping: true
  predict3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict3
      looping: true
  predict4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_predict4
      looping: true
  target1:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target1
      looping: true
  target2:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target2
      looping: true
  target3:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target3
      looping: true
  target4:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target4
      looping: true
  target5:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target5
      looping: true
  target6:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target6
      looping: true
  target7:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target7
      looping: true
  target8:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_target8
      looping: true
  front_right:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_front_right
      looping: true
      has_depth: true
  front_left:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_front_left
      looping: true
      has_depth: true
  back_right:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_back_right
      looping: true
      has_depth: true
  back_left:
    bootloader: latest_buffer
    args:
      latest_buffer_name: cv_back_left
      looping: true
      has_depth: true
  hubble1:
    bootloader: kaya
    ip: **********
servo:
  pan1:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 1
  pan2:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 2
  pan3:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 3
  pan4:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 4
  pan5:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 5
  pan6:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 6
  pan7:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 7
  pan8:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 8
  tilt1:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 1
  tilt2:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 2
  tilt3:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 3
  tilt4:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 4
  tilt5:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 5
  tilt6:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 6
  tilt7:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 7
  tilt8:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_servo
    func: AimbotProcessServo
    kwargs:
      scanner_id: 8
gimbal:
  gimbal1:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 1
  gimbal2:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 2
  gimbal3:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 3
  gimbal4:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 4
  gimbal5:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 5
  gimbal6:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 6
  gimbal7:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 7
  gimbal8:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_gimbal
    func: AimbotProcessGimbal
    kwargs:
      scanner_id: 8
laser:
  laser1:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 1
  laser2:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 2
  laser3:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 3
  laser4:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 4
  laser5:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 5
  laser6:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 6
  laser7:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 7
  laser8:
    bootloader: express
    module: core.controls.exterminator.nodes.aimbot_process.aimbot_process_laser
    func: AimbotProcessLaser
    kwargs:
      scanner_id: 8
lens:
  lens1:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 1
  lens2:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 2
  lens3:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 3
  lens4:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 4
  lens5:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 5
  lens6:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 6
  lens7:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 7
  lens8:
    bootloader: aimbot_process_lens
    kwargs:
      scanner_id: 8
  lens9:
    bootloader: kaya
hydraulics:
  veggiedrive:
    bootloader: express
    module: core.drivers.hydraulics.veggiedrive
    func: Veggiedrive
    kwargs:
      can_device: can0
      rotary_limits_frame_angles_deg:
        front_left: [-45, 45]
        front_right: [-45, 45]
      min_drive_current_mA: 350
      max_drive_current_mA: 1000
rotary_encoder:
  rotary_encoder3:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 20000
      directional: true
  rotary_encoder4:
    bootloader: nofx_rotary_encoder
    kwargs:
      device_id: nofx_board_1
      TPR: 20000
      directional: true
parking_brake:
  parking_brake1:
    bootloader: nofx_parking_brake
    kwargs:
      device_id: nofx_board_1
gps:
  benjamin_gps:
    bootloader: benjamin_gps
    ip: ***********
fuel_sensor:
  fuel_sensor1:
    bootloader: nofx_fuel_sensor
    kwargs:
      device_id: nofx_board_1
      min_lvl: 1.0
      max_lvl: 0.6224
