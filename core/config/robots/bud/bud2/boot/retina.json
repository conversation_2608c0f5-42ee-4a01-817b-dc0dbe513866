{"retina": {"camera:predict1": {"ppi": 200.0}, "camera:predict2": {"ppi": 200.0}, "camera:predict3": {"ppi": 200.0}, "camera:predict4": {"ppi": 200.0}, "camera:target1": {"ppi": 182.0}, "camera:target2": {"ppi": 182.0}, "camera:target3": {"ppi": 182.0}, "camera:target4": {"ppi": 182.0}, "camera:target5": {"ppi": 182.0}, "camera:target6": {"ppi": 182.0}, "camera:target7": {"ppi": 182.0}, "camera:target8": {"ppi": 182.0}, "lens1": {"focus": 92, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens1"]}}, "max": 255, "min": 0}, "lens2": {"focus": 159, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens2"]}}, "max": 255, "min": 0}, "lens3": {"focus": 101, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens3"]}}, "max": 255, "min": 0}, "lens4": {"focus": 146, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens4"]}}, "max": 255, "min": 0}, "lens5": {"focus": 92, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens5"]}}, "max": 255, "min": 0}, "lens6": {"focus": 83, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens6"]}}, "max": 255, "min": 0}, "lens7": {"focus": 128, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens7"]}}, "max": 255, "min": 0}, "lens8": {"focus": 156, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens8"]}}, "max": 255, "min": 0}, "lens9": {"focus": 0.0, "lens_id": {"py/object": "core.model.id.ReferenceId", "py/newargs": {"py/tuple": ["lens9"]}}, "max": 1.0, "min": -1.0}}}