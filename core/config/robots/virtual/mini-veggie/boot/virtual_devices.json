{"virtual_devices": [{"type": "servo", "id": "servo1", "resolution": 20000, "min": 0, "max": 5500}, {"type": "servo", "id": "servo2", "resolution": 20000, "min": 0, "max": 20000}, {"type": "servo", "id": "servo3", "resolution": 20000, "min": 9500, "max": 20000}, {"type": "servo", "id": "servo4", "resolution": 20000, "min": 0, "max": 20000}, {"type": "servo", "id": "servo5", "resolution": 20000, "min": 0, "max": 10500}, {"type": "servo", "id": "servo6", "resolution": 20000, "min": 0, "max": 20000}, {"type": "servo", "id": "servo7", "resolution": 20000, "min": 9500, "max": 20000}, {"type": "servo", "id": "servo8", "resolution": 20000, "min": 0, "max": 20000}, {"type": "cam", "id": "cam1", "scale": 0.1, "source": "ground1", "resolution": [1280, 960], "get_pos": {"type": "servo", "servos": ["servo1", "servo2"]}}, {"type": "cam", "id": "cam2", "scale": 0.1, "source": "ground1", "resolution": [1280, 960], "get_pos": {"type": "servo", "servos": ["servo3", "servo4"]}}, {"type": "cam", "id": "cam3", "scale": 0.1, "source": "ground2", "resolution": [1280, 960], "get_pos": {"type": "servo", "servos": ["servo5", "servo6"]}}, {"type": "cam", "id": "cam4", "scale": 0.1, "source": "ground2", "resolution": [1280, 960], "get_pos": {"type": "servo", "servos": ["servo7", "servo8"]}}, {"type": "laser", "id": "laser1", "offset": [0, 0], "radius": 5, "power": 80, "source": "ground1", "get_pos": {"type": "target", "target": "cam1"}}, {"type": "laser", "id": "laser2", "offset": [0, 0], "radius": 5, "power": 80, "source": "ground1", "get_pos": {"type": "target", "target": "cam2"}}, {"type": "laser", "id": "laser3", "offset": [0, 0], "radius": 5, "power": 80, "source": "ground2", "get_pos": {"type": "target", "target": "cam3"}}, {"type": "laser", "id": "laser4", "offset": [0, 0], "radius": 5, "power": 80, "source": "ground2", "get_pos": {"type": "target", "target": "cam3"}}, {"type": "cam", "id": "source1", "scale": 1, "source": "ground1", "resolution": [3000, 1200], "get_pos": {"type": "fixed", "offset": [0, 0]}, "master_type": "ground"}, {"type": "cam", "id": "pcam1", "scale": 1, "source": "ground1", "resolution": [1920, 1200], "get_pos": {"type": "fixed", "offset": [0, 0]}}, {"type": "cam", "id": "pcam2", "scale": 1, "source": "ground1", "resolution": [1920, 1200], "get_pos": {"type": "fixed", "offset": [1, 0]}}, {"type": "cam", "id": "source2", "scale": 1, "source": "ground2", "resolution": [3000, 1200], "get_pos": {"type": "fixed", "offset": [0, 0]}, "master_type": "ground"}, {"type": "cam", "id": "pcam3", "scale": 1, "source": "ground2", "resolution": [1920, 1200], "get_pos": {"type": "fixed", "offset": [0, 0]}}, {"type": "cam", "id": "pcam4", "scale": 1, "source": "ground2", "resolution": [1920, 1200], "get_pos": {"type": "fixed", "offset": [1, 0]}}]}