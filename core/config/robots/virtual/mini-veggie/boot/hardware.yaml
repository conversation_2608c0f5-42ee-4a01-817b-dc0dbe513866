laser:
  laser1:
    virtual_id: laser1
    bootloader: virtual_laser
  laser2:
    virtual_id: laser2
    bootloader: virtual_laser
  laser3:
    virtual_id: laser3
    bootloader: virtual_laser
  laser4:
    virtual_id: laser4
    bootloader: virtual_laser
lens:
  lens1:
    virtual_id: lens1
    cam_id: cam1
    bootloader: virtual_lens
  lens2:
    virtual_id: lens2
    cam_id: cam2
    bootloader: virtual_lens
  lens3:
    virtual_id: lens3
    cam_id: cam3
    bootloader: virtual_lens
  lens4:
    virtual_id: lens4
    cam_id: cam4
    bootloader: virtual_lens
camera:
  predict1:
    virtual_id: pcam1
    bootloader: virtual_cam
  predict2:
    virtual_id: pcam2
    bootloader: virtual_cam
  predict3:
    virtual_id: pcam3
    bootloader: virtual_cam
  predict4:
    virtual_id: pcam4
    bootloader: virtual_cam
  target1:
    virtual_id: cam1
    bootloader: virtual_cam
    args:
      latency_ms: 150
  target2:
    virtual_id: cam2
    bootloader: virtual_cam
    args:
      latency_ms: 150
  target3:
    virtual_id: cam3
    bootloader: virtual_cam
    args:
      latency_ms: 150
  target4:
    virtual_id: cam4
    bootloader: virtual_cam
    args:
      latency_ms: 150
ins:
  device_to_body_rotation: [["cos(0)", "-sin(0)", 0], ["sin(0)", "cos(0)", 0], [0, 0, 1]]
servo:
  servo1:
    virtual_id: servo1
    bootloader: virtual_servo
  servo2:
    virtual_id: servo2
    bootloader: virtual_servo
  servo3:
    virtual_id: servo3
    bootloader: virtual_servo
  servo4:
    virtual_id: servo4
    bootloader: virtual_servo
  servo5:
    virtual_id: servo5
    bootloader: virtual_servo
  servo6:
    virtual_id: servo6
    bootloader: virtual_servo
  servo7:
    virtual_id: servo7
    bootloader: virtual_servo
  servo8:
    virtual_id: servo8
    bootloader: virtual_servo
