laser:
  laser1:
    virtual_id: laser1
    bootloader: virtual_laser
lens:
  lens1:
    virtual_id: lens1
    cam_id: cam1
    bootloader: virtual_lens
camera:
  predict1:
    bootloader: buffer
    args:
      ref_buffer_name: cv_predict1
      looping: true
  target1:
    bootloader: buffer
    args:
      ref_buffer_name: cv_target1
      looping: true
ins:
  device_to_body_rotation: [["cos(0)", "-sin(0)", 0], ["sin(0)", "cos(0)", 0], [0, 0, 1]]
servo:
  servo1:
    virtual_id: servo1
    bootloader: virtual_servo
  servo2:
    virtual_id: servo2
    bootloader: virtual_servo