import json
import os
import platform
from typing import Dict, List, Optional

from lib.common.error import MakaException
from lib.common.generation import GENERATION, generation


class MakaConfigException(MakaException):
    pass


_CONFIG_DIR = os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "robots"))
_GEO_DIR = os.path.realpath(os.path.join(os.getcwd(), os.path.dirname(__file__), "geo"))
_ROBOT_NAME_KEY = "MAKA_ROBOT_NAME"


# List of Robots by name (Must be unique)
_ROBOT_CONFIG_DIR_MAPPING = {}
for model in os.listdir(_CONFIG_DIR):
    model_path = os.path.join(_CONFIG_DIR, model)
    if not os.path.isdir(model_path):
        continue
    for robot in os.listdir(model_path):
        robot_path = os.path.join(model_path, robot)
        if not os.path.isdir(robot_path):
            continue
        _ROBOT_CONFIG_DIR_MAPPING[robot] = robot_path

_GEO_CONFIG_DIR_MAPPING = {}
for geo in os.listdir(_GEO_DIR):
    geo_path = os.path.join(_GEO_DIR, geo)
    _GEO_CONFIG_DIR_MAPPING[geo] = geo_path


def _config_exists(name: str) -> bool:
    return name in _ROBOT_CONFIG_DIR_MAPPING


class ConfigCategory:
    BOOT = "boot"
    CALIBRATION = "calibration"
    PYLON = "pylon"


class PhysicalRobotConfiguration:
    def __init__(self, path: str):
        self._path = path
        self._config_paths: Dict[str, Dict[str, str]] = {}
        self.reload()

    @property
    def path(self) -> str:
        return self._path

    def reload(self) -> None:
        self._config_paths = {}
        for category in os.listdir(self.path):
            category_path = os.path.join(self.path, category)
            if not os.path.isdir(category_path):
                continue
            self._config_paths[category] = {}
            for config in os.listdir(category_path):
                self._config_paths[category][config] = os.path.join(category_path, config)

    def get(self, category: str, name: str) -> str:
        try:
            return self._config_paths[category][name]
        except KeyError:
            raise MakaConfigException(
                f"Requested config category {category} and name {name} do not exist. Available Configs: {self}"
            )

    def get_or_make_path(self, category: str, name: str) -> str:
        try:
            return self.get(category, name)
        except MakaConfigException:
            pass
        return os.path.join(self.path, category, name)

    def get_no_fail(self, category: str, name: str) -> Optional[str]:
        category_paths = self._config_paths.get(category)
        if category_paths is not None:
            return category_paths.get(name)
        return None

    def __str__(self) -> str:
        return f"Robot Configuration: {self.path} \n {json.dumps(self._config_paths, indent=4)}"


def _load_config(name: str) -> PhysicalRobotConfiguration:
    try:
        robot_path = _ROBOT_CONFIG_DIR_MAPPING[name]
        return PhysicalRobotConfiguration(robot_path)
    except KeyError:
        raise MakaConfigException(
            f"Config for Robot: {name} not found. Available configs are: {list(_ROBOT_CONFIG_DIR_MAPPING.keys())}"
        )


def get_all_config_files_of_type(category: str, name: str) -> List[str]:
    results: List[str] = []
    for robot_name in _ROBOT_CONFIG_DIR_MAPPING.keys():
        config = _load_config(robot_name)
        file = config.get_no_fail(category, name)
        if file is not None:
            results.append(file)
    return results


def get_robot_name() -> str:
    return os.environ.get(_ROBOT_NAME_KEY, platform.node().split(".")[0])


def get_geofences_dirname(geo: str) -> Optional[str]:
    return _GEO_CONFIG_DIR_MAPPING.get(geo)


def override_robot_name(name: str) -> None:
    os.environ[_ROBOT_NAME_KEY] = name


def load_config(name: Optional[str] = None) -> PhysicalRobotConfiguration:
    if name is not None:
        return _load_config(name)
    gen = generation()
    if gen == GENERATION.SLAYER:
        if os.getenv("MAKA_ROBOT_NAME") == "slayertb1":
            return _load_config("slayertb1")
        else:
            return _load_config(gen.to_str())
    return _load_config(get_robot_name())


def config_exists() -> bool:
    gen = generation()
    if gen == GENERATION.SLAYER:
        if os.getenv("MAKA_ROBOT_NAME") == "slayertb1":
            return _config_exists("slayertb1")
        else:
            return _config_exists(gen.to_str())
    return _config_exists(get_robot_name())
