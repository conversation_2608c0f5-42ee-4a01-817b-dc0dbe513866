from typing import List, Optional

import lib.common.logging
import lib.common.tasks
from core.archive.bundle import Bundle
from core.archive.filter import Filter, to_filter
from core.archive.pipeline import ArchivePipeline
from core.archive.process import get_processors
from core.archive.request import ArchivePipelineRequest, ArchiveRequest
from core.archive.source import Source, to_source

LOG = lib.common.logging.get_logger(__name__)


def _pipeline(
    bundle: Bundle, processor_strs: List[str], archive_pipeline_request: ArchivePipelineRequest
) -> Optional[ArchivePipeline]:
    sources: List[Source] = [to_source(s) for s in archive_pipeline_request.sources]
    if len(sources) == 0:
        return None
    filters: List[Filter] = [to_filter(name, f) for name, f in archive_pipeline_request.filters.items()]
    processors = get_processors(bundle, processor_strs)

    return ArchivePipeline(bundle=bundle, sources=sources, filters=filters, processors=processors)


def archive(archive_request: ArchiveRequest) -> Bundle:
    bundle: Bundle = archive_request.bundle.to_bundle()

    for name, req in archive_request._pipelines.items():
        archive_pipeline = _pipeline(bundle, archive_request.processors, req)
        if archive_pipeline is not None:
            lib.common.tasks.start(name=f"archive/{name}", target=archive_pipeline.run)

    return bundle
