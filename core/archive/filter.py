import abc
import re
from datetime import datetime, timedelta
from typing import Dict, List, Union

import pytz

import lib.common.logging

LOG = lib.common.logging.get_logger(__name__)

ISO_HOUR_FMT = "%Y-%m-%dT%H"
ISO_FMT = "%Y-%m-%dT%H:%M:%D"


class Filter(abc.ABC):
    """
    Filters a source
    """

    def __init__(self, name: str):
        self._name: str = name

    @property
    def name(self) -> str:
        return self._name

    @abc.abstractmethod
    def apply(self, val: str) -> bool:
        pass


class RegexFilter(Filter):
    def __init__(self, name: str, pattern: str):
        super().__init__(name)
        self._pattern: str = pattern
        LOG.debug(f"Matching {pattern}")
        self._regex = re.compile(pattern)

    def apply(self, filename: str) -> bool:
        result = self._regex.search(filename) is None
        if result:
            LOG.debug(f"X {self._name} filtered {filename}")
        return result


class TimeFilter(RegexFilter):
    """
    A filter that matches a specified time range, provided as an ISO 8601 date
    trimmed to the hour, and a time zone string.
    """

    def __init__(self, *, start: str, end: str, tz: str):
        # parse them in as timezone-naive datetime objects
        start_dt_naive = datetime.strptime(start, ISO_HOUR_FMT)
        end_dt_naive = datetime.strptime(end, ISO_HOUR_FMT).replace(minute=59, second=59, microsecond=999999)
        # this is the timezone of the provided start/end time
        specified_tz = pytz.timezone(tz)
        start_dt_localized = specified_tz.localize(start_dt_naive)
        end_dt_localized = specified_tz.localize(end_dt_naive)
        # then get the values as UTC
        start_dt_utc = start_dt_localized.astimezone(tz=pytz.UTC)
        end_dt_utc = end_dt_localized.astimezone(tz=pytz.UTC)

        if specified_tz != pytz.UTC:
            LOG.debug(f"Matching {start_dt_utc} - {end_dt_utc} (specified as {start} - {end} {tz})")

        # match hours as strings
        hour_strs = [h.replace("T", "(T|_)") for h in TimeFilter._hours_between(start_dt_utc, end_dt_utc)]
        hours_pattern = f".*({'|'.join(hour_strs)}).*"

        super().__init__("time", hours_pattern)

    @staticmethod
    def _hours_between(start: datetime, end: datetime) -> List[str]:
        DATE_TIME_STRING_FORMAT = "%Y-%m-%dT%H"

        result = [start.strftime(DATE_TIME_STRING_FORMAT)]
        cur = start + timedelta(hours=1)
        while cur <= end:
            result.append(cur.strftime(DATE_TIME_STRING_FORMAT))
            cur = cur + timedelta(hours=1)

        return result


class MatchesAnyStringFilter(RegexFilter):
    """
    A filter that matches at least one of a given list of patterns
    """

    def __init__(self, name: str, patterns: List[str]):
        assert len(patterns) > 0
        super().__init__(name, f".*({'|'.join(patterns)}).*")


class MultiFilter(Filter):
    """
    Bridges multiple filters to appear as one
    """

    def __init__(self, name: str, filters: List[Filter]):
        super().__init__(name)
        self._filters: List[Filter] = filters

    def apply(self, val: str) -> bool:
        filterf: Filter
        for filterf in self._filters:
            if filterf.apply(val):
                return True
        return False


def to_filter(filter_type: str, vals: Union[List[str], Dict[str, str]]) -> Filter:
    # TODO enum?
    if filter_type == "regex":
        assert isinstance(vals, list)
        return MatchesAnyStringFilter(filter_type, vals)
    elif filter_type == "time":
        assert isinstance(vals, dict)
        return TimeFilter(start=vals["start_time"], end=vals["end_time"], tz=vals["timezone"])
    else:
        assert False, f"Unexpected filter_type: {filter_type}"
