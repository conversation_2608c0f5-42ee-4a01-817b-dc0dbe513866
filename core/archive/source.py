import abc
import os
from typing import Generator, List


class Source(abc.ABC):
    """
    A generator for archival data
    """

    def __init__(self, name: str) -> None:
        self._name = name

    @property
    def name(self) -> str:
        return self._name

    @abc.abstractmethod
    def generate(self) -> Generator[str, None, None]:
        pass


class FileSource(Source):
    """
    A single file
    """

    def __init__(self, name: str, filepath: str) -> None:
        super().__init__(name)
        self._filepath = os.path.abspath(filepath)

    @property
    def filepath(self) -> str:
        return self._filepath

    def generate(self) -> Generator[str, None, None]:
        """
        Yield each file from the sources one by one
        """
        yield self._filepath


class RecursiveFileDirectorySource(Source):
    """
    An archival source which recursively walks a file directory
    """

    def __init__(self, name: str, base_dir: str) -> None:
        super().__init__(name)
        self._base_dir = base_dir

    @property
    def base_dir(self) -> str:
        return self._base_dir

    def generate(self) -> Generator[str, None, None]:
        """
        Yield each file from the sources one by one
        """
        # r=root, d=directories, f = files
        for r, d, f in os.walk(self._base_dir):
            for file in f:
                yield os.path.join(r, file)


class MultiSource(Source):
    """
    Bridges multiple sources to appear as one
    """

    def __init__(self, name: str, sources: List[Source]) -> None:
        super().__init__(name)
        self._sources = sources

    def generate(self) -> Generator[str, None, None]:
        """
        Yield each source
        """
        for s in self._sources:
            for val in s.generate():
                yield val


def to_source(path: str) -> Source:
    if os.path.isdir(path):
        return RecursiveFileDirectorySource(os.path.basename(path), base_dir=path)
    else:
        return FileSource(path, filepath=path)
