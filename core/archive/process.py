import abc
import os
import shutil
from typing import List

import lib.common.logging
from core.archive.bundle import Bundle, get_bundle_subdir

LOG = lib.common.logging.get_logger(__name__)


class Processor(abc.ABC):
    """
    Processes a source
    """

    def __init__(self, name: str):
        self._name: str = name

    @property
    def name(self) -> str:
        return self._name

    @abc.abstractmethod
    def apply(self, val: str) -> None:
        """
        Archive a single record
        """
        pass


class BaseFileOpArchiver(Processor):
    """
    Base class for operations on a file
    """

    def __init__(self, name: str, bundle: Bundle):
        super().__init__(name)
        self._bundle = bundle

    def _dest(self, src_filename: str) -> str:
        bundle_subdir = os.path.join(self._bundle.dir, get_bundle_subdir(src_filename))
        os.makedirs(bundle_subdir, exist_ok=True)

        # determine the output filename
        return os.path.join(bundle_subdir, os.path.basename(src_filename))


class CopyArchiver(BaseFileOpArchiver):
    """
    Copy a single record
    """

    def apply(self, f: str) -> None:
        cpy_path = self._dest(f)

        # do the copy
        shutil.copy2(f, cpy_path)
        LOG.info(f"✓ Copy: {f} ---> {cpy_path}")


class SymlinkArchiver(BaseFileOpArchiver):
    """
    Symlinks a single record
    """

    def apply(self, f: str) -> None:
        symlink = self._dest(f)

        # do the symlink
        os.symlink(f, symlink)
        LOG.info(f"✓ Symlink: {symlink} ---> {f}")


class MultiProcessor(Processor):
    """
    Bridges multiple processors to appear as one
    """

    def __init__(self, name: str, processors: List[Processor]) -> None:
        super().__init__(name)
        self._processors: List[Processor] = processors

    def apply(self, val: str) -> None:
        processor: Processor
        for processor in self._processors:
            processor.apply(val)


def get_processors(bundle: Bundle, processor_strs: List[str]) -> List[Processor]:
    result: List[Processor] = []
    for s in processor_strs:
        if s == "symlink":
            result.append(SymlinkArchiver(s, bundle))
        elif s == "copy":
            result.append(CopyArchiver(s, bundle))
        else:
            assert False, f"Unexpected processor: {s}"
    return result
