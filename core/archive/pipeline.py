from typing import List

import lib.common.logging
from core.archive.bundle import Bundle
from core.archive.filter import Filter, MultiFilter
from core.archive.process import MultiProcessor, Processor
from core.archive.source import MultiSource, Source

LOG = lib.common.logging.get_logger(__name__)


class ArchivePipeline:
    """
    Drain sources --> Filter --> Process --> Bundle
    """

    def __init__(self, bundle: Bundle, sources: List[Source], filters: List[Filter], processors: List[Processor]):
        self._bundle = bundle

        self._multi_source = MultiSource("all", sources)

        self._multi_filter = MultiFilter("all", filters)

        self._multi_processor = MultiProcessor("all", processors)

    def run(self) -> None:
        """
        Runs the archival pipeline
        """
        count = 0
        for f in self._multi_source.generate():
            if not self._multi_filter.apply(f):
                self._multi_processor.apply(f)
                count += 1

        # Done
        if count == 0:
            LOG.warning("No files found matching criteria")
            return None
