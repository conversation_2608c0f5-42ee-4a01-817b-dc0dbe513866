from typing import Any, Dict, List

from core.archive.bundle import Bundle
from lib.common.serialization.json import JsonSerializable

# TODO I wrote this all by hand and it sucked. We need to build out serde


class ArchiveBundleRequest(JsonSerializable):
    def __init__(self, id_: str, base_dir: str) -> None:
        self._id = id_
        self._base_dir = base_dir

    @property
    def id(self) -> str:
        return self._id

    def __str__(self) -> str:
        return str(self.to_json())

    def to_json(self) -> Dict[str, str]:
        return {"id": self._id, "base_dir": self._base_dir}

    def to_bundle(self) -> Bundle:
        return Bundle(name=self._id, base_dir=self._base_dir)

    @classmethod
    def from_json(cls, data: Dict[str, str]) -> "ArchiveBundleRequest":
        return ArchiveBundleRequest(data["id"], data["base_dir"])


class ArchivePipelineRequest(JsonSerializable):
    def __init__(self, sources: List[str], filters: Dict[str, List[str]]):
        self._sources = sources
        self._filters = filters

    @property
    def sources(self) -> List[str]:
        return self._sources

    @property
    def filters(self) -> Dict[str, List[str]]:
        return self._filters

    def __str__(self) -> str:
        return str(self.to_json())

    def to_json(self) -> Dict[str, Any]:
        return {"sources": self._sources, "filters": self._filters}

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ArchivePipelineRequest":
        return ArchivePipelineRequest(sources=data.get("sources", []), filters=data.get("filters", {}))


class ArchiveRequest(JsonSerializable):
    def __init__(
        self, bundle: ArchiveBundleRequest, processors: List[str], pipelines: Dict[str, ArchivePipelineRequest]
    ):
        self._bundle: ArchiveBundleRequest = bundle
        self._processors: List[str] = processors
        self._pipelines: Dict[str, ArchivePipelineRequest] = pipelines

    def __str__(self) -> str:
        return str(self.to_json())

    @property
    def bundle(self) -> ArchiveBundleRequest:
        return self._bundle

    @property
    def processors(self) -> List[str]:
        return self._processors

    @property
    def pipelines(self) -> Dict[str, ArchivePipelineRequest]:
        return self._pipelines

    def to_json(self) -> Dict[str, Any]:
        return {
            "bundle": self._bundle.to_json(),
            "processors": self._processors,
            "pipelines": {k: v.to_json() for k, v in self._pipelines.items()},
        }

    @classmethod
    def from_json(cls, data: Dict[str, Any]) -> "ArchiveRequest":
        return ArchiveRequest(
            ArchiveBundleRequest.from_json(data["bundle"]),
            data["processors"],
            {k: ArchivePipelineRequest.from_json(v) for k, v in data["pipelines"].items()},
        )
