import os
from typing import Dict

from lib.common.serialization.json import JsonSerializable


class Bundle(JsonSerializable):
    """
    A bundle is the output of an archival process.
    """

    def __init__(self, name: str, base_dir: str):
        assert ":" not in name, 'Only troublemakers use ":" in filenames'
        self._name = name

        self._dir = os.path.join(base_dir, name)
        assert not os.path.exists(self._dir), f"{self._dir} already exists!"

    @property
    def name(self) -> str:
        return self._name

    @property
    def dir(self) -> str:
        return self._dir

    def to_json(self) -> Dict[str, str]:
        return {"name": self._name, "dir": self._dir}

    @classmethod
    def from_json(cls, data: Dict[str, str]) -> "Bundle":
        return Bundle(data["name"], data["dir"])


def get_bundle_subdir(filename: str) -> str:
    # inelegant
    if ".log" in filename:
        return "logs"
    elif "predict" in filename:
        return "predict"
    elif "target" in filename:
        return "target"
    elif "drive" in filename or "front_right" in filename:
        return "drive"
    else:
        return "logs"
