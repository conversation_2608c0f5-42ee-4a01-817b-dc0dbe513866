import argparse
import async<PERSON>
from argparse import Argument<PERSON><PERSON><PERSON>
from uuid import uuid1

from google.protobuf.json_format import Message<PERSON><PERSON><PERSON><PERSON>

from config.client.cpp.config_client_python import DEFAULT_PORT, ConfigSubscriber, make_robot_local_addr
from lib.common.generation import is_rtc
from lib.common.tasks.manager import get_event_loop_by_name
from software_manager.client import SoftwareManagerClient, pb


def is_valid_version(metadata: pb.SoftwareVersionMetadata) -> bool:
    return metadata.tag != "" and metadata.available and metadata.ready


async def get_version_summary(
    client: SoftwareManagerClient, config_subscriber: ConfigSubscriber, args: argparse.Namespace
) -> None:
    resp = await client.get_version_summary()
    print(MessageToJson(resp))


async def update(client: SoftwareManagerClient, config_subscriber: ConfigSubscriber, args: argparse.Namespace) -> None:
    summary = await client.get_version_summary()
    if not is_valid_version(summary.target):
        raise Exception(f"Target version error target summary={summary.target}")
    if not is_valid_version(summary.current):
        raise Exception(f"Current version error current summary={summary.current}")
    if summary.current.tag == summary.target.tag:
        raise Exception(f"Software already up to date current = target {summary.current.tag}")
    if await update_to_version(client, summary.target.tag, summary.current.tag):
        config_subscriber.get_client().set_string_value("common/software_manager/previous_version", summary.current.tag)
    else:
        raise Exception("Failed to update")


async def revert(client: SoftwareManagerClient, config_subscriber: ConfigSubscriber, args: argparse.Namespace) -> None:
    summary = await client.get_version_summary()
    if not is_valid_version(summary.previous):
        raise Exception(f"Previous version error previous summary={summary.previous}")
    if not is_valid_version(summary.current):
        raise Exception(f"Current version error current summary={summary.current}")
    if summary.current.tag == summary.previous.tag:
        raise Exception(f"Software already up to date current = previous {summary.current.tag}")
    if not await update_to_version(client, summary.previous.tag, summary.current.tag):
        raise Exception("Failed to revert")


async def update_to_version(client: SoftwareManagerClient, new_version: str, old_version: str) -> bool:
    if not new_version or not old_version:
        print(f"Invalid version old='{old_version}, new='{new_version}'")
    req_id = str(uuid1())
    try:
        await client.prepare_update(new_version, req_id)
    except Exception as ex:
        print(f"Failed to prepare update error={ex}")
        try:
            await client.abort_update(old_version, req_id)
        except Exception as ex:
            print(f"Abort failed: error={ex}")
        return False
    try:
        await client.trigger_update(new_version, req_id)
    except Exception as ex:
        print(f"Trigger update failed: error={ex}")
        return False
    return True


async def main() -> None:
    parser = ArgumentParser("Software Manager CLI client")
    subparsers = parser.add_subparsers(help="Software Manager command", dest="cmd", required=True)

    subparser = subparsers.add_parser("version_summary", help="get version summary")
    subparser.set_defaults(func=get_version_summary)

    subparser = subparsers.add_parser("update", help="update software")
    subparser.set_defaults(func=update)

    subparser = subparsers.add_parser("revert", help="update software")
    subparser.set_defaults(func=revert)
    try:
        args = parser.parse_args()
    except Exception as e:
        print(f"Invalid use of the tool: {e}")
        raise e

    config_subscriber = ConfigSubscriber(make_robot_local_addr(DEFAULT_PORT))
    if is_rtc():
        config_subscriber.add_config_tree("common", "common", "services/rtc_common.yaml")
    else:
        config_subscriber.add_config_tree("common", "common", "services/common.yaml")
    config_subscriber.start()
    await asyncio.get_event_loop().run_in_executor(None, lambda: config_subscriber.wait_until_ready())
    client = SoftwareManagerClient()
    await args.func(client, config_subscriber, args)


if __name__ == "__main__":
    asyncio.run_coroutine_threadsafe(main(), get_event_loop_by_name()).result()
