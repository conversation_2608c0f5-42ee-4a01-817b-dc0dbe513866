#!/usr/bin/env python3
"""
Algorithm Visualization Tool

This tool calls GetVisualizationData() from the commander CL<PERSON> and plots
the CLDAlgorithmSnapshot data on a graph, saving it to a file.

The CLDAlgorithmSnapshot contains:
- graph_points_x, graph_points_y: Main algorithm curve data points
- minimas_x, minimas_y: Local minima detected by the algorithm
- lines: Vertical line positions (e.g., crop row centers)
- timestamp_ms: When the data was captured

Usage:
    python algo_viewer.py output.png --row-id 1 --timestamp 0
    python algo_viewer.py output.gif --row-id 1 --frames 10
    python algo_viewer.py --help

Examples:
    # Get latest data for row 1 and save to file
    python algo_viewer.py algorithm_plot.png --row-id 1

    # Get data for row 2 at specific timestamp and save to file
    python algo_viewer.py algorithm_plot.png --row-id 2 --timestamp 1000

    # Create animated GIF with 10 frames of latest data
    python algo_viewer.py animation.gif --row-id 1 --frames 10

    # Just print summary without plotting
    python algo_viewer.py --summary-only
"""

import argparse
import asyncio
import os
import sys
import tempfile
from typing import Any, List, Optional, Tuple

# Set matplotlib backend for file output only
import matplotlib
import matplotlib.pyplot as plt

from generated.weed_tracking.proto import weed_tracking_pb2
from lib.common.commander.client import CommanderClient

matplotlib.use("Agg")  # Use non-interactive backend for file output


try:
    from PIL import Image

    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class AlgoVisualizationTool:
    """Tool for visualizing CLD Algorithm data from commander."""

    def __init__(self, commander_host: str = "localhost", commander_port: int = 61002):
        """Initialize the visualization tool.

        Args:
            commander_host: Hostname for commander service
            commander_port: Port for commander service
        """
        self.commander_host = commander_host
        self.commander_port = commander_port
        self.client: Optional[CommanderClient] = None

    def connect(self) -> None:
        """Initialize the commander client."""
        self.client = CommanderClient(self.commander_host, self.commander_port)

    async def close(self) -> None:
        """Close the commander client connection."""
        if self.client:
            await self.client.close()

    async def get_visualization_data(self, timestamp_ms: int = 0, row_id: int = 1) -> Any:
        """Fetch visualization data from commander.

        Args:
            timestamp_ms: Timestamp in milliseconds (0 for latest)
            row_id: Row ID to fetch data for

        Returns:
            GetNextVisualizationData2Response containing DiagnosticsSnapshot
        """
        if not self.client:
            raise RuntimeError("Client not connected. Call connect() first.")

        print(f"Fetching visualization data for row {row_id}, timestamp {timestamp_ms}...")
        response = await self.client.GetVisualizationData(timestamp_ms=timestamp_ms, row_id=row_id)
        return response

    async def _collect_single_frame(
        self, last_timestamp: int, row_id: int, frame_num: int, total_frames: int
    ) -> Tuple[Optional[weed_tracking_pb2.CLDAlgorithmSnapshot], int]:
        """Collect a single frame using long polling.

        Args:
            last_timestamp: Timestamp to use for long polling
            row_id: Row ID to fetch data for
            frame_num: Current frame number (1-based)
            total_frames: Total number of frames being collected

        Returns:
            Tuple of (snapshot, new_timestamp) where new_timestamp is the timestamp to use for next poll
        """
        if not self.client:
            raise RuntimeError("Client not connected. Call connect() first.")

        print(f"Collecting frame {frame_num}/{total_frames}...")
        response = await self.client.GetVisualizationData(timestamp_ms=last_timestamp, row_id=row_id)
        snapshot = self.extract_cld_algorithm_snapshot(response)

        if snapshot is not None:
            new_timestamp = response.ts.timestamp_ms
            print(f"Frame {frame_num} collected at timestamp {new_timestamp}")
            return snapshot, new_timestamp
        else:
            print(f"Warning: No data available for frame {frame_num}")
            return None, last_timestamp

    async def get_multiple_frames(
        self, num_frames: int, row_id: int = 1
    ) -> List[weed_tracking_pb2.CLDAlgorithmSnapshot]:
        """Fetch multiple frames of visualization data using long polling.

        Args:
            num_frames: Number of frames to capture
            row_id: Row ID to fetch data for

        Returns:
            List of CLDAlgorithmSnapshot objects
        """
        snapshots = []
        last_timestamp = 0  # Start with 0 to get the first frame
        print(f"Capturing {num_frames} frames for row {row_id}...")

        for i in range(num_frames):
            snapshot, last_timestamp = await self._collect_single_frame(last_timestamp, row_id, i + 1, num_frames)
            if snapshot is not None:
                snapshots.append(snapshot)

        return snapshots

    async def collect_and_generate_gif_async(
        self, num_frames: int, row_id: int, output_path: str, duration: float = 0.5
    ) -> None:
        """Collect snapshots and generate plots asynchronously while collecting data.

        Args:
            num_frames: Number of frames to capture
            row_id: Row ID to fetch data for
            output_path: Path to save the GIF
            duration: Duration per frame in seconds
        """
        if not PIL_AVAILABLE:
            raise RuntimeError("PIL (Pillow) is required for GIF creation. Install with: pip install Pillow")

        if not self.client:
            raise RuntimeError("Client not connected. Call connect() first.")

        print(f"Starting async collection of {num_frames} frames for row {row_id}...")

        # Shared state between producer and consumer
        snapshots = []
        temp_files = []
        collection_complete = False
        last_timestamp = 0

        async def collect_snapshots() -> None:
            """Producer: Collect snapshots from commander."""
            nonlocal last_timestamp, collection_complete

            for i in range(num_frames):
                snapshot, last_timestamp = await self._collect_single_frame(last_timestamp, row_id, i + 1, num_frames)
                if snapshot is not None:
                    snapshots.append(snapshot)

            collection_complete = True
            print("Snapshot collection complete")

        async def generate_plots() -> None:
            """Consumer: Generate plots as snapshots become available."""
            processed_count = 0

            while not collection_complete or processed_count < len(snapshots):
                # Wait for new snapshots to be available
                if processed_count >= len(snapshots):
                    await asyncio.sleep(0.1)  # Small delay before checking again
                    continue

                # Process available snapshots
                while processed_count < len(snapshots):
                    snapshot = snapshots[processed_count]
                    frame_num = processed_count + 1

                    print(f"Generating plot for frame {frame_num}/{num_frames}...")
                    temp_file = await self._plot_frame_to_buffer_async(snapshot, frame_num, num_frames)
                    temp_files.append(temp_file)
                    processed_count += 1
                    print(f"Plot generated for frame {frame_num}")

        # Run collection and plot generation concurrently
        await asyncio.gather(collect_snapshots(), generate_plots())

        # Create GIF from generated plots
        self._create_gif_from_temp_files(temp_files, output_path, duration)

    def extract_cld_algorithm_snapshot(self, response: Any) -> Optional[weed_tracking_pb2.CLDAlgorithmSnapshot]:
        """Extract CLDAlgorithmSnapshot from the response.

        Args:
            response: GetNextVisualizationData2Response

        Returns:
            CLDAlgorithmSnapshot if available, None otherwise
        """
        if not response.data:
            print("No diagnostics data in response")
            return None

        if not response.data.HasField("banding_algorithm_snapshot"):
            print("No banding algorithm snapshot available in diagnostics data")
            return None

        # Type cast to ensure proper return type
        return response.data.banding_algorithm_snapshot  # type: ignore[no-any-return]

    def _create_plot(self, snapshot: weed_tracking_pb2.CLDAlgorithmSnapshot, title: str) -> Tuple[Any, Any]:
        """Create a matplotlib plot for the given snapshot.

        Args:
            snapshot: CLDAlgorithmSnapshot containing the data to plot
            title: Title for the plot

        Returns:
            Tuple of (figure, axes) objects
        """
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))

        # Plot main graph points
        if snapshot.graph_points_x and snapshot.graph_points_y:
            ax.plot(
                snapshot.graph_points_x, snapshot.graph_points_y, "b-", linewidth=2, label="Graph Points", alpha=0.7
            )

        # Plot minimas
        if snapshot.minimas_x and snapshot.minimas_y:
            ax.scatter(snapshot.minimas_x, snapshot.minimas_y, color="red", s=50, label="Minimas", zorder=5)

        # Plot lines (assuming they represent vertical or horizontal lines)
        if snapshot.lines:
            for i, line_val in enumerate(snapshot.lines):
                ax.axvline(x=line_val, color="green", linestyle="--", alpha=0.6, label="Lines" if i == 0 else "")

        ax.set_xlabel("X Position (mm)")
        ax.set_ylabel("Y Position (mm)")
        ax.set_title(title)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        return fig, ax

    def plot_algorithm_data(self, snapshot: weed_tracking_pb2.CLDAlgorithmSnapshot, save_path: str) -> None:
        """Plot the CLDAlgorithmSnapshot data and save to file.

        Args:
            snapshot: CLDAlgorithmSnapshot containing the data to plot
            save_path: Path to save the plot
        """
        title = f"CLD Algorithm Visualization (Timestamp: {snapshot.timestamp_ms})"
        fig, _ = self._create_plot(snapshot, title)

        # Save plot to file
        plt.savefig(save_path, dpi=300, bbox_inches="tight")
        print(f"Plot saved to: {save_path}")

        # Close the figure to free memory
        plt.close(fig)

    def plot_frame_to_buffer(
        self, snapshot: weed_tracking_pb2.CLDAlgorithmSnapshot, frame_num: int, total_frames: int
    ) -> str:
        """Plot a single frame and save to a temporary file.

        Args:
            snapshot: CLDAlgorithmSnapshot containing the data to plot
            frame_num: Current frame number (1-based)
            total_frames: Total number of frames

        Returns:
            Path to the temporary file containing the frame
        """
        title = f"CLD Algorithm Visualization - Frame {frame_num}/{total_frames} (Timestamp: {snapshot.timestamp_ms})"
        fig, _ = self._create_plot(snapshot, title)

        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
        plt.savefig(temp_file.name, dpi=150, bbox_inches="tight")  # Lower DPI for GIF
        plt.close(fig)

        return temp_file.name

    def _create_gif_from_temp_files(self, temp_files: List[str], output_path: str, duration: float) -> None:
        """Create a GIF from temporary image files.

        Args:
            temp_files: List of paths to temporary image files
            output_path: Path to save the GIF
            duration: Duration per frame in seconds
        """
        try:
            print(f"Assembling GIF from {len(temp_files)} frames...")
            images = []
            for temp_file in temp_files:
                img = Image.open(temp_file)
                images.append(img)

            if images:
                images[0].save(
                    output_path,
                    save_all=True,
                    append_images=images[1:],
                    duration=int(duration * 1000),  # Convert to milliseconds
                    loop=0,  # Infinite loop
                )
                print(f"GIF saved to: {output_path}")
            else:
                raise RuntimeError("No images were created for GIF")

        finally:
            # Clean up temporary files
            for temp_file in temp_files:
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass

    async def create_gif_from_snapshots_async(
        self, snapshots: List[weed_tracking_pb2.CLDAlgorithmSnapshot], output_path: str, duration: float = 0.5
    ) -> None:
        """Create an animated GIF from multiple snapshots asynchronously.

        Args:
            snapshots: List of CLDAlgorithmSnapshot objects
            output_path: Path to save the GIF
            duration: Duration per frame in seconds
        """
        if not PIL_AVAILABLE:
            raise RuntimeError("PIL (Pillow) is required for GIF creation. Install with: pip install Pillow")

        if not snapshots:
            raise ValueError("No snapshots provided for GIF creation")

        print(f"Creating GIF with {len(snapshots)} frames...")

        # Create temporary frame files asynchronously
        # Create tasks for generating all frames concurrently
        frame_tasks = []
        for i, snapshot in enumerate(snapshots):
            task = asyncio.create_task(self._plot_frame_to_buffer_async(snapshot, i + 1, len(snapshots)))
            frame_tasks.append(task)

        # Wait for all frames to be generated
        temp_files = await asyncio.gather(*frame_tasks)
        print(f"Generated all {len(snapshots)} frames")

        # Create GIF from generated plots
        self._create_gif_from_temp_files(temp_files, output_path, duration)

    async def _plot_frame_to_buffer_async(
        self, snapshot: weed_tracking_pb2.CLDAlgorithmSnapshot, frame_num: int, total_frames: int
    ) -> str:
        """Plot a single frame asynchronously and save to a temporary file.

        Args:
            snapshot: CLDAlgorithmSnapshot containing the data to plot
            frame_num: Current frame number (1-based)
            total_frames: Total number of frames

        Returns:
            Path to the temporary file containing the frame
        """
        # Run the plotting in a thread pool to avoid blocking the event loop
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.plot_frame_to_buffer, snapshot, frame_num, total_frames)

    def print_algorithm_summary(self, snapshot: weed_tracking_pb2.CLDAlgorithmSnapshot) -> None:
        """Print a summary of the algorithm data.

        Args:
            snapshot: CLDAlgorithmSnapshot to summarize
        """
        print("\n=== CLD Algorithm Data Summary ===")
        print(f"Timestamp: {snapshot.timestamp_ms} ms")
        print(f"Graph Points: {len(snapshot.graph_points_x)} points")
        print(f"Minimas: {len(snapshot.minimas_x)} points")
        print(f"Lines: {len(snapshot.lines)} lines")

        if snapshot.graph_points_x and snapshot.graph_points_y:
            x_range = (min(snapshot.graph_points_x), max(snapshot.graph_points_x))
            y_range = (min(snapshot.graph_points_y), max(snapshot.graph_points_y))
            print(f"X Range: {x_range[0]:.2f} to {x_range[1]:.2f} mm")
            print(f"Y Range: {y_range[0]:.2f} to {y_range[1]:.2f} mm")

        if snapshot.minimas_x and snapshot.minimas_y:
            print(f"Minima X positions: {[f'{x:.2f}' for x in snapshot.minimas_x]}")
            print(f"Minima Y positions: {[f'{y:.2f}' for y in snapshot.minimas_y]}")

        if snapshot.lines:
            print(f"Line positions: {[f'{line:.2f}' for line in snapshot.lines]}")

        print("=" * 35)


def create_test_snapshot() -> weed_tracking_pb2.CLDAlgorithmSnapshot:
    """Create a test CLDAlgorithmSnapshot for demonstration purposes."""
    import math

    snapshot = weed_tracking_pb2.CLDAlgorithmSnapshot()
    snapshot.timestamp_ms = 1234567890

    # Create some sample graph points (simulating a crop line detection curve)
    x_points = list(range(0, 1000, 10))  # 0 to 1000mm in 10mm steps
    y_points = [100 + 50 * math.sin(x / 100) + 10 * math.sin(x / 30) for x in x_points]

    snapshot.graph_points_x.extend(x_points)
    snapshot.graph_points_y.extend(y_points)

    # Add some minimas
    snapshot.minimas_x.extend([200, 500, 800])
    snapshot.minimas_y.extend([75, 85, 95])

    # Add some line positions
    snapshot.lines.extend([250, 750])

    return snapshot


async def main() -> int:
    """Main function to run the algorithm visualization tool."""
    parser = argparse.ArgumentParser(
        description="Visualize CLD Algorithm data from commander and save to file or create animated GIF",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s plot.png --row-id 1                    # Get latest data for row 1
  %(prog)s plot.png --row-id 2 --timestamp 1000   # Get data for row 2 at timestamp 1000ms
  %(prog)s animation.gif --row-id 1 --frames 10   # Create animated GIF with 10 frames
  %(prog)s --summary-only                         # Only print data summary, don't plot
        """,
    )

    parser.add_argument(
        "output_file", nargs="?", type=str, help="Output file path for the plot/GIF (required unless --summary-only)"
    )
    parser.add_argument("--row-id", "-r", type=int, default=1, help="Row ID to fetch data for (default: 1)")
    parser.add_argument(
        "--timestamp",
        "-t",
        type=int,
        default=0,
        help="Timestamp in ms (default: 0 for latest, ignored when --frames is used)",
    )
    parser.add_argument(
        "--frames",
        "-f",
        type=int,
        help="Number of frames to capture for animated GIF (creates GIF instead of single image)",
    )
    parser.add_argument(
        "--duration",
        "-d",
        type=float,
        default=0.5,
        help="Duration per frame in seconds for GIF animation (default: 0.5)",
    )
    parser.add_argument("--host", type=str, default="localhost", help="Commander host (default: localhost)")
    parser.add_argument("--port", type=int, default=61002, help="Commander port (default: 61002)")
    parser.add_argument("--summary-only", action="store_true", help="Only print data summary, don't plot")
    parser.add_argument("--test", action="store_true", help="Use test data instead of connecting to commander")

    args = parser.parse_args()

    # Validate that output_file is provided unless --summary-only is used
    if not args.summary_only and not args.output_file:
        parser.error("output_file is required unless --summary-only is specified")

    # Handle test mode
    if args.test:
        print("Using test data...")
        tool = AlgoVisualizationTool(args.host, args.port)  # Create tool for plotting methods

        if args.frames:
            # Create multiple test snapshots for GIF
            snapshots = []
            for i in range(args.frames):
                snapshot = create_test_snapshot()
                # Modify timestamp to make frames different
                snapshot.timestamp_ms = 1234567890 + (i * 1000)
                snapshots.append(snapshot)

            # Print summary of first frame
            tool.print_algorithm_summary(snapshots[0])

            # Create GIF unless summary-only mode
            if not args.summary_only:
                await tool.create_gif_from_snapshots_async(snapshots, args.output_file, args.duration)
        else:
            # Single frame mode
            snapshot = create_test_snapshot()

            # Print summary
            tool.print_algorithm_summary(snapshot)

            # Plot data unless summary-only mode
            if not args.summary_only:
                tool.plot_algorithm_data(snapshot, args.output_file)

        return 0
    else:
        # Create and connect to visualization tool
        tool = AlgoVisualizationTool(args.host, args.port)

        try:
            tool.connect()

            if args.frames:
                # Multi-frame mode for GIF creation with async processing
                if not args.summary_only:
                    await tool.collect_and_generate_gif_async(args.frames, args.row_id, args.output_file, args.duration)
                else:
                    # For summary-only mode, just collect one frame to show summary
                    snapshots = await tool.get_multiple_frames(1, args.row_id)
                    if snapshots:
                        tool.print_algorithm_summary(snapshots[0])
                    else:
                        print("No CLD algorithm data available.")
                        return 1
            else:
                # Single frame mode
                response = await tool.get_visualization_data(args.timestamp, args.row_id)
                snapshot_optional = tool.extract_cld_algorithm_snapshot(response)

                if snapshot_optional is None:
                    print("No CLD algorithm data available.")
                    return 1

                # At this point we know snapshot_optional is not None
                snapshot = snapshot_optional

                # Print summary
                tool.print_algorithm_summary(snapshot)

                # Plot data unless summary-only mode
                if not args.summary_only:
                    tool.plot_algorithm_data(snapshot, args.output_file)

            return 0

        except Exception as e:
            print(f"Error connecting to commander: {e}", file=sys.stderr)
            return 1
        finally:
            await tool.close()


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
