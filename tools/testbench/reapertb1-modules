#!/bin/bash

# Simple script to launch a tmux session connecting to the 4 modules on reapertb1
#
# requies an ssh config entry for reapertb1-module-1..4
# e.g.
# Host reapertb1-module-1
#   HostName reapertb1-module-1
#   User carbon
#   ForwardAgent yes
#   StrictHostKeyChecking no
#
# Usage:
# reapertb1-modules [D]
# D - kill the session

MODE=$1
echo "MODE=$MODE"

# Set session name
SESSION_NAME="reapertb1-modules"

if [[ "$MODE" == "D" ]]; then
	echo "Kiiling existing session $SESSION_NAME";
	tmux kill-sess -t "$SESSION_NAME";
	exit 0;
fi

if [[ $(tmux ls | grep "$SESSION_NAME" ) ]]; then
	echo "Attaching to existing session $SESSION_NAME";
	tmux attach -t "$SESSION_NAME"
	exit 0;
else
	echo "Creating new session $SESSION_NAME";
	# Start a new tmux session with the given name
	tmux new-session -d -s "$SESSION_NAME"
	tmux split-window -v -t "$SESSION_NAME"

	# Select the top pane (0) and split it horizontally (left/right)
	tmux select-pane -t "$SESSION_NAME":0
	tmux split-window -h -t "$SESSION_NAME"

	# Select the bottom pane (1) and split it horizontally (left/right)
	tmux select-pane -t "$SESSION_NAME":1
	tmux split-window -h -t "$SESSION_NAME"

	tmux select-layout -t "$SESSION_NAME" tiled

	tmux select-pane -t "$SESSION_NAME":0.0
        tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-module-1" Enter;

	tmux select-pane -t "$SESSION_NAME":0.1
        tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-module-2" Enter;

	tmux select-pane -t "$SESSION_NAME":0.2
        tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-module-3" Enter;

	tmux select-pane -t "$SESSION_NAME":0.3
	tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-module-4" Enter;

	# Attach to the session
	tmux attach -t "$SESSION_NAME";
	exit 0;
fi
