#!/bin/bash

# Simple script to launch a tmux session connecting to reapertb1-command with 2 panes
#
# requies an ssh config entry for reapertb1-command
# e.g.
# Host reapertb1-command
#   HostName reapertb1-command
#   User carbon
#   ForwardAgent yes
#   StrictHostKeyChecking no
#
# Usage:
# reapertb1-command [D]
# D - kill the session

MODE=$1
echo "MODE=$MODE"

# Set session name
SESSION_NAME="reapertb1-command"

if [[ "$MODE" == "D" ]]; then
	echo "Kiiling existing session $SESSION_NAME";
	tmux kill-sess -t "$SESSION_NAME";
	exit 0;
fi

if [[ $(tmux ls | grep "$SESSION_NAME" ) ]]; then
	echo "Attaching to existing session $SESSION_NAME";
	tmux attach -t "$SESSION_NAME"
	exit 0;
else
	echo "Creating new session $SESSION_NAME";
	# Start a new tmux session with the given name
	tmux new-session -d -s "$SESSION_NAME";
	tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-command" Enter;

	tmux split-window -h -t "$SESSION_NAME";
	tmux send-keys -t "$SESSION_NAME" "ssh reapertb1-command" Enter;

	# Attach to the session
	tmux attach -t "$SESSION_NAME";
	exit 0;
fi
