#include <sys/time.h>

#include "logging.h"
#include "network.h"
#include "schedule.h"
#include "packet_track.h"

#define PACKET_TRACK_INTERVAL_SECS 10

extern int can_received_packet_counter;

static struct {
  int started;
  int packet_count;
} packet_track = {.started = 0, .packet_count = 0};

static void packet_track_check(sock_pair *ss, struct timeval *scheduled, struct timeval *now) {
  if (packet_track.packet_count == can_received_packet_counter) {
    clwarn("TIM Packet Counter Not Moving! (%d/%d)\n", packet_track.packet_count, can_received_packet_counter);
  }
  packet_track.packet_count = can_received_packet_counter;
  schedule_relative(packet_track_check, now, PACKET_TRACK_INTERVAL_SECS, 0);
}

void start_can_packet_track() {

  if (!packet_track.started) {
    packet_track.started = 1;
    packet_track.packet_count = can_received_packet_counter;
    struct timeval now = getnow();

    schedule_relative(packet_track_check, &now, PACKET_TRACK_INTERVAL_SECS, 0);
  }
}
