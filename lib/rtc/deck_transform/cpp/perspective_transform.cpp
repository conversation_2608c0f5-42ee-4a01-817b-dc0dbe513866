#include "lib/rtc/deck_transform/cpp/perspective_transform.hpp"

#include <vector>

#include <opencv2/core.hpp>
#include <opencv2/cudawarping.hpp>
#include <opencv2/imgproc.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>
namespace carbon::deck_transform {

cv::Point2f from_px_pos(const PxPos &p) {
  return cv::Point2f(static_cast<float>(std::get<0>(p)), static_cast<float>(std::get<1>(p)));
}

PerspectiveTransform::PerspectiveTransform(PxPos top_left, PxPos bottom_left, PxPos top_right, PxPos bottom_right,
                                           PxPos out_shape)
    : output_size_(std::get<0>(out_shape), std::get<1>(out_shape)) {
  std::vector<cv::Point2f> roi_corners;
  std::vector<cv::Point2f> dst_corners;
  roi_corners.push_back(from_px_pos(top_left));
  dst_corners.emplace_back(0.0f, 0.0f);
  roi_corners.push_back(from_px_pos(bottom_left));
  dst_corners.emplace_back(0.0f, static_cast<float>(std::get<1>(out_shape)));
  roi_corners.push_back(from_px_pos(top_right));
  dst_corners.emplace_back(static_cast<float>(std::get<0>(out_shape)), 0.0f);
  roi_corners.push_back(from_px_pos(bottom_right));
  dst_corners.emplace_back(static_cast<float>(std::get<0>(out_shape)), static_cast<float>(std::get<1>(out_shape)));
  perspective_mat_ = cv::getPerspectiveTransform(roi_corners, dst_corners);
}
cv::cuda::GpuMat PerspectiveTransform::transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) {
  cv::cuda::GpuMat out;
  cv::cuda::warpPerspective(img, out, perspective_mat_, output_size_, cv::INTER_LINEAR, cv::BORDER_CONSTANT,
                            cv::Scalar(), stream);
  return out;
}

} // namespace carbon::deck_transform