#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

#include <tuple>

namespace carbon::deck_transform {
class PerspectiveTransform : public CudaTransform {
public:
  PerspectiveTransform(PxPos top_left, PxPos bottom_left, PxPos top_right, PxPos bottom_right, PxPos out_shape);
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  cv::Size output_size_;
  cv::Mat perspective_mat_;
};
} // namespace carbon::deck_transform