#pragma once
#include "lib/rtc/deck_transform/cpp/transform.hpp"

namespace carbon::deck_transform {
class Undistort : public CudaTransform {
public:
  Undistort(uint32_t width, uint32_t height, cv::Mat cam_mat, cv::Mat dist_coeff);
  virtual cv::cuda::GpuMat transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) override;

private:
  const uint32_t width_;
  const uint32_t height_;
  cv::cuda::GpuMat remap_x_;
  cv::cuda::GpuMat remap_y_;
};
} // namespace carbon::deck_transform