#include "lib/rtc/deck_transform/cpp/undistort.hpp"

#include <opencv2/calib3d.hpp>
#include <opencv2/core.hpp>
#include <opencv2/cudawarping.hpp>
#include <opencv2/imgproc.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>
namespace carbon::deck_transform {
Undistort::Undistort(uint32_t width, uint32_t height, cv::Mat cam_mat, cv::Mat dist_coeff)
    : width_(width), height_(height) {
  cv::Size size(width_, height_);
  auto new_cam_mat = cv::getOptimalNewCameraMatrix(cam_mat, dist_coeff, size, 1, size);
  cv::Mat remap_x, remap_y;
  cv::initUndistortRectifyMap(cam_mat, dist_coeff, cv::Mat(), new_cam_mat, size, CV_32FC1, remap_x, remap_y);
  remap_x_.upload(remap_x);
  remap_y_.upload(remap_y);
}
cv::cuda::GpuMat Undistort::transform(cv::cuda::GpuMat img, cv::cuda::Stream stream) {
  cv::cuda::GpuMat out;
  cv::cuda::remap(img, out, remap_x_, remap_y_, cv::INTER_LINEAR, cv::BORDER_CONSTANT, cv::Scalar(), stream);
  return out;
}

} // namespace carbon::deck_transform