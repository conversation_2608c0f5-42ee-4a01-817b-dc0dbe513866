import asyncio
from enum import Enum
from math import modf
from time import time
from typing import Optional

import grpc
from google.protobuf import timestamp_pb2
from tenacity import AsyncRetrying, RetryCallState, RetryError, stop_after_attempt

from generated.proto.geo import geo_pb2
from generated.proto.rtc import jobs_pb2, jobs_pb2_grpc
from hw_client_gps_distributor.pybind.hw_client_gps_distributor_python import CarrierSolution, FixType
from hw_client_gps_distributor.python.hw_client_gps_distributor import RawGpsData, get_next_non_opt
from lib.cloud.auth import TokenStore
from lib.cloud.grpc import GrpcConf, get_raw_channel
from lib.cloud.services import RTC_JOBS_GRPC_HOST, RTC_JOBS_GRPC_PORT
from lib.common.environment import robot_name
from lib.common.logging import get_logger
from lib.common.units.angle import Angle
from lib.rtc.tractor_pose import TRACTOR_POSE
from lib.rtc.tractor_pose import Position as PosePos

LOG = get_logger(__name__)


class InterventionCause(Enum):
    UNSPECIFIED = jobs_pb2.Intervention.INTERVENTION_CAUSE_UNSPECIFIED
    SENSOR_TRIGGERED = jobs_pb2.Intervention.SENSOR_TRIGGERED
    SAFETY_DRIVER_ACTION = jobs_pb2.Intervention.SAFETY_DRIVER_ACTION
    TRACTOR_REQUEST = jobs_pb2.Intervention.TRACTOR_REQUEST


def _fixed_pos(pos: RawGpsData) -> PosePos:
    antenna_pos = PosePos(x=pos.longitude, y=pos.latitude)
    if pos.dual is None:
        return antenna_pos
    return TRACTOR_POSE.get_fixed_point(antenna_pos, Angle.from_degrees(pos.dual.heading_deg.value))


def _pos_internal_to_grpc(pos: RawGpsData) -> geo_pb2.Point:
    fix_type = geo_pb2.FIX_TYPE_UNSPECIFIED
    if pos.fix_flags.carr_soln == CarrierSolution.FIXED_SOLUTION:
        fix_type = geo_pb2.RTK_FIXED
    elif pos.fix_flags.carr_soln == CarrierSolution.FLOATING_SOLUTION:
        fix_type = geo_pb2.RTK_FLOAT
    elif pos.fix_type == FixType.NO_FIX:
        fix_type = geo_pb2.NO_FIX
    elif pos.fix_type == FixType.GNSS_DR:
        fix_type = geo_pb2.GNSS
    elif pos.fix_type == FixType.DEAD_RECKONING_ONLY:
        fix_type = geo_pb2.DEAD_RECKONING
    fixed_pos = _fixed_pos(pos)
    return geo_pb2.Point(
        lng=fixed_pos.longitude,
        lat=fixed_pos.latitude,
        alt=pos.height_mm / 1000.0,
        capture_info=geo_pb2.CaptureInfo(fix_type=fix_type),
    )


def _make_proto_ts() -> timestamp_pb2.Timestamp:
    now = modf(time())
    return timestamp_pb2.Timestamp(seconds=int(now[1]), nanos=int(now[0] * 1e9))


class JobsServiceClient:
    def __init__(self, ts: TokenStore, conf: Optional[GrpcConf] = None) -> None:
        self._ts = ts
        if conf is None:
            self._conf = GrpcConf(RTC_JOBS_GRPC_HOST, RTC_JOBS_GRPC_PORT)
        else:
            self._conf = conf
        self.__stub: Optional[jobs_pb2_grpc.JobServiceStub] = None
        self._lock = asyncio.Lock()
        self._metadata = grpc.aio.Metadata(("robot", robot_name()),)

    async def _maybe_connect(self) -> None:
        async with self._lock:
            if self.__stub is not None:
                return
            channel = get_raw_channel(self._conf, await self._ts.fetch())
            self.__stub = jobs_pb2_grpc.JobServiceStub(channel)

    @property
    def _stub(self) -> jobs_pb2_grpc.JobServiceStub:
        assert self.__stub is not None
        return self.__stub

    async def get_active_task(self, pos: Optional[RawGpsData] = None) -> Optional[jobs_pb2.Task]:
        if pos is None:
            pos = await get_next_non_opt(0)
        req = jobs_pb2.GetActiveTaskRequest(current_location=_pos_internal_to_grpc(pos))
        async for attempt in AsyncRetrying(stop=stop_after_attempt(2), retry=self.__handle_retry):
            with attempt:
                await self._maybe_connect()
                resp: jobs_pb2.GetActiveTaskResponse = await self._stub.GetActiveTask(req, metadata=self._metadata)
                if resp.task is None or resp.task.id == 0:
                    return None
                return resp.task
        return None

    async def set_task_in_progress(self, task_id: int, pos: Optional[RawGpsData] = None) -> None:
        await self._maybe_connect()
        if pos is None:
            pos = await get_next_non_opt(0)
        assert pos.dual is not None
        req = jobs_pb2.UpdateTaskRequest(
            task_id=task_id,
            state=jobs_pb2.IN_PROGRESS,
            started_at=_make_proto_ts(),
            start_location=_pos_internal_to_grpc(pos),
            start_heading=pos.dual.heading_deg.value,
        )
        await self.__update_task(req)

    async def set_task_failed(self, task_id: int, pos: Optional[RawGpsData] = None) -> None:
        await self.__end_task(task_id, False, pos)

    async def set_task_complete(self, task_id: int, pos: Optional[RawGpsData] = None) -> None:
        await self.__end_task(task_id, True, pos)

    async def __end_task(self, task_id: int, success: bool, pos: Optional[RawGpsData] = None) -> None:
        if pos is None:
            pos = await get_next_non_opt(0)
        assert pos.dual is not None
        state = jobs_pb2.COMPLETED if success else jobs_pb2.FAILED
        req = jobs_pb2.UpdateTaskRequest(
            task_id=task_id,
            state=state,
            ended_at=_make_proto_ts(),
            end_location=_pos_internal_to_grpc(pos),
            end_heading=pos.dual.heading_deg.value,
        )
        await self.__update_task(req)

    def __handle_retry(self, retry_state: RetryCallState) -> bool:
        assert retry_state.outcome is not None
        if retry_state.outcome.failed:
            exception = retry_state.outcome.exception()
            assert exception is not None
            if isinstance(exception, grpc.aio.AioRpcError):
                if exception.code() == grpc.StatusCode.UNAVAILABLE:
                    return True
                elif exception.code() == grpc.StatusCode.UNAUTHENTICATED:
                    self.__stub = None
                    return True
                else:
                    LOG.error(f"GRPC call failed with code={exception.code()}, details={exception.details()}")
        return False

    async def __update_task(self, req: jobs_pb2.UpdateTaskRequest) -> None:
        try:
            async for attempt in AsyncRetrying(stop=stop_after_attempt(2), retry=self.__handle_retry):
                with attempt:
                    await self._maybe_connect()
                    await self._stub.UpdateTask(req, metadata=self._metadata)
        except RetryError:
            LOG.exception(f"Failed to update task on cloud {req}")

    async def create_intervention(
        self, task_id: int, qualification: str, description: str, cause: InterventionCause
    ) -> None:
        req = jobs_pb2.CreateInterventionRequest(
            intervention=jobs_pb2.Intervention(
                task_id=task_id,
                qualification=qualification,
                description=description,
                state=jobs_pb2.NEW,
                robot_serial=robot_name(),
                cause=cause.value,
            )
        )
        try:
            async for attempt in AsyncRetrying(stop=stop_after_attempt(2), retry=self.__handle_retry):
                with attempt:
                    await self._maybe_connect()
                    await self._stub.CreateIntervention(req, metadata=self._metadata)
        except RetryError:
            LOG.exception(f"Failed to create intervention {req}")

    async def get_task(self, task_id: int) -> jobs_pb2.Task:
        req = jobs_pb2.GetTaskRequest(task_id=task_id)
        async for attempt in AsyncRetrying(stop=stop_after_attempt(2), retry=self.__handle_retry):
            with attempt:
                await self._maybe_connect()
                resp: jobs_pb2.GetTaskResponse = await self._stub.GetTask(req, metadata=self._metadata, timeout=1)
                return resp.task
        return jobs_pb2.Task()  # Should never get here but needed for typechecking
