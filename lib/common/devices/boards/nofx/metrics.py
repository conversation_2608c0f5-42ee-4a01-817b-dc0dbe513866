from prometheus_client import Gauge

from lib.common.logging import get_logger

LOG = get_logger(__name__)

# Metrics for nofx board
sub_system = "nofx"


WHEEL_ENCODER_TICKS_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_ticks",
    documentation="Total ticks per wheel encoder",
    labelnames=["encoder"],
)
WHEEL_ENCODER_DELTA_TICKS_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_delta_ticks",
    documentation="delta ticks per same unit time per wheel encoder",
    labelnames=["encoder"],
)
WHEEL_ENCODER_AVG_VEL_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_avg_vel_mps",
    documentation="vel averaged per unit time per wheel encoder [m/s]",
    labelnames=["encoder"],
)
WHEEL_ENCODER_ERROR_GAUGE = Gauge(
    subsystem=sub_system,
    name="wheel_encoder_error",
    documentation="Anomaly detection is actively detecting an error on this encoder.",
    labelnames=["encoder"],
)
