import asyncio
from abc import abstractmethod
from typing import Dict, Optional

from lib.common.logging import get_logger
from lib.common.tasks.owner import Owner

LOG = get_logger(__name__)


class DailyMetric(Owner):
    def __init__(self, name: str) -> None:
        self._name = name
        self._task: Optional[asyncio.Task[None]] = None
        self._lock = asyncio.Lock()

    async def add_metrics(self, key_map: Dict[str, str]) -> None:
        async with self._lock:
            await self._do_add_metrics(key_map)

    @abstractmethod
    async def _do_add_metrics(self, key_map: Dict[str, str]) -> None:
        pass

    @abstractmethod
    async def _loop(self) -> None:
        pass

    async def _loop_catch(self) -> None:
        try:
            await self._loop()
        except Exception as e:
            LOG.warning(f"{self._name}: Failed with unhandled exception {e}")

    async def start(self) -> None:
        if self._task is None:
            self._task = asyncio.get_event_loop().create_task(self._loop_catch())

    async def stop(self) -> None:
        if self._task is not None:
            self._task.cancel()
            await self._task
            self._task = None

    def __repr__(self) -> str:
        return self.__str__()

    def __str__(self) -> str:
        return f"DailyMetrics for {self._name}"
