#include "lib/common/cpp/fixed_buffer.h"
#include "gtest/gtest.h"

TEST(FixedBuffer, GetNearest) {
  auto fixed_buffer = lib::common::FixedBuffer<int, std::string>(3);

  fixed_buffer.push(10, "abc");
  fixed_buffer.push(20, "cde");
  fixed_buffer.push(0, "efg");

  auto [key, value] = fixed_buffer.get_nearest(16, [](int a, int b) { return std::abs(a - b); });
  EXPECT_EQ(key, 20);
  EXPECT_EQ(value, "cde");
}