#include "lib/common/cpp/utils/thread_pool.hpp"
#include <spdlog/spdlog.h>

namespace carbon::common {
ThreadPool::ThreadPool(size_t num_threads)
    : next_id_(0), shutdown_(false), bse_(lib::common::bot::BotStopHandler::get().create_event(
                                         "ThreadPool", std::bind(&ThreadPool::terminate, this))) {
  for (size_t i = 0; i < num_threads; ++i) {
    threads_.emplace_back(&ThreadPool::runner, this);
  }
}
ThreadPool::~ThreadPool() {
  try {
    terminate();
  } catch (const std::exception &ex) {
    spdlog::warn("Failed to terminate thread pool: {}", ex.what());
  }
}
void ThreadPool::terminate() {
  if (shutdown_.exchange(true)) {
    return;
  }
  requested_jobs_.terminate();
  cv_.notify_all();
  for (auto &thrd : threads_) {
    if (thrd.joinable()) {
      thrd.join();
    }
  }
  bse_->set();
}
ThreadPool::JobID ThreadPool::add_job(Func func) {
  if (shutdown_) {
    return 0;
  }
  auto id = ++next_id_;
  {
    std::unique_lock<std::mutex> lk(mut_);
    active_jobs_.insert(id);
  }
  requested_jobs_.emplace_add(id, func);
  return id;
}
bool ThreadPool::await_job(JobID id) {
  if (id == 0) {
    return false;
  }
  while (!shutdown_) {
    std::unique_lock<std::mutex> lk(mut_);
    if (active_jobs_.find(id) == active_jobs_.end()) {
      return true;
    }
    cv_.wait(lk);
  }
  return false;
}

void ThreadPool::runner() {
  while (!shutdown_) {
    auto opt_job = requested_jobs_.wait_pop(0);
    if (!opt_job) {
      continue;
    }
    opt_job.value().second();
    {
      std::unique_lock<std::mutex> lk(mut_);
      active_jobs_.erase(opt_job.value().first);
    }
    cv_.notify_all();
  }
}
} // namespace carbon::common