#pragma once

// LOG_EVERY_N_SECONDS macros for rate-limited logging compatible with spdlog.
// These macros log a message at most once every N seconds from a given call site.
// They use thread-safe static initializers (C++11+) and a benign data race on
// subsequent calls for maximum performance. This may result in an occasional
// extra log under heavy contention but avoids locking overhead.
//
// Usage Examples:
//   LOG_WARN_EVERY_N_SECONDS(5, "This warning appears at most once every 5 seconds");
//   LOG_ERROR_EVERY_N_SECONDS(30, "Database connection failed, attempt {}", attempt_count);
//   LOG_INFO_EVERY_N_SECONDS(10, "Heartbeat - system is running normally");

#include <chrono>
#include <spdlog/spdlog.h>

#define LOG_EVERY_N_SECONDS_IMPL(n, level, ...)                                                                        \
  do {                                                                                                                 \
    static auto last_log_time = std::chrono::steady_clock::now();                                                      \
    auto now = std::chrono::steady_clock::now();                                                                       \
    if (std::chrono::duration_cast<std::chrono::seconds>(now - last_log_time).count() >= n) {                          \
      last_log_time = now;                                                                                             \
      spdlog::level(__VA_ARGS__);                                                                                      \
    }                                                                                                                  \
  } while (false)

// Specific log level macros for convenience
#define LOG_INFO_EVERY_N_SECONDS(n, ...) LOG_EVERY_N_SECONDS_IMPL(n, info, __VA_ARGS__)
#define LOG_WARN_EVERY_N_SECONDS(n, ...) LOG_EVERY_N_SECONDS_IMPL(n, warn, __VA_ARGS__)
#define LOG_ERROR_EVERY_N_SECONDS(n, ...) LOG_EVERY_N_SECONDS_IMPL(n, error, __VA_ARGS__)
#define LOG_DEBUG_EVERY_N_SECONDS(n, ...) LOG_EVERY_N_SECONDS_IMPL(n, debug, __VA_ARGS__)

// Generic macro that defaults to info level for backward compatibility
#define LOG_EVERY_N_SECONDS(n, ...) LOG_INFO_EVERY_N_SECONDS(n, __VA_ARGS__)
