import asyncio
from contextlib import asynccontextmanager
from typing import AsyncIterator


@asynccontextmanager
async def linked_event(parent: asyncio.Event) -> AsyncIterator[asyncio.Event]:
    """If the parent event is set so too is the child, but the child event can be set without affecting the parent"""
    child = asyncio.Event()

    async def __inner() -> None:
        await parent.wait()
        child.set()

    watcher = asyncio.create_task(__inner())
    try:
        yield child
    finally:
        watcher.cancel()
        try:
            await watcher
        except asyncio.CancelledError:
            pass
