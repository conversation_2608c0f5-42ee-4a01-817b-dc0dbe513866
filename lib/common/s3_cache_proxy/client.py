import io
import logging
import os
from typing import List, Optional, <PERSON>ple

import boto3
import numpy as np
import requests
from PIL import Image
from tenacity import before_sleep_log, retry, stop_after_delay, wait_exponential

LOG = logging.getLogger(__name__)


class S3CacheProxyClient:
    def __init__(self, s3_cache_proxy_host: Optional[str], timeout: int = 30):
        self._s3_cache_proxy_host = s3_cache_proxy_host
        self._timeout = timeout

        self._s3_cache_proxy_hosts_list = []
        if self._s3_cache_proxy_host is not None:
            self._s3_cache_proxy_hosts_list = self._s3_cache_proxy_host.split(",")

        self._round_robin = False
        if len(self._s3_cache_proxy_hosts_list) > 1:
            self._round_robin = True

        self._rng = np.random.default_rng(seed=0)

        if self._s3_cache_proxy_host is None:
            self._s3_resource = boto3.resource("s3")
        else:
            self._s3_resource = None

    def get(self, bucket: str, key: str) -> bytes:

        if self._s3_cache_proxy_host is None:
            data = self._get_from_s3(bucket, key)
        else:
            data = self._get_from_s3_cache_proxy(bucket, key)

        return data

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def get_image(
        self, bucket: str, key: str, formats: Optional[List[str]] = None, mode: Optional[str] = None
    ) -> Image.Image:

        data = self.get(bucket, key)

        if formats is None:
            formats = ["png"]

        if mode is None:
            mode = "RGB"

        image = Image.open(io.BytesIO(data), formats=formats).convert(mode)
        return image

    def download(self, bucket: str, key: str, filepath: str, make_dirs: bool = True, exist_ok: bool = False) -> None:
        if not exist_ok:
            if os.path.exists(filepath):
                raise FileExistsError(f"File already exists: {filepath}")

        directory = os.path.dirname(filepath)
        if not os.path.exists(directory) and directory != "":
            if make_dirs:
                os.makedirs(os.path.dirname(filepath))
            else:
                raise RuntimeError(f"Directory does not exist: {os.path.dirname(filepath)}")

        if self._s3_cache_proxy_host is None:
            self._s3_resource.Bucket(bucket).download_file(key, filepath)
        else:
            data = self.get(bucket, key)

            with open(filepath, "wb") as f:
                f.write(data)

    @staticmethod
    def split_uri(uri: str) -> Tuple[str, str]:
        bucket, key = uri.replace("s3://", "").split("/", 1)
        return bucket, key

    def _make_url(self, bucket: str, key: str) -> str:

        if self._round_robin:
            s3_cache_proxy_host = self._rng.choice(self._s3_cache_proxy_hosts_list)
        else:
            s3_cache_proxy_host = self._s3_cache_proxy_host

        return f"http://{s3_cache_proxy_host}/{bucket}/{key}"

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _get_from_s3_cache_proxy(self, bucket: str, key: str) -> bytes:
        url = self._make_url(bucket, key)

        with requests.Session() as session:
            response = session.get(url, timeout=self._timeout)

        assert response.ok, f"Failed to retrieve item ({bucket}/{key}) from cache: {response.text}"
        data = response.content
        return data

    def _get_from_s3(self, bucket: str, key: str) -> bytes:
        s3_object = self._s3_resource.Object(bucket, key)
        data: bytes = s3_object.get()["Body"].read()
        return data

    def get_image_subset(
        self, bucket: str, key: str, x: float, y: float, radius: float, fill: bool = False
    ) -> Image.Image:

        if self._s3_cache_proxy_host is None:
            image = self._get_image_subset_from_s3(bucket, key, x, y, radius)
        else:
            image = self._get_image_subset_from_s3_cache_proxy(bucket, key, x, y, radius, fill)

        return image

    @retry(
        wait=wait_exponential(multiplier=1, min=1, max=5 * 60),
        stop=stop_after_delay(30 * 60),
        before_sleep=before_sleep_log(LOG, logging.INFO),
    )
    def _get_image_subset_from_s3_cache_proxy(
        self, bucket: str, key: str, x: float, y: float, radius: float, fill: bool = False, fill_color: str = "ffffff"
    ) -> Image.Image:

        x = int(x - radius)
        y = int(y - radius)
        width = int(2 * radius)
        height = int(2 * radius)

        if not fill:
            if "bud" in key:
                if x + width > 3000:
                    width = 3000 - x
                if y + height > 4096:
                    height = 4096 - y
            elif "slayer" in key or "reaper" in key:
                if x + width > 4096:
                    width = 4096 - x
                if y + height > 3000:
                    height = 3000 - y
            else:
                raise ValueError(f"Unknown key: {key}")

            if x < 0:
                width += x
                x = 0
            if y < 0:
                height += y
                y = 0

        with requests.Session() as session:
            response = session.get(
                f"http://{self._s3_cache_proxy_host}/{bucket}/{key}?x={x}&y={y}&width={width}&height={height}{f'&fill={fill_color}' if fill else ''}",
                timeout=30,
                headers={"Connection": "close"},
            )
        assert response.ok, f"Failed to retrieve item ({key}) from cache: {response.text}"
        image = Image.open(io.BytesIO(response.content), formats=["png"]).convert("RGB")

        image_width, image_height = image.size

        assert (
            image_width == width
        ), f"Returned image does not have expected width: {image_width} != {width}. Key: {key}"
        assert (
            image_height == height
        ), f"Returned image does not have expected height: {image_height} != {height}. Key: {key}"

        return image

    def _get_image_subset_from_s3(self, bucket: str, key: str, x: float, y: float, radius: float) -> Image.Image:

        image = self.get_image(bucket, key)

        image_subset = image.crop((x - radius, y - radius, x + radius, y + radius))

        return image_subset
