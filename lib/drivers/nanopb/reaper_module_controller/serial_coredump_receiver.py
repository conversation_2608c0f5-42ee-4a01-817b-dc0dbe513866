import asyncio
from argparse import ArgumentParser
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from typing import TYPE_CHECKING, Optional, cast

import lib.common.logging
from generated.lib.drivers.nanopb.proto.reaper_module_controller_pb2 import CoreDumpReply
from lib.common.file import atomic_file_binary
from lib.common.tasks.manager import get_event_loop_by_name
from lib.drivers.nanopb.reaper_module_controller.reaper_module_controller_oob_connector import (
    REAPER_MODULE_CONTROLLER_COREDUMP_BAUD,
    ReaperModuleOobConnector,
    get_board,
)
from lib.drivers.serial.serial_connector import SerialConnector

if TYPE_CHECKING:
    TASK_TYPE = asyncio.Task[None]
else:
    TASK_TYPE = asyncio.Task

LOG = lib.common.logging.get_logger(__name__)


@dataclass
class CoredumpData:
    # Flag to indicate this was the last chunk and the file should be closed
    is_done: bool
    # data to append to file
    data: bytes


class CoredumpReceiver:
    """
    State machine to handle reception of a coredump from the MCB. The dump file is written to disk
    atomically at the conclusion of the coredump.
    """

    def __init__(self, connector: ReaperModuleOobConnector, coredump_base: Path) -> None:
        self._connector = connector
        self._dir_base = coredump_base

        self._start_event = asyncio.Event()
        self._data_queue: "asyncio.Queue[CoredumpData]" = asyncio.Queue()

        # received coredump start packet
        self._in_coredump = False
        # received coredump end packet
        self._is_done = False
        # Task that handles receiving coredumps
        self._worker: Optional[TASK_TYPE] = None

    @property
    def in_coredump(self) -> bool:
        """
        Is the MCB actively outputting a coredump?
        """
        return self._in_coredump

    async def start(self) -> TASK_TYPE:
        """
        Start the background worker task of the coredump receiver
        """
        assert not self._worker, "Already started!"

        self._connector.set_coredump_callback(self._coredump_received)

        self._worker = asyncio.create_task(self._work_loop(), name="MCB Coredump Receiver")
        return self._worker

    async def _coredump_received(self, message: CoreDumpReply) -> None:
        """
        Callback for MCB coredump message reception
        """
        if message.WhichOneof("payload") == "start":
            assert not self._in_coredump, "Coredump already started"

            LOG.info(f"Received coredump start notifification from MCB: {message}")
            self._in_coredump = True

            self._start_event.set()
        elif message.WhichOneof("payload") == "end":
            assert self._in_coredump, "Must already be in coredump"
            assert not self._is_done, "Coredump already done"

            await self._data_queue.put(CoredumpData(is_done=True, data=bytes()))
            self._is_done = True
        elif message.WhichOneof("payload") == "data":
            assert self._in_coredump, "Must already be in coredump"
            assert not self._is_done, "Coredump already done"

            await self._data_queue.put(CoredumpData(is_done=False, data=message.data.data))
        else:
            LOG.error(f"Unsupported coredump message: {message}")

    async def _work_loop(self) -> None:
        """
        Coredump receiver

        Register the unsolicited messages handler with the connector and wait for coredump related
        messages.
        """
        # wait for coredump to start
        await self._start_event.wait()
        LOG.warning("MCB coredump started!")

        serial_connector = cast(SerialConnector, self._connector.transport)
        await serial_connector.set_baud_rate(REAPER_MODULE_CONTROLLER_COREDUMP_BAUD)

        LOG.info("Updated baud rate, waiting for coredump data")

        # open the file, start writing blocks to it
        date_str = datetime.now().isoformat(timespec="seconds")
        coredump_path = self._dir_base.joinpath(f"mcb-coredump-{date_str}.bin")

        LOG.info(f"Writing MCB coredump to {coredump_path}")
        with atomic_file_binary(coredump_path) as file:
            should_run = True

            while should_run:
                try:
                    work = await self._data_queue.get()

                    if work.is_done:
                        LOG.info("Coredump receiver complete!")
                        should_run = False

                    if work.data:
                        file.write(work.data)
                except BaseException:
                    LOG.exception("Error during coredump receiver iteration")
                finally:
                    self._data_queue.task_done()

        # Coredump is complete
        LOG.info(f"Coredump complete (written to {coredump_path})!")


# run tests
async def _test(port: str) -> None:
    board = await get_board(port)

    # register coredump stuff
    dumpy = CoredumpReceiver(board, Path("./"))
    dumpy_task = await dumpy.start()

    # ensure we can ping
    LOG.info(f"Ping latency (sec): {await board.ping()}")

    await dumpy_task
    LOG.warning("Coredump task exited!")


# main stuff
if __name__ == "__main__":
    parser = ArgumentParser("Reaper Module OOB Coredump Receiver")
    parser.add_argument("--port", type=str)
    args = parser.parse_args()
    lib.common.logging.init_log()

    # connect board
    future = _test(args.port)
    asyncio.run_coroutine_threadsafe(future, get_event_loop_by_name()).result()
