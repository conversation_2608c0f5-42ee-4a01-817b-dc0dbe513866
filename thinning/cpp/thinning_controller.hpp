#pragma once

#include <memory>
#include <optional>
#include <thread>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/redis/redis_client.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>

namespace carbon {
namespace scheduling {
class GlobalScheduler;
}
namespace crop_based_weed_targeting {
template <typename TrajectoryType>
class CropBasedWeedTargeter;
}

namespace arbiter {
class Arbiter;
}
namespace thinning {
class ThinningAlgo;
class ThinningController {
public:
  ThinningController(std::shared_ptr<scheduling::GlobalScheduler> scheduler,
                     std::shared_ptr<crop_based_weed_targeting::CropBasedWeedTargeter<trajectory::Trajectory>> targeter,
                     std::shared_ptr<arbiter::Arbiter> arbiter);
  ~ThinningController();

private:
  void _shutdown();
  void _add_thinning_algo();
  void _remove_thinning_algo();
  void _reload_algo();
  void _watch_redis();

  std::shared_ptr<scheduling::GlobalScheduler> scheduler_;
  std::shared_ptr<crop_based_weed_targeting::CropBasedWeedTargeter<trajectory::Trajectory>> targeter_;
  std::shared_ptr<arbiter::Arbiter> arbiter_;
  std::shared_ptr<ThinningAlgo> thinning_algo_;
  arbiter::ARBITER_CLIENT_ID thinning_arbiter_id_;

  // redis watch thread
  lib::common::bot::ScopedBotStopEvent bse_;
  std::shared_ptr<lib::common::RedisClient> redis_client_;
  std::pair<int64_t, int32_t> last_request_id_; // [timestamp, sequence]
  std::thread watch_redis_thread_;
};
} // namespace thinning
} // namespace carbon