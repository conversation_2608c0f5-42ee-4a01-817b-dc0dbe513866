#include <thinning/cpp/thinning_controller.hpp>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/utils/environment.hpp>
#include <lib/common/redis/redis_client.hpp>
#include <scheduling/cpp/global_scheduler.hpp>
#include <targeting/cpp/targeting_mode_watcher.hpp>
#include <thinning/cpp/thinning_algo.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter.hpp>
#include <weed_tracking_libs/crop_based_weed_targeting/cpp/crop_based_weed_targeting.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon::thinning {

constexpr std::string_view kStreamKey = "stream:thinning:reload_requests";
constexpr std::string_view kReloadConfType = "reload_conf";
constexpr std::string_view kConsumerId = "thinning_controller";

constexpr int kStreamWaitingTimeoutMs = 5000;
constexpr int kNumMessagesToRead = 0; // 0 means read all available messages
constexpr int kErrorRetrySleepMs = 1000;

ThinningController::ThinningController(
    std::shared_ptr<scheduling::GlobalScheduler> scheduler,
    std::shared_ptr<crop_based_weed_targeting::CropBasedWeedTargeter<trajectory::Trajectory>> targeter,
    std::shared_ptr<arbiter::Arbiter> arbiter)
    : scheduler_(scheduler), targeter_(targeter), arbiter_(arbiter), thinning_algo_(nullptr), thinning_arbiter_id_(0),
      bse_(lib::common::bot::BotStopHandler::get().create_scoped_event("thinning_controller",
                                                                       [this]() { this->_shutdown(); })),
      redis_client_(std::make_shared<lib::common::RedisClient>()), last_request_id_(0, 0) {
  targeting::TargetingModeWatcher::get().register_callback(std::bind(&ThinningController::_reload_algo, this));
  watch_redis_thread_ = std::thread([this] { this->_watch_redis(); });
  _reload_algo();
}

ThinningController::~ThinningController() {
  bse_.stop();
  _shutdown();
}

// expects stop is called on the bse either by the global handler or by the destructor
void ThinningController::_shutdown() {
  if (watch_redis_thread_.joinable()) {
    watch_redis_thread_.join();
  }
  bse_.set();
}

// creates a new thinning algo if one doesn't exist and adds it to the arbiter as a client
void ThinningController::_add_thinning_algo() {
  if (!thinning_algo_) {
    thinning_algo_ = ThinningAlgo::get_thinning_algo(scheduler_, targeter_);
  }
  if (thinning_arbiter_id_ == 0) {
    thinning_arbiter_id_ = arbiter_->add_client(thinning_algo_);
  }
}

// removes the thinning algo from the arbiter and deletes the algo
void ThinningController::_remove_thinning_algo() {
  if (thinning_arbiter_id_ != 0) {
    arbiter_->remove_client(thinning_arbiter_id_);
    thinning_arbiter_id_ = 0;
  }
  if (thinning_algo_) {
    thinning_algo_.reset();
  }
}

void ThinningController::_reload_algo() {
  spdlog::info("ThinningController: reloading thinning algo");
  if (targeting::TargetingModeWatcher::get().thinning_enabled()) {
    if (thinning_algo_) {
      if (thinning_algo_->supports_config()) {
        thinning_algo_->reload_required();
      } else {
        // algo doesn't support the current config, so remove it
        _remove_thinning_algo();
      }
    }
    _add_thinning_algo(); // no-op if already added, otherwise creates a new algo and reloads the config
  } else {
    _remove_thinning_algo();
  }
}

void ThinningController::_watch_redis() {
  const std::string_view kGroupId =
      fmt::format("aimbot_{}", common::get_row_id()); // group names must be unique per row
  bool created_group = false;
  std::vector<lib::common::RedisClient::KeyIdPair> stream_requests = {
      {kStreamKey, ">"}}; // read from the last id we processed

  redis_client_->wait_until_ready();
  while (!bse_.is_stopped()) {
    try {
      if (!created_group) {
        created_group = redis_client_->xgroup_create(kStreamKey, kGroupId, "$", true);
        if (!created_group) {
          spdlog::error("Failed to create stream group for thinning, will retry");
          lib::common::bot::BotStopHandler::get().sleep_safe(kErrorRetrySleepMs);
          continue;
        }
      }

      auto results = redis_client_->xreadgroup(kGroupId, kConsumerId, stream_requests, kStreamWaitingTimeoutMs,
                                               kNumMessagesToRead);
      if (results.empty()) {
        // no message on the stream, retry
        continue;
      }
      bool reload_required = false;
      auto &stream = results.front().second; // There should only be one stream since we only provided one key
      spdlog::info("ThinningController: received {} messages from notification stream", stream.size());

      // might be multiple messages in the stream, we only care about the latest one
      std::vector<sw::redis::StringView> ids_to_ack;
      auto highest_request_id = last_request_id_;
      for (const auto &[msg_id, fields_opt] : stream) {
        if (!fields_opt.has_value()) {
          continue;
        }
        auto &fields = fields_opt.value(); // fields is a vector of pairs
        auto request_type_it =
            std::find_if(fields.begin(), fields.end(), [](const auto &pair) { return pair.first == "type"; });
        if (request_type_it == fields.end() || request_type_it->second != kReloadConfType) {
          continue;
        }

        // default keys are formatted timestamp-sequence, where sequence number is 0 based and incremented for requests
        // in the same ts. I think redis handles AMO delivery, but better safe than sorry

        auto ts_and_seq = redis_client_->parse_ts_seq_message_id(msg_id);
        if (ts_and_seq.first > highest_request_id.first ||
            (ts_and_seq.first == highest_request_id.first && ts_and_seq.second > highest_request_id.second)) {
          highest_request_id = ts_and_seq;
          reload_required = true;
        }
        ids_to_ack.push_back(msg_id);
      }

      if (!ids_to_ack.empty() && !redis_client_->xack(kStreamKey, kGroupId, ids_to_ack)) {
        spdlog::error("Failed to ack messages in stream {}", kStreamKey);
        lib::common::bot::BotStopHandler::get().sleep_safe(kErrorRetrySleepMs);
        continue;
      }

      if (reload_required) {
        spdlog::info("ThinningController: new highest message id: {}-{}", highest_request_id.first,
                     highest_request_id.second);
        last_request_id_ = highest_request_id;
        _reload_algo();
      }
    } catch (const std::exception &ex) {
      spdlog::error("Exception in ThinningAlgo::_watch_redis: {}", ex.what());
      lib::common::bot::BotStopHandler::get().sleep_safe(kErrorRetrySleepMs);
    }
  }
}
} // namespace carbon::thinning