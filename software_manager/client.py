from typing import Optional

import grpc

from generated.proto.software_manager import software_manager_pb2 as pb
from generated.proto.software_manager import software_manager_pb2_grpc as pb_grpc
from lib.common.logging import get_logger

LOG = get_logger(__name__)

DEFAULT_PORT = 61005
DEFAULT_TIMEOUT = 0.5


class SoftwareManagerClient:
    def __init__(self, hostname: str = "localhost", port: int = DEFAULT_PORT):
        self._hostname = hostname
        self._port = port
        self._channel = None
        self._stub: Optional[pb_grpc.SoftwareManagerServiceStub] = None

    def _maybe_connect(self) -> None:
        if self._stub is None:
            self._channel = grpc.aio.insecure_channel(f"{self._hostname}:{self._port}")
            self._stub = pb_grpc.SoftwareManagerServiceStub(self._channel)

    async def ping(self, timeout_seconds: float = DEFAULT_TIMEOUT) -> bool:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.empty()
        try:
            await self._stub.Ping(req, timeout=timeout_seconds)
            return True
        except grpc.aio.AioRpcError as e:
            if e.code() == grpc.StatusCode.UNIMPLEMENTED:
                LOG.warning(
                    f"Software manager at {self._hostname}:{self._port} service is up, but running old version that does not support ping."
                )
                return True
            if e.code() == grpc.StatusCode.UNAVAILABLE:
                return False
            else:
                raise

    async def prepare_update(self, tag: str, req_id: str) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.PrepareUpdateRequest(tag=tag, req_id=req_id)
        await self._stub.PrepareUpdate(req)

    async def abort_update(self, tag: str, req_id: str) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.AbortUpdateRequest(tag=tag, req_id=req_id)
        await self._stub.AbortUpdate(req)

    async def trigger_update(self, tag: str, req_id: str) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.TriggerUpdateRequest(tag=tag, req_id=req_id)
        await self._stub.TriggerUpdate(req)

    async def reboot(self) -> None:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.empty()
        await self._stub.Reboot(req)

    async def get_sw_version_metadata(self, tag: str) -> pb.SoftwareVersionMetadata:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.SoftwareVersionMetadataRequest(tag=tag)
        resp: pb.SoftwareVersionMetadata = await self._stub.GetSoftwareVersionMetadata(req)
        return resp

    async def get_version_summary(self) -> pb.VersionSummaryReply:
        self._maybe_connect()
        assert self._stub is not None
        req = pb.VersionSummaryRequest()
        resp: pb.VersionSummaryReply = await self._stub.GetVersionsSummary(req)
        return resp
