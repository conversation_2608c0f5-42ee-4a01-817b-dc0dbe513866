#pragma once

#include "lib/common/redis/redis_client.hpp"
#include "gmock/gmock.h"

namespace lib {
namespace common {

class MockRedisClient : public RedisClient {
public:
  MOCK_METHOD(sw::redis::OptionalString, get, (const sw::redis::StringView &key));
  MOCK_METHOD(sw::redis::OptionalLongLong, get_long, (const sw::redis::StringView &key));
  MOCK_METHOD(int64_t, get_long_def, (const sw::redis::StringView &key, int64_t def));

  MOCK_METHOD(bool, set,
              (const sw::redis::StringView &key, const sw::redis::StringView &val,
               const std::chrono::milliseconds &ttl));
  MOCK_METHOD(bool, set_long, (const sw::redis::StringView &key, int64_t val, const std::chrono::milliseconds &ttl));
  MOCK_METHOD(bool, set_long_safe,
              (const sw::redis::StringView &key, int64_t val, const std::chrono::milliseconds &ttl));

  MOCK_METHOD(sw::redis::OptionalString, hget, (const sw::redis::StringView &key, const sw::redis::StringView &field));
  MOCK_METHOD(sw::redis::OptionalLongLong, hget_long,
              (const sw::redis::StringView &key, const sw::redis::StringView &field));
  MOCK_METHOD(sw::redis::OptionalDouble, hget_double,
              (const sw::redis::StringView &key, const sw::redis::StringView &field));
  MOCK_METHOD(int64_t, hget_long_def,
              (const sw::redis::StringView &key, const sw::redis::StringView &field, int64_t def));
  MOCK_METHOD(double, hget_double_def,
              (const sw::redis::StringView &key, const sw::redis::StringView &field, double def));
  MOCK_METHOD(bool, hset,
              (const sw::redis::StringView &key, const sw::redis::StringView &field, const sw::redis::StringView &val));
  MOCK_METHOD(bool, hset_safe,
              (const sw::redis::StringView &key, const sw::redis::StringView &field, const sw::redis::StringView &val));
  MOCK_METHOD(bool, expire, (const sw::redis::StringView &key, const std::chrono::seconds &timeout));

  MOCK_METHOD(bool, hset_numeric_safe_impl,
              (const sw::redis::StringView &key, const sw::redis::StringView &field, const std::string &val));
  template <typename T>
  bool hset_numeric_safe(const sw::redis::StringView &key, const sw::redis::StringView &field, T val) {
    return hset_numeric_safe_impl(key, field, std::to_string(val));
  }

  MOCK_METHOD(bool, exists, (const sw::redis::StringView &key));
  MOCK_METHOD(bool, hexists, (const sw::redis::StringView &key, const sw::redis::StringView &field));
  MOCK_METHOD(void, ping, ());
  MOCK_METHOD(void, wait_until_ready, ());
};

} // namespace common
} // namespace lib
