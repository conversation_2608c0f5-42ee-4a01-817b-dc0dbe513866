add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(logging SHARED ${SOURCES})
target_compile_definitions(logging PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(logging PUBLIC stdc++fs pthread rt fmt spdlog logging_proto)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(logging_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(logging_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(logging_python PUBLIC logging)
set_target_properties(logging_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)