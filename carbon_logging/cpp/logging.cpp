#include "carbon_logging/cpp/logging.hpp"

#include <memory>

#include <spdlog/async.h>
#include <spdlog/sinks/basic_file_sink.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/spdlog.h>

namespace carbon::logging {
bool _init_logger(const std::string &filename) {
  auto file_sink = std::make_shared<spdlog::sinks::basic_file_sink_mt>("/data/logs/" + filename, false);

  spdlog::init_thread_pool(8192, 1);
  auto stdout_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
  std::vector<spdlog::sink_ptr> sinks = {stdout_sink, file_sink};
  auto logger = std::make_shared<spdlog::async_logger>("default", sinks.begin(), sinks.end(), spdlog::thread_pool(),
                                                       spdlog::async_overflow_policy::block);
  spdlog::set_default_logger(logger);
  spdlog::flush_every(std::chrono::seconds(1));
  spdlog::flush_on(spdlog::level::err);
  return true;
}
void init_logger(const std::string &filename) {
  // only use do something on first call
  const static bool run_once(_init_logger(filename));
  (void)run_once;
}
bool set_log_level(LogLevel level) {
  switch (level) {
  case carbon::logging::LogLevel::TRACE: {
    spdlog::set_level(spdlog::level::trace);
    break;
  }
  case carbon::logging::LogLevel::DEBUG: {
    spdlog::set_level(spdlog::level::debug);
    break;
  }
  case carbon::logging::LogLevel::WARNING: {
    spdlog::set_level(spdlog::level::warn);
    break;
  }
  case carbon::logging::LogLevel::INFO: {
    spdlog::set_level(spdlog::level::info);
    break;
  }
  case carbon::logging::LogLevel::ERROR: {
    spdlog::set_level(spdlog::level::err);
    break;
  }
  case carbon::logging::LogLevel::FATAL: {
    spdlog::set_level(spdlog::level::critical);
    break;
  }
  default: {
    return false;
  }
  }
  return true;
}
} // namespace carbon::logging