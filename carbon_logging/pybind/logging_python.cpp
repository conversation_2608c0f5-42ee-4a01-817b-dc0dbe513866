#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "carbon_logging/cpp/logging.hpp"

namespace py = pybind11;

namespace carbon::logging {

PYBIND11_MODULE(logging_python, m) {

  m.def("init_logger", &init_logger, py::arg("filename"), py::call_guard<py::gil_scoped_release>());
  m.def("set_log_level", &set_log_level, py::arg("level"), py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::logging