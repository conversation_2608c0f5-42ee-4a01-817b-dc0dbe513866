import os
import zipfile
from collections import defaultdict
from typing import Any, Dict, List, <PERSON>ple

import boto3
import numpy as np
import plotly.graph_objects as go
import streamlit as st

from tools.recorder.diagnostics_snapshot_reader import DiagnosticsSnapshotReader
from tools.recorder.lane_height_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>eader


def get_zips_from_s3(robot_id: str) -> List[str]:
    s3 = boto3.resource("s3")
    diags = []
    for x in s3.Bucket("carbon-diagnostics").objects.all():
        if not x.key.startswith(f"diagnostics/{robot_id}/"):
            continue
        if x.key.endswith(".zip"):
            diags.append((x.last_modified.timestamp(), x.key))

    diags = sorted(diags, key=lambda x: x[0], reverse=True)
    diags = [x[1] for x in diags]
    return diags


def download_zip_from_s3(zip_path: str) -> str:
    s3 = boto3.client("s3")
    local_file = f"/data/{zip_path}"
    os.makedirs(os.path.dirname(local_file), exist_ok=True)
    if not os.path.exists(local_file):
        s3.download_file("carbon-diagnostics", zip_path, local_file)
    return local_file


def unzip(zip_path: str) -> str:
    path = f"/data/traj_diagnostics/{os.path.splitext(zip_path)[0]}"
    os.makedirs(path, exist_ok=True)
    with zipfile.ZipFile(zip_path, "r") as zf:
        zf.extractall(path)
    return path


def list_files(unzipped_path: str) -> List[str]:
    files = []
    for (dirpath, _, filenames) in os.walk(unzipped_path):
        for f in filenames:
            files.append(os.path.join(os.path.join(dirpath, f)))

    return files


def get_trajectories(filepath: str, ind: str) -> Tuple[Dict[int, Dict[str, Any]], int, int]:
    snap = DiagnosticsSnapshotReader(filepath)
    per_id_history = defaultdict(list)
    all_timestamps = []

    if not snap.is_valid:
        return per_id_history, 0, 0

    frame = snap.next()

    while frame:
        frame_timestamp = frame.timestamp_ms if hasattr(frame, "timestamp_ms") else 0
        all_timestamps.append(frame_timestamp)

        for traj in frame.trajectories:
            traj_key = f"{ind}_{traj.id}"

            # Capture all available trajectory information
            traj_data = {
                "y_mm": traj.y_mm,
                "z_mm": traj.z_mm,
                "timestamp_ms": frame_timestamp,
                "x_mm": traj.x_mm,
                "id": traj.id,
                "kill_status": getattr(traj, "kill_status", None),
                "weed_score": getattr(traj, "weed_score", None),
                "crop_score": getattr(traj, "crop_score", None),
                "plant_score": getattr(traj, "plant_score", None),
                "num_detections_used_for_decision": getattr(traj, "num_detections_used_for_decision", None),
                "classification": getattr(traj, "classification", None),
                "is_reachable": getattr(traj, "is_reachable", None),
                "intersected_with_nonshootable": getattr(traj, "intersected_with_nonshootable", None),
                "nonshootable_type_string": getattr(traj, "nonshootable_type_string", None),
                "protected_by_traj": getattr(traj, "protected_by_traj", None),
                "band_status": getattr(traj, "band_status", None),
                "crop_line_id": getattr(traj, "crop_line_id", None),
                "size_mm": getattr(traj, "size_mm", None),
                "shoot_time_actual_ms": getattr(traj, "shoot_time_actual_ms", None),
                "speculative_shoot_time_actual_ms": getattr(traj, "speculative_shoot_time_actual_ms", None),
            }

            per_id_history[traj_key].append(traj_data)
        frame = snap.next()

    min_time = min(all_timestamps) if all_timestamps else 0
    max_time = max(all_timestamps) if all_timestamps else 0

    return per_id_history, min_time, max_time


def get_lane_heights(filepath: str, predict_id: str) -> Dict[int, Dict[str, Any]]:
    """Load lane height data from predictX_lane_height.carbon files"""
    reader = LaneHeightReader(filepath)
    lane_height_data = {}

    if not reader.is_valid:
        return lane_height_data

    frame = reader.current()
    while frame is not None and not reader.is_done:
        timestamp = frame.record_timestamp_ms

        # Store weed and crop heights with their positions
        weed_heights = list(frame.snapshot.weed_height)
        crop_heights = list(frame.snapshot.crop_height)

        lane_height_data[timestamp] = {
            "predict_id": predict_id,
            "weed_heights": weed_heights,
            "crop_heights": crop_heights,
            "timestamp_ms": timestamp,
        }

        frame = reader.next()

    return lane_height_data


def get_trajectory_start_times(trajectory_by_id: Dict[int, Dict[str, Any]]) -> Dict[str, int]:
    """Extract the start time (first timestamp) for each trajectory"""
    start_times = {}
    for traj_id, points in trajectory_by_id.items():
        if points:
            start_times[traj_id] = points[0].get("timestamp_ms", 0)
    return start_times


def get_trajectory_start_x_positions(trajectory_by_id: Dict[int, Dict[str, Any]]) -> Dict[str, float]:
    """Extract the start x_mm position for each trajectory"""
    start_x_positions = {}
    for traj_id, points in trajectory_by_id.items():
        if points:
            x_mm = points[0].get("x_mm")
            if x_mm is not None:
                start_x_positions[traj_id] = x_mm
    return start_x_positions


def get_x_mm_range(trajectory_by_id: Dict[int, Dict[str, Any]]) -> Tuple[float, float]:
    """Get the min and max x_mm values across all trajectories"""
    start_x_positions = get_trajectory_start_x_positions(trajectory_by_id)
    if not start_x_positions:
        return 0.0, 0.0

    x_values = list(start_x_positions.values())
    return min(x_values), max(x_values)


def plot_line_chart(  # noqa: C901
    trajectories: Dict[str, Tuple[float, float]],
    title: str,
    x_axis: str,
    trajectory_by_id: Dict[str, List[Dict[str, Any]]] = None,
) -> None:
    fig = go.Figure()

    for key, traj in trajectories.items():
        if len(traj) == 0:
            continue
        x, y = zip(*traj)

        # Create enhanced hover text with all trajectory information
        hover_text = []
        if trajectory_by_id and key in trajectory_by_id:
            for i, point in enumerate(trajectory_by_id[key]):
                if i < len(x):  # Ensure we don't go out of bounds
                    hover_info = [
                        f"<b>Trajectory ID:</b> {key}",
                        f"<b>{x_axis}:</b> {x[i]:.2f}",
                        f"<b>z_mm:</b> {y[i]:.2f}",
                        f"<b>Timestamp:</b> {point.get('timestamp_ms', 'N/A')} ms",
                        "",
                    ]

                    # Add all available trajectory information
                    if point.get("x_mm") is not None:
                        hover_info.append(f"<b>x_mm:</b> {point['x_mm']:.2f}")
                    if point.get("y_mm") is not None:
                        hover_info.append(f"<b>y_mm:</b> {point['y_mm']:.2f}")
                    if point.get("weed_score") is not None:
                        hover_info.append(f"<b>Weed Score:</b> {point['weed_score']:.3f}")
                    if point.get("crop_score") is not None:
                        hover_info.append(f"<b>Crop Score:</b> {point['crop_score']:.3f}")
                    if point.get("plant_score") is not None:
                        hover_info.append(f"<b>Plant Score:</b> {point['plant_score']:.3f}")
                    if point.get("kill_status") is not None:
                        hover_info.append(f"<b>Kill Status:</b> {point['kill_status']}")
                    if point.get("classification") is not None:
                        hover_info.append(f"<b>Classification:</b> {point['classification']}")
                    if point.get("num_detections_used_for_decision") is not None:
                        hover_info.append(f"<b>Detections Used:</b> {point['num_detections_used_for_decision']}")
                    if point.get("is_reachable") is not None:
                        hover_info.append(f"<b>Is Reachable:</b> {point['is_reachable']}")
                    if point.get("intersected_with_nonshootable") is not None:
                        hover_info.append(f"<b>Intersected Nonshootable:</b> {point['intersected_with_nonshootable']}")
                    if point.get("nonshootable_type_string"):
                        hover_info.append(f"<b>Nonshootable Type:</b> {point['nonshootable_type_string']}")
                    if point.get("protected_by_traj") is not None:
                        hover_info.append(f"<b>Protected by Traj:</b> {point['protected_by_traj']}")
                    if point.get("band_status"):
                        hover_info.append(f"<b>Band Status:</b> {point['band_status']}")
                    if point.get("crop_line_id") is not None:
                        hover_info.append(f"<b>Crop Line ID:</b> {point['crop_line_id']}")
                    if point.get("size_mm") is not None:
                        hover_info.append(f"<b>Size (mm):</b> {point['size_mm']:.2f}")
                    if point.get("shoot_time_actual_ms") is not None:
                        hover_info.append(f"<b>Shoot Time:</b> {point['shoot_time_actual_ms']} ms")
                    if point.get("speculative_shoot_time_actual_ms") is not None:
                        hover_info.append(
                            f"<b>Speculative Shoot Time:</b> {point['speculative_shoot_time_actual_ms']} ms"
                        )

                    hover_text.append("<br>".join(hover_info))

        # Add trace with enhanced hover information
        if hover_text:
            fig.add_trace(
                go.Scatter(
                    x=x,
                    y=y,
                    mode="lines+markers",
                    name=key,
                    hovertemplate="%{customdata}<extra></extra>",
                    customdata=hover_text,
                )
            )
        else:
            # Fallback to simple hover if no trajectory data available
            fig.add_trace(go.Scatter(x=x, y=y, mode="lines+markers", name=key))

    fig.update_layout(title=title, xaxis_title=x_axis, yaxis_title="z_mm", legend_title="Trajectories")

    st.plotly_chart(fig)


def plot_height_by_y(keys: List[str], trajectory_by_id: Dict[int, Dict[str, Any]], max_ymm: float) -> None:
    trajectories = {}
    filtered_trajectory_data = {}

    for key in keys:
        val = trajectory_by_id[key]
        # Filter trajectories by max_ymm and create coordinate pairs
        filtered_points = [v for v in val if v["y_mm"] < max_ymm]
        trajectories[key] = [(v["y_mm"], v["z_mm"]) for v in filtered_points]
        filtered_trajectory_data[key] = filtered_points

    plot_line_chart(trajectories, title="Height by Y", x_axis="y_mm", trajectory_by_id=filtered_trajectory_data)


def plot_height_by_timestamp(keys: List[str], trajectory_by_id: Dict[int, Dict[str, Any]]):
    trajectories = {}

    for key in keys:
        val = trajectory_by_id[key]
        trajectories[key] = [(v["timestamp_ms"], v["z_mm"]) for v in val]

    plot_line_chart(trajectories, title="Height by Timestamp", x_axis="timestamp_ms", trajectory_by_id=trajectory_by_id)


def plot_height_statistics_by_timestamp(keys: List[str], trajectory_by_id: Dict[int, Dict[str, Any]]):
    """Plot mean and variance of heights per y_mm bucket (25mm increments)"""
    # Collect all points with their y_mm positions and heights
    y_bucket_heights = defaultdict(list)

    for key in keys:
        val = trajectory_by_id[key]
        for point in val:
            y_mm = point["y_mm"]
            height = point["z_mm"]

            # Bucket y_mm values by 25mm increments
            y_bucket = int(y_mm // 25) * 25
            y_bucket_heights[y_bucket].append(height)

    # Calculate statistics for each y_mm bucket
    y_buckets = []
    means = []
    variances = []
    std_devs = []
    counts = []

    for y_bucket in sorted(y_bucket_heights.keys()):
        heights = y_bucket_heights[y_bucket]
        if len(heights) > 0:
            y_buckets.append(y_bucket)
            mean_height = np.mean(heights)
            var_height = np.var(heights)
            std_height = np.std(heights)

            means.append(mean_height)
            variances.append(var_height)
            std_devs.append(std_height)
            counts.append(len(heights))

    # Create the plot
    fig = go.Figure()

    # Add mean line
    fig.add_trace(
        go.Scatter(
            x=y_buckets,
            y=means,
            mode="lines+markers",
            name="Mean Height",
            line=dict(color="blue", width=2),
            hovertemplate="<b>Y Position Bucket:</b> %{x} mm<br>"
            + "<b>Mean Height:</b> %{y:.2f} mm<br>"
            + "<b>Sample Count:</b> %{customdata}<br>"
            + "<extra></extra>",
            customdata=counts,
        )
    )

    # Add variance as filled area (mean ± std dev)
    upper_bound = [m + s for m, s in zip(means, std_devs)]
    lower_bound = [m - s for m, s in zip(means, std_devs)]

    # Add upper bound
    fig.add_trace(
        go.Scatter(x=y_buckets, y=upper_bound, mode="lines", line=dict(width=0), showlegend=False, hoverinfo="skip")
    )

    # Add lower bound with fill
    fig.add_trace(
        go.Scatter(
            x=y_buckets,
            y=lower_bound,
            mode="lines",
            line=dict(width=0),
            fill="tonexty",
            fillcolor="rgba(0, 100, 80, 0.2)",
            name="±1 Std Dev",
            hovertemplate="<b>Y Position Bucket:</b> %{x} mm<br>"
            + "<b>Height Range:</b> %{y:.2f} mm<br>"
            + "<extra></extra>",
        )
    )

    fig.add_trace(
        go.Scatter(
            x=y_buckets,
            y=std_devs,
            mode="lines+markers",
            name="Std Dev",
            line=dict(color="red", width=2, dash="dash"),
            yaxis="y2",
            hovertemplate="<b>Y Position Bucket:</b> %{x} mm<br>"
            + "<b>Std D:</b> %{y:.2f} mm²<br>"
            + "<extra></extra>",
        )
    )

    # Update layout with dual y-axes
    fig.update_layout(
        title="Height Statistics by Y Position (25mm buckets)",
        xaxis_title="Y Position (mm)",
        yaxis_title="Height (mm)",
        yaxis2=dict(title="Std Dev (mm)", overlaying="y", side="right", showgrid=False),
        legend_title="Statistics",
        hovermode="x unified",
    )

    st.plotly_chart(fig)


def plot_3d_lane_heights(lane_height_data: Dict[str, Dict[int, Dict[str, Any]]]):
    rows_data = defaultdict(lambda: defaultdict(list))

    for row_id, predict_data in lane_height_data.items():
        for _, data in predict_data.items():
            for timestamp, timestamped_data in data.items():
                predict_id = timestamped_data["predict_id"]
                weed_heights = timestamped_data["weed_heights"]
                crop_heights = timestamped_data["crop_heights"]

                # Store data organized by row and predict_id
                rows_data[row_id][predict_id].append(
                    {"timestamp": timestamp, "weed_heights": weed_heights, "crop_heights": crop_heights}
                )

    if not rows_data:
        st.warning("No lane height data found in the selected timestamp range")
        return

    # Create 3D plots for each row
    for row_id in sorted(rows_data.keys()):
        st.subheader(f"3D Lane Heights - {row_id}")

        fig = go.Figure()

        # Get all predict IDs for this row
        predict_ids = sorted(rows_data[row_id].keys(), key=lambda x: int(x.replace("predict", "")))

        if not predict_ids:
            continue

        min_timestamp = float("inf")
        for predict_index, predict_id in enumerate(predict_ids):
            predict_data = rows_data[row_id][predict_id]
            if not predict_data:
                continue
            min_timestamp = min(min(frame_data["timestamp"] for frame_data in predict_data), min_timestamp)

        # Process each predict camera for this row
        for predict_idx, predict_id in enumerate(predict_ids):
            predict_data = rows_data[row_id][predict_id]

            # Organize data by lane index for 3D plotting
            weed_lanes_data = defaultdict(list)
            crop_lanes_data = defaultdict(list)

            num_lanes = len(predict_data[0]["weed_heights"])

            for frame_data in predict_data:
                timestamp = frame_data["timestamp"]
                weed_heights = frame_data["weed_heights"]
                crop_heights = frame_data["crop_heights"]

                # Process each lane's weed heights
                for lane_idx, height in enumerate(weed_heights):
                    weed_lanes_data[lane_idx].append({"timestamp": timestamp, "height": height, "lane_idx": lane_idx})

                # Process each lane's crop heights
                for lane_idx, height in enumerate(crop_heights):
                    crop_lanes_data[lane_idx].append({"timestamp": timestamp, "height": height, "lane_idx": lane_idx})

            # Plot weed height lines for each lane

            for lane_idx, lane_data in weed_lanes_data.items():
                # Sort by timestamp for proper line connection
                lane_data.sort(key=lambda x: x["timestamp"])

                timestamps = [point["timestamp"] - min_timestamp for point in lane_data]
                heights = [point["height"] for point in lane_data]
                lane_positions = [(predict_idx * num_lanes) + lane_idx for _ in lane_data]

                hover_text = [
                    "".join(
                        [
                            f"<b>Predict ID:</b> {predict_id}<br>"
                            + f"<b>Lane Index:</b> {lane_idx}<br>"
                            + f"<b>Timestamp:</b> {point['timestamp']} ms<br>"
                            + f"<b>Height:</b> {point['height']} mm<br>"
                        ]
                    )
                    for point in lane_data
                ]

                fig.add_trace(
                    go.Scatter3d(
                        x=lane_positions,
                        y=timestamps,
                        z=heights,
                        mode="lines+markers",
                        name=f"{predict_id}_Lane{lane_idx}_Weed",
                        line=dict(width=3, color="red"),
                        marker=dict(size=2, color="red"),
                        legendgroup=f"{predict_id}_weed",
                        customdata=hover_text,
                        hovertemplate="%{customdata}<extra></extra>",
                    )
                )

            # Plot crop height lines for each lane
            for lane_idx, lane_data in crop_lanes_data.items():
                # Sort by timestamp for proper line connection
                lane_data.sort(key=lambda x: x["timestamp"])

                timestamps = [point["timestamp"] - min_timestamp for point in lane_data]
                heights = [point["height"] for point in lane_data]
                lane_positions = [(predict_idx * num_lanes) + lane_idx for _ in lane_data]

                hover_text = [
                    "".join(
                        [
                            f"<b>Predict ID:</b> {predict_id}<br>"
                            + f"<b>Lane Index:</b> {lane_idx}<br>"
                            + f"<b>Timestamp:</b> {point['timestamp']} ms<br>"
                            + f"<b>Height:</b> {point['height']} mm<br>"
                        ]
                    )
                    for point in lane_data
                ]

                fig.add_trace(
                    go.Scatter3d(
                        x=lane_positions,
                        y=timestamps,
                        z=heights,
                        mode="lines+markers",
                        name=f"{predict_id}_Lane{lane_idx}_Crop",
                        line=dict(width=3, color="green", dash="dash"),
                        marker=dict(size=2, color="green"),
                        legendgroup=f"{predict_id}_crop",
                        customdata=hover_text,
                        hovertemplate="%{customdata}<extra></extra>",
                    )
                )

        # Update layout for 3D plot
        fig.update_layout(
            title=f"3D Lane Heights - {row_id}",
            scene=dict(
                xaxis_title="Lane Index",
                yaxis_title="Timestamp (ms)",
                zaxis_title="Height (mm)",
                zaxis=dict(autorange="reversed"),  # Invert z-axis so higher values are lower
                camera=dict(eye=dict(x=1.5, y=1.5, z=1.5)),
            ),
            legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.01),
            height=700,
        )

        st.plotly_chart(fig, use_container_width=True)


def plot_3d_trajectories(  # noqa: C901
    trajectory_by_id: Dict[str, List[Dict[str, Any]]], keys: List[str], max_ymm: float
) -> None:
    """Create a 3D plot showing all trajectory points connected by lines"""
    fig = go.Figure()

    for key in keys:
        points = trajectory_by_id[key]
        if not points:
            continue

        # Extract coordinates
        x_coords = []
        y_coords = []
        z_coords = []
        hover_text = []

        for point in points:
            x_mm = point.get("x_mm")
            y_mm = point.get("y_mm")
            z_mm = point.get("z_mm")

            # Only include points that have all 3D coordinates and are within y_mm filter
            if x_mm is not None and y_mm is not None and z_mm is not None and y_mm < max_ymm:
                x_coords.append(x_mm)
                y_coords.append(y_mm)
                z_coords.append(z_mm)

                # Create enhanced hover text with all trajectory information
                hover_info = [
                    f"<b>Trajectory ID:</b> {key}",
                    f"<b>x_mm:</b> {x_mm:.2f}",
                    f"<b>y_mm:</b> {y_mm:.2f}",
                    f"<b>z_mm:</b> {z_mm:.2f}",
                    f"<b>Timestamp:</b> {point.get('timestamp_ms', 'N/A')} ms",
                    "",
                ]

                # Add all available trajectory information
                if point.get("weed_score") is not None:
                    hover_info.append(f"<b>Weed Score:</b> {point['weed_score']:.3f}")
                if point.get("crop_score") is not None:
                    hover_info.append(f"<b>Crop Score:</b> {point['crop_score']:.3f}")
                if point.get("plant_score") is not None:
                    hover_info.append(f"<b>Plant Score:</b> {point['plant_score']:.3f}")
                if point.get("kill_status") is not None:
                    hover_info.append(f"<b>Kill Status:</b> {point['kill_status']}")
                if point.get("classification") is not None:
                    hover_info.append(f"<b>Classification:</b> {point['classification']}")
                if point.get("num_detections_used_for_decision") is not None:
                    hover_info.append(f"<b>Detections Used:</b> {point['num_detections_used_for_decision']}")
                if point.get("is_reachable") is not None:
                    hover_info.append(f"<b>Is Reachable:</b> {point['is_reachable']}")
                if point.get("intersected_with_nonshootable") is not None:
                    hover_info.append(f"<b>Intersected Nonshootable:</b> {point['intersected_with_nonshootable']}")
                if point.get("nonshootable_type_string"):
                    hover_info.append(f"<b>Nonshootable Type:</b> {point['nonshootable_type_string']}")
                if point.get("protected_by_traj") is not None:
                    hover_info.append(f"<b>Protected by Traj:</b> {point['protected_by_traj']}")
                if point.get("band_status"):
                    hover_info.append(f"<b>Band Status:</b> {point['band_status']}")
                if point.get("crop_line_id") is not None:
                    hover_info.append(f"<b>Crop Line ID:</b> {point['crop_line_id']}")
                if point.get("size_mm") is not None:
                    hover_info.append(f"<b>Size (mm):</b> {point['size_mm']:.2f}")
                if point.get("shoot_time_actual_ms") is not None:
                    hover_info.append(f"<b>Shoot Time:</b> {point['shoot_time_actual_ms']} ms")
                if point.get("speculative_shoot_time_actual_ms") is not None:
                    hover_info.append(f"<b>Speculative Shoot Time:</b> {point['speculative_shoot_time_actual_ms']} ms")

                hover_text.append("<br>".join(hover_info))

        # Add 3D scatter trace with lines connecting the points
        if x_coords and y_coords and z_coords:
            fig.add_trace(
                go.Scatter3d(
                    x=x_coords,
                    y=y_coords,
                    z=z_coords,
                    mode="lines+markers",
                    name=key,
                    line=dict(width=3),
                    marker=dict(size=4),
                    hovertemplate="%{customdata}<extra></extra>",
                    customdata=hover_text,
                )
            )

    # Update layout for 3D plot
    fig.update_layout(
        title="3D Trajectory Visualization",
        scene=dict(
            xaxis_title="x_mm",
            yaxis_title="y_mm",
            zaxis_title="z_mm",
            zaxis=dict(autorange="reversed"),  # Invert z-axis so higher values are lower
            camera=dict(eye=dict(x=1.5, y=1.5, z=1.5)),
        ),
        legend_title="Trajectories",
        height=700,
    )

    st.plotly_chart(fig, use_container_width=True)


def plot_trajectories(trajectory_by_id: Dict[int, Dict[str, Any]], keys: List[str], max_ymm: float):
    plot_height_by_y(keys, trajectory_by_id, max_ymm)
    plot_height_by_timestamp(keys, trajectory_by_id)

    # Fallback to old trajectory-based statistics
    plot_height_statistics_by_timestamp(keys, trajectory_by_id)


def plot_lane_heights(lane_height_data: Dict[str, Dict[str, Dict[int, Dict[str, Any]]]]):
    plot_3d_lane_heights(lane_height_data)


def main():  # noqa: C901
    st.title("Trajectory Viewer")

    robot_id = st.text_input("Robot ID")
    diags = []
    if not robot_id:
        return
    diags = get_zips_from_s3(robot_id)

    num_trajectories = st.sidebar.number_input("Number of trajectories", min_value=1, max_value=1000, value=50)
    max_ymm = st.sidebar.slider(
        "Max y_mm position", min_value=1, max_value=2000, value=200
    )  # 200mm is roughly between center and bottom of predict, assuming 200 ppi

    diags = ["<select>"] + diags

    diag = st.selectbox("Select", diags)

    if diag == "<select>":
        return
    zip_path = download_zip_from_s3(diag)
    unzipped_path = unzip(zip_path)
    st.text(f"Downloaded and unzipped {zip_path}")

    files = list_files(unzipped_path)

    trajectory_by_id = {}
    lane_height_data = defaultdict(dict)  # {row_id: {predict_id: lane_height_data}}
    file_min_time = float("inf")
    file_max_time = 0

    with st.spinner("Processing trajectories and lane heights..."):
        row_ids = []
        for file in files:
            if file.endswith("diagnostic_snapshots.carbon"):
                row_id = file.split("/")[-2]
                trajectories, min_time, max_time = get_trajectories(file, row_id)
                trajectory_by_id.update(trajectories)
                if min_time > 0:
                    file_min_time = min(file_min_time, min_time)
                    file_max_time = max(file_max_time, max_time)
                row_ids.append(row_id)
            elif "_lane_height.carbon" in file:
                # Extract row_id and predict_id from filename
                # Expected format: /path/to/row1/predict2_lane_height.carbon
                row_id = file.split("/")[-2]
                filename = file.split("/")[-1]
                predict_id = filename.split("_lane_height.carbon")[0]

                lane_heights = get_lane_heights(file, predict_id)
                if lane_heights:
                    lane_height_data[row_id][predict_id] = lane_heights

    row_ids = st.sidebar.multiselect("Select Rows", sorted(row_ids))

    if row_ids:
        trajectory_by_id = {k: v for k, v in trajectory_by_id.items() if k.split("_")[0] in row_ids}

    if file_min_time > 0 and file_max_time > 0:
        total_duration_ms = file_max_time - file_min_time
        total_duration_sec = total_duration_ms / 1000.0

        st.sidebar.write(f"File time range: {file_min_time} - {file_max_time} ms")
        st.sidebar.write(f"Duration: {total_duration_sec:.3f} seconds")

        def format_relative_time(timestamp_ms):
            relative_ms = timestamp_ms - file_min_time
            seconds = relative_ms / 1000.0
            return f"{seconds:.3f}s"

        min_time_input, max_time_input = st.sidebar.slider(
            "Start timestamp range",
            value=(file_min_time, file_max_time),
            min_value=file_min_time,
            max_value=file_max_time,
            step=1000,
        )

        st.sidebar.write(
            f"**Selected range: {format_relative_time(min_time_input)} to {format_relative_time(max_time_input)}**"
        )

        min_start_time_ms = int(min_time_input)
        max_start_time_ms = int(max_time_input)

        if min_start_time_ms != file_min_time or max_start_time_ms != file_max_time:
            filtered_trajectory_by_id = {}
            for traj_id, points in trajectory_by_id.items():
                if points:
                    start_time = points[0].get("timestamp_ms", 0)

                    if start_time >= min_start_time_ms and start_time <= max_start_time_ms:
                        filtered_trajectory_by_id[traj_id] = points

            filtered_lane_heights = {
                row_id: {
                    predict_id: {
                        timestamp: data
                        for timestamp, data in predict_data.items()
                        if min_start_time_ms <= timestamp <= max_start_time_ms
                    }
                    for predict_id, predict_data in row_data.items()
                }
                for row_id, row_data in lane_height_data.items()
            }

            lane_height_data = filtered_lane_heights

            trajectory_by_id = filtered_trajectory_by_id
    else:
        st.sidebar.warning("No timestamps found in diagnostic files")

    if trajectory_by_id:
        file_min_x, file_max_x = get_x_mm_range(trajectory_by_id)

        min_x_input, max_x_input = st.sidebar.slider(
            "Start x_mm position range",
            value=(file_min_x, file_max_x),
            min_value=file_min_x,
            max_value=file_max_x,
            step=(file_max_x - file_min_x) / 100.0 if file_max_x != file_min_x else 1.0,
            format="%.2f",
        )

        st.sidebar.write(f"**Selected x_mm range: {min_x_input:.2f} to {max_x_input:.2f} mm**")

        if min_x_input != file_min_x or max_x_input != file_max_x:
            filtered_trajectory_by_id = {}
            for traj_id, points in trajectory_by_id.items():
                if points:
                    start_x = points[0].get("x_mm")

                    if start_x is not None and start_x >= min_x_input and start_x <= max_x_input:
                        filtered_trajectory_by_id[traj_id] = points

            trajectory_by_id = filtered_trajectory_by_id

    if not trajectory_by_id:
        st.warning("No trajectories found with the current filters")
        return

    keys_and_counts = [(key, len(val)) for key, val in trajectory_by_id.items()]
    keys_and_counts = sorted(keys_and_counts, key=lambda x: x[1], reverse=True)
    keys = [keys_and_counts[i][0] for i in range(min(num_trajectories, len(trajectory_by_id)))]

    st.subheader("Trajectory Statistics")
    col1, col2 = st.columns(2)
    with col1:
        st.metric("Total Trajectories", len(trajectory_by_id))
    with col2:
        total_points = sum(len(val) for val in trajectory_by_id.values())
        st.metric("Total Points", total_points)

    # Create tabs for different visualizations
    tab1, tab2, tab3 = st.tabs(["2D Line Charts", "3D Trajectory View", "Lane Height Viewer"])

    with tab1:
        st.subheader("2D Trajectory Analysis")

        plot_trajectories(trajectory_by_id, keys, max_ymm)

    with tab2:
        st.subheader("3D Trajectory Visualization")
        st.write("This view shows all trajectory points in 3D space (x_mm, y_mm, z_mm) connected by lines.")

        # Check if we have x_mm data for 3D plotting
        has_3d_data = False
        for key in keys:
            if key in trajectory_by_id:
                for point in trajectory_by_id[key]:
                    if point.get("x_mm") is not None:
                        has_3d_data = True
                        break
                if has_3d_data:
                    break

        if has_3d_data:
            plot_3d_trajectories(trajectory_by_id, keys, max_ymm)
        else:
            st.warning(
                "No x_mm coordinate data available for 3D visualization. The trajectories may not have been captured with 3D position information."
            )

        with tab3:
            st.subheader("Lane Heights")
            # Get timestamp range for lane height filtering
            plot_lane_heights(lane_height_data)


if __name__ == "__main__":
    main()
