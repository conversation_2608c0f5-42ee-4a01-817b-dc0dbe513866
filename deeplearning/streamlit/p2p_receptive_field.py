import math
import os

import numpy as np
import streamlit as st
import torch
from torch.nn import functional as F

from deeplearning.p2p.config import P2PConfig
from deeplearning.p2p.datasets import P2PDataset, label_coord
from deeplearning.p2p.models.p2p_model_v1 import P2PModelV1
from deeplearning.scripts.p2p.train import PROD_DATA_ROOTS
from deeplearning.tools import download_makannotations_data
from deeplearning.trt_extensions.p2p_loops import upsample_like


def rescale(arr):
    # Rescale the array to be between 0 and 1
    arr = (arr - arr.min()) / (arr.max() - arr.min())
    return arr


def convert_label_to_ds(selected, target, x, y):
    # Convert the label to the downsampled coordinates
    x = x / target.shape[2] * selected.shape[2]
    y = y / target.shape[3] * selected.shape[3]
    return int(x), int(y)


def upscale_vis(arr, x, y, target_size):
    new_arr = torch.zeros_like(arr)
    new_arr[0, 0, y, x] = 1
    final = F.interpolate(new_arr, size=(target_size, target_size), mode="bilinear", align_corners=False)
    final[final > 0] = 1
    # final[final < 1] = 0
    return final[0, 0].cpu().numpy()


def most_common(arr):
    # Given a numpy array, return the most common value
    vals, counts = np.unique(arr, return_counts=True)
    max_count_index = np.argmax(counts)
    return vals[max_count_index]


def vis_grad(arr, grad, selected, most_common_val_thresh=0.01):
    if output_type == "hit_ds" and most_common_val_thresh > 0:
        grad[
            abs(grad - most_common(grad.numpy())) < (grad.max() - grad.min()) / (1 / most_common_val_thresh)
        ] = 0  # remove the most common value and anything 2% away

    if use_filter:
        grad[grad < min_filter] = 0
        grad[grad >= max_filter] = 0
        grad[grad != 0] = 1

    vis = rescale(grad).numpy()

    if output_type != "match":
        new_box = upscale_vis(selected, x, y, target_size=arr.shape[2])

        vis = vis + np.array(new_box) * 0.5
        vis = rescale(vis)

    vis = rescale(vis)
    arr = target[0].permute(1, 2, 0).detach().cpu().numpy()
    arr = rescale(arr)

    with st.sidebar:
        alpha = st.slider("Gradient Opacity", 0.0, 1.0, 0.5, step=0.01)

    vis = np.stack([vis] * 3, axis=-1)

    vis = vis * alpha + arr * (1 - alpha)

    st.image(vis, use_column_width=True)


def crop_target_from_hit_ds(target, grad, hit_ds, perspective_size):
    image_pad = math.ceil(perspective_size / 2) + 16
    image_crop_size = perspective_size + 32

    out_softmax_up_nz = upsample_like(hit_ds[0:1], list(target.shape)).nonzero()
    y, x = out_softmax_up_nz[0, 2], out_softmax_up_nz[0, 3]
    target_padded = F.pad(target[0], [image_pad, image_pad, image_pad, image_pad])
    target_cropped = target_padded[:, y : y + image_crop_size, x : x + image_crop_size]

    grad_padded = F.pad(grad[0], [image_pad, image_pad, image_pad, image_pad])
    grad_cropped = grad_padded[:, y : y + image_crop_size, x : x + image_crop_size]

    return target_cropped.unsqueeze(0), grad_cropped.unsqueeze(0)


if __name__ == "__main__":

    if "model" not in st.session_state:
        dl_config = P2PConfig.from_dict({})

        model: P2PModelV1 = P2PModelV1(config=dl_config).requires_grad_(False).cuda()
        for param in model.parameters():
            param.requires_grad = False

        with st.spinner("Downloading Dataset"):
            download_makannotations_data.sync_images_with_masks(PROD_DATA_ROOTS, dest_path="/data/deeplearning/p2p")

        data_roots_with_path = [os.path.join("/data/deeplearning/p2p/", x) for x in PROD_DATA_ROOTS]
        dataset = P2PDataset(roots=data_roots_with_path, smearing=0,)

        st.session_state["dataset"] = dataset
        st.session_state["model"] = model
    else:
        dataset = st.session_state["dataset"]
        model = st.session_state["model"]

    with st.sidebar:
        use_dataset = st.checkbox("Use Dataset", True)
        if use_dataset:
            dataset_index = st.number_input("Dataset Index", 0, len(dataset) - 1, 0, step=1)
            perspective = dataset[dataset_index][0].clone().cuda().unsqueeze(0)
            target = dataset[dataset_index][1].clone().cuda().unsqueeze(0)
            label = label_coord(dataset[dataset_index][2].unsqueeze(0), 0)

        if not use_dataset:
            target_size = st.number_input("Target Size", 100, 10000, 600, step=1)
            perspective = (
                st.session_state["random_perspective"]
                if "random_perspective" in st.session_state
                else torch.randn((1, 3, 100, 100)).float().cuda()
            )
            target = (
                st.session_state["random_target"]
                if "random_target" in st.session_state
                else torch.randn((1, 3, target_size, target_size)).float().cuda()
            )

        target.requires_grad_(True)
        perspective.requires_grad_(True)

    model = model.eval().cuda().requires_grad_(False)
    model.zero_grad()
    perspective.grad = None
    target.grad = None

    _, hit_ds, hit, offset_ds, offset, match, _, _ = model(perspective, target)

    with st.sidebar:
        output_type = st.selectbox("Output Type", ["hit_ds", "offset_ds", "match"])

    if output_type == "hit_ds":
        selected = hit_ds
    elif output_type == "offset_ds":
        selected = offset_ds
    elif output_type == "match":
        selected = match

    with st.sidebar:
        if output_type in ["hit_ds", "offset_ds"]:
            use_position_label = st.checkbox("Use Position Label", True)
            if use_position_label:
                x, y = convert_label_to_ds(selected, target, label[0], label[1])
            else:
                x = st.slider("X", 0, selected.shape[2], 0)
                y = st.slider("Y", 0, selected.shape[3], 0)
        else:
            x, y = 0, 0

    with torch.no_grad():
        selected_copy = selected.clone()
        if output_type in ["hit_ds", "offset_ds"]:
            selected_copy[0, 0, y, x] = 100
        elif output_type == "match":
            selected_copy[0] = abs(selected_copy[0] - 1)  # flip the values 0 -> 1, 1 -> 0

    loss = ((selected - selected_copy) ** 2).sum()
    loss.backward()

    with st.sidebar:
        use_filter = st.checkbox("Filter Vals")
        min_filter = st.number_input("Filter Bottom Percent", 0.0, 1.0, 0.0, step=1e-2, format="%0.2f")
        max_filter = st.number_input("Filter Top Percent", 0.0, 1.0, 1.0, step=1e-2, format="%0.2f")
        most_common_val_thresh = st.number_input(
            "Remove Vals Within Percent of Mode", 0.0, 1.0, 0.00, step=1e-2, format="%0.2f"
        )

    if output_type == "offset_ds":
        target, grad = crop_target_from_hit_ds(
            target, target.grad.cpu().detach(), hit_ds, perspective_size=perspective.shape[2]
        )
        grad = grad[0].sum(axis=0).cpu().detach()

    else:

        grad = target.grad[0].sum(axis=0).cpu().detach()

    maximum, minimum = grad.max(), grad.min()
    grad = rescale(grad)

    st.title("Gradient Visualization")
    st.write(f"Maximum: {maximum:.3f}, Minimum: {minimum:.3f}")
    vis_grad(target, grad, selected, most_common_val_thresh=most_common_val_thresh)

    col1, col2 = st.columns(2)
    with col1:
        st.title("Target")
        example_image = target[0].cpu().detach().permute(1, 2, 0).numpy()
        min = example_image.min()
        max = example_image.max()
        example_image = (example_image - min) / (max - min)
        st.image(example_image, use_column_width=True)

    with col2:
        st.title("Perspective")
        example_image = perspective[0].cpu().detach().permute(1, 2, 0).numpy()
        min = example_image.min()
        max = example_image.max()
        example_image = (example_image - min) / (max - min)
        st.image(example_image, use_column_width=True)
