import os

import cv2
import streamlit as st
import torch

from cv.comparison.comparison_python import ComparisonModel
from deeplearning.utils.download_utils import download_records
from lib.common.model.cpp.model_python import AtomicModel


def main():
    model_id = st.sidebar.text_input("Comparison Model", value="cmp-20250221-n6u5miogir")

    if model_id == "":
        return

    if not os.path.exists(f"/data/deeplearning/models/{model_id}/trt_fp32.trt"):
        download_records(model_id)

    model = ComparisonModel(AtomicModel(f"/data/deeplearning/models/{model_id}/trt_fp32.trt", 0))

    input_dir = st.sidebar.text_input("Input dir", value="/data/deeplearning/chips_to_look_at")

    only_files = sorted(
        [f for f in os.listdir(input_dir) if os.path.isfile(os.path.join(input_dir, f)) and f[-4:] == ".png"]
    )

    files_to_compare = st.multiselect("Files to compare", only_files)
    select_all = st.checkbox("Select All")
    if select_all:
        files_to_compare = only_files
    if len(files_to_compare) < 2:
        return
    tmp_images = [cv2.cvtColor(cv2.imread(f"{input_dir}/{f}"), cv2.COLOR_BGR2RGB) for f in files_to_compare]
    images = [torch.moveaxis(torch.from_numpy(im), -1, 0) for im in tmp_images]

    comparison_embeddings = [
        model.infer(image_tensor=image.unsqueeze(0), x=image.shape[-1] // 2, y=image.shape[-2] // 2) for image in images
    ]

    for first_index in range(len(images)):
        with st.expander(files_to_compare[first_index], expanded=False):
            for second_index in range(len(images)):
                first_name = files_to_compare[first_index]
                second_name = files_to_compare[second_index]
                first_image = images[first_index]
                second_image = images[second_index]
                first_comparison = comparison_embeddings[first_index]
                second_comparison = comparison_embeddings[second_index]
                cols = st.columns(3, gap="large")
                with cols[0]:
                    display_image(first_image, first_name)

                with cols[1]:
                    display_image(second_image, second_name)

                with cols[2]:
                    st.text(
                        torch.nn.functional.cosine_similarity(
                            torch.vstack(first_comparison.embedding), torch.vstack(second_comparison.embedding)
                        ).item()
                    )


def display_image(image: torch.Tensor, name: str) -> None:
    _, height, width = image.shape
    y_c = height // 2
    x_c = width // 2
    np_image = torch.moveaxis(image, 0, -1).numpy()
    top_left = (y_c - 200, x_c - 200)
    top_right = (y_c - 200, x_c + 200)
    bottom_left = (y_c + 200, x_c - 200)
    bottom_right = (y_c + 200, x_c + 200)
    np_image = cv2.line(np_image, top_left, top_right, (0, 255, 0), 2)
    np_image = cv2.line(np_image, bottom_left, bottom_right, (0, 255, 0), 2)
    np_image = cv2.line(np_image, top_left, bottom_left, (0, 255, 0), 2)
    np_image = cv2.line(np_image, top_right, bottom_right, (0, 255, 0), 2)

    st.image(np_image, caption=name, use_column_width=True)


if __name__ == "__main__":
    main()
