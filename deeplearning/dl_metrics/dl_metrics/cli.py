#!/usr/bin/env python3
"""
Command Line Interface for DL Metrics

This module provides a CLI for running deep learning metrics on points_db files.
"""

import argparse
import sys
from datetime import date
from typing import Dict, List, Optional, Set, Tuple, Type

import pandas as pd

from . import metrics_list
from .defaults import CPT, CROP_PROTECTION_RADIUS_MM, DISTANCE_THRESHOLD_MM, PLANT_THRESHOLD, WPT
from .metric import Metric
from .points_db import Image, get_db, get_distinct_filepaths, get_session


def parse_date(date_str: str) -> date:
    """Parse date string in YYYY-MM-DD format."""
    return date.fromisoformat(date_str)


def parse_set_arg(arg: Optional[str]) -> Optional[Set[str]]:
    """Parse comma-separated string into a set."""
    if arg is None:
        return None
    return set(item.strip() for item in arg.split(",") if item.strip())


def parse_int_set_arg(arg: Optional[str]) -> Optional[Set[int]]:
    """Parse comma-separated string into a set of integers."""
    if arg is None:
        return None
    return set(int(item.strip()) for item in arg.split(",") if item.strip())


def parse_float_tuple_arg(arg: Optional[str]) -> Optional[Tuple[float, float]]:
    """Parse comma-separated string into a tuple of floats."""
    if arg is None:
        return None
    parts = arg.split(",")
    if len(parts) != 2:
        raise ValueError("Float tuple must have exactly 2 values separated by comma")
    return (float(parts[0].strip()), float(parts[1].strip()))


def parse_date_range_arg(arg: Optional[str]) -> Optional[Tuple[date, date]]:
    """Parse date range string in format 'YYYY-MM-DD,YYYY-MM-DD'."""
    if arg is None:
        return None
    parts = arg.split(",")
    if len(parts) != 2:
        raise ValueError("Date range must have exactly 2 dates separated by comma")
    return (parse_date(parts[0].strip()), parse_date(parts[1].strip()))


def list_metrics() -> None:
    """List all available metrics."""
    print("Available metrics:")
    for i, metric_class in enumerate(metrics_list, 1):
        metric = metric_class()
        print(f"{i:2d}. {metric.name}")
        if metric.description:
            print(f"     {metric.description.strip()}")
        print()


def find_overlapping_images(db_paths: List[str]) -> Dict[str, List[str]]:
    """Find overlapping images across multiple databases."""
    if len(db_paths) < 2:
        raise ValueError("At least 2 database paths are required for comparison")

    # Get all filepaths from each database
    all_filepaths = {}
    for db_path in db_paths:
        try:
            engine = get_db(db_path)
            session = get_session(engine)
            filepaths = get_distinct_filepaths(session)
            all_filepaths[db_path] = filepaths
            session.close()
        except Exception as e:
            print(f"Error reading database {db_path}: {e}")
            sys.exit(1)

    # Find overlapping filepaths
    overlapping = set(all_filepaths[db_paths[0]])
    for db_path in db_paths[1:]:
        overlapping = overlapping.intersection(set(all_filepaths[db_path]))

    if not overlapping:
        return {}

    # Return overlapping filepaths for each database
    result = {}
    for db_path in db_paths:
        result[db_path] = list(overlapping)

    return result


def run_metric(
    db_path: str,
    metric_class: Type[Metric],
    crop_protection_radius_mm: float = CROP_PROTECTION_RADIUS_MM,
    distance_threshold_mm: float = DISTANCE_THRESHOLD_MM,
    wpt: float = WPT,
    cpt: float = CPT,
    plant_threshold: float = PLANT_THRESHOLD,
    date_range: Optional[Tuple[date, date]] = None,
    robot_ids: Optional[Set[str]] = None,
    crops: Optional[Set[str]] = None,
    session_names: Optional[Set[str]] = None,
    geohash_prefixes: Optional[Set[str]] = None,
    filepaths_to_exclude: Optional[Set[str]] = None,
    filepaths_to_include: Optional[Set[str]] = None,
    point_ids: Optional[Set[int]] = None,
    point_uuids: Optional[Set[str]] = None,
    point_size: Optional[Tuple[float, float]] = None,
    embedding_bucket: Optional[Set[int]] = None,
    group_by_date: bool = False,
    group_by_size: bool = False,
    group_by_filepath: bool = False,
    group_by_weed_density: bool = False,
    group_by_crop_count: bool = False,
    group_by_robot_id: bool = False,
    group_by_category_class_name: bool = False,
    group_by_crop_id: bool = False,
    group_by_geohash: bool = False,
    group_by_image_id: bool = False,
    group_by_y_position: bool = False,
    group_by_point_id: bool = False,
    group_by_uuid: bool = False,
    group_by_geohash_prefix: Optional[int] = None,
    group_by_embedding_bucket: Optional[List[int]] = None,
) -> pd.DataFrame:
    """Run a specific metric on the database and return the result DataFrame."""
    # Create metric instance with parameters
    metric = metric_class(
        crop_protection_radius_mm=crop_protection_radius_mm,
        distance_threshold_mm=distance_threshold_mm,
        wpt=wpt,
        cpt=cpt,
        plant_threshold=plant_threshold,
    )

    # Apply filters
    if any(
        [
            date_range,
            robot_ids,
            crops,
            session_names,
            geohash_prefixes,
            filepaths_to_exclude,
            filepaths_to_include,
            point_ids,
            point_uuids,
            point_size,
            embedding_bucket,
        ]
    ):
        metric = metric.filter(
            date_range=date_range,
            robot_ids=robot_ids,
            crops=crops,
            session_names=session_names,
            geohash_prefixes=geohash_prefixes,
            filepaths_to_exclude=filepaths_to_exclude,
            filepaths_to_include=filepaths_to_include,
            filter_point_ids=point_ids,
            filter_point_uuids=point_uuids,
            size=point_size,
            embedding_bucket=embedding_bucket,
        )

    # Apply grouping
    if any(
        [
            group_by_date,
            group_by_size,
            group_by_filepath,
            group_by_weed_density,
            group_by_crop_count,
            group_by_robot_id,
            group_by_category_class_name,
            group_by_crop_id,
            group_by_geohash,
            group_by_image_id,
            group_by_y_position,
            group_by_point_id,
            group_by_uuid,
            group_by_geohash_prefix,
            group_by_embedding_bucket,
        ]
    ):
        metric = metric.group_by(
            date=group_by_date,
            size=group_by_size,
            filepath=group_by_filepath,
            crop_count=group_by_crop_count,
            robot_id=group_by_robot_id,
            category_class_name=group_by_category_class_name,
            crop_id=group_by_crop_id,
            geohash=group_by_geohash,
            image_id=group_by_image_id,
            y_position=group_by_y_position,
            uuid=group_by_uuid,
            point_id=group_by_point_id,
            embedding_bucket=group_by_embedding_bucket,
            geohash_prefix=group_by_geohash_prefix,
        )

    # Connect to database and run metric
    try:
        engine = get_db(db_path)
        session = get_session(engine)

        result = metric(session)
        return result

    except Exception as e:
        print(f"Error running metric: {e}")
        sys.exit(1)
    finally:
        if "session" in locals():
            session.close()


def run_metric_or_compare(  # noqa: C901
    db_paths: List[str],
    metric_name: str,
    output_format: str = "csv",
    output_file: Optional[str] = None,
    use_overlapping_only: bool = True,
    **kwargs,
) -> None:
    """Run a metric on a single database or compare across multiple databases."""

    # Find the metric class to get name and description
    metric_class = None
    for mc in metrics_list:
        if mc().name.lower() == metric_name.lower():
            metric_class = mc
            break

    if metric_class is None:
        print(f"Error: Metric '{metric_name}' not found.")
        print("Use 'list' command to see available metrics.")
        sys.exit(1)

    metric = metric_class()
    is_single_db = len(db_paths) == 1

    # Display metric header information
    print(f"Metric: {metric.name}")
    if metric.description:
        print(f"Description: {metric.description.strip()}")
    print(f"Databases: {', '.join(db_paths)}")
    if not is_single_db:
        print(f"Using overlapping images only: {use_overlapping_only}")
    print("-" * 80)

    # Find overlapping images if multiple databases and requested
    overlapping_filepaths = None
    if not is_single_db and use_overlapping_only:
        overlapping_filepaths = find_overlapping_images(db_paths)
        if not overlapping_filepaths:
            print("\nNo overlapping images found. Cannot perform comparison.")
            return

        print(f"\nFound {len(overlapping_filepaths[db_paths[0]])} overlapping images")

        # Show total images in each database for context
        print("Database image counts:")
        for db_path in db_paths:
            try:
                engine = get_db(db_path)
                session = get_session(engine)
                total_images = session.query(Image).count()
                session.close()
                overlap_count = len(overlapping_filepaths[db_path])
                overlap_percentage = (overlap_count / total_images * 100) if total_images > 0 else 0
                print(f"  {db_path}: {total_images} total images ({overlap_percentage:.1f}% overlap)")
            except Exception as e:
                print(f"  {db_path}: Error reading database - {e}")

    # Run metric on each database
    results = {}
    for db_path in db_paths:
        print(f"\nRunning on {db_path}...")
        try:
            # Set filepaths_to_include for this specific database if using overlapping images
            current_filepaths_to_include = kwargs.get("filepaths_to_include")
            if not is_single_db and use_overlapping_only and overlapping_filepaths and db_path in overlapping_filepaths:
                current_filepaths_to_include = set(overlapping_filepaths[db_path])

            kwargs["filepaths_to_include"] = current_filepaths_to_include
            result = run_metric(db_path=db_path, metric_class=metric_class, **kwargs)
            results[db_path] = result
            print(f"  Found {len(result)} result rows")
        except Exception as e:
            print(f"  Error: {e}")
            results[db_path] = pd.DataFrame()

    # Summary statistics for each database
    for db_path in db_paths:
        if db_path in results and not results[db_path].empty:
            df = results[db_path]
            print(f"\n{db_path}:")
            if "numerator" in df.columns and "denominator" in df.columns:
                total_numerator = df["numerator"].sum()
                total_denominator = df["denominator"].sum()
                if total_denominator > 0:
                    overall_metric = total_numerator / total_denominator
                    print(f"  Overall metric: {overall_metric:.4f} ({total_numerator:.0f}/{total_denominator:.0f})")
                else:
                    print("  Overall metric: N/A (denominator is 0)")
            print(f"  Total rows: {len(df)}")

    # For single database, check if we have results
    if is_single_db:
        output_df = results[db_paths[0]]
        if output_df.empty:
            print("\nNo results found for the given filters.")
            return
    else:
        # For multiple databases, combine results for comparison
        combined_results = []
        for db_path, result in results.items():
            if not result.empty:
                # Add database identifier column
                result_copy = result.copy()
                result_copy["database"] = db_path
                combined_results.append(result_copy)

        if not combined_results:
            print("\nNo results found across any database.")
            return

        # Combine all results
        output_df = pd.concat(combined_results, ignore_index=True)

    # Output results in the specified format
    if output_file:
        if output_format.lower() == "csv":
            output_df.to_csv(output_file, index=False)
            print(f"\nRaw results saved to: {output_file}")
        elif output_format.lower() == "json":
            output_df.to_json(output_file, orient="records", indent=2)
            print(f"\nRaw results saved to: {output_file}")
    else:
        if output_format.lower() == "csv":
            print("\nRaw results:")
            print(output_df.to_csv(index=False))
        elif output_format.lower() == "json":
            print("\nRaw results:")
            print(output_df.to_json(orient="records", indent=2))


def main() -> None:
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Deep Learning Metrics CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all available metrics
  python -m dl_metrics.cli list

  # Run weeds detected metric on a single database
  python -m dl_metrics.cli run "Weeds detected" --db-paths data.db

  # Run with filters
  python -m dl_metrics.cli run "Weeds detected" --db-paths data.db \\
    --date-range "2023-01-01,2023-01-31" \\
    --robot-ids "robot1,robot2" \\
    --crops "corn,soybeans"

  # Run with grouping
  python -m dl_metrics.cli run "Weeds detected" --db-paths data.db \\
    --group-by-date --group-by-robot-id

  # Save output to file
  python -m dl_metrics.cli run "Weeds detected" --db-paths data.db \\
    --output-file results.csv --output-format csv

  # Compare metrics across multiple databases
  python -m dl_metrics.cli run "Weeds detected" \\
    --db-paths data1.db data2.db data3.db \\
    --output-file comparison.csv

  # Compare using all images (not just overlapping)
  python -m dl_metrics.cli run "Weeds detected" \\
    --db-paths data1.db data2.db \\
    --use-all-images
        """,
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # List command
    subparsers.add_parser("list", help="List all available metrics")

    # Run command (now handles both single and multiple databases)
    run_parser = subparsers.add_parser("run", help="Run a metric on one or more databases")
    run_parser.add_argument("metric_name", help="Name of the metric to run")
    run_parser.add_argument("--db-paths", nargs="+", required=True, help="Paths to the points_db files (one or more)")
    run_parser.add_argument(
        "--output-format", choices=["csv", "json"], default="csv", help="Output format (default: csv)"
    )
    run_parser.add_argument("--output-file", help="Output file path (default: stdout)")
    run_parser.add_argument(
        "--use-overlapping-only",
        action="store_true",
        default=True,
        help="Only use overlapping images across databases (default: True, only applies to multiple databases)",
    )
    run_parser.add_argument(
        "--use-all-images",
        action="store_true",
        help="Use all images from each database (overrides --use-overlapping-only, only applies to multiple databases)",
    )

    # Metric parameters
    metric_group = run_parser.add_argument_group("Metric Parameters")
    metric_group.add_argument(
        "--crop-protection-radius-mm",
        type=float,
        default=CROP_PROTECTION_RADIUS_MM,
        help=f"Crop protection radius in mm (default: {CROP_PROTECTION_RADIUS_MM})",
    )
    metric_group.add_argument(
        "--distance-threshold-mm",
        type=float,
        default=DISTANCE_THRESHOLD_MM,
        help=f"Distance threshold in mm (default: {DISTANCE_THRESHOLD_MM})",
    )
    metric_group.add_argument("--wpt", type=float, default=WPT, help=f"Weed point threshold (default: {WPT})")
    metric_group.add_argument("--cpt", type=float, default=CPT, help=f"Crop point threshold (default: {CPT})")
    metric_group.add_argument(
        "--plant-threshold", type=float, default=PLANT_THRESHOLD, help=f"Plant threshold (default: {PLANT_THRESHOLD})"
    )

    # Filter options
    filter_group = run_parser.add_argument_group("Filter Options")
    filter_group.add_argument("--date-range", help="Date range in format 'YYYY-MM-DD,YYYY-MM-DD'")
    filter_group.add_argument("--robot-ids", help="Comma-separated list of robot IDs")
    filter_group.add_argument("--crops", help="Comma-separated list of crop IDs")
    filter_group.add_argument("--session-names", help="Comma-separated list of session names")
    filter_group.add_argument("--geohash-prefixes", help="Comma-separated list of geohash prefixes")
    filter_group.add_argument("--filepaths-to-exclude", help="Comma-separated list of filepaths to exclude")
    filter_group.add_argument("--filepaths-to-include", help="Comma-separated list of filepaths to include")
    filter_group.add_argument("--point-ids", help="Comma-separated list of point IDs")
    filter_group.add_argument("--point-uuids", help="Comma-separated list of point UUIDs")
    filter_group.add_argument("--point-size", help="Point size range in format 'min,max' (mm)")
    filter_group.add_argument("--embedding-bucket", help="Comma-separated list of embedding bucket IDs")

    # Group by options
    group_group = run_parser.add_argument_group("Group By Options")
    group_group.add_argument("--group-by-date", action="store_true", help="Group results by date")
    group_group.add_argument("--group-by-size", action="store_true", help="Group results by size")
    group_group.add_argument("--group-by-filepath", action="store_true", help="Group results by filepath")
    group_group.add_argument("--group-by-weed-density", action="store_true", help="Group results by weed density")
    group_group.add_argument("--group-by-crop-count", action="store_true", help="Group results by crop count")
    group_group.add_argument("--group-by-robot-id", action="store_true", help="Group results by robot ID")
    group_group.add_argument(
        "--group-by-category-class-name", action="store_true", help="Group results by category class name"
    )
    group_group.add_argument("--group-by-crop-id", action="store_true", help="Group results by crop ID")
    group_group.add_argument("--group-by-geohash", action="store_true", help="Group results by geohash")
    group_group.add_argument("--group-by-image-id", action="store_true", help="Group results by image ID")
    group_group.add_argument("--group-by-y-position", action="store_true", help="Group results by Y position")
    group_group.add_argument("--group-by-point-id", action="store_true", help="Group results by point ID")
    group_group.add_argument("--group-by-uuid", action="store_true", help="Group results by UUID")
    group_group.add_argument("--group-by-geohash-prefix", type=int, help="Group results by geohash prefix length")
    group_group.add_argument(
        "--group-by-embedding-bucket", help="Comma-separated list of embedding bucket IDs for grouping"
    )

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        return

    if args.command == "list":
        list_metrics()
    elif args.command == "run":
        # Handle overlapping images option (only applies to multiple databases)
        use_overlapping_only = args.use_overlapping_only and not args.use_all_images

        # Parse complex arguments
        date_range = parse_date_range_arg(args.date_range)
        robot_ids = parse_set_arg(args.robot_ids)
        crops = parse_set_arg(args.crops)
        session_names = parse_set_arg(args.session_names)
        geohash_prefixes = parse_set_arg(args.geohash_prefixes)
        filepaths_to_exclude = parse_set_arg(args.filepaths_to_exclude)
        filepaths_to_include = parse_set_arg(args.filepaths_to_include)
        point_ids = parse_int_set_arg(args.point_ids)
        point_uuids = parse_set_arg(args.point_uuids)
        point_size = parse_float_tuple_arg(args.point_size)
        embedding_bucket = parse_int_set_arg(args.embedding_bucket)
        group_by_embedding_bucket = parse_int_set_arg(args.group_by_embedding_bucket)

        # Convert group_by_embedding_bucket to list if specified
        if group_by_embedding_bucket:
            group_by_embedding_bucket = list(group_by_embedding_bucket)

        run_metric_or_compare(
            db_paths=args.db_paths,
            metric_name=args.metric_name,
            output_format=args.output_format,
            output_file=args.output_file,
            use_overlapping_only=use_overlapping_only,
            crop_protection_radius_mm=args.crop_protection_radius_mm,
            distance_threshold_mm=args.distance_threshold_mm,
            wpt=args.wpt,
            cpt=args.cpt,
            plant_threshold=args.plant_threshold,
            date_range=date_range,
            robot_ids=robot_ids,
            crops=crops,
            session_names=session_names,
            geohash_prefixes=geohash_prefixes,
            filepaths_to_exclude=filepaths_to_exclude,
            filepaths_to_include=filepaths_to_include,
            point_ids=point_ids,
            point_uuids=point_uuids,
            point_size=point_size,
            embedding_bucket=embedding_bucket,
            group_by_date=args.group_by_date,
            group_by_size=args.group_by_size,
            group_by_filepath=args.group_by_filepath,
            group_by_weed_density=args.group_by_weed_density,
            group_by_crop_count=args.group_by_crop_count,
            group_by_robot_id=args.group_by_robot_id,
            group_by_category_class_name=args.group_by_category_class_name,
            group_by_crop_id=args.group_by_crop_id,
            group_by_geohash=args.group_by_geohash,
            group_by_image_id=args.group_by_image_id,
            group_by_y_position=args.group_by_y_position,
            group_by_point_id=args.group_by_point_id,
            group_by_uuid=args.group_by_uuid,
            group_by_geohash_prefix=args.group_by_geohash_prefix,
            group_by_embedding_bucket=group_by_embedding_bucket,
        )


if __name__ == "__main__":
    main()
