from typing import List, Optional, Union

import pandas as pd
from sqlalchemy import and_, or_

from . import comparison_db
from .metric import Metric


class QueryComparisonPairs(Metric):
    DESCRIPTION = "Query the comparison pairs for the image viewer."
    PAIR_COLUMNS = ["match", "user", "source", "split", "epoch", "batch_nb", "comparison_score"]
    ITEM_COLUMNS = [
        "uuid",
        "image_id",
        "s3_url",
        "label_id",
        "category",
        "x",
        "y",
        "radius",
        "geohash",
        "overlap_by_circle",
        "overlap_by_center",
    ]

    def __init__(
        self,
        name: str = "Query Comparison Pairs",
        negative_pred_threshold: float = 0.2,
        positive_pred_threshold: float = 0.8,
        ascending: bool = True,
    ) -> None:
        super().__init__(
            name=name, negative_pred_threshold=negative_pred_threshold, positive_pred_threshold=positive_pred_threshold
        )
        self._is_ascending = ascending

    def __call__(self, session: comparison_db.Session) -> pd.DataFrame:
        (
            Item1,
            Item2,
            comparison_pair_filters,
            comparison_item_filters1,
            comparison_item_filters2,
        ) = self.create_initial_filters()

        query = (
            session.query(
                *self.get_labeled_columns(model=comparison_db.ComparisonPair, columns=self.PAIR_COLUMNS, suffix=None),
                *self.get_labeled_columns(model=Item1, columns=self.ITEM_COLUMNS, suffix=1),
                *self.get_labeled_columns(model=Item2, columns=self.ITEM_COLUMNS, suffix=2),
            )
            .select_from(comparison_db.ComparisonPair)
            .join(Item1, Item1.id == comparison_db.ComparisonPair.item1_id)
            .join(Item2, Item2.id == comparison_db.ComparisonPair.item2_id)
            .filter(*comparison_pair_filters)
            .filter(or_(and_(*comparison_item_filters1), and_(*comparison_item_filters2)))
        )

        if self._is_ascending:
            query = query.order_by(comparison_db.ComparisonPair.comparison_score.asc())
        else:
            query = query.order_by(comparison_db.ComparisonPair.comparison_score.desc())

        return pd.read_sql_query(query.statement, session.bind)

    def get_labeled_columns(
        self,
        model: Union[comparison_db.ComparisonPair, comparison_db.ComparisonItem],
        columns: List[str],
        suffix: Optional[int],
    ):
        if suffix is not None:
            return [getattr(model, col).label(f"{col}{suffix}") for col in columns]
        else:
            return [getattr(model, col).label(f"{col}") for col in columns]
