import pandas as pd
from sqlalchemy import and_, or_

from . import comparison_db
from .metric import Metric


class Precision(Metric):
    DESCRIPTION = "TP / (TP + FP)"

    def __init__(
        self, name: str = "Precision", negative_pred_threshold: float = 0.2, positive_pred_threshold: float = 0.8
    ) -> None:
        super().__init__(
            name=name, negative_pred_threshold=negative_pred_threshold, positive_pred_threshold=positive_pred_threshold
        )

    def __call__(self, session: comparison_db.Session) -> pd.DataFrame:
        (
            Item1,
            Item2,
            comparison_pair_filters,
            comparison_item_filters1,
            comparison_item_filters2,
        ) = self.create_initial_filters()

        numerator_filters = and_(
            (comparison_db.ComparisonPair.match == 1),
            (comparison_db.ComparisonPair.comparison_score >= self._positive_pred_threshold),
        )  # TP

        denominator_filters = or_(
            and_(
                comparison_db.ComparisonPair.match == 1,
                comparison_db.ComparisonPair.comparison_score >= self._positive_pred_threshold,
            ),
            and_(
                comparison_db.ComparisonPair.match == 0,
                comparison_db.ComparisonPair.comparison_score >= self._positive_pred_threshold,
            ),
        )  # TP + FP

        return self.query_points(
            session=session,
            Item1=Item1,
            Item2=Item2,
            comparison_pair_filters=comparison_pair_filters,
            comparison_item_filters1=comparison_item_filters1,
            comparison_item_filters2=comparison_item_filters2,
            numerator_filters=numerator_filters,
            denominator_filters=denominator_filters,
        )
