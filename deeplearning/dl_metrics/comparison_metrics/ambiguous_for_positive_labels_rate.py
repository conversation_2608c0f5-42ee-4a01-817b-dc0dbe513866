import pandas as pd
from sqlalchemy import and_

from . import comparison_db
from .metric import Metric


class AmbiguousPositiveLabelsRate(Metric):
    DESCRIPTION = "Number of (positive labels & ambiguous predictions) / Number of positive labels"

    def __init__(
        self,
        name: str = "Ambiguous Positive Labels Rate",
        negative_pred_threshold: float = 0.2,
        positive_pred_threshold: float = 0.8,
    ) -> None:
        super().__init__(
            name=name, negative_pred_threshold=negative_pred_threshold, positive_pred_threshold=positive_pred_threshold
        )

    def __call__(self, session: comparison_db.Session) -> pd.DataFrame:
        (
            Item1,
            Item2,
            comparison_pair_filters,
            comparison_item_filters1,
            comparison_item_filters2,
        ) = self.create_initial_filters()

        numerator_filters = and_(
            comparison_db.ComparisonPair.match == 1,
            comparison_db.ComparisonPair.comparison_score >= self._negative_pred_threshold,
            comparison_db.ComparisonPair.comparison_score <= self._positive_pred_threshold,
        )

        denominator_filters = comparison_db.ComparisonPair.match == 1

        return self.query_points(
            session=session,
            Item1=Item1,
            Item2=Item2,
            comparison_pair_filters=comparison_pair_filters,
            comparison_item_filters1=comparison_item_filters1,
            comparison_item_filters2=comparison_item_filters2,
            numerator_filters=numerator_filters,
            denominator_filters=denominator_filters,
        )
