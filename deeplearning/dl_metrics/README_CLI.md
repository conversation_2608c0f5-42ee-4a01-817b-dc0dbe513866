# DL Metrics CLI

A command-line interface for running deep learning metrics on points_db files and comparing metrics across multiple databases.

## Installation

The CLI is part of the dl_metrics package. Make sure you have the required dependencies installed:

```bash
pip install pandas sqlalchemy alembic
```

## Usage

### Basic Commands

#### List Available Metrics

```bash
python -m dl_metrics.cli list
```

This will show all available metrics with their descriptions.

#### Run a Metric

```bash
# Single database
python -m dl_metrics.cli run "Weeds detected" --db-paths data.db

# Multiple databases (automatic comparison)
python -m dl_metrics.cli run "Weeds detected" --db-paths data1.db data2.db data3.db
```

## Command Reference

### `list` Command

Lists all available metrics with descriptions.

```bash
python -m dl_metrics.cli list
```

### `run` Command

Runs a metric on one or more databases. Automatically detects whether to run a single metric or compare across multiple databases.

**Required Arguments:**
- `metric_name`: Name of the metric to run
- `--db-paths`: Paths to the points_db files (one or more)

**Optional Arguments:**
- `--output-format`: Output format (csv or json, default: csv)
- `--output-file`: Output file path (default: stdout)
- `--use-overlapping-only`: Only use overlapping images across databases (default: True, only applies to multiple databases)
- `--use-all-images`: Use all images from each database (overrides --use-overlapping-only, only applies to multiple databases)

**Examples:**

```bash
# Single database - basic usage
python -m dl_metrics.cli run "Weeds detected" --db-paths data.db

# Single database - save to file
python -m dl_metrics.cli run "Weeds detected" --db-paths data.db --output-file results.csv

# Single database - JSON output
python -m dl_metrics.cli run "Weeds detected" --db-paths data.db --output-format json

# Multiple databases - automatic comparison
python -m dl_metrics.cli run "Weeds detected" --db-paths model1.db model2.db model3.db

# Multiple databases - using all images (not just overlapping)
python -m dl_metrics.cli run "Weeds detected" --db-paths model1.db model2.db --use-all-images

# Multiple databases - save comparison results
python -m dl_metrics.cli run "Weeds detected" --db-paths data1.db data2.db --output-file comparison.csv
```

## Filtering and Grouping Options

The `run` command supports comprehensive filtering and grouping options:

### Metric Parameters

- `--crop-protection-radius-mm`: Crop protection radius in mm
- `--distance-threshold-mm`: Distance threshold in mm
- `--wpt`: Weed point threshold
- `--cpt`: Crop point threshold
- `--plant-threshold`: Plant threshold

### Filter Options

- `--date-range`: Date range in format 'YYYY-MM-DD,YYYY-MM-DD'
- `--robot-ids`: Comma-separated list of robot IDs
- `--crops`: Comma-separated list of crop IDs
- `--session-names`: Comma-separated list of session names
- `--geohash-prefixes`: Comma-separated list of geohash prefixes
- `--filepaths-to-exclude`: Comma-separated list of filepaths to exclude
- `--filepaths-to-include`: Comma-separated list of filepaths to include
- `--point-ids`: Comma-separated list of point IDs
- `--point-uuids`: Comma-separated list of point UUIDs
- `--point-size`: Point size range in format 'min,max' (mm)
- `--embedding-bucket`: Comma-separated list of embedding bucket IDs

### Group By Options

- `--group-by-date`: Group results by date
- `--group-by-size`: Group results by size
- `--group-by-filepath`: Group results by filepath
- `--group-by-weed-density`: Group results by weed density
- `--group-by-crop-count`: Group results by crop count
- `--group-by-robot-id`: Group results by robot ID
- `--group-by-category-class-name`: Group results by category class name
- `--group-by-crop-id`: Group results by crop ID
- `--group-by-geohash`: Group results by geohash
- `--group-by-image-id`: Group results by image ID
- `--group-by-y-position`: Group results by Y position
- `--group-by-point-id`: Group results by point ID
- `--group-by-uuid`: Group results by UUID
- `--group-by-geohash-prefix`: Group results by geohash prefix length
- `--group-by-embedding-bucket`: Comma-separated list of embedding bucket IDs for grouping

## Advanced Examples

### Single Database with Filtering

```bash
python -m dl_metrics.cli run "Weeds detected" \
  --db-paths data.db \
  --date-range "2023-06-01,2023-06-30" \
  --robot-ids "robot1,robot2" \
  --crops "corn,soybeans" \
  --group-by-date \
  --output-file filtered_results.csv
```

### Multiple Database Comparison with Filtering

```bash
python -m dl_metrics.cli run "Weeds detected" \
  --db-paths model1.db model2.db \
  --date-range "2023-06-01,2023-06-30" \
  --robot-ids "robot1,robot2" \
  --crops "corn,soybeans" \
  --group-by-date \
  --output-file filtered_comparison.csv
```

### Grouped Analysis

```bash
python -m dl_metrics.cli run "Weeds detected" \
  --db-paths data.db \
  --group-by-date \
  --group-by-robot-id \
  --group-by-crop-id \
  --output-file grouped_results.csv
```

### Complex Filtering

```bash
python -m dl_metrics.cli run "Crops detected" \
  --db-paths data.db \
  --date-range "2023-05-01,2023-05-31" \
  --robot-ids "robot1,robot2,robot3" \
  --crops "corn" \
  --point-size "5.0,50.0" \
  --filepaths-to-exclude "bad_images/001.jpg,bad_images/002.jpg" \
  --output-file filtered_crops.csv
```

## Output Formats

### CSV Output

The default output format is CSV, which is suitable for further analysis in spreadsheet applications or pandas.

### JSON Output

JSON output is useful for programmatic processing or when you need structured data:

```bash
python -m dl_metrics.cli run "Weeds detected" --db-paths data.db --output-format json
```

## Automatic Behavior

The CLI automatically detects your intent based on the number of database files provided:

### Single Database
- Runs the metric on the single database
- Outputs results directly
- No overlapping image detection needed

### Multiple Databases
- Automatically performs comparison across databases
- Finds overlapping images (unless `--use-all-images` is specified)
- Provides statistical comparison and difference analysis
- Outputs combined results with database path identifiers

## Comparison Features

When using multiple databases, the CLI provides:

1. **Overlapping Image Detection**: Automatically finds images that exist in all databases
2. **Statistical Comparison**: Calculates overall metrics and shows best/worst performing databases
3. **Difference Analysis**: Shows percentage differences between databases
4. **Combined Results**: Outputs all results with a database path identifier column

## Error Handling

The CLI provides informative error messages for common issues:

- Missing database files
- Invalid metric names
- No overlapping images found
- Database connection errors
- Invalid parameter values

## Tips

1. **Use descriptive file paths** when comparing to make results easier to interpret
2. **Check overlapping images** before running comparisons to ensure meaningful results
3. **Use filters** to focus on specific subsets of data
4. **Group results** to get more detailed insights
5. **Save outputs** to files for further analysis

## Troubleshooting

### No Overlapping Images Found

If you get a "No overlapping images found" error, try:

1. Check that the databases contain the same images
2. Use `--use-all-images` to compare without requiring overlap
3. Verify the database files are valid and accessible

### Metric Not Found

If you get a "Metric not found" error:

1. Use `python -m dl_metrics.cli list` to see available metrics
2. Check the exact spelling and case of the metric name
3. Make sure you're using the full metric name as shown in the list

### Database Connection Errors

If you get database connection errors:

1. Verify the database file paths are correct
2. Check that the database files are not corrupted
3. Ensure you have read permissions for the database files 