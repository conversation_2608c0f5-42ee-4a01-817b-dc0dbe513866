from typing import Any, <PERSON><PERSON>

import torch

from ..dataset_types import DatapointRole, Episode
from ..encoders import BaseEncoder
from .base import BaseModel


class KNNClassifier(BaseModel):
    def __init__(
        self,
        encoder: BaseEncoder,
        k: int = 1,
        distance_metric: str = "euclidean",
    ):
        super().__init__()

        self._encoder = encoder
        self.metric = distance_metric
        self.k = k

    @property
    def encoder(self) -> BaseEncoder:
        return self._encoder

    def cosine_cdist(self, x1: torch.Tensor, x2: torch.Tensor) -> Any:
        x1_norm = x1 / x1.norm(dim=1, keepdim=True)
        x2_norm = x2 / x2.norm(dim=1, keepdim=True)
        return x1_norm @ x2_norm.t()

    # flake8: noqa: C901
    def forward(self, episode: Episode) -> torch.Tensor:
        embeddings = self._encoder(episode.x)

        class_to_num = {class_name: i for i, class_name in enumerate(episode.class_names)}

        classes = []
        support_indices = []
        support_datapoints = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.SUPPORT:
                support_indices.append(index)
                classes.append(class_to_num[datapoint.label])
                support_datapoints.append(datapoint)

        support_embeddings = embeddings[support_indices]
        support_classes = torch.tensor(classes).to(embeddings.device)
        class_to_indices = {i: torch.where(support_classes == i)[0] for i in range(len(episode.class_names))}

        if len(support_embeddings.shape) == 1:
            support_embeddings = support_embeddings.unsqueeze(0)

        query_indices = []
        for index, datapoint in enumerate(episode.datapoints):
            if datapoint.role == DatapointRole.QUERY:
                query_indices.append(index)

        query_set = embeddings[query_indices]

        if self.metric == "cosine":
            distances = -self.cosine_cdist(support_embeddings, query_set)
        elif self.metric == "manhattan":
            distances = torch.cdist(support_embeddings, query_set, p=1)
        elif self.metric == "dot-product":
            distances = torch.mm(support_embeddings, query_set.T)
        elif self.metric == "euclidean":
            distances = torch.cdist(support_embeddings, query_set, p=2)
        else:
            raise RuntimeError(f"Unknown metric {self.metric}")

        if self.k == 1:
            logits = torch.zeros(len(episode.class_names), query_set.shape[0]).to(embeddings.device)
            for cls in range(len(episode.class_names)):
                mins = torch.min(distances[class_to_indices[cls]], dim=0)

                logits[cls, :] = -mins.values

            return logits.t()

        topk = torch.topk(distances, self.k, dim=0, largest=False)
        topk_indices = topk.indices
        topk_values = topk.values

        class_vectors = support_classes[topk_indices]
        logits = torch.zeros(len(episode.class_names), query_set.shape[0]).to(embeddings.device)

        for i in range(class_vectors.shape[0]):
            for j in range(class_vectors.shape[1]):
                if self.metric == "cosine":
                    logits[class_vectors[i, j], j] -= topk_values[i, j]
                else:
                    logits[class_vectors[i, j], j] += 1 / (topk_values[i, j])

        return logits.t()
