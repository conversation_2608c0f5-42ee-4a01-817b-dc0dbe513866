from typing import Optional, <PERSON><PERSON>

import torch
from torch import nn
from torchvision.transforms.functional import center_crop

from deeplearning.comparison.models.utils import ComparisonEmbeddingOutput, ComparisonModelBase, ComparisonModelOutput
from deeplearning.self_supervised.encoders import BaseEncoder

EPS = 1e-5


class selfsup_comparison_model(ComparisonModelBase):
    def __init__(
        self,
        pretrained_model: Optional[str] = None,
        encoder: Optional[BaseEncoder] = None,
        input_shape: Optional[Tuple[int, int]] = None,
    ) -> None:
        super().__init__()
        self._encoder = nn.SyncBatchNorm.convert_sync_batchnorm(encoder) if encoder is not None else encoder
        self.cosine_sim = nn.CosineSimilarity(dim=1)
        self.version = 0
        self._input_shape = input_shape

    def forward(self, image: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        assert self._input_shape is not None

        image = center_crop(image, self._input_shape)

        image_reduced: torch.Tensor = self._encoder(image)
        return ComparisonEmbeddingOutput(version=self.version, out=image_reduced).pack()

    def forward_similarity(
        self, embedding_one: torch.Tensor, embedding_two: torch.Tensor
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        assert (
            embedding_one.shape == embedding_two.shape
        ), f"Expected two embeddings with the same shape, got: {embedding_one.shape} and {embedding_two.shape}"
        cosine_sim = self.cosine_sim(embedding_one, embedding_two)

        output = ComparisonModelOutput(version=self.version, out=cosine_sim,)
        return output.pack()
