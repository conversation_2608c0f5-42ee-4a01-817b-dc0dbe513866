from typing import Any

import torch

from ..encoders.base import BaseEncoder


class BaseModel(torch.nn.Module):
    def __init__(self) -> None:
        super().__init__()

    def forward(self, *args: torch.Tensor) -> torch.Tensor:
        raise NotImplementedError()

    @property
    def encoder(self) -> BaseEncoder:
        raise NotImplementedError()

    def set_seed(self, seed: int) -> None:
        raise NotImplementedError()

    def get_image_sample(self, *args: torch.Tensor) -> Any:
        raise NotImplementedError()

    def load_weights(self, path: str) -> None:
        state_dict = torch.load(path)

        state_dict = {key[7:] if key.startswith("module.") else key: value for key, value in state_dict.items()}

        self.load_state_dict(state_dict)
