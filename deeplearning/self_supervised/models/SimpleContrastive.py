from typing import Any

import numpy as np
import torch
import wandb

from ..losses import quadruplet_loss
from ..models.base import BaseEncoder
from ..utilities import IMAGENET_MEAN, IMAGENET_STD, create_text_image
from .base import BaseModel

COMPATIBLE_LOSSES = ["quadruplet", "triplet", "hard-triplet"]


class SimpleContrastiveModel(BaseModel):
    def __init__(
        self,
        encoder: BaseEncoder,
        loss_fn: str = "quadruplet",
        distance: str = "euclidean",
        margin: float = 0.5,
        use_projection: bool = False,
    ):
        super().__init__()

        self._encoder = torch.nn.SyncBatchNorm.convert_sync_batchnorm(encoder)
        self._loss_fn_name = loss_fn
        self._distance = distance
        self._margin = margin
        self._use_projection = use_projection

        if distance not in ["euclidean", "cosine"]:
            raise ValueError(f"Distance function must be either 'euclidean' or 'cosine'. Got: {distance}")

        if loss_fn not in COMPATIBLE_LOSSES:
            raise ValueError(f"Unknown loss function: {loss_fn}")

        if self._use_projection:
            self._projection = torch.nn.Sequential(
                torch.nn.Linear(encoder.embedding_dim, 4096),
                torch.nn.ReLU(inplace=True),
                torch.nn.Linear(4096, 4096),
                torch.nn.ReLU(inplace=True),
                torch.nn.Linear(4096, 4096),
            )

    @property
    def encoder(self) -> Any:
        return self._encoder

    def get_image_sample(self, *args: torch.Tensor) -> Any:
        assert len(args) == 4, f"Expected 4 inputs, got {len(args)}"
        x1, x2, x1_t, x2_t = args
        batch_idx = np.random.randint(0, x1.shape[0])

        if len(x1.shape) == 4:
            x1, x2, x1_t, x2_t = x1[batch_idx], x2[batch_idx], x1_t[batch_idx], x2_t[batch_idx]

        try:
            rank = torch.distributed.get_rank()
        except Exception:
            rank = 0

        x1 = x1.unsqueeze(0).to(f"cuda:{rank}")
        x2 = x2.unsqueeze(0).to(f"cuda:{rank}")
        x1_t = x1_t.unsqueeze(0).to(f"cuda:{rank}")
        x2_t = x2_t.unsqueeze(0).to(f"cuda:{rank}")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)

        emb_1_t = self._encoder(x1_t)
        emb_2_t = self._encoder(x2_t)

        imagenet_mean = torch.tensor(IMAGENET_MEAN).reshape(-1, 1, 1).to(f"cuda:{rank}")
        imagenet_std = torch.tensor(IMAGENET_STD).reshape(-1, 1, 1).to(f"cuda:{rank}")

        out = [
            wandb.Image((x1[0] * imagenet_std) + imagenet_mean, caption="Sample 1"),
            wandb.Image((x2[0] * imagenet_std) + imagenet_mean, caption="Sample 2"),
            wandb.Image((x1_t[0] * imagenet_std) + imagenet_mean, caption="Sample 1 Transformed"),
            wandb.Image((x2_t[0] * imagenet_std) + imagenet_mean, caption="Sample 2 Transformed"),
        ]

        # NOTE: These are computed without the projection
        s1_s2 = (emb_1 @ emb_2.T) / (emb_1.norm() * emb_2.norm())
        s1_t_s2_t = (emb_1_t @ emb_2_t.T) / (emb_1_t.norm() * emb_2_t.norm())
        s1_s1_t = (emb_1 @ emb_1_t.T) / (emb_1.norm() * emb_1_t.norm())
        s2_s2_t = (emb_2 @ emb_2_t.T) / (emb_2.norm() * emb_2_t.norm())

        if self._use_projection:
            emb_1 = self._projection(emb_1)
            emb_2 = self._projection(emb_2)

            emb_1_t = self._projection(emb_1_t)
            emb_2_t = self._projection(emb_2_t)

        if self._loss_fn_name == "quadruplet":
            if self._use_projection:
                emb_2_t = self._projection(emb_2_t)

            loss = quadruplet_loss(emb_1, emb_2, emb_1_t, emb_2_t, margin=self._margin, distance=self._distance)
        elif self._loss_fn_name == "triplet":
            distance_function = (
                lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
                if self._distance == "cosine"
                else torch.nn.PairwiseDistance()(x, y)
            )
            loss = torch.nn.functional.triplet_margin_with_distance_loss(
                emb_1, emb_1_t, emb_2, margin=self._margin, distance_function=distance_function
            )
        el

        caption = f"d(S1, S2): {s1_s2.item():.4f}\nd(S1_t, S2_t): {s1_t_s2_t.item():.4f}\nd(S1, S1_t): {s1_s1_t.item():.4f}\nd(S2, S2_t): {s2_s2_t.item():.4f}\nLoss: {loss.item():.4f}"

        out.append(wandb.Image(create_text_image(caption), caption="Cosine Similarity"))
        return out

    def forward(self, *args: torch.Tensor) -> torch.Tensor:
        assert len(args) == 4, f"Expected 4 inputs, got {len(args)}"
        x1, x2, x1_t, x2_t = args

        rank = torch.distributed.get_rank()
        x1 = x1.to(f"cuda:{rank}")
        x2 = x2.to(f"cuda:{rank}")
        x1_t = x1_t.to(f"cuda:{rank}")

        emb_1 = self._encoder(x1)
        emb_2 = self._encoder(x2)

        emb_1_t = self._encoder(x1_t)

        if self._use_projection:
            emb_1 = self._projection(emb_1)
            emb_2 = self._projection(emb_2)

            emb_1_t = self._projection(emb_1_t)

        if self._loss_fn_name == "quadruplet":
            x2_t = x2_t.to(f"cuda:{rank}")
            emb_2_t = self._encoder(x2_t)
            if self._use_projection:
                emb_2_t = self._projection(emb_2_t)

            loss = quadruplet_loss(emb_1, emb_2, emb_1_t, emb_2_t, margin=self._margin, distance=self._distance)
        elif self._loss_fn_name == "triplet":
            distance_function = (
                lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
                if self._distance == "cosine"
                else torch.nn.PairwiseDistance()(x, y)
            )
            loss = torch.nn.functional.triplet_margin_with_distance_loss(
                emb_1, emb_1_t, emb_2, margin=self._margin, distance_function=distance_function
            )
        elif self._loss_fn_name == "hard-triplet":
            # Hard triplet loss: for each anchor in emb_1, find the hardest negative from emb_2
            # and use emb_1_t as the positive. We ignore x2_t/emb_2_t.

            # Compute distance function
            if self._distance == "cosine":
                # For cosine distance, we want the hardest negative to be the one with highest similarity (lowest distance)
                # Compute cosine similarity matrix between emb_1 and emb_2
                emb_1_norm = emb_1 / emb_1.norm(dim=1, keepdim=True)
                emb_2_norm = emb_2 / emb_2.norm(dim=1, keepdim=True)
                similarity_matrix = emb_1_norm @ emb_2_norm.t()  # [batch_size, batch_size]

                # Find hardest negatives (highest similarity/lowest distance)
                hard_negative_indices = torch.argmax(similarity_matrix, dim=1)
                hard_negatives = emb_2[hard_negative_indices]

                # Use cosine distance function for triplet loss
                distance_function = lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
            else:
                # For euclidean distance, hardest negative is the one with smallest euclidean distance
                distance_matrix = torch.cdist(emb_1, emb_2, p=2)  # [batch_size, batch_size]

                # Find hardest negatives (smallest distance)
                hard_negative_indices = torch.argmin(distance_matrix, dim=1)
                hard_negatives = emb_2[hard_negative_indices]

                # Use euclidean distance function for triplet loss
                distance_function = torch.nn.PairwiseDistance()

            # Apply triplet margin loss with hard negatives
            # anchor=emb_1, positive=emb_1_t, negative=hard_negatives
            loss = torch.nn.functional.triplet_margin_with_distance_loss(
                emb_1, emb_1_t, hard_negatives, margin=self._margin, distance_function=distance_function
            )

        return loss
