import logging
from typing import Any, Iterator, <PERSON><PERSON>

import numpy as np
import torch

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


# For if we ever want to use custom samplers
class BaseSampler(torch.utils.data.Sampler[int]):
    def __init__(
        self, dataset: torch.utils.data.Dataset[Any], num_samples: int, seed: int = 1000, mode: str = "train"
    ) -> None:
        super().__init__()
        self._dataset = dataset
        self._num_samples = num_samples
        self._rng = np.random.RandomState(seed)

    @property
    def num_samples(self) -> int:
        return self._num_samples

    def __len__(self) -> int:
        return self._num_samples

    def __iter__(self) -> Iterator[int]:
        for _ in range(self._num_samples):
            yield self._rng.randint(0, len(self._dataset))  # type: ignore


class DoubleSampler(torch.utils.data.Sampler[Tuple[int, int]]):
    def __init__(
        self, dataset: torch.utils.data.Dataset[Any], num_samples: int, seed: int = 1000, mode: str = "train"
    ) -> None:
        super().__init__()
        self._dataset = dataset
        self._num_samples = num_samples
        self._rng = np.random.RandomState(seed)
        self._mode = mode

        assert self._mode in ["train", "validation"], f"Invalid mode {self._mode}"

        self._num_items = len(self._dataset)  # type: ignore
        max_len = self._num_items // 2

        if self._num_samples > max_len:
            LOG.warning(f"Num samples for validation dataset is larger than half of dataset size, setting to {max_len}")
            self._num_samples = max_len

    @property
    def num_samples(self) -> int:
        return self._num_samples

    def __len__(self) -> int:
        return self._num_samples

    def __iter__(self) -> Iterator[Tuple[int, int]]:
        for i in range(self._num_samples):
            if self._mode == "train":
                i1 = self._rng.randint(0, self._num_items)
                i2 = self._rng.randint(0, self._num_items)
                while i2 == i1:
                    i2 = self._rng.randint(0, self._num_items)
            elif self._mode == "validation":
                i1 = i
                i2 = i + self._num_items // 2
            yield (i1, i2)
