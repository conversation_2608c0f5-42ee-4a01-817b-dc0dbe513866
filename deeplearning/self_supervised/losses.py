import torch


def cosine_cdist(x1: torch.Tensor, x2: torch.Tensor) -> torch.Tensor:
    x1_norm = x1 / x1.norm(dim=1, keepdim=True)
    x2_norm = x2 / x2.norm(dim=1, keepdim=True)
    out: torch.Tensor = x1_norm @ x2_norm.t()
    return -out


def quadruplet_loss(
    emb_1: torch.Tensor,
    emb_2: torch.Tensor,
    emb_1_t: torch.Tensor,
    emb_2_t: torch.Tensor,
    margin: float = 0.1,
    distance: str = "cosine",
) -> torch.Tensor:
    distance_fn = (
        lambda x, y: (1.0 - torch.nn.functional.cosine_similarity(x, y, dim=1))
        if distance == "cosine"
        else torch.nn.PairwiseDistance()(x, y)
    )
    emb1_pos = distance_fn(emb_1, emb_1_t)
    emb2_pos = distance_fn(emb_2, emb_2_t)

    emb1_neg = distance_fn(emb_1_t, emb_2_t)
    emb2_neg = distance_fn(emb_2_t, emb_1_t)

    l1 = torch.nn.ReLU()(emb1_pos - emb1_neg + margin)
    l2 = torch.nn.ReLU()(emb2_pos - emb2_neg + margin)
    loss: torch.Tensor = l1.mean() + l2.mean()
    return loss
