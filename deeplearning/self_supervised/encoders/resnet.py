import torch
import torchvision

from .base import BaseEncoder


class ResNet(BaseEncoder):
    def __init__(
        self,
        architecture: str = "resnet18",
        pretrained: bool = True,
        final_relu: bool = True,
        embedding_dim: int = 1024,
    ) -> None:
        super().__init__()

        self._architecture = architecture
        self._pretrained = pretrained
        self._final_relu = final_relu
        self._embedding_dim = embedding_dim

        if architecture == "resnet18":
            model = torchvision.models.resnet18
            if pretrained:
                self.model = model(weights=torchvision.models.ResNet18_Weights.IMAGENET1K_V1)
            else:
                self.model = model()
        elif architecture == "resnet50":
            model = torchvision.models.resnet50
            if pretrained:
                self.model = model(weights=torchvision.models.ResNet50_Weights.IMAGENET1K_V1)
            else:
                self.model = model()
        elif architecture == "resnet152":
            model = torchvision.models.resnet152
            if pretrained:
                self.model = model(weights=torchvision.models.ResNet152_Weights.IMAGENET1K_V1)
            else:
                self.model = model()

        else:
            raise ValueError(f"Unknown architecture: {architecture}")

        self.downsample = torch.nn.Linear(self.model.fc.in_features, self._embedding_dim)

    def load_weights(self, path: str) -> None:
        state_dict = torch.load(path)

        state_dict = {key[7:] if key.startswith("module.") else key: value for key, value in state_dict.items()}

        self.model.load_state_dict(state_dict)

    @property
    def embedding_dim(self) -> int:
        return self._embedding_dim

    def basic_block_forward_no_relu(self, x: torch.Tensor, block: torchvision.models.resnet.BasicBlock) -> torch.Tensor:
        identity = x
        out: torch.Tensor
        out = block.conv1(x)
        out = block.bn1(out)
        out = block.relu(out)

        out = block.conv2(out)
        out = block.bn2(out)

        if block.downsample is not None:
            identity = block.downsample(x)

        out += identity

        return out

    def bottleneck_forward_no_relu(self, x: torch.Tensor, block: torchvision.models.resnet.Bottleneck) -> torch.Tensor:

        identity = x
        out: torch.Tensor
        out = block.conv1(x)
        out = block.bn1(out)
        out = block.relu(out)

        out = block.conv2(out)
        out = block.bn2(out)
        out = block.relu(out)

        out = block.conv3(out)
        out = block.bn3(out)

        if block.downsample is not None:
            identity = block.downsample(x)

        out += identity

        return out

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.model.conv1(x)
        x = self.model.bn1(x)
        x = self.model.relu(x)
        x = self.model.maxpool(x)

        x = self.model.layer1(x)
        x = self.model.layer2(x)
        x = self.model.layer3(x)

        if self._final_relu:
            x = self.model.layer4(x)
        else:
            for block_index in range(len(self.model.layer4) - 1):
                block = self.model.layer4[block_index]
                x = block(x)

            if self._architecture == "resnet18":
                x = self.basic_block_forward_no_relu(x, self.model.layer4[-1])
            else:
                x = self.bottleneck_forward_no_relu(x, self.model.layer4[-1])

            if not self._final_relu:
                x = torch.nn.functional.relu(x)

        x = self.model.avgpool(x)
        x = torch.flatten(x, 1)
        x = self.downsample(x)

        return x
