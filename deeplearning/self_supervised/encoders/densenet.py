from typing import Any

import torch
from torch import nn
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.densenet import densenet201

from .base import BaseEncoder


class DenseNet(BaseEncoder):
    def __init__(self, pretrained: bool = True, batch_norm: Any = nn.BatchNorm2d, embedding_dim: int = 1024) -> None:
        assert embedding_dim % 4 == 0
        assert embedding_dim % 32 == 0
        super().__init__()
        self.backbone1 = IntermediateLayerGetter(densenet201(pretrained=pretrained), return_layers={"features": "out"},)
        self._embedding_dim = embedding_dim
        self.convs1 = nn.Sequential(
            nn.Conv2d(1920, 1920, 5, padding=3, stride=2, bias=False, groups=128),
            batch_norm(1920, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1920, embedding_dim, 5, padding=3, stride=2, bias=False, groups=embedding_dim // 32),
            batch_norm(embedding_dim, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(embedding_dim, embedding_dim, 3, padding=1, bias=False, groups=embedding_dim // 4),
        )

        self.cosine_sim = nn.CosineSimilarity(dim=1)

        # Softplus is a smooth approximation of relu
        self.softplus = nn.Softplus(beta=20, threshold=2)

        self.reduction = nn.Sequential(
            batch_norm(embedding_dim, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(embedding_dim, embedding_dim, 5, padding=0, bias=False, groups=512),
        )

    @property
    def embedding_dim(self) -> int:
        return self._embedding_dim

    def forward(self, image: torch.Tensor) -> torch.Tensor:
        assert image.shape[2] == image.shape[3], f"Expected square images with same shape, got: {image.shape}"

        image_bb = self.backbone1(image)["out"]
        image_conved = self.convs1(image_bb)

        image_reduced: torch.Tensor = self.reduction(image_conved)
        return image_reduced.reshape(-1, self._embedding_dim)
