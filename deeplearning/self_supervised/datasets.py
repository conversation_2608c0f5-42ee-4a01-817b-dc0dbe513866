import logging
import traceback
from typing import Any, Callable, List, Tuple

import numpy as np
import pydantic
import torch
from PIL import Image

LOG = logging.getLogger(__name__)


class DatasetArguments(pydantic.BaseModel):
    paths: List[Any]
    transform_fn: Callable[[Any], torch.Tensor]
    load_fn: Callable[[Any], Any]
    transform_list: List[Callable[[Any], Any]]
    num_compose: int = 1


class ChipDataset(torch.utils.data.Dataset[torch.Tensor]):
    def __init__(self, args: DatasetArguments) -> None:
        self._paths = args.paths
        self._load_fn = args.load_fn
        self._transform_fn = args.transform_fn

    def __len__(self) -> int:
        return len(self._paths)

    def __getitem__(self, index: int) -> torch.Tensor:
        image = self._load_fn(self._paths[index])
        image_tensor = self._transform_fn(image)
        return image_tensor


class FourChipDataset(torch.utils.data.Dataset[Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]]):
    def __init__(self, args: DatasetArguments) -> None:
        self._paths = args.paths
        self._load_fn = args.load_fn
        self._transform_fn = args.transform_fn
        self._transform_list = args.transform_list
        self._num_compose = args.num_compose
        assert len(self._transform_list), "FourChipDataset requires transforms"
        assert len(self._transform_list) >= self._num_compose

    def __len__(self) -> int:
        return len(self._paths)

    def __getitem__(self, indexes: Tuple[int, int]) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        i1, i2 = indexes

        im1 = self._load_fn(self._paths[i1])
        im2 = self._load_fn(self._paths[i2])

        try:
            t1 = np.random.choice(self._transform_list, self._num_compose)  # type: ignore
            t2 = np.random.choice(self._transform_list, self._num_compose)  # type: ignore

            im1_t = im1
            im2_t = im2
            for i, t in enumerate(t1):
                im1_t = t(im1_t)
                if i != len(t1) - 1 and type(im1_t) == np.ndarray:
                    im1_t = Image.fromarray(im1_t)

            for i, t in enumerate(t2):
                im2_t = t(im2_t)
                if i != len(t2) - 1 and type(im2_t) == np.ndarray:
                    im2_t = Image.fromarray(im2_t)

            im1 = self._transform_fn(im1)
            im2 = self._transform_fn(im2)

            im1_t = self._transform_fn(im1_t)
            im2_t = self._transform_fn(im2_t)
        except Exception as e:
            LOG.warning(f"i1 metadata: {self._paths[i1]}")
            LOG.warning(f"i2 metadata: {self._paths[i2]}")
            traceback.print_exc()
            raise e

        return im1, im2, im1_t, im2_t
