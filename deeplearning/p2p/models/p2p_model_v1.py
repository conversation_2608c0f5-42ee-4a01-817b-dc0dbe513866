import json
import math
from typing import Any, Dict, Optional, <PERSON><PERSON>

import torch
import torch.nn.functional as F
from torch import nn
from torchvision.models._utils import IntermediateLayerGetter
from torchvision.models.resnet import resnet18, resnet50

from deeplearning.deepweed.model import get_resnet50_fpn
from deeplearning.p2p.config import P2PConfig
from deeplearning.p2p.models.utils import P2PModel, P2PModelOutput
from deeplearning.p2p.version import get_version
from deeplearning.trt_extensions import (
    InstanceWiseConv2dLoop,
    UpsampleAlignLoop,
    UpsamplePadCropLoop,
    UpsampleThreshold,
)

EPS = 1e-5

# TODO: crop 0.1x0.1 inch, classify if good match or not
# TODO: check out torchvision model zoo for 3x3 models: MNASNet?
# TODO: replace resnet50 for diffrent model for backbone?
# TODO: pretrain stage3 separately by doing aligned & random crops?


def extract_new_state_dict(
    pretrain_bb_architecture: str, state_dict: Dict[str, Any], bb2_architecture: str
) -> Tuple[Dict[str, Any], bool]:
    new_state_dict = {}
    strict = True
    for key in state_dict:
        new_key = None
        # Special check to extract proper information from resnet50_fpn backbone
        if pretrain_bb_architecture == "resnet50_fpn" and bb2_architecture == "resnet50":
            strict = False
            if key.startswith("backbone_point.model"):
                new_key = key[len("backbone_point.model.") :]
        else:
            if key.startswith("backbone_point"):
                new_key = key[len("backbone_point.") :]

        if new_key is not None:
            new_state_dict[new_key] = state_dict[key]

    return new_state_dict, strict


class P2PModelV1(P2PModel):
    def __init__(
        self, is_fp16: bool = False, pretrained_deepweed_model: Optional[str] = None, config: P2PConfig = P2PConfig(),
    ) -> None:
        super().__init__()
        self._is_fp16 = is_fp16
        self._eps = EPS
        self._version = get_version()

        pretrained_model_architecture = None
        if pretrained_deepweed_model is not None:
            ckpt = torch.load(pretrained_deepweed_model, map_location=torch.device("cpu"))
            state_dict = ckpt["state_dict"]
            pretrained_model_architecture = json.loads(ckpt["metadata"]).get("backbone_architecture", "resnet50")
            new_state_dict, strict = extract_new_state_dict(
                pretrained_model_architecture, state_dict, config.backbone2_architecture
            )

        self.backbone1 = IntermediateLayerGetter(resnet18(pretrained=True), return_layers={"layer3": "out"})
        self.convs1 = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1, bias=False, groups=256),
            nn.InstanceNorm2d(256, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1, bias=False, groups=256),
            nn.InstanceNorm2d(256, affine=True),
        )
        self.convs1_perspective = nn.Sequential(
            nn.Conv2d(256, 256, 3, padding=1, bias=False, groups=256),
            nn.InstanceNorm2d(256, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, 256, 3, padding=1, bias=False, groups=256),
            nn.InstanceNorm2d(256, affine=True),
        )
        self.post_proc1 = nn.Sequential(
            nn.InstanceNorm2d(1, affine=True),
            nn.Conv2d(1, 1, 3, padding=1, bias=False),
            nn.InstanceNorm2d(1, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1, 1, 3, padding=1),
        )

        if config.backbone2_architecture == "resnet50_fpn":
            self.backbone2 = get_resnet50_fpn(load_weights=True)[0]
        else:
            bb2_backbone = resnet50(pretrained=True, replace_stride_with_dilation=[True, True, True])
            self.backbone2 = IntermediateLayerGetter(bb2_backbone, return_layers={"layer3": "out"})

        if pretrained_deepweed_model is not None:
            self.backbone2.load_state_dict(new_state_dict, strict=strict)
        self.convs2 = nn.Sequential(
            nn.Conv2d(1024, 1024, 3, padding=1, bias=False, groups=1024),
            nn.InstanceNorm2d(1024, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, 3, padding=1, bias=False, groups=1024),
            nn.InstanceNorm2d(1024, affine=True),
        )
        self.convs2_perspective = nn.Sequential(
            nn.Conv2d(1024, 1024, 3, padding=1, bias=False, groups=1024),
            nn.InstanceNorm2d(1024, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1024, 1024, 3, padding=1, bias=False, groups=1024),
            nn.InstanceNorm2d(1024, affine=True),
        )
        self.post_proc2 = nn.Sequential(
            nn.InstanceNorm2d(1, affine=True),
            nn.Conv2d(1, 1, 3, padding=1, bias=False),
            nn.InstanceNorm2d(1, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(1, 1, 3, padding=1),
        )

        self.instance_wise_conv2d = InstanceWiseConv2dLoop()
        self.upsample_pad_crop = UpsamplePadCropLoop()
        self.upsample_align = UpsampleAlignLoop()
        self.upsample_threshold = UpsampleThreshold()

        self.backbone3 = IntermediateLayerGetter(resnet18(pretrained=True), return_layers={"layer2": "out"})
        self.convs3 = nn.Sequential(
            nn.Conv2d(128, 128, 3, padding=1, bias=False, groups=128),
            nn.InstanceNorm2d(128, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1, bias=False, groups=128),
            nn.InstanceNorm2d(128, affine=True),
        )
        self.convs3_perspective = nn.Sequential(
            nn.Conv2d(128, 128, 3, padding=1, bias=False, groups=128),
            nn.InstanceNorm2d(128, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(128, 128, 3, padding=1, bias=False, groups=128),
            nn.InstanceNorm2d(128, affine=True),
        )
        self.post_proc3 = nn.Sequential(
            nn.InstanceNorm2d(1, affine=True),
            nn.Conv2d(1, 16, 3),
            nn.InstanceNorm2d(16, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 16, 3),
            nn.InstanceNorm2d(16, affine=True),
            nn.ReLU(inplace=True),
            nn.Conv2d(16, 1, 3),
            nn.AdaptiveMaxPool2d((1, 1)),
        )

    # TODO(asergeev): this code is awful, but then this model is also not great. Not planning to beautify
    # this until we get sufficient amount of data to make data driven decisions about model improvements.
    def forward(
        self, perspective: torch.Tensor, image: torch.Tensor
    ) -> Tuple[
        torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor
    ]:
        assert perspective.shape[2] == perspective.shape[3], f"Expected square perspective, got: {perspective.shape}"
        perspective_size = perspective.shape[2]
        perspective_half_size = math.ceil(perspective.shape[2] / 2)

        # TODO: interleaved grid (both P and T)?
        perspective_bb = self.backbone1(perspective)["out"]
        perspective_conved = self.convs1_perspective(perspective_bb)

        image_bb = self.backbone1(image)["out"]
        image_conved = self.convs1(image_bb)

        out = self.instance_wise_conv2d(image_conved, perspective_conved, False)
        out = self.post_proc1(out)

        out_reshaped = out.reshape(out.shape[0], 1, -1)
        out_reshaped_softmax = F.softmax(out_reshaped, dim=-1)
        out_softmax1 = out_reshaped_softmax.reshape(out.shape)

        # TODO: optimize this
        second_stage_image_crop_size = perspective_size + 32
        second_stage_image_pad_size = second_stage_image_crop_size // 2
        image_cropped = self.upsample_pad_crop(
            out_softmax1, image, second_stage_image_pad_size, second_stage_image_crop_size
        )

        perspective_bb = self.backbone2(perspective)["out"]
        perspective_conved = self.convs2_perspective(perspective_bb)

        image_cropped_bb = self.backbone2(image_cropped)["out"]
        image_conved = self.convs2(image_cropped_bb)

        out = self.instance_wise_conv2d(image_conved, perspective_conved, False)
        out = self.post_proc2(out)

        out_reshaped = out.reshape(out.shape[0], 1, -1)
        out_reshaped_softmax = F.softmax(out_reshaped, dim=-1)
        out_softmax2 = out_reshaped_softmax.reshape(out.shape)

        # third stage, classify final crop
        third_stage_half_size = (perspective_half_size // 16) * 16
        perspective_cropped = perspective[
            :,
            :,
            int(perspective.shape[2] / 2)
            - third_stage_half_size : int(perspective.shape[2] / 2)
            + third_stage_half_size,
            int(perspective.shape[3] / 2)
            - third_stage_half_size : int(perspective.shape[3] / 2)
            + third_stage_half_size,
        ]
        perspective_cropped_bb = self.backbone3(perspective_cropped)["out"]
        perspective_conved = self.convs3_perspective(perspective_cropped_bb)

        image_cropped = self.upsample_align(
            out_softmax1,
            out_softmax2,
            image,
            perspective,
            second_stage_image_pad_size,
            second_stage_image_crop_size,
            third_stage_half_size,
        )
        image_cropped_bb = self.backbone3(image_cropped)["out"]
        image_conved = self.convs3(image_cropped_bb)

        out = self.instance_wise_conv2d(image_conved, perspective_conved, self._is_fp16)

        out = self.post_proc3(out)
        assert out.shape[-1] == 1 and out.shape[-2] == 1 and out.shape[-3] == 1, f"Wrong shape: {out.shape}"
        match = torch.sigmoid(out).squeeze(-1).squeeze(-1).squeeze(-1)

        out_softmax1_up = self.upsample_threshold(out_softmax1, image.shape, min_threshold=self._eps)
        out_softmax2_up = self.upsample_threshold(
            out_softmax2, F.pad(perspective, [16, 16, 16, 16]).shape, min_threshold=self._eps
        )

        output = P2PModelOutput(
            version=self._version,
            hit_ds=out_softmax1,
            hit=out_softmax1_up,
            offset_ds=out_softmax2,
            offset=out_softmax2_up,
            match=match,
            debug_perspective_cropped=perspective_cropped,
            debug_image_cropped=image_cropped,
        )
        return output.pack()
