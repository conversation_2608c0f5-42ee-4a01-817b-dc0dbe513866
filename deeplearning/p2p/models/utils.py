import abc
import math
from typing import Any, Optional, <PERSON><PERSON>

import torch

from deeplearning.p2p.config import P2PConfig

EPS = 1e-5


class P2PModel(torch.nn.Module):
    def __init__(
        self, is_fp16: bool = False, pretrained_deepweed_model: Optional[str] = None, config: P2PConfig = P2PConfig()
    ) -> None:
        super(P2PModel, self).__init__()
        self.is_fp16 = is_fp16

    @abc.abstractmethod
    def forward(
        self, perspective: torch.Tensor, image: torch.Tensor
    ) -> Tuple[
        torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor
    ]:
        pass


class P2PModelOutput:
    def __init__(
        self,
        version: int,
        hit_ds: torch.Tensor,
        hit: torch.Tensor,
        offset_ds: torch.Tensor,
        offset: torch.Tensor,
        match: torch.Tensor,
        debug_perspective_cropped: torch.Tensor,
        debug_image_cropped: torch.Tensor,
    ):
        self.version = version
        self.hit_ds = hit_ds
        self.hit = hit
        self.offset_ds = offset_ds
        self.offset = offset
        self.match = match
        self.debug_perspective_cropped = debug_perspective_cropped
        self.debug_image_cropped = debug_image_cropped

    def pack(
        self,
    ) -> Tuple[
        torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor
    ]:
        return (
            torch.tensor([self.version], dtype=torch.int32, device=self.hit_ds.device),  # version
            self.hit_ds,
            self.hit,
            self.offset_ds,
            self.offset,
            self.match,
            self.debug_perspective_cropped,
            self.debug_image_cropped,
        )

    def coord(
        self, n: int, hit_threshold: float = EPS, score_threshold: float = 0.5, stage: int = -1
    ) -> Optional[Tuple[int, int]]:
        """Coordinate of the match."""
        coords = (self.hit[n, 0] > hit_threshold).nonzero()
        if not len(coords):
            return None

        y = int(coords[0][0])
        x = int(coords[0][1])
        if stage == 1:
            return (x, y)

        offset_max_nz = (self.offset[n, 0] >= self.offset[n, 0].max()).nonzero()
        offset_y = int(offset_max_nz[0][0] - math.ceil(self.offset.shape[2] / 2))
        offset_x = int(offset_max_nz[0][1] - math.ceil(self.offset.shape[3] / 2))
        y += offset_y
        x += offset_x
        if stage == 2:
            return (x, y)

        keep = self.score(n) > score_threshold
        if not keep:
            return None

        return (x, y)

    def score(self, n: int) -> float:
        """
        Score of the match.
        Note: only use after ensuring coord() is not None.
        """
        return self.match[n].item()

    @property
    # TODO: JIT fails with unknown type torch.device
    # def device(self) -> torch.device:
    def device(self) -> str:
        return str(self.hit_ds.device)


class P2PModelOutputFactory:
    @staticmethod
    def unpack(input: Tuple[Any, ...]) -> P2PModelOutput:
        version, hit_ds, hit, offset_ds, offset, match, debug_perspective_cropped, debug_image_cropped = input
        return P2PModelOutput(
            version, hit_ds, hit, offset_ds, offset, match, debug_perspective_cropped, debug_image_cropped
        )
