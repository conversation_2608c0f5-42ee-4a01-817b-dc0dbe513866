import logging
import os
import pickle
from concurrent import futures
from typing import Any, Callable, Dict, List, Tuple, Union

import grpc

from deeplearning.scripts.utils.elastic_launcher import distributed_elastic_launcher
from generated.deeplearning.scripts.utils.proto import distributed_launch_pb2, distributed_launch_pb2_grpc

LOG = logging.getLogger(__name__)


class DistributedLaunchServer(distributed_launch_pb2_grpc.DistributedLaunchServiceServicer):
    def __init__(self, rdzv_endpoint: str, nodes: int, nproc_per_node: int, env_var_list: List[str]):
        self.sequence_number = 0
        self.command: Union[
            Tuple[Callable[..., Any], Callable[[], Any], Dict[str, str], Any], None
        ] = None  # Single (function, failure_fn, env_vars, args) tuple
        self.rdzv_endpoint = rdzv_endpoint
        self.nodes = nodes
        self.nproc_per_node = nproc_per_node
        self.env_var_list = env_var_list

    def run(self, port: int) -> None:
        self._server = grpc.server(futures.ThreadPoolExecutor(max_workers=2))
        distributed_launch_pb2_grpc.add_DistributedLaunchServiceServicer_to_server(self, self._server)
        self._server.add_insecure_port(f"[::]:{port}")
        self._server.start()
        LOG.info(f"Distributed launch server started on port {port}")

    def set_command(
        self, fn: Callable[..., Any], failure_fn: Callable[[], Any], env_vars: Dict[str, str], *args: Any
    ) -> None:
        """Set the command to be executed by clients, replacing any existing command."""
        self.command = (fn, failure_fn, env_vars, args)
        self.sequence_number += 1

    def run_command(self, fn: Callable[..., Any], failure_fn: Callable[[], Any], *args: Any) -> None:
        """Set the command and run it using elastic_launcher."""
        env_vars = {k: os.environ[k] for k in self.env_var_list}
        self.set_command(fn, failure_fn, env_vars, *args)

        # Run the command using elastic_launcher
        distributed_elastic_launcher(
            self.rdzv_endpoint, self.nodes, self.nproc_per_node, fn, failure_fn, *args,
        )

    def GetCommand(
        self, request: distributed_launch_pb2.GetCommandRequest, context: grpc.ServicerContext
    ) -> distributed_launch_pb2.GetCommandResponse:
        """Implementation of the GetCommand RPC."""
        if self.command is None:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details("No command available")
            return distributed_launch_pb2.GetCommandResponse()

        # Get the command
        func, failure_fn, env_vars, args = self.command

        # Create response
        response = distributed_launch_pb2.GetCommandResponse(
            sequence_number=self.sequence_number,
            pickled_function=pickle.dumps(func),
            pickled_failure_function=pickle.dumps(failure_fn),
            pickled_environment_vars=pickle.dumps(env_vars),
            pickled_arguments=pickle.dumps(args),
        )

        return response
