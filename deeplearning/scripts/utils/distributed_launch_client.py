import argparse
import logging
import os
import pickle
import sys
import time
from dataclasses import dataclass
from typing import Any, Callable, Dict, <PERSON><PERSON>

import grpc

from deeplearning.scripts.utils.elastic_launcher import distributed_elastic_launcher
from generated.deeplearning.scripts.utils.proto import distributed_launch_pb2, distributed_launch_pb2_grpc

LOG = logging.getLogger(__name__)


@dataclass
class Command:
    sequence_number: int
    function: Callable[..., Any]
    failure_function: Callable[[], Any]
    environment_vars: Dict[str, str]
    arguments: Tuple[Any, ...]


class DistributedLaunchClient:
    def __init__(self, server_address: str, rdzv_endpoint: str, nodes: int, nproc_per_node: int):
        """Initialize the client with server address."""
        self.server_address = server_address
        self.channel = grpc.insecure_channel(self.server_address)
        self.stub = distributed_launch_pb2_grpc.DistributedLaunchServiceStub(self.channel)
        self.rdzv_endpoint = rdzv_endpoint
        self.nodes = nodes
        self.nproc_per_node = nproc_per_node
        self.last_sequence = -1

    def wait_for_server(self, timeout_s: int = 900) -> bool:
        """Wait for server to become available with a long timeout."""
        LOG.info(f"Waiting for distributed launch server at {self.server_address} (timeout: {timeout_s} seconds)...")
        start_time = time.time()

        while time.time() - start_time < timeout_s:
            try:
                # Try to get a command
                self.stub.GetCommand(distributed_launch_pb2.GetCommandRequest())
                LOG.info("Distributed launch server is available!")
                return True
            except grpc.RpcError as e:
                if e.code() == grpc.StatusCode.NOT_FOUND:
                    # Server is up but no commands yet - this is fine
                    LOG.info("Distributed launch server is available!")
                    return True
                elif e.code() in (grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.UNKNOWN):
                    # Server is not available yet
                    LOG.debug("Distributed launch server not available yet, retrying in 5 seconds...")
                    time.sleep(5)  # Wait 5 seconds between attempts
                    continue
                else:
                    LOG.error(f"Unexpected error while waiting for distributed launch server: {e}")
                    return False
            except Exception as e:
                LOG.error(f"Error while waiting for distributed launch server: {e}")
                return False

        LOG.error(f"Distributed launch server did not become available within {timeout_s} seconds")
        return False

    def get_command(self) -> Command:
        """Get the command from the server."""
        response = self.stub.GetCommand(distributed_launch_pb2.GetCommandRequest())

        # Unpickle the function and arguments
        function = pickle.loads(response.pickled_function)
        failure_function = pickle.loads(response.pickled_failure_function)
        environment_vars = pickle.loads(response.pickled_environment_vars)
        args = pickle.loads(response.pickled_arguments)

        return Command(
            sequence_number=response.sequence_number,
            function=function,
            failure_function=failure_function,
            environment_vars=environment_vars,
            arguments=args,
        )

    def poll_and_run(self, poll_interval: float = 1.0, max_errors: int = 3) -> None:
        """Poll for new commands and run them using elastic_launcher."""
        consecutive_errors = 0

        while True:
            try:
                command = self.get_command()
                consecutive_errors = 0  # Reset error counter on successful command
                if command.sequence_number > self.last_sequence:
                    self.last_sequence = command.sequence_number

                    # backup environment
                    env_bak = os.environ.copy()

                    # add environment overrides
                    os.environ.update(command.environment_vars)

                    # Run the command using elastic_launcher
                    distributed_elastic_launcher(
                        self.rdzv_endpoint,
                        self.nodes,
                        self.nproc_per_node,
                        command.function,
                        command.failure_function,
                        *command.arguments,
                    )

                    # restore environment
                    os.environ.clear()
                    os.environ.update(env_bak)
                time.sleep(poll_interval)
            except grpc.RpcError as e:
                if e.code() == grpc.StatusCode.NOT_FOUND:
                    LOG.debug("No command available")
                elif e.code() in (grpc.StatusCode.UNAVAILABLE, grpc.StatusCode.UNKNOWN):
                    LOG.debug(f"Distributed launch server is not available: {e}")
                    consecutive_errors += 1
                else:
                    LOG.error(f"RPC failed: {e}")
                    consecutive_errors += 1
                time.sleep(poll_interval)
            except KeyboardInterrupt:
                LOG.info("\nStopping command polling...")
                break
            except Exception as e:
                LOG.error(f"Error during polling: {e}")
                consecutive_errors += 1
                time.sleep(poll_interval)
            finally:
                if consecutive_errors >= max_errors:
                    LOG.error("Too many consecutive errors. Shutting down client...")
                    return

    def close(self) -> None:
        """Close the gRPC channel."""
        self.channel.close()


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Distributed Launch Client")
    parser.add_argument(
        "--server-address", type=str, required=True, help="Address of the distributed launch server (host:port)"
    )
    parser.add_argument(
        "--rdzv-endpoint", type=str, required=True, help="Address of the rendezvous endpoint (host:port)"
    )
    parser.add_argument("--nodes", type=int, required=True, help="Number of nodes to use for distributed training")
    parser.add_argument("--nproc-per-node", type=int, required=True, help="Number of processes per node")
    parser.add_argument("--poll-interval", type=float, default=1.0, help="Polling interval in seconds")
    parser.add_argument("--connection-timeout", type=int, default=900, help="Initial connection timeout in seconds")
    parser.add_argument(
        "--max-errors", type=int, default=3, help="Maximum number of consecutive errors before shutting down"
    )
    return parser.parse_args()


def main() -> None:
    args = parse_args()

    # Configure logging
    logging.basicConfig(
        level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
    )

    client = DistributedLaunchClient(
        server_address=args.server_address,
        rdzv_endpoint=args.rdzv_endpoint,
        nodes=args.nodes,
        nproc_per_node=args.nproc_per_node,
    )
    try:
        # Wait for server with long timeout
        if not client.wait_for_server(timeout_s=args.connection_timeout):
            LOG.error("Failed to connect to server. Exiting...")
            return

        # Start polling once connected
        client.poll_and_run(poll_interval=args.poll_interval, max_errors=args.max_errors)
    finally:
        client.close()
        sys.exit(0)  # Ensure clean exit


if __name__ == "__main__":
    main()
