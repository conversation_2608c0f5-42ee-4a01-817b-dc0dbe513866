import argparse
import logging
import os
import sys


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--tensorrt-version", choices=["8.0.1.6", "10.0.1"], required=True)
    parser.add_argument("--calibration-dataset-id", type=str, default=None)
    args, _ = parser.parse_known_args()

    sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
    sys.path.insert(0, f"/opt/TensorRT-{args.tensorrt_version}/python/site-packages")

    import torch
    from lib.common.veselka.client import VeselkaClient
    from deeplearning.utils.download_utils import download_records
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        get_tensorrt_file_name,
        upload_directory,
        get_tensorrt_version,
        get_compute_capability,
        compute_md5sum,
    )
    from deeplearning.constants import CARBON_DATA_DIR
    from deeplearning.scripts.utils.utils import get_pipeline
    from deeplearning.scripts.fewshot.train_crops import get_datasets, get_model, Config
    from deeplearning.scripts.fewshot.utils import convert_trt

    download_records(item_id=args.model_id)
    veselka_client = VeselkaClient()
    model_info = veselka_client.get_model_info(args.model_id)
    config = Config(**model_info["dl_config"])
    data_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{args.model_id}"

    weights_path = f"{data_dir}/weights/best.pt"
    tensorrt_file_name = get_tensorrt_file_name(convert_int8=True)
    trt_file_path = os.path.join(f"{data_dir}", tensorrt_file_name)

    assert os.path.exists(weights_path), f"Model weights not found at {weights_path}"

    dataset_id = args.calibration_dataset_id
    if not dataset_id:
        dataset_id = config.dataset_id

    assert dataset_id is not None, f"No dataset_id recorded for {args.model_id}"

    fewshot_pipeline = get_pipeline("9c3bb2b5-d672-4790-b49e-5ade7bc067a6")

    train, val, _ = get_datasets(config, pipeline=fewshot_pipeline, fast_run=False)

    model = get_model(
        backbone=config.backbone,
        pretrained=False,
        model_type=config.model_type,
        distance_metric=config.distance_metric,
    )
    state_dict = torch.load(weights_path, weights_only=True)
    state_dict = {key[7:] if key.startswith("module.") else key: value for key, value in state_dict.items()}
    model.load_state_dict(state_dict)

    convert_trt(
        model=model, calib_dataset=train, validation_dataset=val, config=config, trt_path=trt_file_path, num_samples=100
    )

    upload_directory(data_dir)

    veselka_client.post_model_artifact(
        model_id=args.model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{args.model_id}/{tensorrt_file_name}",
        checksum=compute_md5sum(trt_file_path),
    )


if __name__ == "__main__":
    main()
