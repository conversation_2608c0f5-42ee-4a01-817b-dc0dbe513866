import copy
import json
import logging
import os
import random
import time
from typing import Any, Callable, List, Optional, Tuple

import numpy as np
import pydantic
import tensorrt as trt
import torch
import torchvision.transforms as TF
import tqdm
from PIL import Image, ImageDraw
from scipy.ndimage import laplace, sobel
from torch2trt import torch2trt

from deeplearning.constants import CARBON_DATA_DIR
from deeplearning.model_io.metadata import ModelMetadata
from deeplearning.model_io.tensorrt import save_tensorrt_model
from deeplearning.scripts.utils.utils import get_dataset_v2
from deeplearning.self_supervised.datasets import DatasetArguments, FourChipDataset
from deeplearning.self_supervised.encoders import BaseEncoder, DenseNet, ResNet
from deeplearning.self_supervised.models import BaseModel, SimpleContrastiveModel
from deeplearning.self_supervised.samplers import DoubleSampler
from deeplearning.self_supervised.utilities import IMAGENET_MEAN, IMAGENET_STD, crop_transform_fn, default_transform_fn
from lib.common.s3_cache_proxy.client import S3CacheProxyClient

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)

MIN_RADIUS = 1


class Config(pydantic.BaseModel):
    model_id: str
    num_epochs: int = 50
    num_samples: int = 2000
    num_validation_samples: int = 250
    pretrained: bool = True
    num_workers: int = 2
    backbone: str = "resnet50"
    learning_rate: float = 1e-4
    description: Optional[str] = None
    model_type: str = "SimpleContrastive"
    wandb_project: str
    num_gpus: int
    fast_run: bool
    data_dir: str
    loss_fn: str = "triplet"
    distance: str = "cosine"
    use_radius: bool = False
    resize_method: str = "interpolation"  # interpolation or crop
    batch_size: int = 16
    chip_size: int = 400
    embedding_dim: int = 1024
    use_projection: bool = False
    num_compose: int = 1

    # Triplet/Quadruplet loss:
    margin: float = 0.5


def sobel_filter(img: Image) -> Any:
    img = np.array(img).astype(np.float32)
    h = sobel(img, axis=0)
    v = sobel(img, axis=1)
    magnitude = np.sqrt(h ** 2 + v ** 2)
    magnitude *= 255.0 / np.max(magnitude)

    return magnitude


def laplacian(img: Image) -> Any:
    return laplace(img)


def randomerasing(img: Image) -> Any:
    img = TF.ToTensor()(img)
    return TF.RandomErasing(scale=(0.02, 0.4), ratio=(0.3, 3.3))(img)


class RandomErasingKeepCenter:
    def __init__(
        self, scale: Tuple[float, float] = (0.2, 0.4), ratio: Tuple[float, float] = (1.0, 3.3), center_frac: float = 0.3
    ):
        self._scale = scale
        self._ratio = ratio

        self._center_frac = center_frac

    def __call__(self, img: Image) -> Any:
        orig_img = img
        img = copy.deepcopy(img)
        img_w, img_h = img.size

        draw = ImageDraw.Draw(img)

        area = img_w * img_h

        target_area = random.uniform(*self._scale) * area
        aspect_ratio = random.uniform(*self._ratio)

        h = int(round((target_area * aspect_ratio) ** 0.5))
        w = int(round((target_area / aspect_ratio) ** 0.5))

        if h > img_h:
            h = img_h
        if w > img_w:
            w = img_w

        top = random.randint(0, img_h - h)
        left = random.randint(0, img_w - w)
        erase_box = (left, top, left + w, top + h)

        draw.rectangle(erase_box, fill=(0, 0, 0))

        c_h = int(img_h * self._center_frac)
        c_w = int(img_w * self._center_frac)

        center_left = (img_w - c_w) // 2
        center_top = (img_h - c_h) // 2
        center_box = (center_left, center_top, center_left + c_w, center_top + c_h)

        original_crop = orig_img.crop(center_box)

        img.paste(original_crop, center_box)

        return img


def simulate_border(img: Image) -> Any:
    size = np.random.randint(0, img.size[0] // 2)
    img = copy.deepcopy(img)
    imrec = ImageDraw.Draw(img)
    loc = np.random.choice(["top", "bottom", "left", "right"])
    if loc == "top":
        imrec.rectangle((0, 0, img.size[0], size), fill=0)
    elif loc == "right":
        imrec.rectangle((img.size[0] - size, 0, img.size[0], img.size[1]), fill=0)
    elif loc == "left":
        imrec.rectangle((0, 0, size, img.size[1]), fill=0)
    elif loc == "bottom":
        imrec.rectangle((0, img.size[1] - size, img.size[0], img.size[1]), fill=0)

    return img


BASE_TRANSFORMS = [
    TF.GaussianBlur(kernel_size=3, sigma=(0.7, 2.0)),
    TF.ColorJitter(brightness=0.5, contrast=0.5, saturation=0.5, hue=0.5),
    TF.Grayscale(num_output_channels=3),
    TF.RandomHorizontalFlip(p=1),
    TF.RandomVerticalFlip(p=1),
    TF.RandomRotation((15, 45)),
    laplacian,
    RandomErasingKeepCenter(),
    simulate_border,
]

MODEL_DATASET_MAP = {
    "SimpleContrastive": FourChipDataset,
}

MODEL_SAMPLER_MAP = {
    "SimpleContrastive": DoubleSampler,
}


S3_CLIENT = S3CacheProxyClient(s3_cache_proxy_host=os.getenv("S3_CACHE_PROXY_SERVICE_HOST"))


class s3_cache_load_fn:
    def __init__(self, use_radius: bool = True, chip_size: int = 400):
        self.use_radius = use_radius
        self.chip_size = chip_size

    def __call__(self, info: Any) -> Image.Image:
        uri = info["uri"]
        x = info["x"]
        y = info["y"]
        radius = info["radius"] if self.use_radius else self.chip_size // 2
        bucket, key = S3_CLIENT.split_uri(uri)
        return S3_CLIENT._get_image_subset_from_s3_cache_proxy(
            bucket=bucket, key=key, x=x, y=y, radius=radius, fill=True, fill_color="000000"
        )


def get_model(config: Config) -> BaseModel:
    encoder: BaseEncoder
    if config.backbone.lower().startswith("resnet"):
        encoder = ResNet(
            architecture=config.backbone,
            pretrained=config.pretrained,
            final_relu=True,
            embedding_dim=config.embedding_dim,
        )
    elif config.backbone.lower() == "densenet":
        encoder = DenseNet(pretrained=config.pretrained, embedding_dim=config.embedding_dim)
    else:
        raise ValueError(f"Unknown backbone: {config.backbone}")

    if config.model_type.lower() == "SimpleContrastive".lower():
        model = SimpleContrastiveModel(
            encoder=encoder,
            loss_fn=config.loss_fn,
            distance=config.distance,
            margin=config.margin,
            use_projection=config.use_projection,
        )
    else:
        raise ValueError(f"Unknown model type: {config.model_type}")

    return model


def get_transform_fn(config: Config) -> Callable[[Any], torch.Tensor]:
    if config.resize_method == "crop":
        return crop_transform_fn(chip_size=config.chip_size)
    elif config.resize_method == "interpolation":
        return default_transform_fn(chip_size=config.chip_size)
    else:
        raise ValueError(f"Unknown resize method: {config.resize_method}")


def get_datasets(
    config: Config, dataset_id: Optional[str] = None, pipeline_id: Optional[str] = None, exist_ok: bool = False
) -> List[Any]:
    size_limit = 1000 if config.fast_run else None
    LOG.info("Fetching dataset...")
    get_dataset = False
    if exist_ok:
        for split in ["train", "validation", "test"]:
            if not os.path.exists(os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{split}.jsonl")):
                get_dataset = True
                break

    if get_dataset or not exist_ok:
        dataset_id, _ = get_dataset_v2(
            dataset_id,
            pipeline_id,
            train_set_size_limit=size_limit,
            validation_set_size_limit=size_limit,
            test_set_size_limit=size_limit,
        )

    LOG.info(f"Dataset ID: {dataset_id}")
    datasets = []

    for split in ["train", "validation", "test"]:
        LOG.info(f"Processing dataset split: {split}")
        assert split is not None, f"Dataset split {split} does not exist"
        split_json_path = os.path.join(CARBON_DATA_DIR, f"deeplearning/datasets/{dataset_id}/{split}.jsonl")
        assert os.path.exists(split_json_path), f"Dataset split {split_json_path} does not exist"

        data = []

        with open(split_json_path) as f:
            for line in tqdm.tqdm(f):
                image = json.loads(line)

                for point in image["points"]:
                    if point["radius"] > MIN_RADIUS:
                        info = {
                            "uri": image["uri"],
                            "x": point["x"],
                            "y": point["y"],
                            "radius": point["radius"],
                        }

                        data.append(info)
            args = DatasetArguments(
                paths=data,
                transform_fn=get_transform_fn(config),
                load_fn=s3_cache_load_fn(use_radius=config.use_radius, chip_size=config.chip_size),
                transform_list=BASE_TRANSFORMS,
                num_compose=config.num_compose,
            )
            dataset = MODEL_DATASET_MAP[config.model_type](args)

        datasets.append(dataset)

    return datasets


IMAGE_S3_PATH = "s3://maka-pono/media/slayer97/2025-03-06/0c065c8e-2996-4bb1-8500-87bb5850c91e/predict_slayer97_row2_predict2_2025-03-06T20-00-44-606000Z.png"
LETTUCE_COORDS_1 = (519, 963)
LETTUCE_COORDS_2 = (577, 2278)
WEED_COORDS_1 = (2444, 2200)


class DatasetWrapper:
    def __init__(self, dataset: torch.utils.data.Dataset[Any], num_samples: Optional[int] = None) -> None:
        self._dataset = dataset
        self._num_samples = num_samples

    def __getitem__(self, index: int) -> Any:
        return self._dataset[index, index][0].unsqueeze(0).cuda()

    def __len__(self) -> int:
        return len(self._dataset) if not self._num_samples else self._num_samples  # type: ignore


TIMING_WARMUP = 3


def convert_trt(
    encoder: torch.nn.Module,
    calib_dataset: DatasetWrapper,
    validation_dataset: DatasetWrapper,
    config: Config,
    trt_path: str,
    max_batch_size: int = 3,
    calibration_batch_size: int = 16,
    get_metrics: bool = True,
) -> None:

    test_input = [calib_dataset[0].repeat(max_batch_size, 1, 1, 1)]

    encoder.eval().cuda()

    pyt_embeddings = []
    pyt_timing = []
    if get_metrics:
        for i in range(min(20, len(validation_dataset))):
            start = time.time()
            pyt_embeddings.append(encoder(validation_dataset[i].cuda()))
            end = time.time()
            if i > TIMING_WARMUP:  # warmup
                pyt_timing.append(end - start)

        stacked_pyt_embeddings = torch.cat(pyt_embeddings, dim=0)
        stacked_pyt_embeddings /= stacked_pyt_embeddings.norm(dim=1, keepdim=True)

    with torch.no_grad():
        trt_encoder = torch2trt(
            encoder,
            test_input,
            fp16_mode=False,
            int8_mode=False,
            int8_calib_dataset=calib_dataset,
            int8_calib_batch_size=calibration_batch_size,
            max_batch_size=max_batch_size,
            max_workspace_size=1 << 22,
            log_level=trt.Logger.INFO,
            strict_type_constraints=True,
            use_implicit_batch_dimension=False,
        )

    trt_embeddings = []
    trt_timing = []
    if get_metrics:
        for i in range(min(20, len(validation_dataset))):
            start = time.time()
            emb = trt_encoder(validation_dataset[i].cuda())
            trt_embeddings.append(emb[0, :].unsqueeze(0))
            end = time.time()
            if i > TIMING_WARMUP:  # warmup
                trt_timing.append(end - start)
        stacked_trt_embeddings = torch.cat(trt_embeddings, dim=0)
        stacked_trt_embeddings /= stacked_trt_embeddings.norm(dim=1, keepdim=True)

        cos_sims = stacked_pyt_embeddings @ stacked_trt_embeddings.T
        embedding_error = torch.diag(cos_sims, 0)

        print("Embedding error (Cosine similarity): ")
        print(f"\tmean: {embedding_error.mean():.5f}")
        print(f"\tmedian: {torch.median(embedding_error):.5f}")
        print(f"\tmin: {embedding_error.min():.5f}")
        print(f"\tmax: {embedding_error.max():.5f}")
        print(f"\t99%tile: {torch.quantile(embedding_error, 0.99):.5f}")

        print(f"Average pre-cvt time: {1000 * sum(pyt_timing) / len(pyt_timing):.5f} ms")
        print(f"Average post-cvt time: {1000 * sum(trt_timing) / len(trt_timing):.5f} ms")

        load_fn = s3_cache_load_fn(use_radius=config.use_radius, chip_size=config.chip_size)
        transform_fn = get_transform_fn(config)

        lettuce_image_1 = load_fn(
            {"uri": IMAGE_S3_PATH, "x": LETTUCE_COORDS_1[0], "y": LETTUCE_COORDS_1[1], "radius": config.chip_size // 2}
        )
        lettuce1 = transform_fn(lettuce_image_1)
        lettuce1 = lettuce1.unsqueeze(0).cuda()

        lettuce_emb1 = trt_encoder(lettuce1).detach().cpu().numpy()

        lettuce_image_2 = load_fn(
            {"uri": IMAGE_S3_PATH, "x": LETTUCE_COORDS_2[0], "y": LETTUCE_COORDS_2[1], "radius": config.chip_size // 2}
        )
        lettuce2 = transform_fn(lettuce_image_2)
        lettuce2 = lettuce2.unsqueeze(0).cuda()

        lettuce_emb2 = trt_encoder(lettuce2).detach().cpu().numpy()

        weed_image_1 = load_fn(
            {"uri": IMAGE_S3_PATH, "x": WEED_COORDS_1[0], "y": WEED_COORDS_1[1], "radius": config.chip_size // 2}
        )
        weed1 = transform_fn(weed_image_1)
        weed1 = weed1.unsqueeze(0).cuda()

        weed_emb = trt_encoder(weed1).detach().cpu().numpy()

        L_L_dist = lettuce_emb1 @ lettuce_emb2.T / (np.linalg.norm(lettuce_emb1) * np.linalg.norm(lettuce_emb2))
        L_W_dist = lettuce_emb1 @ weed_emb.T / (np.linalg.norm(lettuce_emb1) * np.linalg.norm(weed_emb))

        print(f"D(Lettuce1, Lettuce2): {L_L_dist.item()}")
        print(f"D(Lettuce1, Weed): {L_W_dist.item()}")

    metadata = ModelMetadata(
        input_dtype=torch.float32,
        input_size=test_input[0].shape[2:],
        supports_half=False,
        trained_embeddings=True,
        backbone_architecture=config.backbone,
        model_class=config.model_type,
        max_batch_size=max_batch_size,
        means=IMAGENET_MEAN,
        stds=IMAGENET_STD,
    )

    save_tensorrt_model(trt_encoder, metadata, trt_path)
