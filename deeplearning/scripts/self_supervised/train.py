import argparse
import json
import logging
import time

import torch

from deeplearning.comparison.models.selfsup_model import selfsup_comparison_model
from deeplearning.constants import CARBON_DATA_DIR, S3_BUCKET
from deeplearning.scripts.self_supervised.utils import (
    MODEL_SAMPLER_MAP,
    Config,
    DatasetWrapper,
    convert_trt,
    get_datasets,
    get_model,
)
from deeplearning.scripts.utils.utils import add_common_arguments, generate_model_id, setup_logging
from deeplearning.self_supervised.train import train
from deeplearning.utils.trainer import compute_md5sum, get_compute_capability, get_tensorrt_version, upload_directory
from lib.common.veselka.client import VeselkaClient

setup_logging()

LOG = logging.getLogger(__name__)
LOG.setLevel(logging.INFO)


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    add_common_arguments(parser)
    args = parser.parse_args()

    model_id = args.job_id
    if model_id is None:
        model_id = generate_model_id()

    config_dict = {
        "num_epochs": 1 if args.fast_run else 100,
        "num_samples": 64 if args.fast_run else 10000,
        "num_validation_samples": 2 if args.fast_run else 2000,
        "wandb_project": "selfsup-fast-run" if args.fast_run else "selfsup",
        "num_gpus": args.nproc_per_node,
        "model_id": model_id,
        "description": args.description,
        "fast_run": args.fast_run,
        "data_dir": f"{CARBON_DATA_DIR}/deeplearning/models/{model_id}",
        **args.dl_config,
    }

    config = Config(**config_dict)
    LOG.info(f"Config: \n{json.dumps(config.__dict__, indent=4)}")

    stub_json = {"id": config.model_id}
    VeselkaClient().post_model(stub_json)

    train_dataset, val_dataset, _ = get_datasets(
        config=config, dataset_id=args.dataset_id, pipeline_id=args.pipeline_id, exist_ok=True
    )

    LOG.info("Downloading Model...")
    model = get_model(config)
    LOG.info("Finished downloading model")
    train(model, train_dataset, val_dataset, MODEL_SAMPLER_MAP[config.model_type], **config.model_dump())

    num_trt_samples = 100

    # Convert model for comparison training
    cmp_cvt_model = get_model(config)
    cmp_cvt_model.load_state_dict(torch.load(f"{config.data_dir}/weights/best.pt"))

    cmp_trt_path = f"{config.data_dir}/trt_fp32.trt"
    convert_trt(
        selfsup_comparison_model(encoder=cmp_cvt_model.encoder, input_shape=(config.chip_size, config.chip_size)),
        calib_dataset=DatasetWrapper(train_dataset, num_samples=num_trt_samples),
        validation_dataset=DatasetWrapper(val_dataset, num_samples=num_trt_samples),
        config=config,
        trt_path=cmp_trt_path,
        max_batch_size=24,
        calibration_batch_size=16,
        get_metrics=False,
    )

    model.load_state_dict(torch.load(f"{config.data_dir}/weights/best.pt"))
    trt_file_name = (
        f"trt_fp32_{get_tensorrt_version().replace('.', '')}_{get_compute_capability().replace('.', '')}.trt"
    )
    trt_path = f"{config.data_dir}/{trt_file_name}"

    convert_trt(
        model.encoder,
        calib_dataset=DatasetWrapper(train_dataset, num_samples=num_trt_samples),
        validation_dataset=DatasetWrapper(val_dataset, num_samples=num_trt_samples),
        config=config,
        trt_path=trt_path,
        max_batch_size=3,
        calibration_batch_size=16,
        get_metrics=True,
    )

    model_json = {
        "id": config.model_id,
        "trained_at": int(time.time()),
        "url": f"s3://{S3_BUCKET}/models/{config.model_id}/{trt_file_name}",
        "dl_config": config.model_dump(),
        "tensorrt_version": get_tensorrt_version(),
        "compute_capability": get_compute_capability(),
        "pipeline_id": args.pipeline_id,
        "dataset_id": args.dataset_id,
        "checksum": compute_md5sum(trt_path),
        "fast_run": args.fast_run,
        "description": args.description,
        "type": "selfsup",
    }

    VeselkaClient().post_model(model_json)

    upload_directory(config.data_dir)
