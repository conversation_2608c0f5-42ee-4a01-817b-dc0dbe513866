import argparse
import logging
import os
import sys


def main() -> None:
    logging.basicConfig()
    logging.getLogger().setLevel("INFO")

    parser = argparse.ArgumentParser()
    parser.add_argument("--model-id", required=True)
    parser.add_argument("--tensorrt-version", choices=["8.0.1.6", "10.0.1"], required=True)
    parser.add_argument("--calibration-dataset-id", type=str, required=True)
    args, _ = parser.parse_known_args()

    sys.path = [x for x in sys.path if not x.startswith("/opt/TensorRT")]
    sys.path.insert(0, f"/opt/TensorRT-{args.tensorrt_version}/python/site-packages")

    import torch
    from lib.common.veselka.client import VeselkaClient
    from deeplearning.utils.download_utils import download_records
    from deeplearning.utils.trainer import (
        S3_BUCKET,
        upload_directory,
        get_tensorrt_version,
        get_compute_capability,
        compute_md5sum,
    )
    from deeplearning.constants import CARBON_DATA_DIR
    from deeplearning.scripts.self_supervised.utils import get_datasets, Config, convert_trt, get_model, DatasetWrapper

    download_records(item_id=args.model_id)
    veselka_client = VeselkaClient()
    model_info = veselka_client.get_model_info(args.model_id)
    config = Config(**model_info["dl_config"])
    data_dir = f"{CARBON_DATA_DIR}/deeplearning/models/{args.model_id}"

    weights_path = f"{data_dir}/weights/best.pt"
    tensorrt_file_name = f"{config.data_dir}/trt_fp32_{get_tensorrt_version().replace('.', '')}_{get_compute_capability().replace('.', '')}.trt"
    trt_file_path = os.path.join(f"{data_dir}", tensorrt_file_name)

    assert os.path.exists(weights_path), f"Model weights not found at {weights_path}"

    dataset_id = args.calibration_dataset_id

    assert dataset_id is not None, f"No dataset_id recorded for {args.model_id}"

    prt_pipeline = "ff928c0a-e1d1-4400-ac80-07be02e66cab"

    train, val, _ = get_datasets(config=config, dataset_id=dataset_id, pipeline_id=prt_pipeline, exist_ok=True)

    model = get_model(config)
    state_dict = torch.load(weights_path, weights_only=True)
    model.load_state_dict(state_dict)

    convert_trt(
        encoder=model.encoder,
        calib_dataset=DatasetWrapper(train, num_samples=100),
        validation_dataset=DatasetWrapper(val, num_samples=100),
        config=config,
        trt_path=trt_file_path,
    )

    upload_directory(data_dir)

    veselka_client.post_model_artifact(
        model_id=args.model_id,
        tensorrt_version=get_tensorrt_version(),
        compute_capability=get_compute_capability(),
        url=f"s3://{S3_BUCKET}/models/{args.model_id}/{tensorrt_file_name}",
        checksum=compute_md5sum(trt_file_path),
    )


if __name__ == "__main__":
    main()
