#pragma once

#include <cmath>
#include <nanoflann.hpp>
#include <optional>
#include <set>
#include <spdlog/spdlog.h>
#include <tuple>

#include <lib/common/cpp/exceptions.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <trajectory/cpp/trajectory.hpp>
#include <weed_tracking/cpp/tracking/tracker.h>

constexpr float kPixelsPerMM = (float)(200 / 25.4);

namespace carbon {

namespace kd_tree {

template <class IndexTypeT, class ItemTypeT, int DIM = 2, class Distance = nanoflann::metric_L2>
class KDTreeAdaptorBase {
public:
  typedef KDTreeAdaptorBase<IndexTypeT, ItemTypeT, DIM, Distance> self_t;
  typedef typename Distance::template traits<float, self_t>::distance_t metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<metric_t, self_t, DIM> index_t;
  KDTreeAdaptorBase(const std::vector<std::pair<IndexTypeT, ItemTypeT>> &items, const size_t leaf_max_size = 10)
      : items_(items), index_(new index_t(2, *this, nanoflann::KDTreeSingleIndexAdaptorParams(leaf_max_size))) {
    index_->buildIndex();
  }

  inline size_t kdtree_get_point_count() const { return items_.size(); }

  inline float kdtree_get_pt(const size_t idx, const size_t dim) const {
    if (dim == 0) {
      return items_[idx].second.get_x();
    } else if (dim == 1) {
      return items_[idx].second.get_y();
    } else {
      throw maka_error("unsupported");
    }
  }

  template <class BBOX>
  bool kdtree_get_bbox(BBOX &) const {
    return false;
  }

  virtual inline size_t query_radius(float query_point_x, float query_point_y, float radius,
                                     std::vector<std::pair<size_t, float>> &out_points) const {
    std::vector<float> query_point = {query_point_x, query_point_y};
    return index_->radiusSearch(query_point.data(), radius, out_points, nanoflann::SearchParams());
  }

  virtual inline size_t query_nn(float query_point_x, float query_point_y, int num_items,
                                 std::vector<size_t> &out_indices, std::vector<float> &out_dist_sqr) const {
    std::vector<float> query_point = {query_point_x, query_point_y};
    return index_->knnSearch(query_point.data(), num_items, &out_indices[0], &out_dist_sqr[0]);
  }

protected:
  std::vector<std::pair<IndexTypeT, ItemTypeT>> items_;
  std::unique_ptr<index_t> index_;
};

template class KDTreeAdaptorBase<size_t, carbon::trajectory::TrackedItemCentroid>;
template class KDTreeAdaptorBase<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>;
} // namespace kd_tree

namespace weed_tracking {

// Even though the distance metric is L2 here. The actual distance returned is the squared L2 norm.
template <int DIM = 2, class Distance = nanoflann::metric_L2>
class KDTreeAdaptor : public carbon::kd_tree::KDTreeAdaptorBase<size_t, trajectory::TrackedItemCentroid> {
public:
  typedef KDTreeAdaptor<DIM, Distance> self_t;
  typedef typename Distance::template traits<float, self_t>::distance_t metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<metric_t, self_t, DIM> index_t;

  KDTreeAdaptor(const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &centroids,
                const size_t leaf_max_size = 10)
      : KDTreeAdaptorBase<size_t, trajectory::TrackedItemCentroid>(centroids, leaf_max_size) {}
};

void undistort_model_centroids(
    std::vector<trajectory::TrackedItemCentroid> &model_centroids, torch::Tensor d2u_mapx, torch::Tensor d2u_mapy,
    std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys);

std::tuple<std::map<uint32_t, std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>>,
           std::map<uint32_t, trajectory::TrackedItemCentroid>>
deduplicate_ransac(Tracker *tracker, std::shared_ptr<lib::common::geometric::GeometricCam> geo_cam,
                   int64_t timestamp_ms, float plant_dedup_radius,
                   const KDTreeAdaptor<2, nanoflann::metric_L2> &kd_tree_plant_centroids,
                   const std::vector<std::pair<size_t, trajectory::TrackedItemCentroid>> &plant_centroids_with_keys);
} // namespace weed_tracking
} // namespace carbon
