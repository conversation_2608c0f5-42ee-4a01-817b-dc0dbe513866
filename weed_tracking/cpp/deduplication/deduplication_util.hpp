#pragma once

#include <nanoflann.hpp>

// Split from deduplication.cpp to avoid circular dependency, needs a better home

namespace carbon::deduplication::util {

constexpr size_t max_neighbor_testing = 3; // cap nearest neighbor search to 3
using PType = std::pair<size_t, float>;
struct NN_data {
  size_t index;
  std::vector<PType> neighbors;
  NN_data() : index(0) {}
  NN_data(const NN_data &rhs) : index(rhs.index), neighbors(rhs.neighbors) {}
  void sort() {
    std::sort(neighbors.begin(), neighbors.end(),
              [](const PType &lhs, const PType &rhs) { return lhs.second < rhs.second; });
  }
  inline bool valid() const { return index < neighbors.size(); }
  const PType &get() const { return neighbors[index]; }
  float get_dist(float def) const {
    if (valid()) {
      return neighbors[index].second;
    }
    return def;
  }
  NN_data &operator++() {
    ++index;
    return *this;
  }
  NN_data &operator--() {
    --index;
    return *this;
  }
};

struct CandidateMatch {
  double error;
  float shift_x;
  float shift_y;
  std::vector<NN_data> matches;
  CandidateMatch(size_t track_size, float _shift_x, float _shift_y)
      : error(0.0), shift_x(_shift_x), shift_y(_shift_y), matches(track_size) {}
};

void calculate_specific_average_error(CandidateMatch *option, size_t max_matches, double default_score) {
  if (max_matches == 0) {
    option->error = 0.0;
    return;
  }
  double sum = 0.0;
  size_t count = 0;
  for (auto &match : option->matches) {
    if (match.valid()) {
      ++count;
      sum += static_cast<double>(match.get().second);
    }
  }
  if (count < max_matches) {
    sum += default_score * (static_cast<double>(max_matches - count));
  }
  option->error = sum / (double)max_matches;
}

void calc_final_matches_recurse(CandidateMatch *candidate, std::unordered_map<size_t, size_t> &model_to_track,
                                size_t i) {
  if (!candidate->matches[i].valid()) {
    return;
  }
  const auto &[candidate_match, inserted] = model_to_track.try_emplace(candidate->matches[i].get().first, i);
  if (!inserted) {
    if (candidate->matches[candidate_match->second].get().second < candidate->matches[i].get().second) {
      ++candidate->matches[i];
      calc_final_matches_recurse(candidate, model_to_track, i);
    } else {
      size_t j = candidate_match->second;
      candidate_match->second = i;
      ++candidate->matches[j];
      calc_final_matches_recurse(candidate, model_to_track, j);
    }
  }
}

void calc_final_matches(CandidateMatch *candidate, size_t max_matches, double dedup_radius) {
  std::unordered_map<size_t, size_t> model_to_track;
  for (size_t i = 0; i < candidate->matches.size(); ++i) {
    calc_final_matches_recurse(candidate, model_to_track, i);
  }
  calculate_specific_average_error(candidate, max_matches, 2 * dedup_radius);
}

template <typename IndexTypeT, typename ItemTypeT>
void shift_all_tracked_objects_by_vector_and_get_distances(
    CandidateMatch *candidate, const std::vector<std::optional<std::tuple<float, float>>> &tracked_items_xy,
    const carbon::kd_tree::KDTreeAdaptorBase<IndexTypeT, ItemTypeT> &kd_tree_centroids, size_t max_matches,
    double dedup_radius) {
  auto &model_matches = candidate->matches;
  auto dedup_radius_sqrd = dedup_radius * dedup_radius;
  for (size_t k = 0; k < tracked_items_xy.size(); k++) {
    auto &neighbor_data = model_matches[k];
    if (tracked_items_xy[k].has_value()) {
      std::vector<size_t> indices(max_neighbor_testing, 0);
      std::vector<float> distances(max_neighbor_testing, 0.0f);
      auto [tracked_x, tracked_y] = tracked_items_xy[k].value();

      auto count = kd_tree_centroids.query_nn(tracked_x + candidate->shift_x, tracked_y + candidate->shift_y,
                                              max_neighbor_testing, indices, distances);
      for (size_t i = 0; i < count; ++i) {
        if (distances[i] < dedup_radius_sqrd) {
          neighbor_data.neighbors.emplace_back(indices[i], std::sqrt(distances[i]));
        }
      }
      neighbor_data.sort();
    }
  }
  calc_final_matches(candidate, max_matches, dedup_radius);
}

} // namespace carbon::deduplication::util