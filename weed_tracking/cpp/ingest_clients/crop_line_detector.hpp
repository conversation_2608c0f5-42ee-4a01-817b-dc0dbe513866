#pragma once

#include <atomic>
#include <boost/lockfree/spsc_queue.hpp>
#include <fstream>
#include <iostream>
#include <list>
#include <memory.h>
#include <optional>
#include <thread>

#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <weed_tracking/cpp/ingest_clients/banding.hpp>
#include <weed_tracking/cpp/tracking/tracker.h>
#include <weed_tracking/proto/weed_tracking.pb.h>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>

namespace carbon::weed_tracking {

class ITrajectoryWrapper {
public:
  virtual uint32_t get_id() = 0;
  virtual double get_last_x_mm() = 0;
  virtual double get_last_y_mm() = 0;
  virtual double get_size_mm() = 0;
  virtual int get_crop_line_id() = 0;
  virtual void set_crop_line_id(int crop_line_id) = 0;
  virtual bool untracked() = 0;
  virtual trajectory::DuplicateStatus duplicate_status() = 0;
};

class TrajectoryWrapper : public ITrajectoryWrapper {
private:
  std::shared_ptr<trajectory::Trajectory> trajectory;

public:
  TrajectoryWrapper(std::shared_ptr<trajectory::Trajectory> t) : trajectory{t} {};
  inline uint32_t get_id() override { return trajectory->id(); };
  inline double get_last_x_mm() override { return trajectory->get_last_item().get_x_mm(); };
  inline double get_last_y_mm() override { return trajectory->get_last_item().get_y_mm(); };
  inline double get_size_mm() override { return trajectory->get_last_item().get_size(); };
  inline void set_crop_line_id(int crop_line_id) { trajectory->set_crop_line(crop_line_id); };
  inline int get_crop_line_id() override { return trajectory->get_crop_line(); };
  inline bool untracked() override { return trajectory->untracked(); }
  inline trajectory::DuplicateStatus duplicate_status() override { return trajectory->duplicate_status(); };
};

class Line {
private:
  double start_mm_;
  double end_mm_;
  bool is_synthetic_;

public:
  Line(double start_mm, double end_mm) : start_mm_(start_mm), end_mm_(end_mm), is_synthetic_(false) {}
  inline double get_start_mm() const { return start_mm_; };
  inline double get_end_mm() const { return end_mm_; };
  inline double get_center_mm() const { return start_mm_ + (end_mm_ - start_mm_) / 2; };
  inline bool is_synthetic() const { return is_synthetic_; };
  inline void set_is_synthetic(bool is_synthetic) { is_synthetic_ = is_synthetic; };
  inline void set_start_mm(double start_mm) { start_mm_ = start_mm; };
  inline void set_end_mm(double end_mm) { end_mm_ = end_mm; };
  inline bool contains(double x_mm) const { return x_mm >= start_mm_ && x_mm <= end_mm_; };
  inline bool in_radius(double x_mm, double radius) const {
    return x_mm >= get_center_mm() - radius && x_mm <= get_center_mm() + radius;
  };
  Line &operator+=(const double rhs) {
    start_mm_ += rhs;
    end_mm_ += rhs;
    return *this;
  }
  friend Line operator+(Line lhs, const double rhs) {
    lhs += rhs;
    return lhs;
  }
};

class SaveReplay {
  std::string file_name_;
  std::ofstream file_;
  int64_t end_ts_ms_;

public:
  SaveReplay(){};

  void remove_trajectory(uint32_t removed_id);
  void add_trajectory(std::shared_ptr<ITrajectoryWrapper>);
  void update_trajectory(std::shared_ptr<ITrajectoryWrapper>);
  void pause(uint64_t ts);
  void found_line(Line &l, std::string command);
  void debug_info(std::string);
  void static_band(Band &b);
  inline bool is_on() { return file_.is_open(); };

  void start(std::string filename, uint32_t ttl_ms, std::vector<Line> &last_detected_lines);
  void close();
};

class CLDAlgorithm;

class CropLineDetectorConfig {
protected:
  std::atomic<bool> refresh_needed_ = true;
  bool detection_enabled_ = false;
  uint32_t work_interval_ms_ = 200;
  uint32_t num_lines_;
  double row_padding_mm_;
  uint32_t min_items_in_crop_line_;
  double default_crop_radius_mm_;
  double smoothing_factor_;
  bool enable_logging_ = false;
  std::vector<Band> static_bands_;
  bool use_clamping_ = true;
  uint32_t clamping_distance_mm_;
  uint32_t search_radius_initial_mm_;
  uint32_t search_radius_increment_mm_;
  uint32_t search_radius_max_mm_;
  std::shared_ptr<CLDAlgorithm> algorithm_;
  double kde_a_;
  double kde_c_;
  double kde_nearby_;
  double kde_precision_;
  double kde_select_factor_;
  double outlier_removal_percent_;
  bool use_squeeze_averaging_;
  double squeeze_distance_mm_;
  uint32_t squeeze_outlier_count_;
  double seed_line_spacing_mm_;
  bool diagnostic_feature_enabled_;
  bool kde_require_all_minima_;

public:
  virtual bool refresh() = 0;

  inline bool is_detection_enabled() { return detection_enabled_; }
  inline uint32_t work_interval_ms() { return work_interval_ms_; }
  inline uint32_t num_lines() { return num_lines_; };
  inline double row_padding_mm() { return row_padding_mm_; };
  inline uint32_t min_items_in_crop_line() { return min_items_in_crop_line_; };
  inline double default_crop_radius_mm() { return default_crop_radius_mm_; };
  inline double smoothing_factor() { return smoothing_factor_; };
  inline bool enable_logging() { return enable_logging_; }
  inline std::vector<Band> &get_static_bands() { return static_bands_; };
  inline bool use_clamping() { return use_clamping_; };
  inline uint32_t clamping_distance_mm() { return clamping_distance_mm_; };
  inline uint32_t search_radius_initial_mm() { return search_radius_initial_mm_; };
  inline uint32_t search_radius_increment_mm() { return search_radius_increment_mm_; };
  inline uint32_t search_radius_max_mm() { return search_radius_max_mm_; };
  inline std::shared_ptr<CLDAlgorithm> get_algorithm() { return algorithm_; };
  inline double kde_a() { return kde_a_; };
  inline double kde_c() { return kde_c_; };
  inline double kde_nearby() { return kde_nearby_; };
  inline double kde_precision() { return kde_precision_; };
  inline double kde_select_factor() { return kde_select_factor_; };
  inline double outlier_removal_percent() { return outlier_removal_percent_; };
  inline bool use_squeeze_averaging() { return use_squeeze_averaging_; };
  inline double squeeze_distance_mm() { return squeeze_distance_mm_; };
  inline uint32_t squeeze_outlier_count() { return squeeze_outlier_count_; };
  inline double seed_line_spacing_mm() { return seed_line_spacing_mm_; };
  inline bool diagnostic_feature_enabled() { return diagnostic_feature_enabled_; };
  inline bool kde_require_all_minima() { return kde_require_all_minima_; };

  virtual void set_refresh_needed();
};

class CropLineDetectorConfigProd : public CropLineDetectorConfig {
  std::shared_ptr<carbon::config::ConfigTree> conf_tree_;
  std::shared_ptr<carbon::config::ConfigTree> feature_flags_;

public:
  CropLineDetectorConfigProd();
  bool refresh() override;
};

struct SaveReplayRequest {
  std::string filename;
  uint32_t ttl;

  SaveReplayRequest(std::string f, uint32_t t) : filename(f), ttl(t) {}
};

class CLDAlgorithm {
public:
  virtual std::optional<std::vector<Line>>
  detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
         std::shared_ptr<CropLineDetectorConfig> config, std::stringstream &log) = 0;
};

class CLDAlgorithmSearchRadius : public CLDAlgorithm {
public:
  std::optional<std::vector<Line>> detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                                          std::shared_ptr<CropLineDetectorConfig> config,
                                          std::stringstream &log) override;
};

class CLDAlgorithmKDE : public CLDAlgorithm {
private:
  int find_first_greater(std::vector<double> &points, double n);
  std::tuple<int, int> find_nearby(std::vector<double> &points, double x, double radius);
  double density(std::vector<double> &points, double x, std::shared_ptr<CropLineDetectorConfig> config);

public:
  std::optional<std::vector<Line>> detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                                          std::shared_ptr<CropLineDetectorConfig> config,
                                          std::stringstream &log) override;
};

class CLDAlgorithmDiffLock : public CLDAlgorithm {
private:
  int get_cost(std::vector<Line> &lines, std::vector<double> &points, double mu, double radius);

public:
  std::optional<std::vector<Line>> detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                                          std::shared_ptr<CropLineDetectorConfig> config,
                                          std::stringstream &log) override;
};

class CropLineDetector : public arbiter::ArbiterClient<> {
  std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> known_trajectories_;
  common::ThreadSafeQueue<arbiter::Request<>> new_requests_;

  SaveReplay save_replay_;
  std::shared_ptr<CropLineDetectorConfig> config_;

  std::mutex save_replay_mutex_;
  std::shared_ptr<SaveReplayRequest> save_replay_request_ = nullptr;

  void apply_smoothing_factor(std::vector<Line> &lines);
  void assign_trajectories_to_lines(std::vector<Line> &lines, std::stringstream &log);
  void adjust_synthetic_lines_for_drift(std::vector<Line> &lines, std::stringstream &log);

protected:
  std::vector<Line> last_detected_lines_;
  std::shared_ptr<std::thread> work_thread_;

  void work();
  void work_loop();
  void detect();
  virtual void trajectory_request(const arbiter::Request<> &req) override;

public:
  CropLineDetector();
  CropLineDetector(std::shared_ptr<CropLineDetectorConfig> config);
  void add_trajectory(std::shared_ptr<ITrajectoryWrapper> t);
  void remove_trajectory(uint32_t id);
  inline virtual bool is_detection_enabled() { return config_->is_detection_enabled(); };
  void start();
  void start_saving_replay(std::string filename, uint32_t ttl_ms);
  void refresh_config();
};

/*
 * Singleton for passing diagnostics data from CropLineDetector to WeedingDiagnostics
 */
class CLDDiagnosticsConnector {
public:
  static CLDDiagnosticsConnector &get() {
    static CLDDiagnosticsConnector instance;
    return instance;
  }

  inline void set_snapshot(std::shared_ptr<::weed_tracking::CLDAlgorithmSnapshot> snapshot) {
    std::lock_guard<std::mutex> lock(mut_);
    snapshot_ = snapshot;
  }

  inline std::shared_ptr<::weed_tracking::CLDAlgorithmSnapshot> get_snapshot() {
    std::lock_guard<std::mutex> lock(mut_);
    return snapshot_;
  }

private:
  std::mutex mut_;
  std::shared_ptr<::weed_tracking::CLDAlgorithmSnapshot> snapshot_;
};

// For testing.
int add_lines_if_disappeared(std::vector<Line> &current_lines, std::vector<Line> &previous_lines, uint32_t max_lines);

} // namespace carbon::weed_tracking
