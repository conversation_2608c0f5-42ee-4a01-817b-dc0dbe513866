add_executable(crop_line_detector_test crop_line_detector_test.cpp)
target_link_directories(crop_line_detector_test PUBLIC /usr/local/lib /usr/local/cuda/lib64/ /opt/hpcx/ompi/lib/)
target_link_libraries(crop_line_detector_test PRIVATE weed_tracking gtest_main gmock)
target_include_directories(crop_line_detector_test PRIVATE ${CMAKE_CURRENT_SOURCE_DIR} ${OpenCV_INCLUDE_DIRS} ${googletest_DIR}/googletest/include ${googletest_DIR}/googlemock/include)
add_test(NAME crop_line_detector_test COMMAND crop_line_detector_test)
