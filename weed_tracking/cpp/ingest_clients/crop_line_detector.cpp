#include "weed_tracking/cpp/ingest_clients/crop_line_detector.hpp"
#include <cmath>
#include <config/client/cpp/config_subscriber.hpp>
#include <fmt/ranges.h>
#include <lib/common/cpp/time.h>
#include <lib/common/cpp/utils/generation.hpp>
#include <lib/common/cpp/utils/logging.h>
#include <lib/common/persistence1d/persistence1d.hpp>
#include <lib/common/robot_definition/cpp/robot_definition.hpp>
#include <limits>
#include <set>
#include <spdlog/spdlog.h>
#include <tuple>
#include <weed_tracking/proto/weed_tracking.pb.h>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>

namespace carbon::weed_tracking {

// These could be configs
constexpr uint32_t kKDEDownsampleFactor = 1;
constexpr uint32_t kDiffLockDownsampleFactor = 1;

/*------------------------------------

    Helper Functions

-------------------------------------*/

inline double gaussian(double a, double x, double b, double c);
void squeeze(std::vector<double> &band_points, size_t &left, size_t &right, uint32_t count, double squeeze_distance_mm);
Band get_clamped_band(const Band &static_band, const Line &l, float dist);
void copy_points_with_down_sampling(std::shared_ptr<::weed_tracking::CLDAlgorithmSnapshot> snapshot,
                                    std::vector<float> &graph_points_x, std::vector<float> &graph_points_y,
                                    uint32_t downsample = 1);

/*------------------------------------

    Helper Classes

-------------------------------------*/

class Point {
  double x_;
  double radius_;
  uint32_t collapsed_times_ = 1;
  std::shared_ptr<std::vector<double>> original_x_positions_;
  double line_center_ = 0;
  double line_width_ = 0;

public:
  Point(double x, double radius) : x_{x}, radius_{radius}, collapsed_times_{1} {
    original_x_positions_ = std::make_shared<std::vector<double>>();
    original_x_positions_->push_back(x);
  };
  Point(double x, double radius, uint32_t collapsed_times, std::shared_ptr<std::vector<double>> original_x_positions)
      : x_{x}, radius_{radius}, collapsed_times_{collapsed_times} {
    original_x_positions_ = original_x_positions;
  };
  inline double get_x() const { return x_; };
  inline double get_radius() const { return radius_; };
  inline uint32_t collapsed_times() const { return collapsed_times_; };
  inline std::shared_ptr<std::vector<double>> original_x_positions() { return original_x_positions_; };
  inline double get_line_center() const { return line_center_; };
  inline double get_line_width() const { return line_width_; };
  inline double get_line_start() const { return line_center_ - line_width_ / 2; };
  inline double get_line_end() const { return line_center_ + line_width_ / 2; };
  void compute_line(double outlier_removal_percent) {
    // original_x_positions always has at least one item
    size_t sz = original_x_positions_->size();
    size_t start = (size_t)((double)sz * (outlier_removal_percent / 100));
    size_t end = sz - start;
    double total = original_x_positions_->at(start);
    for (size_t i = start + 1; i < end; i++) {
      total += original_x_positions_->at(i);
    }
    if (end != start) {
      line_center_ = total / (double)(end - start);
    } else {
      line_center_ = total; // only 1 element in line
    }
    line_width_ = original_x_positions_->at(sz - 1) - original_x_positions_->at(0);
  }
};

class Distance {
  double dist_;
  double from_x_;
  double to_x_;

public:
  Distance(double dist, double from, double to) : dist_{dist}, from_x_{from}, to_x_{to} {};
  inline double get_dist() const { return dist_; };
  inline double get_from_x() const { return from_x_; };
  inline double get_to_x() const { return to_x_; };
};

/*------------------------------------

    SaveReplay

-------------------------------------*/
void SaveReplay::start(std::string filename, uint32_t ttl_ms, std::vector<Line> &last_detected_lines) {
  if (is_on()) {
    file_.close();
  }

  file_name_ = filename;
  file_.open(file_name_);
  end_ts_ms_ = maka_control_timestamp_ms() + ttl_ms;
  Banding::instance()->reload_from_redis();
  auto static_bands = Banding::instance()->get_static_bands();
  for (auto &band : static_bands) {
    static_band(band);
  }

  for (auto &l : last_detected_lines) {
    found_line(l, "last_detected_line");
  }
};

void SaveReplay::static_band(Band &b) {
  std::ostringstream stringStream;
  stringStream << "{\"command\": \"static_band\", \"data\": { \"start\": " << b.get_start_mm()
               << ", \"end\": " << b.get_end_mm() << " }}";
  file_ << stringStream.str() << std::endl;
}

void trajToJsonStr(std::ostringstream &stringStream, std::string command, std::shared_ptr<ITrajectoryWrapper> t) {
  stringStream << "{\"command\": \"" << command << "\", \"data\": { \"id\": " << t->get_id() << ", \"coords\":[{";
  stringStream << " \"size\": " << t->get_size_mm() << ", \"ts\": 0, \"x\": " << t->get_last_x_mm() << ", ";
  stringStream << " \"y\": " << t->get_last_y_mm() << ", \"z\": 0";
  stringStream << "}]} }";
}

void SaveReplay::add_trajectory(std::shared_ptr<ITrajectoryWrapper> t) {
  if (!is_on()) {
    return;
  }
  std::ostringstream stringStream;
  trajToJsonStr(stringStream, "add_trajectory", t);
  file_ << stringStream.str() << std::endl;
}

void SaveReplay::update_trajectory(std::shared_ptr<ITrajectoryWrapper> t) {
  if (!is_on()) {
    return;
  }
  std::ostringstream stringStream;
  trajToJsonStr(stringStream, "update_trajectory", t);
  file_ << stringStream.str() << std::endl;
}

void SaveReplay::remove_trajectory(uint32_t removed_id) {
  if (!is_on()) {
    return;
  }
  std::ostringstream stringStream;
  stringStream << "{\"command\": \"remove_trajectory\", \"data\": " << removed_id << " }";
  file_ << stringStream.str() << std::endl;
}

void SaveReplay::pause(uint64_t loop_idx) {
  if (!is_on()) {
    return;
  }
  auto ts = maka_control_timestamp_ms();
  std::ostringstream stringStream;
  stringStream << "{\"command\": \"pause\", \"data\": { \"ts\": " << ts << ", \"loop\": " << loop_idx << "} }";
  file_ << stringStream.str() << std::endl;

  file_.flush();
  if (maka_control_timestamp_ms() >= end_ts_ms_) {
    file_.close();
  }
}

void SaveReplay::close() {
  if (is_on()) {
    file_.close();
  }
}

void SaveReplay::found_line(Line &l, std::string command) {
  if (!is_on()) {
    return;
  }
  std::ostringstream stringStream;
  stringStream << "{\"command\": \"" << command << "\", \"data\": { \"start\": " << l.get_start_mm()
               << ", \"end\": " << l.get_end_mm() << " }}";
  file_ << stringStream.str() << std::endl;
}

void SaveReplay::debug_info(std::string s) {
  if (!is_on()) {
    return;
  }
  std::ostringstream stringStream;
  stringStream << "{\"command\": \"debug_info\", \"data\": \"" << s << "\" }";
  file_ << stringStream.str() << std::endl;
}

/*------------------------------------

    CropLineDetectorConfig

-------------------------------------*/
void CropLineDetectorConfig::set_refresh_needed() {
  spdlog::debug("CropLineDetector: config refresh required");
  refresh_needed_ = true;
}

CropLineDetectorConfigProd::CropLineDetectorConfigProd() {
  conf_tree_ = config::get_global_config_subscriber()->get_config_node("common", "crop_line_detection");
  conf_tree_->register_callback(std::bind(&CropLineDetectorConfigProd::set_refresh_needed, this));
  feature_flags_ = config::get_global_config_subscriber()->get_config_node("common", "feature_flags");
  feature_flags_->register_callback(std::bind(&CropLineDetectorConfigProd::set_refresh_needed, this));
  set_refresh_needed();
}

bool CropLineDetectorConfigProd::refresh() {
  if (!refresh_needed_) {
    return false;
  }

  try {
    detection_enabled_ = feature_flags_->get_node("dynamic_banding_feature")->get_value<bool>();
    diagnostic_feature_enabled_ = feature_flags_->get_node("banding_algorithm_diagnostic_feature")->get_value<bool>();
    work_interval_ms_ = conf_tree_->get_node("work_interval_ms")->get_value<uint32_t>();
    row_padding_mm_ = conf_tree_->get_node("row_padding_mm")->get_value<double>();
    min_items_in_crop_line_ = conf_tree_->get_node("min_items_in_crop_line")->get_value<uint32_t>();
    default_crop_radius_mm_ = conf_tree_->get_node("default_crop_radius_mm")->get_value<double>();
    smoothing_factor_ = conf_tree_->get_node("smoothing_factor")->get_value<double>();
    Banding::instance()->reload_from_redis();
    static_bands_ = Banding::instance()->get_static_bands();
    num_lines_ = (uint32_t)static_bands_.size();
    use_clamping_ = conf_tree_->get_node("use_clamping")->get_value<bool>();
    clamping_distance_mm_ = conf_tree_->get_node("clamping_distance_mm")->get_value<uint32_t>();
    search_radius_initial_mm_ = conf_tree_->get_node("search_radius_initial_mm")->get_value<uint32_t>();
    search_radius_increment_mm_ = conf_tree_->get_node("search_radius_increment_mm")->get_value<uint32_t>();
    search_radius_max_mm_ = conf_tree_->get_node("search_radius_max_mm")->get_value<uint32_t>();
    auto algo = conf_tree_->get_node("algorithm")->get_value<std::string>();
    if (algo == "kde") {
      algorithm_ = std::make_shared<CLDAlgorithmKDE>();
    } else if (algo == "diff_lock") {
      algorithm_ = std::make_shared<CLDAlgorithmDiffLock>();
    } else {
      algorithm_ = std::make_shared<CLDAlgorithmSearchRadius>();
    }
    kde_a_ = conf_tree_->get_node("kde_a")->get_value<double>();
    kde_c_ = conf_tree_->get_node("kde_c")->get_value<double>();
    kde_nearby_ = conf_tree_->get_node("kde_nearby_mm")->get_value<double>();
    kde_precision_ = conf_tree_->get_node("kde_precision_mm")->get_value<double>();
    kde_select_factor_ = conf_tree_->get_node("kde_select_factor")->get_value<double>();
    outlier_removal_percent_ = std::min(50.0, conf_tree_->get_node("outlier_removal_percent")->get_value<double>());
    bool calculate_std = conf_tree_->get_node("kde_calculate_std")->get_value<bool>();
    if (calculate_std && static_bands_.size() > 0) {
      double total_width = 0;
      for (auto &band : static_bands_) {
        total_width += band.get_end_mm() - band.get_start_mm();
      }
      double avg_width = total_width / static_cast<double>(static_bands_.size());
      double ratio = conf_tree_->get_node("kde_std_band_width_ratio")->get_value<double>();
      double exponent = conf_tree_->get_node("kde_std_band_width_exponent")->get_value<double>();
      double offset = conf_tree_->get_node("kde_std_band_width_offset")->get_value<double>();
      kde_c_ = ratio * pow(avg_width, exponent) + offset;
      kde_nearby_ = kde_c_ * 3;
      spdlog::info("CropLineDetector: calculated std={} nearby={} (ratio={}, exponent={}, offset={})", kde_c_,
                   kde_nearby_, ratio, exponent, offset);
    }
    use_squeeze_averaging_ = conf_tree_->get_node("use_squeeze_averaging")->get_value<bool>();
    squeeze_distance_mm_ = conf_tree_->get_node("squeeze_distance_mm")->get_value<double>();
    squeeze_outlier_count_ = conf_tree_->get_node("squeeze_outlier_count")->get_value<uint32_t>();
    seed_line_spacing_mm_ = conf_tree_->get_node("seed_line_spacing_inches")->get_value<double>() * 25.4;
    kde_require_all_minima_ = conf_tree_->get_node("kde_require_all_minima")->get_value<bool>();
    spdlog::info("CropLineDetector: expecting {} crop lines", num_lines_);

    refresh_needed_ = false;
    return true;
  } catch (std::exception &e) {
    spdlog::error("CropLineDetector: exception while refreshing config: {}", e.what());
    return false;
  }
}

/*------------------------------------

    CropLineDetector

-------------------------------------*/
CropLineDetector::CropLineDetector() : config_{std::make_shared<CropLineDetectorConfigProd>()} {};

CropLineDetector::CropLineDetector(std::shared_ptr<CropLineDetectorConfig> config) : config_{config} {};

void CropLineDetector::start_saving_replay(std::string filename, uint32_t ttl_ms) {
  std::lock_guard<std::mutex> lock(save_replay_mutex_);
  save_replay_request_ = std::make_shared<SaveReplayRequest>(filename, ttl_ms);
}

void CropLineDetector::trajectory_request(const arbiter::Request<> &req) { new_requests_.add(req); }

void CropLineDetector::work_loop() {
  while (true) {
    std::this_thread::sleep_for(std::chrono::milliseconds(config_->work_interval_ms()));
    if (config_->refresh()) {
      last_detected_lines_.clear();
    }
    work();
  }
}

void CropLineDetector::start() { work_thread_ = std::make_shared<std::thread>(&CropLineDetector::work_loop, this); }

void CropLineDetector::work() {
  if (!is_detection_enabled()) {
    return;
  }

  {
    std::lock_guard<std::mutex> lock(save_replay_mutex_);
    if (save_replay_request_ != nullptr) {
      save_replay_.start(save_replay_request_->filename, save_replay_request_->ttl, last_detected_lines_);
      save_replay_request_ = nullptr;
    }
  }

  // process updates, for replay only (updates happen outside of input to this component)
  if (save_replay_.is_on()) {
    for (auto &it : known_trajectories_) {
      if (it.second->duplicate_status() != trajectory::DuplicateStatus::kDuplicate) {
        save_replay_.update_trajectory(it.second);
      }
    }
  }

  // changes are only add or remove, we don't have data on updates and the x coordinate should not change
  bool changed = false;
  for (const auto &req : new_requests_.pop_all()) {
    if (req.type == arbiter::ARBITER_CLIENT_ADD) {
      try {
        if (req.trajectory->decisions_set() && req.trajectory->get_decision(trajectory::DecisionFlag::kBandingCrop)) {
          known_trajectories_[req.trajectory->id()] = std::make_shared<TrajectoryWrapper>(req.trajectory);
          changed = true;
          save_replay_.add_trajectory(known_trajectories_[req.trajectory->id()]);
        }
      } catch (const std::exception &e) {
        spdlog::error("CropLineDetector: {}", e.what());
      }
    } else if (known_trajectories_.erase(req.trajectory->id())) {
      changed = true;
      save_replay_.remove_trajectory(req.trajectory->id());
    }
  }

  if (changed) {
    detect();
  }

  for (auto &l : last_detected_lines_) {
    save_replay_.found_line(l, "found_line");
  }
  save_replay_.pause(0);
}

void CropLineDetector::detect() {
  if (known_trajectories_.size() == 0) {
    return;
  }

  std::stringstream log;

  auto lines = config_->get_algorithm()->detect(known_trajectories_, config_, log);

  if (!lines) {
    return;
  }

  int added_lines = add_lines_if_disappeared(lines.value(), last_detected_lines_, config_->num_lines());
  apply_smoothing_factor(lines.value());
  if (added_lines > 0) {
    adjust_synthetic_lines_for_drift(lines.value(), log);
  }

  last_detected_lines_ = lines.value();

  // setting crop line id to trajectories
  assign_trajectories_to_lines(last_detected_lines_, log);

  std::vector<Band> bands;
  std::vector<Band> static_bands = config_->get_static_bands();
  float dist = static_cast<float>(config_->clamping_distance_mm());
  for (size_t i = 0; i < last_detected_lines_.size(); i++) {
    if (i < static_bands.size()) {
      const auto &new_line = last_detected_lines_[i];
      bands.push_back(config_->use_clamping() ? get_clamped_band(static_bands[i], new_line, dist)
                                              : Band(static_cast<float>(new_line.get_start_mm()),
                                                     static_cast<float>(new_line.get_end_mm())));
    }
  }
  Banding::instance()->update_dynamic_bands(bands);

  if (save_replay_.is_on() && config_->enable_logging()) {
    save_replay_.debug_info(log.str());
  }
}

int add_lines_if_disappeared(std::vector<Line> &current_lines, std::vector<Line> &previous_lines, uint32_t max_lines) {
  if (current_lines.size() >= max_lines || previous_lines.empty()) {
    return 0;
  }

  // Ensure both vectors are sorted by start position.
  std::sort(current_lines.begin(), current_lines.end(),
            [](const Line &a, const Line &b) { return a.get_start_mm() < b.get_start_mm(); });
  std::sort(previous_lines.begin(), previous_lines.end(),
            [](const Line &a, const Line &b) { return a.get_start_mm() < b.get_start_mm(); });

  std::vector<Line> final_lines;
  final_lines.reserve(max_lines);
  int added_lines = 0;

  auto current_it = current_lines.begin();
  auto previous_it = previous_lines.begin();

  while (final_lines.size() < max_lines && (current_it != current_lines.end() || previous_it != previous_lines.end())) {
    bool done_processing_previous = previous_it == previous_lines.end();
    bool current_line_before_previous =
        current_it != current_lines.end() && current_it->get_end_mm() < previous_it->get_start_mm();

    if (done_processing_previous || current_line_before_previous) {
      final_lines.push_back(*current_it);
      ++current_it;
    } else {
      // Process a line from the previous set.
      bool overlapping = current_it != current_lines.end() && current_it->get_start_mm() < previous_it->get_end_mm();
      if (!overlapping) {
        // The previous line has disappeared. Add it as a synthetic line.
        Line synthetic_line = *previous_it;
        synthetic_line.set_is_synthetic(true);
        final_lines.push_back(synthetic_line);
        added_lines++;
      }
      // Move to the next previous line.
      ++previous_it;
    }
  }

  current_lines = final_lines;
  return added_lines;
}

void CropLineDetector::apply_smoothing_factor(std::vector<Line> &lines) {
  if (lines.size() == last_detected_lines_.size()) {
    auto sf = config_->smoothing_factor();
    for (uint32_t idx = 0; idx < last_detected_lines_.size(); idx++) {
      auto &oldl = last_detected_lines_[idx];
      auto &newl = lines[idx];
      newl.set_start_mm(oldl.get_start_mm() * sf + newl.get_start_mm() * (1 - sf));
      newl.set_end_mm(oldl.get_end_mm() * sf + newl.get_end_mm() * (1 - sf));
    }
  }
}

void CropLineDetector::assign_trajectories_to_lines(std::vector<Line> &lines, std::stringstream &) {
  for (auto &it : known_trajectories_) {
    auto &c = it.second;
    auto x = c->get_last_x_mm();
    if (c->get_crop_line_id() == trajectory::Trajectory::invalid_crop_line_id) {
      for (uint32_t i = 0; i < lines.size(); i++) {
        auto &line = lines[i];
        if (line.contains(x)) {
          c->set_crop_line_id((int)i);
          break;
        }
      }
    }
  }
}

void CropLineDetector::adjust_synthetic_lines_for_drift(std::vector<Line> &lines, std::stringstream &log) {
  if (lines.size() != last_detected_lines_.size()) {
    return;
  }
  int num_real_rows = 0;
  double drift_sum = 0;
  bool direction_positive = false;
  bool direction_negative = false;
  for (uint32_t i = 0; i < lines.size(); i++) {
    Line &newl = lines[i];
    Line &oldl = last_detected_lines_[i];
    if (newl.is_synthetic()) {
      continue;
    }
    num_real_rows++;
    auto dx = newl.get_start_mm() - oldl.get_start_mm();
    if (dx < 0) {
      direction_negative = true;
    } else if (dx > 0) {
      direction_positive = true;
    }
    drift_sum += dx;
  }

  if (num_real_rows == 0) {
    return;
  }

  if (!(direction_negative && direction_positive)) {
    // only allow drift in one direction
    double drift = drift_sum / num_real_rows;
    if (direction_negative) {
      // real lines moved to the left
      for (uint32_t i = 0; i < lines.size(); i++) {
        Line *newl = &lines[i];
        if (!newl->is_synthetic()) {
          continue;
        }
        Line *prevl = (i > 0) ? &lines[i - 1] : NULL;
        if (prevl == NULL || prevl->get_end_mm() < newl->get_start_mm() + drift) {
          newl->set_start_mm(newl->get_start_mm() + drift);
          newl->set_end_mm(newl->get_end_mm() + drift);
        }
      }
    } else {
      // directionPositive = true
      for (uint32_t i = 0; i < lines.size(); i++) {
        Line *newl = &lines[i];
        if (!newl->is_synthetic()) {
          continue;
        }
        Line *nextl = (i < lines.size() - 1) ? &lines[i + 1] : NULL;
        if (nextl == NULL || nextl->get_start_mm() > newl->get_end_mm() + drift) {
          newl->set_start_mm(newl->get_start_mm() + drift);
          newl->set_end_mm(newl->get_end_mm() + drift);
        }
      }
    }
    if (config_->enable_logging()) {
      log << " Applied drift of " << drift << "mm";
    }
  }
}

void CropLineDetector::refresh_config() { config_->set_refresh_needed(); }

/*------------------------------------

    Search Radius

-------------------------------------*/

// careful weary traveler for here lies madness
std::optional<std::vector<Line>>
CLDAlgorithmSearchRadius::detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                                 std::shared_ptr<CropLineDetectorConfig> config, std::stringstream &log) {
  double point_radius = config->default_crop_radius_mm();
  auto padding = config->row_padding_mm();
  std::vector<Point> chosen_rows;
  double min_score = std::numeric_limits<double>::max();
  for (uint32_t search_radius = config->search_radius_initial_mm(); search_radius < config->search_radius_max_mm();
       search_radius += config->search_radius_increment_mm()) {
    std::vector<Point> points;
    if (config->enable_logging()) {
      log << "Search radius=" << search_radius << ",";
    }

    for (auto &it : known_trajectories) {
      auto &c = it.second;
      if (c->duplicate_status() != trajectory::DuplicateStatus::kDuplicate) {
        points.push_back(Point(c->get_last_x_mm(), point_radius));
      }
    }
    if (points.empty()) {
      if (!known_trajectories.empty()) {
        LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: no crops passed the banding threshold");
      }
      return {};
    }

    if (config->enable_logging()) {
      log << "Start detect, trajectories num=" << points.size() << "\\n";
    }

    // sort points by x
    std::sort(points.begin(), points.end(), [](const Point &a, const Point &b) { return a.get_x() < b.get_x(); });

    // overlapping points collapsed into one
    std::vector<Point> collapsed;
    collapsed.push_back(points[0]);
    for (uint32_t i = 1; i < points.size(); i++) {
      auto &p1 = collapsed[collapsed.size() - 1];
      auto &p2 = points[i];
      auto e1 = p1.get_x() + p1.get_radius();
      auto s2 = p2.get_x() - p2.get_radius();
      auto s1 = p1.get_x() - p1.get_radius();
      auto e2 = p2.get_x() + p2.get_radius();
      if (s2 - padding - search_radius <= e1 + padding + search_radius) {
        auto news = std::min(s1, s2);
        auto newe = std::max(e1, e2);
        auto newr = (newe - news) / 2;
        auto newx = news + newr;
        auto newCollapsedTimes = p1.collapsed_times() + 1;
        auto x_positions = p1.original_x_positions();
        x_positions->push_back(p2.get_x());
        collapsed[collapsed.size() - 1] = Point(newx, newr, newCollapsedTimes, x_positions);
      } else {
        collapsed.push_back(p2);
      }
    }

    std::sort(collapsed.begin(), collapsed.end(),
              [](const Point &a, const Point &b) { return a.collapsed_times() > b.collapsed_times(); });
    std::vector<Point> rows;
    for (auto &c : collapsed) {
      if (rows.size() >= config->num_lines()) {
        break;
      }
      if (c.collapsed_times() >= config->min_items_in_crop_line()) {
        c.compute_line(config->outlier_removal_percent());
        rows.push_back(c);
      } else {
        break; // no more rows having enough points for a crop line
      }
    }

    std::sort(rows.begin(), rows.end(),
              [](const Point &a, const Point &b) { return a.get_line_center() < b.get_line_center(); });

    double score = 0.0;
    for (size_t i = 0; i < config->get_static_bands().size(); ++i) {
      auto &band = config->get_static_bands()[i];
      if (i >= rows.size()) {
        score += band.get_start_mm() + band.get_end_mm();
      } else {
        auto &row = rows[i];
        score +=
            std::abs(band.get_start_mm() - row.get_line_start()) + std::abs(band.get_end_mm() - row.get_line_end());
      }
    }

    if (config->enable_logging()) {
      log << "Score: " << score << ",";
    }

    if (score < min_score) {
      chosen_rows = rows;
      min_score = score;
      if (config->enable_logging()) {
        log << "For now, chosen score: " << score << " search radius " << search_radius << "\\n";
      }
    }
  }

  std::vector<Line> lines;
  for (auto &row : chosen_rows) {
    lines.push_back(Line(row.get_line_start() - padding, row.get_line_end() + padding));
  }

  return lines;
}

/*------------------------------------

    KDE

-------------------------------------*/

// TODO(sswigart): Replace with std::upper_bound.
int CLDAlgorithmKDE::find_first_greater(std::vector<double> &points, double n) {
  size_t left = 0;
  size_t right = points.size();
  int m = -1;
  while (left < right) {
    m = (int)(left + (right - left) / 2);
    if (points[m] > n) {
      right = m;
      continue;
    }
    if (m == (int)(points.size() - 1)) {
      return -1;
    }
    if (points[m + 1] > n) {
      return (int)m + 1;
    }
    left = m;
  }
  return (int)m;
}

std::tuple<int, int> CLDAlgorithmKDE::find_nearby(std::vector<double> &points, double x, double radius) {
  auto start_index = find_first_greater(points, x - radius);
  auto end_index = find_first_greater(points, x + radius);
  if (start_index == -1 && end_index == -1) {
    return std::make_tuple(-1, -1);
  }
  if (start_index == -1) {
    start_index = 0;
  }
  if (end_index == -1) {
    end_index = (int)(points.size() - 1);
  }
  return std::make_tuple(start_index, end_index);
}

double CLDAlgorithmKDE::density(std::vector<double> &points, double x, std::shared_ptr<CropLineDetectorConfig> config) {
  double a = config->kde_a();
  double c = config->kde_c();
  double nearby = config->kde_nearby();
  int start;
  int end;
  std::tie(start, end) = find_nearby(points, x, nearby);
  if (start == -1 || end == -1) {
    return 0.0;
  }
  double d = 0.0;
  for (int i = start; i < end; i++) {
    double xi = points[i];
    d += gaussian(a, x, xi, c);
  }
  return d;
}

std::optional<std::vector<Line>>
CLDAlgorithmKDE::detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                        std::shared_ptr<CropLineDetectorConfig> config, std::stringstream &) {
  std::vector<Line> lines;

  if (config->get_static_bands().size() == 0) {
    LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: no static bands defined");
    return lines;
  }

  std::vector<double> points;
  for (auto &it : known_trajectories) {
    auto &c = it.second;
    if (c->duplicate_status() != trajectory::DuplicateStatus::kDuplicate) {
      points.push_back(c->get_last_x_mm());
    }
  }
  if (points.empty()) {
    if (!known_trajectories.empty()) {
      LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: no crops passed the banding threshold");
    }
    return lines;
  }
  std::sort(points.begin(), points.end());

  // TODO(jfroel): Adjust radius for reaper based on total width of predict space
  double center = 2682; // avg x between predict2 and predict3
  double radius = 1143; // 45 inches in mm
  if (carbon::common::is_reaper()) {
    center = lib::common::robot_definition::RobotDefinition::get()
                 ->get_local_row_definition()
                 ->get_predict_space_center_mm();
  }
  const double start_offset = center - radius;
  const double end_offset = center + radius;

  double precision = config->kde_precision();

  std::vector<float> x_offsets;
  std::vector<float> densities;
  for (double i = start_offset; i <= end_offset; i += precision) {
    x_offsets.push_back(static_cast<float>(i));
    densities.push_back(static_cast<float>(density(points, i, config)));
  }

  p1d::Persistence1D prs;
  prs.RunPersistence(densities);

  std::vector<p1d::TPairedExtrema> extrema;
  prs.GetPairedExtrema(extrema, 0);

  if (extrema.size() == 0) {
    return lines;
  }

  std::sort(extrema.begin(), extrema.end(),
            [](const p1d::TPairedExtrema &a, const p1d::TPairedExtrema &b) { return a.Persistence > b.Persistence; });

  std::vector<int> selected_minima;
  selected_minima.push_back(prs.GetGlobalMinimumIndex()); // Not included in the extrema.
  selected_minima.push_back(extrema[0].MinIndex);
  auto select_factor = config->kde_select_factor();
  for (size_t i = 1; i < extrema.size(); i++) {
    if (selected_minima.size() == config->get_static_bands().size() + 1) {
      break;
    }
    auto score = extrema[i].Persistence;
    auto prev_score = extrema[i - 1].Persistence;
    if (score * select_factor > prev_score) {
      selected_minima.push_back(extrema[i].MinIndex);
    } else {
      break;
    }
  }

  // if fewer than n+1 minima are found, we can't detect n lines. Return so that we fall back on the last known lines.
  if (config->kde_require_all_minima() && selected_minima.size() < config->get_static_bands().size() + 1) {
    return lines;
  }

  std::sort(selected_minima.begin(), selected_minima.end());

  auto band_start_it = std::upper_bound(points.begin(), points.end(), start_offset + selected_minima[0] * precision);
  for (size_t i = 1; i < selected_minima.size(); i++) {
    auto band_end_x = start_offset + selected_minima[i] * precision;

    auto band_end_it = std::upper_bound(band_start_it, points.end(), band_end_x);
    std::vector<double> band_points(band_start_it, band_end_it);
    band_start_it = band_end_it;

    if (band_points.empty()) {
      continue;
    }

    double band_center = 0;
    if (config->use_squeeze_averaging()) {
      size_t left = 0;
      size_t right = band_points.size() - 1;
      squeeze(band_points, left, right, config->squeeze_outlier_count(), config->squeeze_distance_mm());
      band_center = (band_points[left] + band_points[right]) / 2;
    } else {
      size_t start = (size_t)((double)band_points.size() * (config->outlier_removal_percent() / 100.0));
      size_t end = band_points.size() - start;
      band_center = band_points[start];
      if (start < end) {
        for (size_t j = start + 1; j < end; j++) {
          band_center += band_points[j];
        }
        band_center /= (double)(end - start);
      }
    }

    if (i - 1 < config->get_static_bands().size()) {
      auto width = config->get_static_bands()[i - 1].get_width_mm();
      lines.push_back(Line(band_center - width / 2, band_center + width / 2));
    } else {
      break;
    }
  }

  if (config->diagnostic_feature_enabled()) {
    auto snapshot = std::make_shared<::weed_tracking::CLDAlgorithmSnapshot>();
    copy_points_with_down_sampling(snapshot, x_offsets, densities, kKDEDownsampleFactor);
    for (auto &index : selected_minima) {
      snapshot->add_minimas_x(x_offsets[index]);
      snapshot->add_minimas_y(densities[index]);
    }
    for (auto &line : lines) {
      snapshot->add_lines(static_cast<float>(line.get_center_mm()));
    }
    snapshot->set_timestamp_ms(maka_control_timestamp_ms());
    CLDDiagnosticsConnector::get().set_snapshot(snapshot);
  }

  return lines;
}

/*------------------------------------

    Diff Lock

-------------------------------------*/

int CLDAlgorithmDiffLock::get_cost(std::vector<Line> &lines, std::vector<double> &points, double mu, double radius) {
  int cost = 0;
  for (double point : points) {
    // check if this point belongs to a line (center of the band), if it doesn't, it accrues a cost of 1
    int belongs = 1;
    for (auto &line : lines) {
      if ((radius > 0 && (line + mu).in_radius(point, radius)) || (radius <= 0 && (line + mu).contains(point))) {
        belongs = 0;
        break;
      }
    }
    cost += belongs;
  }
  return cost;
}

std::optional<std::vector<Line>>
CLDAlgorithmDiffLock::detect(std::map<uint32_t, std::shared_ptr<ITrajectoryWrapper>> &known_trajectories,
                             std::shared_ptr<CropLineDetectorConfig> config, std::stringstream &) {
  std::vector<Line> lines;

  if (config->get_static_bands().size() == 0) {
    LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: no static bands defined");
    return lines;
  }

  std::vector<double> points;
  for (auto &it : known_trajectories) {
    auto &c = it.second;
    if (c->duplicate_status() != trajectory::DuplicateStatus::kDuplicate) {
      points.push_back(c->get_last_x_mm());
    }
  }
  if (points.empty()) {
    if (!known_trajectories.empty()) {
      LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: no crops passed the banding threshold");
    }
    return lines;
  }

  double center = 2682; // avg x between predict2 and predict3
  double radius = 1143; // 45 inches in mm
  double precision = config->kde_precision();

  if (carbon::common::is_reaper()) {
    center = lib::common::robot_definition::RobotDefinition::get()
                 ->get_local_row_definition()
                 ->get_predict_space_center_mm();
  }

  auto static_bands = config->get_static_bands();

  double search_left_edge = center - radius;

  // start all the bands on the left edge
  double offset = static_bands[0].get_start_mm() - search_left_edge;
  for (int i = 0; i < static_cast<int>(static_bands.size()); ++i) {
    auto start = static_bands[i].get_start_mm() - offset;
    auto end = static_bands[i].get_end_mm() - offset;
    lines.push_back(Line(start, end));
  }

  double w = lines.back().get_end_mm() - lines.front().get_start_mm();
  double sweep_dist = 2 * radius - w;
  if (sweep_dist < 0) {
    // major error
    spdlog::error("CropLineDetector: sweep_dist is negative");
    return lines;
  }

  // compute the location of mu between a and b using a cost function
  // build a priority queue of tuples where the first element is the mu,
  // the second is the cost and the third is the number of times that cost has been seen consecutively
  // the priority queue is ordered by min cost and then by the max number of times that cost has been seen consecutively

  std::priority_queue<std::tuple<double, int, int>, std::vector<std::tuple<double, int, int>>,
                      std::function<bool(const std::tuple<double, int, int> &, const std::tuple<double, int, int> &)>>
      pq([](const std::tuple<double, int, int> &a, const std::tuple<double, int, int> &b) {
        if (std::get<1>(a) == std::get<1>(b)) {
          return std::get<2>(a) < std::get<2>(b);
        }
        return std::get<1>(a) > std::get<1>(b);
      });

  double seed_line_spacing_mm = config->seed_line_spacing_mm();
  double sweep_left_edge = center - sweep_dist / 2;

  double start_mu = 0;
  double mu_i = 0;
  int mu_count = 1;
  int prev_cost = -1;
  std::vector<float> x_offsets;
  std::vector<float> costs;
  for (; mu_i < sweep_dist; mu_i += precision) {
    int cost_i = get_cost(lines, points, mu_i, seed_line_spacing_mm / 2);
    x_offsets.push_back(static_cast<float>(mu_i + sweep_left_edge));
    costs.push_back(static_cast<float>(cost_i));
    if (cost_i != prev_cost) {
      if (prev_cost != -1) {
        pq.push(std::tuple<double, int, int>((start_mu + mu_i - precision) / 2, prev_cost, mu_count));
      }
      prev_cost = cost_i;
      start_mu = mu_i;
      mu_count = 1;
    } else {
      ++mu_count;
    }
  }
  // push the last cost
  pq.push(std::tuple<double, int, int>((start_mu + mu_i - precision) / 2, prev_cost, mu_count));

  if (pq.empty()) {
    LOG_WARN_EVERY_N_SECONDS(10, "CropLineDetector: pq is empty");
    return {};
  }

  double mu = std::get<0>(pq.top());
  int cost = std::get<1>(pq.top());
  for (auto &line : lines) {
    line += mu;
  }

  if (config->diagnostic_feature_enabled()) {
    auto snapshot = std::make_shared<::weed_tracking::CLDAlgorithmSnapshot>();
    copy_points_with_down_sampling(snapshot, x_offsets, costs, kDiffLockDownsampleFactor);
    snapshot->add_minimas_x(static_cast<float>(mu + sweep_left_edge));
    snapshot->add_minimas_y(static_cast<float>(cost));
    for (auto &line : lines) {
      snapshot->add_lines(static_cast<float>(line.get_center_mm()));
    }
    snapshot->set_timestamp_ms(maka_control_timestamp_ms());
    CLDDiagnosticsConnector::get().set_snapshot(snapshot);
  }

  return lines;
}

/*------------------------------------

    Helpers

-------------------------------------*/

inline double gaussian(double a, double x, double b, double c) {
  return a * pow(2.71828, (-pow(x - b, 2) / (2 * pow(c, 2))));
}

void squeeze(std::vector<double> &band_points, size_t &left, size_t &right, uint32_t count,
             double squeeze_distance_mm) {

  if (left >= band_points.size() || right >= band_points.size()) {
    return;
  }

  auto prev_dist = band_points[right] - band_points[left];

  while (left + count < right) {
    auto new_dist = band_points[right] - band_points[left + count];
    if (prev_dist - new_dist < squeeze_distance_mm) {
      break;
    }
    left++;
    prev_dist = band_points[right] - band_points[left];
  }

  while (right > left + count) {
    auto new_dist = band_points[right - count] - band_points[left];
    if (prev_dist - new_dist < squeeze_distance_mm) {
      break;
    }
    right--;
    prev_dist = band_points[right] - band_points[left];
  }
}

Band get_clamped_band(const Band &static_band, const Line &new_line, float dist) {
  float width = static_band.get_width_mm();
  float new_band_center = static_cast<float>(new_line.get_center_mm());
  float new_band_start = new_band_center - width / 2;
  float new_band_end = new_band_center + width / 2;

  float min_start_mm = static_band.get_start_mm() - dist;
  float max_start_mm = static_band.get_start_mm() + dist;
  if (new_band_start > max_start_mm) {
    new_band_start = max_start_mm;
  } else if (new_band_start < min_start_mm) {
    new_band_start = min_start_mm;
  }
  new_band_end = new_band_start + width;
  return Band(new_band_start, new_band_end);
}

void copy_points_with_down_sampling(std::shared_ptr<::weed_tracking::CLDAlgorithmSnapshot> snapshot,
                                    std::vector<float> &graph_points_x, std::vector<float> &graph_points_y,
                                    uint32_t downsample) {
  auto point_count = static_cast<int>(graph_points_x.size() / downsample);
  snapshot->mutable_graph_points_x()->Reserve(point_count);
  snapshot->mutable_graph_points_y()->Reserve(point_count);
  for (size_t i = 0; i < graph_points_x.size(); i += downsample) {
    snapshot->add_graph_points_x(graph_points_x[i]);
    snapshot->add_graph_points_y(graph_points_y[i]);
  }
}
} // namespace carbon::weed_tracking
