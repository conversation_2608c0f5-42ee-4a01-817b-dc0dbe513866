#include "weed_tracking/cpp/ingest_clients/crop_line_detector.hpp"
#include <gtest/gtest.h>
#include <vector>

using namespace carbon::weed_tracking;

class AddLinesIfDisappearedTest : public ::testing::Test {
protected:
  Line createLine(double start_mm) { return Line(start_mm, start_mm + band_width); }

  std::vector<Line> createStandardLines(const std::vector<double> &start_positions) {
    std::vector<Line> lines;
    for (size_t i = 0; i < start_positions.size(); ++i) {
      lines.push_back(createLine(start_positions[i]));
    }
    return lines;
  }

  void verifyLine(const Line &line, double expected_start, bool expected_synthetic) {
    EXPECT_DOUBLE_EQ(line.get_start_mm(), expected_start);
    EXPECT_DOUBLE_EQ(line.get_end_mm(), expected_start + band_width);
    EXPECT_EQ(line.is_synthetic(), expected_synthetic);
  }

  const double band_width = 100.0;
};

// ============================================================================
// Basic Functionality Tests
// ============================================================================

TEST_F(AddLinesIfDisappearedTest, NoLinesDisappear_ReturnsZero) {
  std::vector<Line> current_lines = createStandardLines({0.0, 300.0, 600.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), 3);
  // Verify no lines were marked as synthetic
  for (const auto &line : current_lines) {
    EXPECT_FALSE(line.is_synthetic());
  }
}

TEST_F(AddLinesIfDisappearedTest, AllLinesPresent_NoChanges) {
  std::vector<Line> current_lines = createStandardLines({0.0, 300.0, 600.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  auto original_size = current_lines.size();
  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), original_size);
}

TEST_F(AddLinesIfDisappearedTest, MoreCurrentThanPrevious_NoChanges) {
  std::vector<Line> current_lines = createStandardLines({0.0, 300.0, 600.0, 900.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 4;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), 4);
}

TEST_F(AddLinesIfDisappearedTest, FirstLineDisappears) {
  std::vector<Line> current_lines = createStandardLines({300.0, 600.0}); // Missing first line
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1);
  EXPECT_EQ(current_lines.size(), 3);

  // First line should be synthetic
  verifyLine(current_lines[0], 0.0, true);
  // Other lines should remain unchanged
  verifyLine(current_lines[1], 300.0, false);
  verifyLine(current_lines[2], 600.0, false);
}

TEST_F(AddLinesIfDisappearedTest, LastLineDisappears) {
  std::vector<Line> current_lines = createStandardLines({0.0, 300.0}); // Missing last line
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1);
  EXPECT_EQ(current_lines.size(), 3);

  // First two lines should remain unchanged
  verifyLine(current_lines[0], 0.0, false);
  verifyLine(current_lines[1], 300.0, false);
  // Last line should be synthetic
  verifyLine(current_lines[2], 600.0, true);
}

TEST_F(AddLinesIfDisappearedTest, MiddleLineDisappears) {
  std::vector<Line> current_lines = createStandardLines({0.0, 600.0}); // Missing middle line
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1);
  EXPECT_EQ(current_lines.size(), 3);

  // Lines should be in correct order
  verifyLine(current_lines[0], 0.0, false);
  verifyLine(current_lines[1], 300.0, true); // Synthetic middle line
  verifyLine(current_lines[2], 600.0, false);
}

TEST_F(AddLinesIfDisappearedTest, MultipleConsecutiveLinesDisappear) {
  std::vector<Line> current_lines = createStandardLines({0.0, 900.0}); // Missing lines 1 and 2
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0, 900.0});
  uint32_t max_lines = 4;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 2);
  EXPECT_EQ(current_lines.size(), 4);

  verifyLine(current_lines[0], 0.0, false);
  verifyLine(current_lines[1], 300.0, true); // Synthetic
  verifyLine(current_lines[2], 600.0, true); // Synthetic
  verifyLine(current_lines[3], 900.0, false);
}

TEST_F(AddLinesIfDisappearedTest, AllButOneLineDisappears) {
  std::vector<Line> current_lines = createStandardLines({300.0}); // Only middle line remains
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 2);
  EXPECT_EQ(current_lines.size(), 3);

  verifyLine(current_lines[0], 0.0, true);    // Synthetic
  verifyLine(current_lines[1], 300.0, false); // Original
  verifyLine(current_lines[2], 600.0, true);  // Synthetic
}

TEST_F(AddLinesIfDisappearedTest, EmptyCurrentLines_EmptyPreviousLines) {
  std::vector<Line> current_lines;
  std::vector<Line> previous_lines;
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), 0);
}

TEST_F(AddLinesIfDisappearedTest, EmptyCurrentLines_HasPreviousLines) {
  std::vector<Line> current_lines;
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 3); // Should add all previous lines when current is empty
  EXPECT_EQ(current_lines.size(), 3);
  verifyLine(current_lines[0], 0.0, true);
  verifyLine(current_lines[1], 300.0, true);
  verifyLine(current_lines[2], 600.0, true);
}

TEST_F(AddLinesIfDisappearedTest, SingleLineInCurrentAndPrevious) {
  std::vector<Line> current_lines = createStandardLines({0.0});
  std::vector<Line> previous_lines = createStandardLines({0.0});
  uint32_t max_lines = 1;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), 1);
  verifyLine(current_lines[0], 0.0, false);
}

TEST_F(AddLinesIfDisappearedTest, SingleLineDisappears) {
  std::vector<Line> current_lines;
  std::vector<Line> previous_lines = createStandardLines({0.0});
  uint32_t max_lines = 1;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1);
  EXPECT_EQ(current_lines.size(), 1);
  verifyLine(current_lines[0], 0.0, true);
}

TEST_F(AddLinesIfDisappearedTest, MaxLinesConstraint_StopsAdding) {
  std::vector<Line> current_lines = createStandardLines({0.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0, 900.0});
  uint32_t max_lines = 2; // Limit to 2 lines

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1); // Should only add one line due to max_lines constraint
  EXPECT_EQ(current_lines.size(), 2);
  EXPECT_LE(current_lines.size(), max_lines);
}

TEST_F(AddLinesIfDisappearedTest, MaxLinesAlreadyReached_NoAddition) {
  std::vector<Line> current_lines = createStandardLines({0.0, 300.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 2; // Already at max

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0);
  EXPECT_EQ(current_lines.size(), 2);
}

TEST_F(AddLinesIfDisappearedTest, LargeNumberOfLines) {
  std::vector<double> current_positions = {0.0, 600.0, 1200.0}; // Every other line
  std::vector<double> previous_positions;
  for (int i = 0; i < 20; ++i) {
    previous_positions.push_back(i * 300.0);
  }

  std::vector<Line> current_lines = createStandardLines(current_positions);
  std::vector<Line> previous_lines = createStandardLines(previous_positions);
  uint32_t max_lines = 10;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 7); // Should add 7 missing lines
  EXPECT_EQ(current_lines.size(), 10);

  for (size_t i = 0; i < current_lines.size(); ++i) {
    EXPECT_DOUBLE_EQ(current_lines[i].get_start_mm(), static_cast<double>(i) * 300.0);
  }
}

// ============================================================================
// Line Drift Tests
// ============================================================================

TEST_F(AddLinesIfDisappearedTest, SmallDrift_PartialOverlap_ShouldMatch) {
  std::vector<Line> current_lines = createStandardLines({25.0}); // 25-125
  std::vector<Line> previous_lines = createStandardLines({0.0}); // 0-100
  uint32_t max_lines = 2;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0); // No synthetic lines needed - they overlap
  EXPECT_EQ(current_lines.size(), 1);
  verifyLine(current_lines[0], 25.0, false); // Original current line unchanged
}

TEST_F(AddLinesIfDisappearedTest, LargeDrift_SmallOverlap_ShouldMatch) {
  std::vector<Line> current_lines = createStandardLines({90.0}); // 90-190
  std::vector<Line> previous_lines = createStandardLines({0.0}); // 0-100
  uint32_t max_lines = 2;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0); // No synthetic lines needed - they still overlap
  EXPECT_EQ(current_lines.size(), 1);
  verifyLine(current_lines[0], 90.0, false);
}

TEST_F(AddLinesIfDisappearedTest, MixedDrift_SomeOverlapSomeNot) {
  std::vector<Line> current_lines = createStandardLines({25.0, 650.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 4;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1); // Should add one synthetic line for the missing middle line
  EXPECT_EQ(current_lines.size(), 3);

  // Should be sorted by start position
  verifyLine(current_lines[0], 25.0, false);  // Drifted first line
  verifyLine(current_lines[1], 300.0, true);  // Synthetic middle line
  verifyLine(current_lines[2], 650.0, false); // Drifted third line
}

TEST_F(AddLinesIfDisappearedTest, LineReordering_DriftCausesPositionSwap) {
  std::vector<Line> current_lines;
  current_lines.push_back(createLine(150.0)); // 150-250
  current_lines.push_back(createLine(50.0));  // 50-150

  std::vector<Line> previous_lines = createStandardLines({0.0, 200.0}); // 0-100, 200-300
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0); // Both should match despite reordering
  EXPECT_EQ(current_lines.size(), 2);

  // Lines should be sorted by start position after insertion
  verifyLine(current_lines[0], 50.0, false);  // Was second, now first
  verifyLine(current_lines[1], 150.0, false); // Was first, now second
}

TEST_F(AddLinesIfDisappearedTest, ProgressiveDrift_AllLinesShiftSameDirection) {
  std::vector<Line> current_lines = createStandardLines({50.0, 350.0, 650.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 4;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0); // All should match due to overlap
  EXPECT_EQ(current_lines.size(), 3);
  verifyLine(current_lines[0], 50.0, false);
  verifyLine(current_lines[1], 350.0, false);
  verifyLine(current_lines[2], 650.0, false);
}

TEST_F(AddLinesIfDisappearedTest, NonUniformDrift_DifferentAmounts) {
  std::vector<Line> current_lines = createStandardLines({10.0, 280.0, 650.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 300.0, 600.0});
  uint32_t max_lines = 4;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 0); // All should match due to overlap
  EXPECT_EQ(current_lines.size(), 3);
  verifyLine(current_lines[0], 10.0, false);
  verifyLine(current_lines[1], 280.0, false);
  verifyLine(current_lines[2], 650.0, false);
}

TEST_F(AddLinesIfDisappearedTest, JustTouchingLines_MinimalOverlap) {
  std::vector<Line> current_lines = createStandardLines({100.0}); // 100-200
  std::vector<Line> previous_lines = createStandardLines({0.0});  // 0-100
  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1); // No overlap, should add synthetic line
  EXPECT_EQ(current_lines.size(), 2);
  verifyLine(current_lines[0], 0.0, true);    // Synthetic
  verifyLine(current_lines[1], 100.0, false); // Original
}

TEST_F(AddLinesIfDisappearedTest, MultipleDirectionsOfDrift) {
  std::vector<Line> current_lines = createStandardLines({50.0, 450.0});
  std::vector<Line> previous_lines = createStandardLines({0.0, 200.0, 400.0, 600.0});
  uint32_t max_lines = 6;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 2); // Should add 2 synthetic lines
  EXPECT_EQ(current_lines.size(), 4);

  // Should be sorted by start position
  verifyLine(current_lines[0], 50.0, false);  // Drifted first line
  verifyLine(current_lines[1], 200.0, true);  // Synthetic second line
  verifyLine(current_lines[2], 450.0, false); // Drifted third line
  verifyLine(current_lines[3], 600.0, true);  // Drifted fourth line
}

TEST_F(AddLinesIfDisappearedTest, MaxLinesConstraint_WithDrift) {
  std::vector<Line> current_lines = createStandardLines({50.0});               // One drifted line
  std::vector<Line> previous_lines = createStandardLines({0.0, 200.0, 400.0}); // Three previous
  uint32_t max_lines = 2;                                                      // Constraint limits to 2 total

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(result, 1); // Should add only 1 synthetic line due to constraint
  EXPECT_EQ(current_lines.size(), 2);
  EXPECT_LE(current_lines.size(), max_lines);
}

// ============================================================================
// Overlapping Bands Tests
// ============================================================================

TEST_F(AddLinesIfDisappearedTest, OverlappingCurrentBands) {
  std::vector<Line> current_lines;
  current_lines.push_back(createLine(0.0));   // Band 0: 0-100
  current_lines.push_back(createLine(80.0));  // Band 1: 80-180 (overlaps with other bands)
  current_lines.push_back(createLine(160.0)); // Band 2: 160-260 (missing band 1)

  std::vector<Line> previous_lines;
  previous_lines.push_back(createLine(0.0));   // Band 0: 0-100
  previous_lines.push_back(createLine(160.0)); // Band 2: 160-260

  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(current_lines.size(), 3);
  EXPECT_EQ(result, 0);
}

// TODO(sswigart): The current implementation does not support re-adding overlapping bands, but this
// can cause problems for customers specifying bands over multiple seedlines on the same bed top.
TEST_F(AddLinesIfDisappearedTest, OverlappingPreviousBands_MissingMiddleBand) {
  std::vector<Line> current_lines;
  current_lines.push_back(createLine(0.0));   // Band 0: 0-100
  current_lines.push_back(createLine(160.0)); // Band 2: 160-260 (missing band 1)

  std::vector<Line> previous_lines;
  previous_lines.push_back(createLine(0.0));   // Band 0: 0-100
  previous_lines.push_back(createLine(80.0));  // Band 1: 80-180 (overlaps both neighbors)
  previous_lines.push_back(createLine(160.0)); // Band 2: 160-260

  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(current_lines.size(), 2);
  EXPECT_EQ(result, 0);
  verifyLine(current_lines[0], 0.0, false);   // Drifted first line
  verifyLine(current_lines[1], 160.0, false); // Synthetic second line
}

TEST_F(AddLinesIfDisappearedTest, OverlappingCurrentAndPreviousBandsWithDrift) {
  std::vector<Line> current_lines;
  current_lines.push_back(createLine(0.0));   // Band 0: 0-100
  current_lines.push_back(createLine(80.0));  // Band 1: 80-180 (overlaps both neighbors)
  current_lines.push_back(createLine(160.0)); // Band 2: 160-260

  std::vector<Line> previous_lines;
  previous_lines.push_back(createLine(5.0));   // Band 0: 5-105
  previous_lines.push_back(createLine(90.0));  // Band 1: 90-190 (overlaps both neighbors)
  previous_lines.push_back(createLine(180.0)); // Band 2: 180-280

  uint32_t max_lines = 3;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(current_lines.size(), 3);
  EXPECT_EQ(result, 0);
  verifyLine(current_lines[0], 0.0, false);   // Drifted first line
  verifyLine(current_lines[1], 80.0, false);  // Drifted second line
  verifyLine(current_lines[2], 160.0, false); // Drifted third line
}

TEST_F(AddLinesIfDisappearedTest, OverlappingPreviousBands_CompleteOverlap) {
  std::vector<Line> current_lines;
  current_lines.push_back(createLine(100.0)); // Band 0: 100-200

  std::vector<Line> previous_lines;
  previous_lines.push_back(createLine(100.0)); // Band 0: 100-200
  previous_lines.push_back(createLine(100.0)); // Band 1: 100-200 (identical position)

  uint32_t max_lines = 2;

  int result = add_lines_if_disappeared(current_lines, previous_lines, max_lines);

  EXPECT_EQ(current_lines.size(), 1);
  EXPECT_EQ(result, 0);
}