// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package services

import (
	"github.com/carbonrobotics/robot/golang/lib/data_upload_manager"
	mock "github.com/stretchr/testify/mock"
)

// NewMockDUMEmergencyClient creates a new instance of MockDUMEmergencyClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockDUMEmergencyClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockDUMEmergencyClient {
	mock := &MockDUMEmergencyClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockDUMEmergencyClient is an autogenerated mock type for the DUMEmergencyClient type
type MockDUMEmergencyClient struct {
	mock.Mock
}

type MockDUMEmergencyClient_Expecter struct {
	mock *mock.Mock
}

func (_m *MockDUMEmergencyClient) EXPECT() *MockDUMEmergencyClient_Expecter {
	return &MockDUMEmergencyClient_Expecter{mock: &_m.Mock}
}

// CompleteDataCaptureSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) CompleteDataCaptureSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for CompleteDataCaptureSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_CompleteDataCaptureSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'CompleteDataCaptureSession'
type MockDUMEmergencyClient_CompleteDataCaptureSession_Call struct {
	*mock.Call
}

// CompleteDataCaptureSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) CompleteDataCaptureSession() *MockDUMEmergencyClient_CompleteDataCaptureSession_Call {
	return &MockDUMEmergencyClient_CompleteDataCaptureSession_Call{Call: _e.mock.On("CompleteDataCaptureSession")}
}

func (_c *MockDUMEmergencyClient_CompleteDataCaptureSession_Call) Run(run func()) *MockDUMEmergencyClient_CompleteDataCaptureSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_CompleteDataCaptureSession_Call) Return(err error) *MockDUMEmergencyClient_CompleteDataCaptureSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_CompleteDataCaptureSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_CompleteDataCaptureSession_Call {
	_c.Call.Return(run)
	return _c
}

// GetCaptureProgress provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) GetCaptureProgress() (data_upload_manager.CaptureProgress, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetCaptureProgress")
	}

	var r0 data_upload_manager.CaptureProgress
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (data_upload_manager.CaptureProgress, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() data_upload_manager.CaptureProgress); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(data_upload_manager.CaptureProgress)
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDUMEmergencyClient_GetCaptureProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCaptureProgress'
type MockDUMEmergencyClient_GetCaptureProgress_Call struct {
	*mock.Call
}

// GetCaptureProgress is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) GetCaptureProgress() *MockDUMEmergencyClient_GetCaptureProgress_Call {
	return &MockDUMEmergencyClient_GetCaptureProgress_Call{Call: _e.mock.On("GetCaptureProgress")}
}

func (_c *MockDUMEmergencyClient_GetCaptureProgress_Call) Run(run func()) *MockDUMEmergencyClient_GetCaptureProgress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_GetCaptureProgress_Call) Return(captureProgress data_upload_manager.CaptureProgress, err error) *MockDUMEmergencyClient_GetCaptureProgress_Call {
	_c.Call.Return(captureProgress, err)
	return _c
}

func (_c *MockDUMEmergencyClient_GetCaptureProgress_Call) RunAndReturn(run func() (data_upload_manager.CaptureProgress, error)) *MockDUMEmergencyClient_GetCaptureProgress_Call {
	_c.Call.Return(run)
	return _c
}

// GetRegularCaptureStatus provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) GetRegularCaptureStatus() (data_upload_manager.RegularCaptureStatus, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetRegularCaptureStatus")
	}

	var r0 data_upload_manager.RegularCaptureStatus
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (data_upload_manager.RegularCaptureStatus, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() data_upload_manager.RegularCaptureStatus); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(data_upload_manager.RegularCaptureStatus)
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDUMEmergencyClient_GetRegularCaptureStatus_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetRegularCaptureStatus'
type MockDUMEmergencyClient_GetRegularCaptureStatus_Call struct {
	*mock.Call
}

// GetRegularCaptureStatus is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) GetRegularCaptureStatus() *MockDUMEmergencyClient_GetRegularCaptureStatus_Call {
	return &MockDUMEmergencyClient_GetRegularCaptureStatus_Call{Call: _e.mock.On("GetRegularCaptureStatus")}
}

func (_c *MockDUMEmergencyClient_GetRegularCaptureStatus_Call) Run(run func()) *MockDUMEmergencyClient_GetRegularCaptureStatus_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_GetRegularCaptureStatus_Call) Return(regularCaptureStatus data_upload_manager.RegularCaptureStatus, err error) *MockDUMEmergencyClient_GetRegularCaptureStatus_Call {
	_c.Call.Return(regularCaptureStatus, err)
	return _c
}

func (_c *MockDUMEmergencyClient_GetRegularCaptureStatus_Call) RunAndReturn(run func() (data_upload_manager.RegularCaptureStatus, error)) *MockDUMEmergencyClient_GetRegularCaptureStatus_Call {
	_c.Call.Return(run)
	return _c
}

// GetSessions provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) GetSessions() ([]data_upload_manager.Session, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetSessions")
	}

	var r0 []data_upload_manager.Session
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() ([]data_upload_manager.Session, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() []data_upload_manager.Session); ok {
		r0 = returnFunc()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]data_upload_manager.Session)
		}
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDUMEmergencyClient_GetSessions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetSessions'
type MockDUMEmergencyClient_GetSessions_Call struct {
	*mock.Call
}

// GetSessions is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) GetSessions() *MockDUMEmergencyClient_GetSessions_Call {
	return &MockDUMEmergencyClient_GetSessions_Call{Call: _e.mock.On("GetSessions")}
}

func (_c *MockDUMEmergencyClient_GetSessions_Call) Run(run func()) *MockDUMEmergencyClient_GetSessions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_GetSessions_Call) Return(sessions []data_upload_manager.Session, err error) *MockDUMEmergencyClient_GetSessions_Call {
	_c.Call.Return(sessions, err)
	return _c
}

func (_c *MockDUMEmergencyClient_GetSessions_Call) RunAndReturn(run func() ([]data_upload_manager.Session, error)) *MockDUMEmergencyClient_GetSessions_Call {
	_c.Call.Return(run)
	return _c
}

// GetUploadProgress provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) GetUploadProgress() (data_upload_manager.UploadProgress, error) {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetUploadProgress")
	}

	var r0 data_upload_manager.UploadProgress
	var r1 error
	if returnFunc, ok := ret.Get(0).(func() (data_upload_manager.UploadProgress, error)); ok {
		return returnFunc()
	}
	if returnFunc, ok := ret.Get(0).(func() data_upload_manager.UploadProgress); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(data_upload_manager.UploadProgress)
	}
	if returnFunc, ok := ret.Get(1).(func() error); ok {
		r1 = returnFunc()
	} else {
		r1 = ret.Error(1)
	}
	return r0, r1
}

// MockDUMEmergencyClient_GetUploadProgress_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetUploadProgress'
type MockDUMEmergencyClient_GetUploadProgress_Call struct {
	*mock.Call
}

// GetUploadProgress is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) GetUploadProgress() *MockDUMEmergencyClient_GetUploadProgress_Call {
	return &MockDUMEmergencyClient_GetUploadProgress_Call{Call: _e.mock.On("GetUploadProgress")}
}

func (_c *MockDUMEmergencyClient_GetUploadProgress_Call) Run(run func()) *MockDUMEmergencyClient_GetUploadProgress_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_GetUploadProgress_Call) Return(uploadProgress data_upload_manager.UploadProgress, err error) *MockDUMEmergencyClient_GetUploadProgress_Call {
	_c.Call.Return(uploadProgress, err)
	return _c
}

func (_c *MockDUMEmergencyClient_GetUploadProgress_Call) RunAndReturn(run func() (data_upload_manager.UploadProgress, error)) *MockDUMEmergencyClient_GetUploadProgress_Call {
	_c.Call.Return(run)
	return _c
}

// PauseBackgroundDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) PauseBackgroundDataUploadSession(name string) error {
	ret := _mock.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for PauseBackgroundDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PauseBackgroundDataUploadSession'
type MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call struct {
	*mock.Call
}

// PauseBackgroundDataUploadSession is a helper method to define mock.On call
//   - name string
func (_e *MockDUMEmergencyClient_Expecter) PauseBackgroundDataUploadSession(name interface{}) *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call {
	return &MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call{Call: _e.mock.On("PauseBackgroundDataUploadSession", name)}
}

func (_c *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call) Run(run func(name string)) *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call) RunAndReturn(run func(name string) error) *MockDUMEmergencyClient_PauseBackgroundDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// PauseDataCaptureSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) PauseDataCaptureSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for PauseDataCaptureSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_PauseDataCaptureSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PauseDataCaptureSession'
type MockDUMEmergencyClient_PauseDataCaptureSession_Call struct {
	*mock.Call
}

// PauseDataCaptureSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) PauseDataCaptureSession() *MockDUMEmergencyClient_PauseDataCaptureSession_Call {
	return &MockDUMEmergencyClient_PauseDataCaptureSession_Call{Call: _e.mock.On("PauseDataCaptureSession")}
}

func (_c *MockDUMEmergencyClient_PauseDataCaptureSession_Call) Run(run func()) *MockDUMEmergencyClient_PauseDataCaptureSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_PauseDataCaptureSession_Call) Return(err error) *MockDUMEmergencyClient_PauseDataCaptureSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_PauseDataCaptureSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_PauseDataCaptureSession_Call {
	_c.Call.Return(run)
	return _c
}

// PauseDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) PauseDataUploadSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for PauseDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_PauseDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'PauseDataUploadSession'
type MockDUMEmergencyClient_PauseDataUploadSession_Call struct {
	*mock.Call
}

// PauseDataUploadSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) PauseDataUploadSession() *MockDUMEmergencyClient_PauseDataUploadSession_Call {
	return &MockDUMEmergencyClient_PauseDataUploadSession_Call{Call: _e.mock.On("PauseDataUploadSession")}
}

func (_c *MockDUMEmergencyClient_PauseDataUploadSession_Call) Run(run func()) *MockDUMEmergencyClient_PauseDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_PauseDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_PauseDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_PauseDataUploadSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_PauseDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// ResumeBackgroundDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) ResumeBackgroundDataUploadSession(name string) error {
	ret := _mock.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for ResumeBackgroundDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResumeBackgroundDataUploadSession'
type MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call struct {
	*mock.Call
}

// ResumeBackgroundDataUploadSession is a helper method to define mock.On call
//   - name string
func (_e *MockDUMEmergencyClient_Expecter) ResumeBackgroundDataUploadSession(name interface{}) *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call {
	return &MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call{Call: _e.mock.On("ResumeBackgroundDataUploadSession", name)}
}

func (_c *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call) Run(run func(name string)) *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call) RunAndReturn(run func(name string) error) *MockDUMEmergencyClient_ResumeBackgroundDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// ResumeDataCaptureSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) ResumeDataCaptureSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for ResumeDataCaptureSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_ResumeDataCaptureSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResumeDataCaptureSession'
type MockDUMEmergencyClient_ResumeDataCaptureSession_Call struct {
	*mock.Call
}

// ResumeDataCaptureSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) ResumeDataCaptureSession() *MockDUMEmergencyClient_ResumeDataCaptureSession_Call {
	return &MockDUMEmergencyClient_ResumeDataCaptureSession_Call{Call: _e.mock.On("ResumeDataCaptureSession")}
}

func (_c *MockDUMEmergencyClient_ResumeDataCaptureSession_Call) Run(run func()) *MockDUMEmergencyClient_ResumeDataCaptureSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeDataCaptureSession_Call) Return(err error) *MockDUMEmergencyClient_ResumeDataCaptureSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeDataCaptureSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_ResumeDataCaptureSession_Call {
	_c.Call.Return(run)
	return _c
}

// ResumeDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) ResumeDataUploadSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for ResumeDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_ResumeDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ResumeDataUploadSession'
type MockDUMEmergencyClient_ResumeDataUploadSession_Call struct {
	*mock.Call
}

// ResumeDataUploadSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) ResumeDataUploadSession() *MockDUMEmergencyClient_ResumeDataUploadSession_Call {
	return &MockDUMEmergencyClient_ResumeDataUploadSession_Call{Call: _e.mock.On("ResumeDataUploadSession")}
}

func (_c *MockDUMEmergencyClient_ResumeDataUploadSession_Call) Run(run func()) *MockDUMEmergencyClient_ResumeDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_ResumeDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_ResumeDataUploadSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_ResumeDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// SnapImages provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) SnapImages(crop string, cropID string, camId string, rowInd uint32, timestampMs int64, sessionName string) error {
	ret := _mock.Called(crop, cropID, camId, rowInd, timestampMs, sessionName)

	if len(ret) == 0 {
		panic("no return value specified for SnapImages")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string, string, uint32, int64, string) error); ok {
		r0 = returnFunc(crop, cropID, camId, rowInd, timestampMs, sessionName)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_SnapImages_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'SnapImages'
type MockDUMEmergencyClient_SnapImages_Call struct {
	*mock.Call
}

// SnapImages is a helper method to define mock.On call
//   - crop string
//   - cropID string
//   - camId string
//   - rowInd uint32
//   - timestampMs int64
//   - sessionName string
func (_e *MockDUMEmergencyClient_Expecter) SnapImages(crop interface{}, cropID interface{}, camId interface{}, rowInd interface{}, timestampMs interface{}, sessionName interface{}) *MockDUMEmergencyClient_SnapImages_Call {
	return &MockDUMEmergencyClient_SnapImages_Call{Call: _e.mock.On("SnapImages", crop, cropID, camId, rowInd, timestampMs, sessionName)}
}

func (_c *MockDUMEmergencyClient_SnapImages_Call) Run(run func(crop string, cropID string, camId string, rowInd uint32, timestampMs int64, sessionName string)) *MockDUMEmergencyClient_SnapImages_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 uint32
		if args[3] != nil {
			arg3 = args[3].(uint32)
		}
		var arg4 int64
		if args[4] != nil {
			arg4 = args[4].(int64)
		}
		var arg5 string
		if args[5] != nil {
			arg5 = args[5].(string)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_SnapImages_Call) Return(err error) *MockDUMEmergencyClient_SnapImages_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_SnapImages_Call) RunAndReturn(run func(crop string, cropID string, camId string, rowInd uint32, timestampMs int64, sessionName string) error) *MockDUMEmergencyClient_SnapImages_Call {
	_c.Call.Return(run)
	return _c
}

// StartBackgroundDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StartBackgroundDataUploadSession(method string, name string) error {
	ret := _mock.Called(method, name)

	if len(ret) == 0 {
		panic("no return value specified for StartBackgroundDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = returnFunc(method, name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartBackgroundDataUploadSession'
type MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call struct {
	*mock.Call
}

// StartBackgroundDataUploadSession is a helper method to define mock.On call
//   - method string
//   - name string
func (_e *MockDUMEmergencyClient_Expecter) StartBackgroundDataUploadSession(method interface{}, name interface{}) *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call {
	return &MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call{Call: _e.mock.On("StartBackgroundDataUploadSession", method, name)}
}

func (_c *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call) Run(run func(method string, name string)) *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 string
		if args[1] != nil {
			arg1 = args[1].(string)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call) RunAndReturn(run func(method string, name string) error) *MockDUMEmergencyClient_StartBackgroundDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// StartDataCaptureSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StartDataCaptureSession(sessionName string, captureRate float64, crop string, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool) error {
	ret := _mock.Called(sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture)

	if len(ret) == 0 {
		panic("no return value specified for StartDataCaptureSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string, float64, string, string, bool, uint32, string, bool) error); ok {
		r0 = returnFunc(sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StartDataCaptureSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartDataCaptureSession'
type MockDUMEmergencyClient_StartDataCaptureSession_Call struct {
	*mock.Call
}

// StartDataCaptureSession is a helper method to define mock.On call
//   - sessionName string
//   - captureRate float64
//   - crop string
//   - cropID string
//   - useLatest bool
//   - rowInd uint32
//   - camId string
//   - snapCapture bool
func (_e *MockDUMEmergencyClient_Expecter) StartDataCaptureSession(sessionName interface{}, captureRate interface{}, crop interface{}, cropID interface{}, useLatest interface{}, rowInd interface{}, camId interface{}, snapCapture interface{}) *MockDUMEmergencyClient_StartDataCaptureSession_Call {
	return &MockDUMEmergencyClient_StartDataCaptureSession_Call{Call: _e.mock.On("StartDataCaptureSession", sessionName, captureRate, crop, cropID, useLatest, rowInd, camId, snapCapture)}
}

func (_c *MockDUMEmergencyClient_StartDataCaptureSession_Call) Run(run func(sessionName string, captureRate float64, crop string, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool)) *MockDUMEmergencyClient_StartDataCaptureSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		var arg1 float64
		if args[1] != nil {
			arg1 = args[1].(float64)
		}
		var arg2 string
		if args[2] != nil {
			arg2 = args[2].(string)
		}
		var arg3 string
		if args[3] != nil {
			arg3 = args[3].(string)
		}
		var arg4 bool
		if args[4] != nil {
			arg4 = args[4].(bool)
		}
		var arg5 uint32
		if args[5] != nil {
			arg5 = args[5].(uint32)
		}
		var arg6 string
		if args[6] != nil {
			arg6 = args[6].(string)
		}
		var arg7 bool
		if args[7] != nil {
			arg7 = args[7].(bool)
		}
		run(
			arg0,
			arg1,
			arg2,
			arg3,
			arg4,
			arg5,
			arg6,
			arg7,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StartDataCaptureSession_Call) Return(err error) *MockDUMEmergencyClient_StartDataCaptureSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StartDataCaptureSession_Call) RunAndReturn(run func(sessionName string, captureRate float64, crop string, cropID string, useLatest bool, rowInd uint32, camId string, snapCapture bool) error) *MockDUMEmergencyClient_StartDataCaptureSession_Call {
	_c.Call.Return(run)
	return _c
}

// StartDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StartDataUploadSession(method string) error {
	ret := _mock.Called(method)

	if len(ret) == 0 {
		panic("no return value specified for StartDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(method)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StartDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StartDataUploadSession'
type MockDUMEmergencyClient_StartDataUploadSession_Call struct {
	*mock.Call
}

// StartDataUploadSession is a helper method to define mock.On call
//   - method string
func (_e *MockDUMEmergencyClient_Expecter) StartDataUploadSession(method interface{}) *MockDUMEmergencyClient_StartDataUploadSession_Call {
	return &MockDUMEmergencyClient_StartDataUploadSession_Call{Call: _e.mock.On("StartDataUploadSession", method)}
}

func (_c *MockDUMEmergencyClient_StartDataUploadSession_Call) Run(run func(method string)) *MockDUMEmergencyClient_StartDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StartDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_StartDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StartDataUploadSession_Call) RunAndReturn(run func(method string) error) *MockDUMEmergencyClient_StartDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// StopBackgroundDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StopBackgroundDataUploadSession(name string) error {
	ret := _mock.Called(name)

	if len(ret) == 0 {
		panic("no return value specified for StopBackgroundDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func(string) error); ok {
		r0 = returnFunc(name)
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopBackgroundDataUploadSession'
type MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call struct {
	*mock.Call
}

// StopBackgroundDataUploadSession is a helper method to define mock.On call
//   - name string
func (_e *MockDUMEmergencyClient_Expecter) StopBackgroundDataUploadSession(name interface{}) *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call {
	return &MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call{Call: _e.mock.On("StopBackgroundDataUploadSession", name)}
}

func (_c *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call) Run(run func(name string)) *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call) RunAndReturn(run func(name string) error) *MockDUMEmergencyClient_StopBackgroundDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}

// StopDataCaptureSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StopDataCaptureSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for StopDataCaptureSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StopDataCaptureSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopDataCaptureSession'
type MockDUMEmergencyClient_StopDataCaptureSession_Call struct {
	*mock.Call
}

// StopDataCaptureSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) StopDataCaptureSession() *MockDUMEmergencyClient_StopDataCaptureSession_Call {
	return &MockDUMEmergencyClient_StopDataCaptureSession_Call{Call: _e.mock.On("StopDataCaptureSession")}
}

func (_c *MockDUMEmergencyClient_StopDataCaptureSession_Call) Run(run func()) *MockDUMEmergencyClient_StopDataCaptureSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StopDataCaptureSession_Call) Return(err error) *MockDUMEmergencyClient_StopDataCaptureSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StopDataCaptureSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_StopDataCaptureSession_Call {
	_c.Call.Return(run)
	return _c
}

// StopDataUploadSession provides a mock function for the type MockDUMEmergencyClient
func (_mock *MockDUMEmergencyClient) StopDataUploadSession() error {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for StopDataUploadSession")
	}

	var r0 error
	if returnFunc, ok := ret.Get(0).(func() error); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Error(0)
	}
	return r0
}

// MockDUMEmergencyClient_StopDataUploadSession_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'StopDataUploadSession'
type MockDUMEmergencyClient_StopDataUploadSession_Call struct {
	*mock.Call
}

// StopDataUploadSession is a helper method to define mock.On call
func (_e *MockDUMEmergencyClient_Expecter) StopDataUploadSession() *MockDUMEmergencyClient_StopDataUploadSession_Call {
	return &MockDUMEmergencyClient_StopDataUploadSession_Call{Call: _e.mock.On("StopDataUploadSession")}
}

func (_c *MockDUMEmergencyClient_StopDataUploadSession_Call) Run(run func()) *MockDUMEmergencyClient_StopDataUploadSession_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockDUMEmergencyClient_StopDataUploadSession_Call) Return(err error) *MockDUMEmergencyClient_StopDataUploadSession_Call {
	_c.Call.Return(err)
	return _c
}

func (_c *MockDUMEmergencyClient_StopDataUploadSession_Call) RunAndReturn(run func() error) *MockDUMEmergencyClient_StopDataUploadSession_Call {
	_c.Call.Return(run)
	return _c
}
