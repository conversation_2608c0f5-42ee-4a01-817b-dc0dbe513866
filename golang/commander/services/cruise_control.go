package services

import (
	"context"

	"github.com/carbonrobotics/robot/golang/commander/state"
	fepb "github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"google.golang.org/grpc"
)

type CruiseControlService struct {
	fepb.UnimplementedCruiseControlServiceServer
	cruiseState *state.CruiseControlState
}

func NewCruiseControlService(grpcServer *grpc.Server, cruiseState *state.CruiseControlState) *CruiseControlService {
	service := &CruiseControlService{
		cruiseState: cruiseState,
	}
	fepb.RegisterCruiseControlServiceServer(grpcServer, service)
	return service
}

func (s CruiseControlService) GetNextCruiseState(ctx context.Context, req *fepb.Timestamp) (*fepb.GetNextCruiseStateResponse, error) {
	resp := &fepb.GetNextCruiseStateResponse{}
	s.cruiseState.ReadOnNext(ctx, req.GetTimestampMs(), func() {
		resp.Ts = &fepb.Timestamp{
			TimestampMs: s.cruiseState.GetTimestampMs(),
		}
		resp.State = &fepb.CruiseState{
			CommandedVelocity: s.cruiseState.CommandedVelocityMph,
			Blocker:           s.cruiseState.CruiseBlocker,
		}
	})
	return resp, nil
}
