package state

import (
	"testing"
	"time"

	"github.com/carbonrobotics/robot/golang/commander/alarms"
	"github.com/carbonrobotics/robot/golang/commander/state/mocks"
	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Test double for AlarmState that allows mocking its embedded ManagedStateImpl behavior.
type TestAlarmState struct {
	*AlarmState
	MockManagedState *mocks.ManagedState
}

func NewTestAlarmState(t *testing.T) *TestAlarmState {
	mockManaged := mocks.NewManagedState(t)
	mockRedis := mocks.NewRedisClient(t)

	as := &AlarmState{
		Alarms:                 make(map[AlarmType]map[string]*alarms.Alarm),
		alarmTypesByIdentifier: make(map[string]AlarmType),
		AlarmLog:               make([]*frontend.AlarmRow, 0),
		AlarmLogMap:            make(map[AlarmLogKey]*frontend.AlarmRow),
		redis:                  mockRedis,
		configNodeCommander:    &config.ConfigTree{}, // Empty config tree
	}
	as.initialize()

	return &TestAlarmState{
		AlarmState:       as,
		MockManagedState: mockManaged,
	}
}

// Override ReadOnCurrent to use the mock.
func (ts *TestAlarmState) ReadOnCurrent(readOnCurrent func()) {
	ts.MockManagedState.ReadOnCurrent(readOnCurrent)
}

// Override WriteOnCurrent to use the mock.
func (ts *TestAlarmState) WriteOnCurrent(writeOnCurrent func()) {
	ts.MockManagedState.WriteOnCurrent(writeOnCurrent)
}

func NewTestAlarmWatcher(alarmState *AlarmState) *AlarmWatcher {
	action := &AlarmWatcher{
		alarmState: alarmState,
	}
	action.triggerChannel = make(chan bool)
	return action
}

func TestAlarmWatcher_getNewlyStableAlarms(t *testing.T) {
	nowMs := time.Now().UnixMilli()
	testFlickerFilterS := uint64(10) // 10 seconds

	tests := []struct {
		name               string
		setupAlarmState    func(*AlarmState)
		expectedAlarms     map[string]*alarms.Alarm
		expectedAlarmTypes map[string]AlarmType
	}{
		{
			name: "Alarm is invalid but within flicker filter period (flickerFilterS=10)",
			setupAlarmState: func(as *AlarmState) {
				alarm1 := &alarms.Alarm{Identifier: "alarm1", Valid: false, TimestampMs: nowMs - 5*1000} // 5 seconds old
				as.Alarms[AlarmTypeSoftware] = map[string]*alarms.Alarm{"alarm1": alarm1}
				as.alarmTypesByIdentifier["alarm1"] = AlarmTypeSoftware
			},
			expectedAlarms:     map[string]*alarms.Alarm{},
			expectedAlarmTypes: map[string]AlarmType{},
		},
		{
			name: "Alarm is invalid and past flicker filter period (flickerFilterS=10)",
			setupAlarmState: func(as *AlarmState) {
				alarm2 := &alarms.Alarm{Identifier: "alarm2", Valid: false, TimestampMs: nowMs - 15*1000} // 15 seconds old, past 10s filter
				as.Alarms[AlarmTypeSoftware] = map[string]*alarms.Alarm{"alarm2": alarm2}
				as.alarmTypesByIdentifier["alarm2"] = AlarmTypeSoftware
			},
			expectedAlarms:     map[string]*alarms.Alarm{"alarm2": {Identifier: "alarm2", Valid: false, TimestampMs: nowMs - 15*1000}},
			expectedAlarmTypes: map[string]AlarmType{"alarm2": AlarmTypeSoftware},
		},
		{
			name: "Alarm is already valid (should not be considered newly stable)",
			setupAlarmState: func(as *AlarmState) {
				alarm3 := &alarms.Alarm{Identifier: "alarm3", Valid: true, TimestampMs: nowMs - 20*1000} // 20 seconds old, already valid
				as.Alarms[AlarmTypeSoftware] = map[string]*alarms.Alarm{"alarm3": alarm3}
				as.alarmTypesByIdentifier["alarm3"] = AlarmTypeSoftware
			},
			expectedAlarms:     map[string]*alarms.Alarm{},
			expectedAlarmTypes: map[string]AlarmType{},
		},
		{
			name: "Multiple alarms, some stable, some not (flickerFilterS=10)",
			setupAlarmState: func(as *AlarmState) {
				alarmA := &alarms.Alarm{Identifier: "alarmA", Valid: false, TimestampMs: nowMs - 5*1000}  // Within filter
				alarmB := &alarms.Alarm{Identifier: "alarmB", Valid: false, TimestampMs: nowMs - 15*1000} // Past filter
				alarmC := &alarms.Alarm{Identifier: "alarmC", Valid: true, TimestampMs: nowMs - 25*1000}  // Already valid
				as.Alarms[AlarmTypeSoftware] = map[string]*alarms.Alarm{
					"alarmA": alarmA,
					"alarmB": alarmB,
					"alarmC": alarmC,
				}
				as.alarmTypesByIdentifier["alarmA"] = AlarmTypeSoftware
				as.alarmTypesByIdentifier["alarmB"] = AlarmTypeSoftware
				as.alarmTypesByIdentifier["alarmC"] = AlarmTypeSoftware
			},
			expectedAlarms:     map[string]*alarms.Alarm{"alarmB": {Identifier: "alarmB", Valid: false, TimestampMs: nowMs - 15*1000}},
			expectedAlarmTypes: map[string]AlarmType{"alarmB": AlarmTypeSoftware},
		},
		{
			name: "No alarms",
			setupAlarmState: func(as *AlarmState) {
				as.Alarms = make(map[AlarmType]map[string]*alarms.Alarm)
				as.alarmTypesByIdentifier = make(map[string]AlarmType)
			},
			expectedAlarms:     map[string]*alarms.Alarm{},
			expectedAlarmTypes: map[string]AlarmType{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			alarmState := NewTestAlarmState(t)

			watcher := NewTestAlarmWatcher(alarmState.AlarmState)

			tt.setupAlarmState(alarmState.AlarmState)

			stableAlarms, stableAlarmTypes := watcher.getNewlyStableAlarms(nowMs, testFlickerFilterS)

			assert.Len(t, stableAlarms, len(tt.expectedAlarms))
			for k, v := range tt.expectedAlarms {
				assert.Contains(t, stableAlarms, k)
				assert.Equal(t, v.Identifier, stableAlarms[k].Identifier)
				assert.Equal(t, v.Valid, stableAlarms[k].Valid)
				assert.Equal(t, v.TimestampMs, stableAlarms[k].TimestampMs)
			}
			assert.Equal(t, tt.expectedAlarmTypes, stableAlarmTypes)
			alarmState.MockManagedState.AssertExpectations(t)
		})
	}
}

func TestAlarmWatcher_getModuleIdsWithHostAlarms(t *testing.T) {
	moduleId1 := uint32(1)
	moduleId2 := uint32(2)

	tests := []struct {
		name              string
		setupAlarmState   func(*AlarmState)
		expectedModuleIds map[uint32]bool
	}{
		{
			name: "Valid host alarm for moduleId1",
			setupAlarmState: func(as *AlarmState) {
				hostAlarm1 := &alarms.Alarm{Identifier: "hostAlarm1", Valid: true, ModuleId: &moduleId1}
				as.Alarms[AlarmTypeHost] = map[string]*alarms.Alarm{"hostAlarm1": hostAlarm1}
			},
			expectedModuleIds: map[uint32]bool{moduleId1: true},
		},
		{
			name: "Invalid host alarm for moduleId2, should still be included",
			setupAlarmState: func(as *AlarmState) {
				hostAlarm1 := &alarms.Alarm{Identifier: "hostAlarm1", Valid: true, ModuleId: &moduleId1}
				hostAlarm2 := &alarms.Alarm{Identifier: "hostAlarm2", Valid: false, ModuleId: &moduleId2}
				as.Alarms[AlarmTypeHost] = map[string]*alarms.Alarm{
					"hostAlarm1": hostAlarm1,
					"hostAlarm2": hostAlarm2,
				}
			},
			expectedModuleIds: map[uint32]bool{moduleId1: true, moduleId2: true},
		},
		{
			name: "No host alarms",
			setupAlarmState: func(as *AlarmState) {
				as.Alarms = make(map[AlarmType]map[string]*alarms.Alarm)
			},
			expectedModuleIds: map[uint32]bool{},
		},
		{
			name: "Host alarm with nil ModuleId",
			setupAlarmState: func(as *AlarmState) {
				hostAlarmNilModuleId := &alarms.Alarm{Identifier: "hostAlarmNil", Valid: true, ModuleId: nil}
				as.Alarms[AlarmTypeHost] = map[string]*alarms.Alarm{"hostAlarmNil": hostAlarmNilModuleId}
			},
			expectedModuleIds: map[uint32]bool{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			alarmState := NewTestAlarmState(t)

			watcher := NewTestAlarmWatcher(alarmState.AlarmState)

			tt.setupAlarmState(alarmState.AlarmState)

			moduleIds := watcher.getModuleIdsWithHostAlarms()

			assert.Equal(t, tt.expectedModuleIds, moduleIds)
			alarmState.MockManagedState.AssertExpectations(t)
		})
	}
}

func Test_shouldSuppressScannerAlarm(t *testing.T) {
	moduleId := uint32(1)
	scannerAlarm := &alarms.Alarm{Identifier: "scannerAlarm", ModuleId: &moduleId}
	scannerAlarmNilModuleId := &alarms.Alarm{Identifier: "scannerAlarmNil"}

	tests := []struct {
		name                         string
		alarmType                    AlarmType
		alarmStruct                  *alarms.Alarm
		moduleIdsWithValidHostAlarms map[uint32]bool
		expectedSuppression          bool
	}{
		{
			name:                         "Scanner alarm with active host alarm on same module -> suppress",
			alarmType:                    AlarmTypeScanner,
			alarmStruct:                  scannerAlarm,
			moduleIdsWithValidHostAlarms: map[uint32]bool{moduleId: true},
			expectedSuppression:          true,
		},
		{
			name:                         "Scanner alarm, but no active host alarm on same module -> do not suppress",
			alarmType:                    AlarmTypeScanner,
			alarmStruct:                  scannerAlarm,
			moduleIdsWithValidHostAlarms: map[uint32]bool{},
			expectedSuppression:          false,
		},
		{
			name:                         "Non-scanner alarm -> do not suppress",
			alarmType:                    AlarmTypeSoftware,
			alarmStruct:                  scannerAlarm,
			moduleIdsWithValidHostAlarms: map[uint32]bool{moduleId: true},
			expectedSuppression:          false,
		},
		{
			name:                         "Scanner alarm with nil ModuleId -> do not suppress",
			alarmType:                    AlarmTypeScanner,
			alarmStruct:                  scannerAlarmNilModuleId,
			moduleIdsWithValidHostAlarms: map[uint32]bool{moduleId: true},
			expectedSuppression:          false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := shouldSuppressScannerAlarm(tt.alarmType, tt.alarmStruct, tt.moduleIdsWithValidHostAlarms)
			assert.Equal(t, tt.expectedSuppression, result)
		})
	}
}

func TestAlarmWatcher_makeAlarmsValid(t *testing.T) {
	nowMs := time.Now().UnixMilli()

	tests := []struct {
		name                       string
		flickerFilterS             uint64
		setupAlarmState            func(*AlarmState)
		expectedValidAlarms        map[string]bool
		expectedAddAlarmToLogCalls int
	}{
		{
			name:           "No stable alarms - Action should do nothing",
			flickerFilterS: 0,
			setupAlarmState: func(as *AlarmState) {
				as.Alarms = make(map[AlarmType]map[string]*alarms.Alarm)
				as.alarmTypesByIdentifier = make(map[string]AlarmType)
			},
			expectedValidAlarms:        map[string]bool{},
			expectedAddAlarmToLogCalls: 0,
		},
		{
			name:           "Alarm type not found for identifier",
			flickerFilterS: 0,
			setupAlarmState: func(as *AlarmState) {
				as.Alarms = map[AlarmType]map[string]*alarms.Alarm{
					AlarmTypeSoftware: {},
				}
				as.alarmTypesByIdentifier = make(map[string]AlarmType) // Empty
			},
			expectedValidAlarms:        map[string]bool{},
			expectedAddAlarmToLogCalls: 0,
		},
		{
			name:           "One alarm becomes stable and therefore valid",
			flickerFilterS: 0,
			setupAlarmState: func(as *AlarmState) {
				alarm1 := &alarms.Alarm{Identifier: "alarm1", Valid: false, TimestampMs: nowMs - 100} // Past flicker filter (0s)
				as.Alarms[AlarmTypeSoftware] = map[string]*alarms.Alarm{"alarm1": alarm1}
				as.alarmTypesByIdentifier["alarm1"] = AlarmTypeSoftware
			},
			expectedValidAlarms:        map[string]bool{"alarm1": true},
			expectedAddAlarmToLogCalls: 1,
		},
		{
			name:           "Scanner alarm suppressed by host alarm",
			flickerFilterS: 0,
			setupAlarmState: func(as *AlarmState) {
				moduleId := uint32(1)
				scannerAlarm := &alarms.Alarm{Identifier: "scannerAlarm", Valid: false, TimestampMs: nowMs - 100, ModuleId: &moduleId}
				hostAlarm := &alarms.Alarm{Identifier: "hostAlarm", Valid: true, TimestampMs: nowMs - 100, ModuleId: &moduleId} // Already valid host alarm

				as.Alarms = map[AlarmType]map[string]*alarms.Alarm{
					AlarmTypeScanner: {"scannerAlarm": scannerAlarm},
					AlarmTypeHost:    {"hostAlarm": hostAlarm},
				}
				as.alarmTypesByIdentifier = map[string]AlarmType{
					"scannerAlarm": AlarmTypeScanner,
					"hostAlarm":    AlarmTypeHost,
				}
			},
			expectedValidAlarms:        map[string]bool{"scannerAlarm": false, "hostAlarm": true},
			expectedAddAlarmToLogCalls: 0, // Scanner alarm should not be added to log
		},
		{
			name:           "Multiple alarms, some made valid, some suppressed",
			flickerFilterS: 0,
			setupAlarmState: func(as *AlarmState) {
				moduleId := uint32(1)
				alarmA := &alarms.Alarm{Identifier: "alarmA", Valid: false, TimestampMs: nowMs - 100}                                    // Should become valid
				scannerAlarmB := &alarms.Alarm{Identifier: "scannerAlarmB", Valid: false, TimestampMs: nowMs - 100, ModuleId: &moduleId} // Should be suppressed
				hostAlarmC := &alarms.Alarm{Identifier: "hostAlarmC", Valid: true, TimestampMs: nowMs - 100, ModuleId: &moduleId}        // Already valid host alarm
				alarmD := &alarms.Alarm{Identifier: "alarmD", Valid: false, TimestampMs: nowMs - 100}                                    // Should become valid
				scannerAlarmE := &alarms.Alarm{Identifier: "scannerAlarmE", Valid: false, TimestampMs: nowMs - 100}                      // Should become valid

				as.Alarms = map[AlarmType]map[string]*alarms.Alarm{
					AlarmTypeSoftware: {"alarmA": alarmA, "alarmD": alarmD},
					AlarmTypeScanner:  {"scannerAlarmB": scannerAlarmB, "scannerAlarmE": scannerAlarmE},
					AlarmTypeHost:     {"hostAlarmC": hostAlarmC},
				}
				as.alarmTypesByIdentifier = map[string]AlarmType{
					"alarmA":        AlarmTypeSoftware,
					"scannerAlarmB": AlarmTypeScanner,
					"hostAlarmC":    AlarmTypeHost,
					"alarmD":        AlarmTypeSoftware,
					"scannerAlarmE": AlarmTypeScanner,
				}
			},
			expectedValidAlarms: map[string]bool{
				"alarmA":        true,
				"scannerAlarmB": false,
				"hostAlarmC":    true,
				"alarmD":        true,
				"scannerAlarmE": true,
			},
			expectedAddAlarmToLogCalls: 3, // alarmA, alarmD, and scannerAlarmE
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			alarmState := NewTestAlarmState(t)

			watcher := NewTestAlarmWatcher(alarmState.AlarmState)

			tt.setupAlarmState(alarmState.AlarmState)

			alarmState.AlarmLog = []*frontend.AlarmRow{}
			alarmState.AlarmLogMap = make(map[AlarmLogKey]*frontend.AlarmRow)

			if tt.expectedAddAlarmToLogCalls > 0 {
				// HSet and SAdd are each called once per alarm added to log
				alarmState.redis.(*mocks.RedisClient).On("HSet",
					mock.AnythingOfType("string"), // hash
					mock.AnythingOfType("string"), // key
					mock.AnythingOfType("string"), // val
				).Return(nil).Times(tt.expectedAddAlarmToLogCalls)
				alarmState.redis.(*mocks.RedisClient).On("SAdd",
					mock.AnythingOfType("string"), // key
					mock.AnythingOfType("string"), // member
				).Return(nil).Times(tt.expectedAddAlarmToLogCalls)
			}

			watcher.makeAlarmsValid(tt.flickerFilterS)

			for identifier, expectedValid := range tt.expectedValidAlarms {
				found := false
				for _, alarmMap := range alarmState.Alarms {
					if al, ok := alarmMap[identifier]; ok {
						assert.Equal(t, expectedValid, al.Valid, "Alarm %s Valid state mismatch", identifier)
						found = true
						break
					}
				}
				if !found {
					t.Errorf("Alarm %s not found in alarmState.Alarms after makeAlarmsValid", identifier)
				}
			}
			assert.Len(t, alarmState.AlarmLog, tt.expectedAddAlarmToLogCalls, "AlarmLog length mismatch")
			alarmState.MockManagedState.AssertExpectations(t)
			alarmState.redis.(*mocks.RedisClient).AssertExpectations(t)
		})
	}
}
