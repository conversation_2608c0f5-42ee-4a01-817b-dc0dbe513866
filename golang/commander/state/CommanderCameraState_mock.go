// Code generated by mockery; DO NOT EDIT.
// github.com/vektra/mockery
// template: testify

package state

import (
	"context"

	mock "github.com/stretchr/testify/mock"
)

// NewMockCommanderCameraState creates a new instance of MockCommanderCameraState. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewMockCommanderCameraState(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockCommanderCameraState {
	mock := &MockCommanderCameraState{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}

// MockCommanderCameraState is an autogenerated mock type for the CommanderCameraState type
type MockCommanderCameraState struct {
	mock.Mock
}

type MockCommanderCameraState_Expecter struct {
	mock *mock.Mock
}

func (_m *MockCommanderCameraState) EXPECT() *MockCommanderCameraState_Expecter {
	return &MockCommanderCameraState_Expecter{mock: &_m.Mock}
}

// GetCamera provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) GetCamera(camID string) (*Camera, bool) {
	ret := _mock.Called(camID)

	if len(ret) == 0 {
		panic("no return value specified for GetCamera")
	}

	var r0 *Camera
	var r1 bool
	if returnFunc, ok := ret.Get(0).(func(string) (*Camera, bool)); ok {
		return returnFunc(camID)
	}
	if returnFunc, ok := ret.Get(0).(func(string) *Camera); ok {
		r0 = returnFunc(camID)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*Camera)
		}
	}
	if returnFunc, ok := ret.Get(1).(func(string) bool); ok {
		r1 = returnFunc(camID)
	} else {
		r1 = ret.Get(1).(bool)
	}
	return r0, r1
}

// MockCommanderCameraState_GetCamera_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetCamera'
type MockCommanderCameraState_GetCamera_Call struct {
	*mock.Call
}

// GetCamera is a helper method to define mock.On call
//   - camID string
func (_e *MockCommanderCameraState_Expecter) GetCamera(camID interface{}) *MockCommanderCameraState_GetCamera_Call {
	return &MockCommanderCameraState_GetCamera_Call{Call: _e.mock.On("GetCamera", camID)}
}

func (_c *MockCommanderCameraState_GetCamera_Call) Run(run func(camID string)) *MockCommanderCameraState_GetCamera_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_GetCamera_Call) Return(camera *Camera, b bool) *MockCommanderCameraState_GetCamera_Call {
	_c.Call.Return(camera, b)
	return _c
}

func (_c *MockCommanderCameraState_GetCamera_Call) RunAndReturn(run func(camID string) (*Camera, bool)) *MockCommanderCameraState_GetCamera_Call {
	_c.Call.Return(run)
	return _c
}

// GetDimensions provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) GetDimensions(camId string) (uint32, uint32, bool) {
	ret := _mock.Called(camId)

	if len(ret) == 0 {
		panic("no return value specified for GetDimensions")
	}

	var r0 uint32
	var r1 uint32
	var r2 bool
	if returnFunc, ok := ret.Get(0).(func(string) (uint32, uint32, bool)); ok {
		return returnFunc(camId)
	}
	if returnFunc, ok := ret.Get(0).(func(string) uint32); ok {
		r0 = returnFunc(camId)
	} else {
		r0 = ret.Get(0).(uint32)
	}
	if returnFunc, ok := ret.Get(1).(func(string) uint32); ok {
		r1 = returnFunc(camId)
	} else {
		r1 = ret.Get(1).(uint32)
	}
	if returnFunc, ok := ret.Get(2).(func(string) bool); ok {
		r2 = returnFunc(camId)
	} else {
		r2 = ret.Get(2).(bool)
	}
	return r0, r1, r2
}

// MockCommanderCameraState_GetDimensions_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetDimensions'
type MockCommanderCameraState_GetDimensions_Call struct {
	*mock.Call
}

// GetDimensions is a helper method to define mock.On call
//   - camId string
func (_e *MockCommanderCameraState_Expecter) GetDimensions(camId interface{}) *MockCommanderCameraState_GetDimensions_Call {
	return &MockCommanderCameraState_GetDimensions_Call{Call: _e.mock.On("GetDimensions", camId)}
}

func (_c *MockCommanderCameraState_GetDimensions_Call) Run(run func(camId string)) *MockCommanderCameraState_GetDimensions_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 string
		if args[0] != nil {
			arg0 = args[0].(string)
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_GetDimensions_Call) Return(width uint32, height uint32, ok bool) *MockCommanderCameraState_GetDimensions_Call {
	_c.Call.Return(width, height, ok)
	return _c
}

func (_c *MockCommanderCameraState_GetDimensions_Call) RunAndReturn(run func(camId string) (uint32, uint32, bool)) *MockCommanderCameraState_GetDimensions_Call {
	_c.Call.Return(run)
	return _c
}

// GetTimestampMs provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) GetTimestampMs() int64 {
	ret := _mock.Called()

	if len(ret) == 0 {
		panic("no return value specified for GetTimestampMs")
	}

	var r0 int64
	if returnFunc, ok := ret.Get(0).(func() int64); ok {
		r0 = returnFunc()
	} else {
		r0 = ret.Get(0).(int64)
	}
	return r0
}

// MockCommanderCameraState_GetTimestampMs_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'GetTimestampMs'
type MockCommanderCameraState_GetTimestampMs_Call struct {
	*mock.Call
}

// GetTimestampMs is a helper method to define mock.On call
func (_e *MockCommanderCameraState_Expecter) GetTimestampMs() *MockCommanderCameraState_GetTimestampMs_Call {
	return &MockCommanderCameraState_GetTimestampMs_Call{Call: _e.mock.On("GetTimestampMs")}
}

func (_c *MockCommanderCameraState_GetTimestampMs_Call) Run(run func()) *MockCommanderCameraState_GetTimestampMs_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockCommanderCameraState_GetTimestampMs_Call) Return(n int64) *MockCommanderCameraState_GetTimestampMs_Call {
	_c.Call.Return(n)
	return _c
}

func (_c *MockCommanderCameraState_GetTimestampMs_Call) RunAndReturn(run func() int64) *MockCommanderCameraState_GetTimestampMs_Call {
	_c.Call.Return(run)
	return _c
}

// ReadOnCurrent provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) ReadOnCurrent(readOnCurrent func()) {
	_mock.Called(readOnCurrent)
	return
}

// MockCommanderCameraState_ReadOnCurrent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReadOnCurrent'
type MockCommanderCameraState_ReadOnCurrent_Call struct {
	*mock.Call
}

// ReadOnCurrent is a helper method to define mock.On call
//   - readOnCurrent func()
func (_e *MockCommanderCameraState_Expecter) ReadOnCurrent(readOnCurrent interface{}) *MockCommanderCameraState_ReadOnCurrent_Call {
	return &MockCommanderCameraState_ReadOnCurrent_Call{Call: _e.mock.On("ReadOnCurrent", readOnCurrent)}
}

func (_c *MockCommanderCameraState_ReadOnCurrent_Call) Run(run func(readOnCurrent func())) *MockCommanderCameraState_ReadOnCurrent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 func()
		if args[0] != nil {
			arg0 = args[0].(func())
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_ReadOnCurrent_Call) Return() *MockCommanderCameraState_ReadOnCurrent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockCommanderCameraState_ReadOnCurrent_Call) RunAndReturn(run func(readOnCurrent func())) *MockCommanderCameraState_ReadOnCurrent_Call {
	_c.Run(run)
	return _c
}

// ReadOnNext provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) ReadOnNext(ctx context.Context, timestampMs int64, readOnNext func()) bool {
	ret := _mock.Called(ctx, timestampMs, readOnNext)

	if len(ret) == 0 {
		panic("no return value specified for ReadOnNext")
	}

	var r0 bool
	if returnFunc, ok := ret.Get(0).(func(context.Context, int64, func()) bool); ok {
		r0 = returnFunc(ctx, timestampMs, readOnNext)
	} else {
		r0 = ret.Get(0).(bool)
	}
	return r0
}

// MockCommanderCameraState_ReadOnNext_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'ReadOnNext'
type MockCommanderCameraState_ReadOnNext_Call struct {
	*mock.Call
}

// ReadOnNext is a helper method to define mock.On call
//   - ctx context.Context
//   - timestampMs int64
//   - readOnNext func()
func (_e *MockCommanderCameraState_Expecter) ReadOnNext(ctx interface{}, timestampMs interface{}, readOnNext interface{}) *MockCommanderCameraState_ReadOnNext_Call {
	return &MockCommanderCameraState_ReadOnNext_Call{Call: _e.mock.On("ReadOnNext", ctx, timestampMs, readOnNext)}
}

func (_c *MockCommanderCameraState_ReadOnNext_Call) Run(run func(ctx context.Context, timestampMs int64, readOnNext func())) *MockCommanderCameraState_ReadOnNext_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 context.Context
		if args[0] != nil {
			arg0 = args[0].(context.Context)
		}
		var arg1 int64
		if args[1] != nil {
			arg1 = args[1].(int64)
		}
		var arg2 func()
		if args[2] != nil {
			arg2 = args[2].(func())
		}
		run(
			arg0,
			arg1,
			arg2,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_ReadOnNext_Call) Return(b bool) *MockCommanderCameraState_ReadOnNext_Call {
	_c.Call.Return(b)
	return _c
}

func (_c *MockCommanderCameraState_ReadOnNext_Call) RunAndReturn(run func(ctx context.Context, timestampMs int64, readOnNext func()) bool) *MockCommanderCameraState_ReadOnNext_Call {
	_c.Call.Return(run)
	return _c
}

// Terminate provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) Terminate() {
	_mock.Called()
	return
}

// MockCommanderCameraState_Terminate_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Terminate'
type MockCommanderCameraState_Terminate_Call struct {
	*mock.Call
}

// Terminate is a helper method to define mock.On call
func (_e *MockCommanderCameraState_Expecter) Terminate() *MockCommanderCameraState_Terminate_Call {
	return &MockCommanderCameraState_Terminate_Call{Call: _e.mock.On("Terminate")}
}

func (_c *MockCommanderCameraState_Terminate_Call) Run(run func()) *MockCommanderCameraState_Terminate_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *MockCommanderCameraState_Terminate_Call) Return() *MockCommanderCameraState_Terminate_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockCommanderCameraState_Terminate_Call) RunAndReturn(run func()) *MockCommanderCameraState_Terminate_Call {
	_c.Run(run)
	return _c
}

// WriteOnCurrent provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) WriteOnCurrent(writeOnCurrent func()) {
	_mock.Called(writeOnCurrent)
	return
}

// MockCommanderCameraState_WriteOnCurrent_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteOnCurrent'
type MockCommanderCameraState_WriteOnCurrent_Call struct {
	*mock.Call
}

// WriteOnCurrent is a helper method to define mock.On call
//   - writeOnCurrent func()
func (_e *MockCommanderCameraState_Expecter) WriteOnCurrent(writeOnCurrent interface{}) *MockCommanderCameraState_WriteOnCurrent_Call {
	return &MockCommanderCameraState_WriteOnCurrent_Call{Call: _e.mock.On("WriteOnCurrent", writeOnCurrent)}
}

func (_c *MockCommanderCameraState_WriteOnCurrent_Call) Run(run func(writeOnCurrent func())) *MockCommanderCameraState_WriteOnCurrent_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 func()
		if args[0] != nil {
			arg0 = args[0].(func())
		}
		run(
			arg0,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_WriteOnCurrent_Call) Return() *MockCommanderCameraState_WriteOnCurrent_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockCommanderCameraState_WriteOnCurrent_Call) RunAndReturn(run func(writeOnCurrent func())) *MockCommanderCameraState_WriteOnCurrent_Call {
	_c.Run(run)
	return _c
}

// WriteOnCurrentWithTimestamp provides a mock function for the type MockCommanderCameraState
func (_mock *MockCommanderCameraState) WriteOnCurrentWithTimestamp(writeOnCurrent func(), timestampMs int64) {
	_mock.Called(writeOnCurrent, timestampMs)
	return
}

// MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'WriteOnCurrentWithTimestamp'
type MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call struct {
	*mock.Call
}

// WriteOnCurrentWithTimestamp is a helper method to define mock.On call
//   - writeOnCurrent func()
//   - timestampMs int64
func (_e *MockCommanderCameraState_Expecter) WriteOnCurrentWithTimestamp(writeOnCurrent interface{}, timestampMs interface{}) *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call {
	return &MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call{Call: _e.mock.On("WriteOnCurrentWithTimestamp", writeOnCurrent, timestampMs)}
}

func (_c *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call) Run(run func(writeOnCurrent func(), timestampMs int64)) *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		var arg0 func()
		if args[0] != nil {
			arg0 = args[0].(func())
		}
		var arg1 int64
		if args[1] != nil {
			arg1 = args[1].(int64)
		}
		run(
			arg0,
			arg1,
		)
	})
	return _c
}

func (_c *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call) Return() *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call {
	_c.Call.Return()
	return _c
}

func (_c *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call) RunAndReturn(run func(writeOnCurrent func(), timestampMs int64)) *MockCommanderCameraState_WriteOnCurrentWithTimestamp_Call {
	_c.Run(run)
	return _c
}
