// Code generated by mockery v2.53.4. DO NOT EDIT.

package mocks

import (
	context "context"

	mock "github.com/stretchr/testify/mock"

	v8 "github.com/go-redis/redis/v8"
)

// RedisClient is an autogenerated mock type for the RedisClient type
type RedisClient struct {
	mock.Mock
}

// HDel provides a mock function with given fields: key, member
func (_m *RedisClient) HDel(key string, member string) error {
	ret := _m.Called(key, member)

	if len(ret) == 0 {
		panic("no return value specified for HDel")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, member)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// HGetAll provides a mock function with given fields: hash
func (_m *RedisClient) HGetAll(hash string) (map[string]string, error) {
	ret := _m.Called(hash)

	if len(ret) == 0 {
		panic("no return value specified for HGetAll")
	}

	var r0 map[string]string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (map[string]string, error)); ok {
		return rf(hash)
	}
	if rf, ok := ret.Get(0).(func(string) map[string]string); ok {
		r0 = rf(hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HGetAllWithContext provides a mock function with given fields: ctx, hash
func (_m *RedisClient) HGetAllWithContext(ctx context.Context, hash string) (map[string]string, error) {
	ret := _m.Called(ctx, hash)

	if len(ret) == 0 {
		panic("no return value specified for HGetAllWithContext")
	}

	var r0 map[string]string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (map[string]string, error)); ok {
		return rf(ctx, hash)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) map[string]string); ok {
		r0 = rf(ctx, hash)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, hash)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HGetWithContext provides a mock function with given fields: ctx, hash, key
func (_m *RedisClient) HGetWithContext(ctx context.Context, hash string, key string) (string, error) {
	ret := _m.Called(ctx, hash, key)

	if len(ret) == 0 {
		panic("no return value specified for HGetWithContext")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string) (string, error)); ok {
		return rf(ctx, hash, key)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string) string); ok {
		r0 = rf(ctx, hash, key)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string) error); ok {
		r1 = rf(ctx, hash, key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HSet provides a mock function with given fields: hash, key, val
func (_m *RedisClient) HSet(hash string, key string, val string) error {
	ret := _m.Called(hash, key, val)

	if len(ret) == 0 {
		panic("no return value specified for HSet")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string, string) error); ok {
		r0 = rf(hash, key, val)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// HSetWithContext provides a mock function with given fields: ctx, hash, val
func (_m *RedisClient) HSetWithContext(ctx context.Context, hash string, val ...interface{}) error {
	var _ca []interface{}
	_ca = append(_ca, ctx, hash)
	_ca = append(_ca, val...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for HSetWithContext")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...interface{}) error); ok {
		r0 = rf(ctx, hash, val...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ReadInt64 provides a mock function with given fields: key, def
func (_m *RedisClient) ReadInt64(key string, def int64) (int64, error) {
	ret := _m.Called(key, def)

	if len(ret) == 0 {
		panic("no return value specified for ReadInt64")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string, int64) (int64, error)); ok {
		return rf(key, def)
	}
	if rf, ok := ret.Get(0).(func(string, int64) int64); ok {
		r0 = rf(key, def)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string, int64) error); ok {
		r1 = rf(key, def)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SAdd provides a mock function with given fields: key, member
func (_m *RedisClient) SAdd(key string, member string) error {
	ret := _m.Called(key, member)

	if len(ret) == 0 {
		panic("no return value specified for SAdd")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, member)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SMembers provides a mock function with given fields: key
func (_m *RedisClient) SMembers(key string) ([]string, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for SMembers")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]string, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) []string); ok {
		r0 = rf(key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SRem provides a mock function with given fields: key, member
func (_m *RedisClient) SRem(key string, member string) error {
	ret := _m.Called(key, member)

	if len(ret) == 0 {
		panic("no return value specified for SRem")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(key, member)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// WriteInt64 provides a mock function with given fields: key, i
func (_m *RedisClient) WriteInt64(key string, i int64) error {
	ret := _m.Called(key, i)

	if len(ret) == 0 {
		panic("no return value specified for WriteInt64")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, int64) error); ok {
		r0 = rf(key, i)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ZAdd provides a mock function with given fields: ctx, key, members
func (_m *RedisClient) ZAdd(ctx context.Context, key string, members ...*v8.Z) (int64, error) {
	_va := make([]interface{}, len(members))
	for _i := range members {
		_va[_i] = members[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, ctx, key)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for ZAdd")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, ...*v8.Z) (int64, error)); ok {
		return rf(ctx, key, members...)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, ...*v8.Z) int64); ok {
		r0 = rf(ctx, key, members...)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, ...*v8.Z) error); ok {
		r1 = rf(ctx, key, members...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ZRangeByScoreWithScores provides a mock function with given fields: ctx, key, opt
func (_m *RedisClient) ZRangeByScoreWithScores(ctx context.Context, key string, opt *v8.ZRangeBy) ([]v8.Z, error) {
	ret := _m.Called(ctx, key, opt)

	if len(ret) == 0 {
		panic("no return value specified for ZRangeByScoreWithScores")
	}

	var r0 []v8.Z
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *v8.ZRangeBy) ([]v8.Z, error)); ok {
		return rf(ctx, key, opt)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *v8.ZRangeBy) []v8.Z); ok {
		r0 = rf(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]v8.Z)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *v8.ZRangeBy) error); ok {
		r1 = rf(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ZRemRangeByScore provides a mock function with given fields: ctx, key, min, max
func (_m *RedisClient) ZRemRangeByScore(ctx context.Context, key string, min string, max string) (int64, error) {
	ret := _m.Called(ctx, key, min, max)

	if len(ret) == 0 {
		panic("no return value specified for ZRemRangeByScore")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) (int64, error)); ok {
		return rf(ctx, key, min, max)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, string, string) int64); ok {
		r0 = rf(ctx, key, min, max)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, string, string) error); ok {
		r1 = rf(ctx, key, min, max)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ZRevRangeByScoreWithScores provides a mock function with given fields: ctx, key, opt
func (_m *RedisClient) ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *v8.ZRangeBy) ([]v8.Z, error) {
	ret := _m.Called(ctx, key, opt)

	if len(ret) == 0 {
		panic("no return value specified for ZRevRangeByScoreWithScores")
	}

	var r0 []v8.Z
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *v8.ZRangeBy) ([]v8.Z, error)); ok {
		return rf(ctx, key, opt)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *v8.ZRangeBy) []v8.Z); ok {
		r0 = rf(ctx, key, opt)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]v8.Z)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *v8.ZRangeBy) error); ok {
		r1 = rf(ctx, key, opt)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewRedisClient creates a new instance of RedisClient. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRedisClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *RedisClient {
	mock := &RedisClient{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
