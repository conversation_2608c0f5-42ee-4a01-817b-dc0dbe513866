package state

import (
	"fmt"
	"net"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/client_owner"
	"github.com/carbonrobotics/robot/golang/lib/environment"
	"github.com/carbonrobotics/robot/golang/lib/hosts"
	"github.com/sirupsen/logrus"
	"golang.org/x/net/icmp"
	"golang.org/x/net/ipv4"
)

const pingTimeout = 200 * time.Millisecond

type PingCheckState struct {
	ManagedStateImpl
	clientOwner             *client_owner.ClientOwner
	moduleOrchestratorState *ModuleOrchestratorState
	robotEnv                environment.Robot
	logger                  *logrus.Entry
}

func NewPingCheckState(clientOwner *client_owner.ClientOwner, moduleOrchestratorState *ModuleOrchestratorState, robotEnv environment.Robot) *PingCheckState {
	pingCheckState := &PingCheckState{
		ManagedStateImpl:        ManagedStateImpl{name: "<PERSON><PERSON>heckState"},
		clientOwner:             clientOwner,
		moduleOrchestratorState: moduleOrchestratorState,
		robotEnv:                robotEnv,
		logger:                  logrus.WithField("module", "PingCheckState"),
	}
	pingCheckState.initialize()
	return pingCheckState
}

func (p *PingCheckState) PingHostByID(hostId uint32) error {
	hostClients := p.clientOwner.GetHostClients()
	for _, host := range hostClients {
		if host.PcId == hostId {
			return p.PingHost(host)
		}
	}
	return fmt.Errorf("host not found: %v", hostId)
}

func (p *PingCheckState) PingHost(host *hosts.HostClients) error {
	if p.robotEnv.IsSimulator() {
		return nil
	}

	err := ping(host.IpAddress)
	if err != nil {
		err = fmt.Errorf("failed to ping host %v: %v", host.IpAddress, err)
		if p.moduleOrchestratorState != nil {
			backupIPs := p.moduleOrchestratorState.GetLastKnownDHCPIPs()
			backupIP, ok := backupIPs[host.PcId]
			if ok {
				err = ping(backupIP)
				if err != nil {
					err = fmt.Errorf("failed to ping host %v with backup ip %v: %v", host.IpAddress, backupIP, err)
				}
			}
		}
	}
	return err
}

func ping(ip string) error {
	// Create ICMP message
	wm := icmp.Message{
		Type: ipv4.ICMPTypeEcho,
		Code: 0,
		Body: &icmp.Echo{
			ID:   1,
			Seq:  1,
			Data: []byte("ping"),
		},
	}
	// Create ICMP connection
	conn, err := icmp.ListenPacket("udp4", "0.0.0.0")
	if err != nil {
		return err
	}
	defer conn.Close()

	msgBytes, err := wm.Marshal(nil)
	if err != nil {
		return err
	}

	// Send ICMP message
	_, err = conn.WriteTo(msgBytes, &net.UDPAddr{IP: net.ParseIP(ip)})
	if err != nil {
		return err
	}

	err = conn.SetDeadline(time.Now().Add(pingTimeout))
	if err != nil {
		return fmt.Errorf("set deadline error: %v", err)
	}

	// Receive ICMP response
	rb := make([]byte, 1500)
	n, _, err := conn.ReadFrom(rb)
	if err != nil {
		return err
	}

	// Parse ICMP response
	rm, err := icmp.ParseMessage(1, rb[:n])
	if err != nil {
		return err
	}

	if rm.Type != ipv4.ICMPTypeEchoReply {
		return fmt.Errorf("expected ICMP echo reply, got %v", rm.Type)
	}

	return nil
}
