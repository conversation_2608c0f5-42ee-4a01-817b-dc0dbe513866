package state

import (
	"context"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/frontend"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/hardware_manager"
	"github.com/sirupsen/logrus"
)

type cruiseControlHWState struct {
	CruiseEnabled     bool
	CruiseAllowEnable bool
}

type CruiseControlHWStateUpdateIF interface {
	SetCruiseControlHWState(*cruiseControlHWState)
}

type CruiseControlState struct {
	ManagedStateImpl
	CommandedVelocityMph float64
	CruiseBlocker        frontend.CruiseBlocker
}

func (c *cruiseControlHWState) updateCruise(hwClient *hardware_manager.HardwareManagerClient, timeout time.Duration) bool {
	cruiseStatus, err := hwClient.GetCruiseStatus(timeout)
	if err != nil {
		logrus.Warningf("Failed to get cruise status: %v", err)
		return false
	}
	enabled := cruiseStatus.Enabled
	allowEnable := cruiseStatus.AllowEnable
	changed := false
	if enabled != c.CruiseEnabled {
		changed = true
		c.CruiseEnabled = enabled
	}
	if allowEnable != c.CruiseAllowEnable {
		changed = true
		c.CruiseAllowEnable = allowEnable
	}
	return changed
}
func cruiseControlHWUpdater(ctx context.Context, ccFeatureFlag, ccUpdateIntervalMsec *config.ConfigTree, hwClient *hardware_manager.HardwareManagerClient, updater CruiseControlHWStateUpdateIF, wg *sync.WaitGroup) {
	if !ccFeatureFlag.GetBoolValue() {
		return
	}
	wg.Add(1)
	go func() {
		defer wg.Done()
		state := cruiseControlHWState{}
		interval := time.Millisecond * time.Duration(ccUpdateIntervalMsec.GetUIntValue())
		for {
			select {
			case <-time.After(interval):
			case <-ctx.Done():
				return
			}
			if state.updateCruise(hwClient, interval) {
				updater.SetCruiseControlHWState(&state)
			}
			interval = time.Millisecond * time.Duration(ccUpdateIntervalMsec.GetUIntValue())
		}
	}()
}

func NewCruiseControlState() *CruiseControlState {
	cc := &CruiseControlState{
		CommandedVelocityMph: 0.0,
		CruiseBlocker:        frontend.CruiseBlocker_NONE,
	}
	cc.initialize()
	return cc
}

func CruiseControlUpdater(ctx context.Context, ccFeatureFlag *config.ConfigTree, ccUpdateIntervalMsec *config.ConfigTree, hwClient *hardware_manager.HardwareManagerClient, velState *VelocityV2State, alarmState *AlarmState, opState *OperationsState, tractorSafety *TractorSafetyState, cruiseState *CruiseControlState, wg *sync.WaitGroup) {
	wg.Add(1)
	defer wg.Done()
	cruiseControlHWUpdater(ctx, ccFeatureFlag, ccUpdateIntervalMsec, hwClient, opState, wg)
	defaultForceTime := time.Second * 5
	minUpdateFreq := time.Millisecond * 200

	lastUpdateTime := time.Time{}
	velSig := velState.AddWatcher()
	alarmSig := alarmState.AddWatcher()
	opSig := opState.AddWatcher()
	tsSig := tractorSafety.AddWatcher()
	forceTime := defaultForceTime
	consecutiveBlockerCount := make(map[int32]int)
	consecutiveBlockerReq := make(map[int32]int)
	for val, _ := range frontend.CruiseBlocker_name {
		consecutiveBlockerCount[val] = 0
		consecutiveBlockerReq[val] = 1
	}
	consecutiveBlockerReq[int32(frontend.CruiseBlocker_ACTIVE_ALARM)] = 2 // handle transient alarms by requiring 2 consecutive cases

	for {
		select {
		case <-velSig:
		case <-alarmSig:
		case <-opSig:
		case <-tsSig:
		case <-time.After(forceTime):
		case <-ctx.Done():
			return
		}
		if ccFeatureFlag.GetBoolValue() {
			actualVel := float64(0.0)
			reqVel := float64(0.0)
			velState.ReadOnCurrent(func() {
				actualVel = velState.CurrentVelocityMph
				reqVel = velState.CruiseControlVelocityMph
			})
			blocker := frontend.CruiseBlocker_NONE
			if alarmState.HasImpactingAlarm() {
				blocker = frontend.CruiseBlocker_ACTIVE_ALARM
			} else if shouldNotUseVel(opState) {
				blocker = frontend.CruiseBlocker_NOT_WEEDING
			} else if !tractorSafety.IsSafeComputed() {
				blocker = frontend.CruiseBlocker_TRACTOR_NOT_SAFE
			}

			if blocker != frontend.CruiseBlocker_NONE {
				for val, _ := range frontend.CruiseBlocker_name {
					if val == int32(blocker) {
						consecutiveBlockerCount[val] += 1
					} else {
						consecutiveBlockerCount[val] = 0
					}
				}
				key := int32(blocker)
				count, found_count := consecutiveBlockerCount[key]
				req, found_req := consecutiveBlockerReq[key]
				if !found_count || !found_req {
					logrus.Errorf("Unknown blocker %v this should not happen", blocker)
					count = 1
					req = 1
				}
				if count >= req {
					reqVel = 0.0
					if count == req {
						str, _ := frontend.CruiseBlocker_name[key]
						logrus.Infof("Cruise control blocked due to %v", str)
					}
				}
			}
			if actualVel != reqVel {
				since := time.Since(lastUpdateTime)
				if since >= minUpdateFreq {
					cruiseState.ConditionalWriteOnCurrent(func() bool {
						changed := cruiseState.CommandedVelocityMph != reqVel || cruiseState.CruiseBlocker != blocker
						if changed {
							cruiseState.CommandedVelocityMph = reqVel
							cruiseState.CruiseBlocker = blocker
						}
						return changed
					})
					_, err := hwClient.SetJimboxSpeed(reqVel, actualVel)
					if err != nil {
						logrus.Warningf("Couldn't set cruise control speed, %v", err)
					} else {
						lastUpdateTime = time.Now()
						forceTime = defaultForceTime
					}
				} else {
					// Need to update, but cant send data too quickly
					forceTime = minUpdateFreq - since // guaranteed to be > 0 based on above if
				}
			} else {
				cruiseState.ConditionalWriteOnCurrent(func() bool {
					changed := cruiseState.CruiseBlocker != blocker
					if changed {
						cruiseState.CruiseBlocker = blocker
					}
					return changed
				})
			}
		}
	}
}
