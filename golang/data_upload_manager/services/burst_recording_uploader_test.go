package services

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
	"time"

	"github.com/spf13/afero"
	"github.com/stretchr/testify/assert"
)

type testFile struct {
	name    string
	modTime time.Time
}

func TestGetFilenameFromDir_Latest(t *testing.T) {
	testDir := "/test/data_upload_manager/latest"
	err := appFs.MkdirAll(testDir, os.ModePerm)
	assert.NoError(t, err)

	fileData := []testFile{}

	for i := 0; i < 200; i++ {
		modifiedTime := time.Now().Add(time.Duration(i) * time.Minute)
		if i == 100 {
			modifiedTime = time.Now().Add(time.Duration(10000000) * time.Minute)
		}
		fileData = append(fileData, testFile{
			fmt.Sprintf("burst-record_robot1_row1_cam2_%d.metadata.json", i),
			modifiedTime,
		})
		fileData = append(fileData, testFile{
			fmt.Sprintf("burst-record_robot1_row1_cam2_%d.png", i),
			modifiedTime,
		})
	}

	rand.Seed(42)
	rand.Shuffle(len(fileData), func(i, j int) { fileData[i], fileData[j] = fileData[j], fileData[i] })

	for _, fd := range fileData {
		filePath := fmt.Sprintf("%s/%s", testDir, fd.name)
		err := afero.WriteFile(appFs, filePath, []byte("test content"), 0644)
		assert.NoError(t, err)

		err = appFs.Chtimes(filePath, fd.modTime, fd.modTime)
		assert.NoError(t, err)
	}

	basename, err := getFilenameFromDir(testDir, "latest")
	assert.NoError(t, err)

	expectedBasename := "burst-record_robot1_row1_cam2_100"
	assert.Equal(t, expectedBasename, basename)
}
