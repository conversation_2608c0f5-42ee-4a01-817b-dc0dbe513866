package state

import (
	"math"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUploadQueue_Push(t *testing.T) {
	queue := NewUploadQueue(5)

	queue.Push(0.5, "file1.png")
	queue.Push(0.8, "file2.png")
	queue.Push(0.3, "file3.png")

	assert.Equal(t, 3, queue.Len(), "Queue should have 3 items")
}

func TestUploadQueue_PushLimit(t *testing.T) {
	queue := NewUploadQueue(1)

	queue.Push(0.5, "file1.png")
	queue.Push(0.8, "file2.png")
	queue.Push(0.3, "file3.png")

	assert.Equal(t, 1, queue.Len(), "Queue should have 3 items")
}

func TestUploadQueue_PopMax(t *testing.T) {
	queue := NewUploadQueue(5)

	queue.Push(0.5, "file1.png")
	queue.Push(0.8, "file2.png") // Highest score
	queue.Push(0.3, "file3.png")

	filename, _, ok := queue.PopMax()
	assert.True(t, ok, "PopMax should succeed")
	assert.Equal(t, "file2.png", filename, "Should pop the item with highest score")

	assert.Equal(t, 2, queue.Len(), "Queue should have 2 items after pop")

	filename, _, ok = queue.PopMax()
	assert.True(t, ok, "PopMax should succeed")
	assert.Equal(t, "file1.png", filename, "Should pop the item with next highest score")

	filename, _, ok = queue.PopMax()
	assert.True(t, ok, "PopMax should succeed")
	assert.Equal(t, "file3.png", filename, "Should pop the item with lowest score")

	filename, _, ok = queue.PopMax()
	assert.False(t, ok, "PopMax should fail on empty queue")
	assert.Equal(t, "", filename, "Filename should be empty string on failure")
}

func TestUploadQueue_Rebalance(t *testing.T) {
	queue := NewUploadQueue(3)

	// Push more than 2*maxSize items
	queue.Push(0.4, "file4.png")
	queue.Push(0.1, "file1.png")
	queue.Push(0.2, "file2.png")
	queue.Push(0.7, "file7.png")
	queue.Push(0.5, "file5.png")
	queue.Push(0.3, "file3.png")
	queue.Push(0.6, "file6.png") // This should trigger rebalance

	assert.Equal(t, 3, queue.Len(), "Queue should be rebalanced to maxSize")

	filename, _, ok := queue.PopMax()
	assert.True(t, ok)
	assert.Equal(t, "file7.png", filename, "Highest score item should remain after rebalance")

	filename, _, ok = queue.PopMax()
	assert.True(t, ok)
	assert.Equal(t, "file6.png", filename, "Second highest score item should remain after rebalance")

	filename, _, ok = queue.PopMax()
	assert.True(t, ok)
	assert.Equal(t, "file5.png", filename, "Third highest score item should remain after rebalance")
}

func TestUploadQueue_Concurrency(t *testing.T) {
	queue := NewUploadQueue(100)

	done := make(chan bool)
	for i := 0; i < 10; i++ {
		go func(i int) {
			for j := 0; j < 10; j++ {
				score := float64(i*10+j) / 100.0
				filename := "file_" + string(rune('a'+i)) + "_" + string(rune('0'+j)) + ".png"
				queue.Push(score, filename)
			}
			done <- true
		}(i)
	}

	for i := 0; i < 10; i++ {
		<-done
	}

	assert.Equal(t, 100, queue.Len(), "Queue should have 100 items")

	EPS := 1e-6

	for i := 0; i < 100; i++ {
		_, score, ok := queue.PopMax()
		assert.True(t, ok, "PopMax should succeed")
		assert.True(t, math.Abs(score-float64(99-i)/100.0) <= EPS, "Score should be in decreasing order")
	}

	assert.Equal(t, 0, queue.Len(), "Queue should be empty after popping all items")
}
