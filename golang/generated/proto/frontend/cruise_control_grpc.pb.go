// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.1
// source: frontend/proto/cruise_control.proto

package frontend

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CruiseControlService_GetNextCruiseState_FullMethodName = "/carbon.frontend.cruise_control.CruiseControlService/GetNextCruiseState"
)

// CruiseControlServiceClient is the client API for CruiseControlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CruiseControlServiceClient interface {
	GetNextCruiseState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextCruiseStateResponse, error)
}

type cruiseControlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCruiseControlServiceClient(cc grpc.ClientConnInterface) CruiseControlServiceClient {
	return &cruiseControlServiceClient{cc}
}

func (c *cruiseControlServiceClient) GetNextCruiseState(ctx context.Context, in *Timestamp, opts ...grpc.CallOption) (*GetNextCruiseStateResponse, error) {
	out := new(GetNextCruiseStateResponse)
	err := c.cc.Invoke(ctx, CruiseControlService_GetNextCruiseState_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CruiseControlServiceServer is the server API for CruiseControlService service.
// All implementations must embed UnimplementedCruiseControlServiceServer
// for forward compatibility
type CruiseControlServiceServer interface {
	GetNextCruiseState(context.Context, *Timestamp) (*GetNextCruiseStateResponse, error)
	mustEmbedUnimplementedCruiseControlServiceServer()
}

// UnimplementedCruiseControlServiceServer must be embedded to have forward compatible implementations.
type UnimplementedCruiseControlServiceServer struct {
}

func (UnimplementedCruiseControlServiceServer) GetNextCruiseState(context.Context, *Timestamp) (*GetNextCruiseStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextCruiseState not implemented")
}
func (UnimplementedCruiseControlServiceServer) mustEmbedUnimplementedCruiseControlServiceServer() {}

// UnsafeCruiseControlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CruiseControlServiceServer will
// result in compilation errors.
type UnsafeCruiseControlServiceServer interface {
	mustEmbedUnimplementedCruiseControlServiceServer()
}

func RegisterCruiseControlServiceServer(s grpc.ServiceRegistrar, srv CruiseControlServiceServer) {
	s.RegisterService(&CruiseControlService_ServiceDesc, srv)
}

func _CruiseControlService_GetNextCruiseState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Timestamp)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CruiseControlServiceServer).GetNextCruiseState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CruiseControlService_GetNextCruiseState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CruiseControlServiceServer).GetNextCruiseState(ctx, req.(*Timestamp))
	}
	return interceptor(ctx, in, info, handler)
}

// CruiseControlService_ServiceDesc is the grpc.ServiceDesc for CruiseControlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CruiseControlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "carbon.frontend.cruise_control.CruiseControlService",
	HandlerType: (*CruiseControlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetNextCruiseState",
			Handler:    _CruiseControlService_GetNextCruiseState_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "frontend/proto/cruise_control.proto",
}
