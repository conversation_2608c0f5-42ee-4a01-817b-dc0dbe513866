// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: frontend/proto/cruise_control.proto

package frontend

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CruiseBlocker int32

const (
	CruiseBlocker_NONE             CruiseBlocker = 0
	CruiseBlocker_ACTIVE_ALARM     CruiseBlocker = 1
	CruiseBlocker_NOT_WEEDING      CruiseBlocker = 2
	CruiseBlocker_TRACTOR_NOT_SAFE CruiseBlocker = 3
)

// Enum value maps for CruiseBlocker.
var (
	CruiseBlocker_name = map[int32]string{
		0: "NONE",
		1: "ACTIVE_ALARM",
		2: "NOT_WEEDING",
		3: "TRACTOR_NOT_SAFE",
	}
	CruiseBlocker_value = map[string]int32{
		"NONE":             0,
		"ACTIVE_ALARM":     1,
		"NOT_WEEDING":      2,
		"TRACTOR_NOT_SAFE": 3,
	}
)

func (x CruiseBlocker) Enum() *CruiseBlocker {
	p := new(CruiseBlocker)
	*p = x
	return p
}

func (x CruiseBlocker) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CruiseBlocker) Descriptor() protoreflect.EnumDescriptor {
	return file_frontend_proto_cruise_control_proto_enumTypes[0].Descriptor()
}

func (CruiseBlocker) Type() protoreflect.EnumType {
	return &file_frontend_proto_cruise_control_proto_enumTypes[0]
}

func (x CruiseBlocker) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CruiseBlocker.Descriptor instead.
func (CruiseBlocker) EnumDescriptor() ([]byte, []int) {
	return file_frontend_proto_cruise_control_proto_rawDescGZIP(), []int{0}
}

type CruiseState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CommandedVelocity float64       `protobuf:"fixed64,1,opt,name=commanded_velocity,json=commandedVelocity,proto3" json:"commanded_velocity,omitempty"`
	Blocker           CruiseBlocker `protobuf:"varint,2,opt,name=blocker,proto3,enum=carbon.frontend.cruise_control.CruiseBlocker" json:"blocker,omitempty"`
}

func (x *CruiseState) Reset() {
	*x = CruiseState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_cruise_control_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CruiseState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruiseState) ProtoMessage() {}

func (x *CruiseState) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_cruise_control_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruiseState.ProtoReflect.Descriptor instead.
func (*CruiseState) Descriptor() ([]byte, []int) {
	return file_frontend_proto_cruise_control_proto_rawDescGZIP(), []int{0}
}

func (x *CruiseState) GetCommandedVelocity() float64 {
	if x != nil {
		return x.CommandedVelocity
	}
	return 0
}

func (x *CruiseState) GetBlocker() CruiseBlocker {
	if x != nil {
		return x.Blocker
	}
	return CruiseBlocker_NONE
}

type GetNextCruiseStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ts    *Timestamp   `protobuf:"bytes,1,opt,name=ts,proto3" json:"ts,omitempty"`
	State *CruiseState `protobuf:"bytes,2,opt,name=state,proto3" json:"state,omitempty"`
}

func (x *GetNextCruiseStateResponse) Reset() {
	*x = GetNextCruiseStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_frontend_proto_cruise_control_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextCruiseStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextCruiseStateResponse) ProtoMessage() {}

func (x *GetNextCruiseStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_frontend_proto_cruise_control_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextCruiseStateResponse.ProtoReflect.Descriptor instead.
func (*GetNextCruiseStateResponse) Descriptor() ([]byte, []int) {
	return file_frontend_proto_cruise_control_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextCruiseStateResponse) GetTs() *Timestamp {
	if x != nil {
		return x.Ts
	}
	return nil
}

func (x *GetNextCruiseStateResponse) GetState() *CruiseState {
	if x != nil {
		return x.State
	}
	return nil
}

var File_frontend_proto_cruise_control_proto protoreflect.FileDescriptor

var file_frontend_proto_cruise_control_proto_rawDesc = []byte{
	0x0a, 0x23, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x2f, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x1a, 0x19, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x85, 0x01, 0x0a, 0x0b, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x5f, 0x76, 0x65,
	0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x65, 0x64, 0x56, 0x65, 0x6c, 0x6f, 0x63, 0x69, 0x74, 0x79, 0x12,
	0x47, 0x0a, 0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x2d, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x2e, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x52,
	0x07, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x22, 0x90, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x4e, 0x65, 0x78, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x02, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65,
	0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x2e, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2a, 0x52, 0x0a, 0x0d, 0x43,
	0x72, 0x75, 0x69, 0x73, 0x65, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x08, 0x0a, 0x04,
	0x4e, 0x4f, 0x4e, 0x45, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x5f, 0x41, 0x4c, 0x41, 0x52, 0x4d, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x4f, 0x54, 0x5f,
	0x57, 0x45, 0x45, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x52, 0x41,
	0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x53, 0x41, 0x46, 0x45, 0x10, 0x03, 0x32,
	0x89, 0x01, 0x0a, 0x14, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f,
	0x6c, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x71, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x78, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f,
	0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x75, 0x74, 0x69, 0x6c, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x1a,
	0x3a, 0x2e, 0x63, 0x61, 0x72, 0x62, 0x6f, 0x6e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x72, 0x75, 0x69, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x43, 0x72, 0x75, 0x69, 0x73, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x10, 0x5a, 0x0e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_frontend_proto_cruise_control_proto_rawDescOnce sync.Once
	file_frontend_proto_cruise_control_proto_rawDescData = file_frontend_proto_cruise_control_proto_rawDesc
)

func file_frontend_proto_cruise_control_proto_rawDescGZIP() []byte {
	file_frontend_proto_cruise_control_proto_rawDescOnce.Do(func() {
		file_frontend_proto_cruise_control_proto_rawDescData = protoimpl.X.CompressGZIP(file_frontend_proto_cruise_control_proto_rawDescData)
	})
	return file_frontend_proto_cruise_control_proto_rawDescData
}

var file_frontend_proto_cruise_control_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_frontend_proto_cruise_control_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_frontend_proto_cruise_control_proto_goTypes = []interface{}{
	(CruiseBlocker)(0),                 // 0: carbon.frontend.cruise_control.CruiseBlocker
	(*CruiseState)(nil),                // 1: carbon.frontend.cruise_control.CruiseState
	(*GetNextCruiseStateResponse)(nil), // 2: carbon.frontend.cruise_control.GetNextCruiseStateResponse
	(*Timestamp)(nil),                  // 3: carbon.frontend.util.Timestamp
}
var file_frontend_proto_cruise_control_proto_depIdxs = []int32{
	0, // 0: carbon.frontend.cruise_control.CruiseState.blocker:type_name -> carbon.frontend.cruise_control.CruiseBlocker
	3, // 1: carbon.frontend.cruise_control.GetNextCruiseStateResponse.ts:type_name -> carbon.frontend.util.Timestamp
	1, // 2: carbon.frontend.cruise_control.GetNextCruiseStateResponse.state:type_name -> carbon.frontend.cruise_control.CruiseState
	3, // 3: carbon.frontend.cruise_control.CruiseControlService.GetNextCruiseState:input_type -> carbon.frontend.util.Timestamp
	2, // 4: carbon.frontend.cruise_control.CruiseControlService.GetNextCruiseState:output_type -> carbon.frontend.cruise_control.GetNextCruiseStateResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_frontend_proto_cruise_control_proto_init() }
func file_frontend_proto_cruise_control_proto_init() {
	if File_frontend_proto_cruise_control_proto != nil {
		return
	}
	file_frontend_proto_util_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_frontend_proto_cruise_control_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CruiseState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_frontend_proto_cruise_control_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextCruiseStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_frontend_proto_cruise_control_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_frontend_proto_cruise_control_proto_goTypes,
		DependencyIndexes: file_frontend_proto_cruise_control_proto_depIdxs,
		EnumInfos:         file_frontend_proto_cruise_control_proto_enumTypes,
		MessageInfos:      file_frontend_proto_cruise_control_proto_msgTypes,
	}.Build()
	File_frontend_proto_cruise_control_proto = out.File
	file_frontend_proto_cruise_control_proto_rawDesc = nil
	file_frontend_proto_cruise_control_proto_goTypes = nil
	file_frontend_proto_cruise_control_proto_depIdxs = nil
}
