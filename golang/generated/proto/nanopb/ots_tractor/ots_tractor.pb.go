// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.1
// source: ots_tractor.proto

package ots_tractor

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type TractorVariantType int32

const (
	TractorVariantType_TV_UNKNOWN TractorVariantType = 0
	TractorVariantType_TV_JD_6LH  TractorVariantType = 1
	TractorVariantType_TV_JD_6LHM TractorVariantType = 2
	TractorVariantType_TV_JD_6PRO TractorVariantType = 3
	TractorVariantType_TV_JD_7LH  TractorVariantType = 4
	TractorVariantType_TV_JD_7PRO TractorVariantType = 5
	TractorVariantType_TV_JD_8RH  TractorVariantType = 6
)

// Enum value maps for TractorVariantType.
var (
	TractorVariantType_name = map[int32]string{
		0: "TV_UNKNOWN",
		1: "TV_JD_6LH",
		2: "TV_JD_6LHM",
		3: "TV_JD_6PRO",
		4: "TV_JD_7LH",
		5: "TV_JD_7PRO",
		6: "TV_JD_8RH",
	}
	TractorVariantType_value = map[string]int32{
		"TV_UNKNOWN": 0,
		"TV_JD_6LH":  1,
		"TV_JD_6LHM": 2,
		"TV_JD_6PRO": 3,
		"TV_JD_7LH":  4,
		"TV_JD_7PRO": 5,
		"TV_JD_8RH":  6,
	}
)

func (x TractorVariantType) Enum() *TractorVariantType {
	p := new(TractorVariantType)
	*p = x
	return p
}

func (x TractorVariantType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TractorVariantType) Descriptor() protoreflect.EnumDescriptor {
	return file_ots_tractor_proto_enumTypes[0].Descriptor()
}

func (TractorVariantType) Type() protoreflect.EnumType {
	return &file_ots_tractor_proto_enumTypes[0]
}

func (x TractorVariantType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TractorVariantType.Descriptor instead.
func (TractorVariantType) EnumDescriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{0}
}

type Gear int32

const (
	Gear_GEAR_PARK      Gear = 0
	Gear_GEAR_REVERSE   Gear = 1
	Gear_GEAR_NEUTRAL   Gear = 2
	Gear_GEAR_FORWARD   Gear = 3
	Gear_GEAR_POWERZERO Gear = 4
)

// Enum value maps for Gear.
var (
	Gear_name = map[int32]string{
		0: "GEAR_PARK",
		1: "GEAR_REVERSE",
		2: "GEAR_NEUTRAL",
		3: "GEAR_FORWARD",
		4: "GEAR_POWERZERO",
	}
	Gear_value = map[string]int32{
		"GEAR_PARK":      0,
		"GEAR_REVERSE":   1,
		"GEAR_NEUTRAL":   2,
		"GEAR_FORWARD":   3,
		"GEAR_POWERZERO": 4,
	}
)

func (x Gear) Enum() *Gear {
	p := new(Gear)
	*p = x
	return p
}

func (x Gear) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gear) Descriptor() protoreflect.EnumDescriptor {
	return file_ots_tractor_proto_enumTypes[1].Descriptor()
}

func (Gear) Type() protoreflect.EnumType {
	return &file_ots_tractor_proto_enumTypes[1]
}

func (x Gear) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gear.Descriptor instead.
func (Gear) EnumDescriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{1}
}

type Lights int32

const (
	Lights_LIGHTS_OFF  Lights = 0
	Lights_LIGHTS_LOW  Lights = 1
	Lights_LIGHTS_HIGH Lights = 2
)

// Enum value maps for Lights.
var (
	Lights_name = map[int32]string{
		0: "LIGHTS_OFF",
		1: "LIGHTS_LOW",
		2: "LIGHTS_HIGH",
	}
	Lights_value = map[string]int32{
		"LIGHTS_OFF":  0,
		"LIGHTS_LOW":  1,
		"LIGHTS_HIGH": 2,
	}
)

func (x Lights) Enum() *Lights {
	p := new(Lights)
	*p = x
	return p
}

func (x Lights) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Lights) Descriptor() protoreflect.EnumDescriptor {
	return file_ots_tractor_proto_enumTypes[2].Descriptor()
}

func (Lights) Type() protoreflect.EnumType {
	return &file_ots_tractor_proto_enumTypes[2]
}

func (x Lights) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Lights.Descriptor instead.
func (Lights) EnumDescriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{2}
}

type HitchCmd int32

const (
	HitchCmd_LIFT    HitchCmd = 0
	HitchCmd_LOWER   HitchCmd = 1
	HitchCmd_PRECISE HitchCmd = 2
)

// Enum value maps for HitchCmd.
var (
	HitchCmd_name = map[int32]string{
		0: "LIFT",
		1: "LOWER",
		2: "PRECISE",
	}
	HitchCmd_value = map[string]int32{
		"LIFT":    0,
		"LOWER":   1,
		"PRECISE": 2,
	}
)

func (x HitchCmd) Enum() *HitchCmd {
	p := new(HitchCmd)
	*p = x
	return p
}

func (x HitchCmd) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HitchCmd) Descriptor() protoreflect.EnumDescriptor {
	return file_ots_tractor_proto_enumTypes[3].Descriptor()
}

func (HitchCmd) Type() protoreflect.EnumType {
	return &file_ots_tractor_proto_enumTypes[3]
}

func (x HitchCmd) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HitchCmd.Descriptor instead.
func (HitchCmd) EnumDescriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{3}
}

type GearState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gear Gear `protobuf:"varint,1,opt,name=gear,proto3,enum=ots_tractor.Gear" json:"gear,omitempty"`
}

func (x *GearState) Reset() {
	*x = GearState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GearState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GearState) ProtoMessage() {}

func (x *GearState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GearState.ProtoReflect.Descriptor instead.
func (*GearState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{0}
}

func (x *GearState) GetGear() Gear {
	if x != nil {
		return x.Gear
	}
	return Gear_GEAR_PARK
}

type LightsState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Lights Lights `protobuf:"varint,1,opt,name=lights,proto3,enum=ots_tractor.Lights" json:"lights,omitempty"`
}

func (x *LightsState) Reset() {
	*x = LightsState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LightsState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LightsState) ProtoMessage() {}

func (x *LightsState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LightsState.ProtoReflect.Descriptor instead.
func (*LightsState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{1}
}

func (x *LightsState) GetLights() Lights {
	if x != nil {
		return x.Lights
	}
	return Lights_LIGHTS_OFF
}

type SpeedControlState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Speed float32 `protobuf:"fixed32,1,opt,name=speed,proto3" json:"speed,omitempty"`
}

func (x *SpeedControlState) Reset() {
	*x = SpeedControlState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SpeedControlState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpeedControlState) ProtoMessage() {}

func (x *SpeedControlState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpeedControlState.ProtoReflect.Descriptor instead.
func (*SpeedControlState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{2}
}

func (x *SpeedControlState) GetSpeed() float32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type RpmDialState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rpms float32 `protobuf:"fixed32,1,opt,name=rpms,proto3" json:"rpms,omitempty"` // 0-1. percentage, not actual
}

func (x *RpmDialState) Reset() {
	*x = RpmDialState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RpmDialState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpmDialState) ProtoMessage() {}

func (x *RpmDialState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpmDialState.ProtoReflect.Descriptor instead.
func (*RpmDialState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{3}
}

func (x *RpmDialState) GetRpms() float32 {
	if x != nil {
		return x.Rpms
	}
	return 0
}

type PTOFailType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BadGear bool `protobuf:"varint,1,opt,name=bad_gear,json=badGear,proto3" json:"bad_gear,omitempty"`
	BadRpms bool `protobuf:"varint,2,opt,name=bad_rpms,json=badRpms,proto3" json:"bad_rpms,omitempty"`
}

func (x *PTOFailType) Reset() {
	*x = PTOFailType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PTOFailType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PTOFailType) ProtoMessage() {}

func (x *PTOFailType) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PTOFailType.ProtoReflect.Descriptor instead.
func (*PTOFailType) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{4}
}

func (x *PTOFailType) GetBadGear() bool {
	if x != nil {
		return x.BadGear
	}
	return false
}

func (x *PTOFailType) GetBadRpms() bool {
	if x != nil {
		return x.BadRpms
	}
	return false
}

type PTOCommandState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Enabled bool `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
}

func (x *PTOCommandState) Reset() {
	*x = PTOCommandState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PTOCommandState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PTOCommandState) ProtoMessage() {}

func (x *PTOCommandState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PTOCommandState.ProtoReflect.Descriptor instead.
func (*PTOCommandState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{5}
}

func (x *PTOCommandState) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type PtoState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Decision:
	//
	//	*PtoState_State
	//	*PtoState_Failure
	Decision isPtoState_Decision `protobuf_oneof:"decision"`
}

func (x *PtoState) Reset() {
	*x = PtoState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PtoState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PtoState) ProtoMessage() {}

func (x *PtoState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PtoState.ProtoReflect.Descriptor instead.
func (*PtoState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{6}
}

func (m *PtoState) GetDecision() isPtoState_Decision {
	if m != nil {
		return m.Decision
	}
	return nil
}

func (x *PtoState) GetState() *PTOCommandState {
	if x, ok := x.GetDecision().(*PtoState_State); ok {
		return x.State
	}
	return nil
}

func (x *PtoState) GetFailure() *PTOFailType {
	if x, ok := x.GetDecision().(*PtoState_Failure); ok {
		return x.Failure
	}
	return nil
}

type isPtoState_Decision interface {
	isPtoState_Decision()
}

type PtoState_State struct {
	State *PTOCommandState `protobuf:"bytes,1,opt,name=state,proto3,oneof"` // Expected state, not actual
}

type PtoState_Failure struct {
	Failure *PTOFailType `protobuf:"bytes,2,opt,name=failure,proto3,oneof"`
}

func (*PtoState_State) isPtoState_Decision() {}

func (*PtoState_Failure) isPtoState_Decision() {}

type HitchV2Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hitch               HitchCmd `protobuf:"varint,1,opt,name=hitch,proto3,enum=ots_tractor.HitchCmd" json:"hitch,omitempty"`
	PreciseHitchPercent float32  `protobuf:"fixed32,2,opt,name=precise_hitch_percent,json=preciseHitchPercent,proto3" json:"precise_hitch_percent,omitempty"`
}

func (x *HitchV2Request) Reset() {
	*x = HitchV2Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HitchV2Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HitchV2Request) ProtoMessage() {}

func (x *HitchV2Request) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HitchV2Request.ProtoReflect.Descriptor instead.
func (*HitchV2Request) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{7}
}

func (x *HitchV2Request) GetHitch() HitchCmd {
	if x != nil {
		return x.Hitch
	}
	return HitchCmd_LIFT
}

func (x *HitchV2Request) GetPreciseHitchPercent() float32 {
	if x != nil {
		return x.PreciseHitchPercent
	}
	return 0
}

type HitchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HitchLiftForce int32 `protobuf:"varint,1,opt,name=hitch_lift_force,json=hitchLiftForce,proto3" json:"hitch_lift_force,omitempty"`
}

func (x *HitchRequest) Reset() {
	*x = HitchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HitchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HitchRequest) ProtoMessage() {}

func (x *HitchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HitchRequest.ProtoReflect.Descriptor instead.
func (*HitchRequest) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{8}
}

func (x *HitchRequest) GetHitchLiftForce() int32 {
	if x != nil {
		return x.HitchLiftForce
	}
	return 0
}

type HitchReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *HitchReply) Reset() {
	*x = HitchReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HitchReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HitchReply) ProtoMessage() {}

func (x *HitchReply) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HitchReply.ProtoReflect.Descriptor instead.
func (*HitchReply) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{9}
}

type ScvRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ScvId     int32 `protobuf:"varint,1,opt,name=scv_id,json=scvId,proto3" json:"scv_id,omitempty"`
	Force     int32 `protobuf:"varint,2,opt,name=force,proto3" json:"force,omitempty"`                            // [-100, 100]
	CmdTimeMs int32 `protobuf:"varint,3,opt,name=cmd_time_ms,json=cmdTimeMs,proto3" json:"cmd_time_ms,omitempty"` // How long to command this force for
}

func (x *ScvRequest) Reset() {
	*x = ScvRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScvRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScvRequest) ProtoMessage() {}

func (x *ScvRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScvRequest.ProtoReflect.Descriptor instead.
func (*ScvRequest) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{10}
}

func (x *ScvRequest) GetScvId() int32 {
	if x != nil {
		return x.ScvId
	}
	return 0
}

func (x *ScvRequest) GetForce() int32 {
	if x != nil {
		return x.Force
	}
	return 0
}

func (x *ScvRequest) GetCmdTimeMs() int32 {
	if x != nil {
		return x.CmdTimeMs
	}
	return 0
}

type ScvReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *ScvReply) Reset() {
	*x = ScvReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ScvReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScvReply) ProtoMessage() {}

func (x *ScvReply) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScvReply.ProtoReflect.Descriptor instead.
func (*ScvReply) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{11}
}

type TractorVariantState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Variant TractorVariantType `protobuf:"varint,1,opt,name=variant,proto3,enum=ots_tractor.TractorVariantType" json:"variant,omitempty"`
}

func (x *TractorVariantState) Reset() {
	*x = TractorVariantState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TractorVariantState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TractorVariantState) ProtoMessage() {}

func (x *TractorVariantState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TractorVariantState.ProtoReflect.Descriptor instead.
func (*TractorVariantState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{12}
}

func (x *TractorVariantState) GetVariant() TractorVariantType {
	if x != nil {
		return x.Variant
	}
	return TractorVariantType_TV_UNKNOWN
}

type WheelAngleCalState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SensorDegPerBit    float32 `protobuf:"fixed32,1,opt,name=sensor_deg_per_bit,json=sensorDegPerBit,proto3" json:"sensor_deg_per_bit,omitempty"`
	SensorCenterVal    uint32  `protobuf:"varint,2,opt,name=sensor_center_val,json=sensorCenterVal,proto3" json:"sensor_center_val,omitempty"`
	CenterTrimDeg      float32 `protobuf:"fixed32,3,opt,name=center_trim_deg,json=centerTrimDeg,proto3" json:"center_trim_deg,omitempty"`
	RightLockDeg       float32 `protobuf:"fixed32,4,opt,name=right_lock_deg,json=rightLockDeg,proto3" json:"right_lock_deg,omitempty"`
	SensorFullLeftVal  uint32  `protobuf:"varint,5,opt,name=sensor_full_left_val,json=sensorFullLeftVal,proto3" json:"sensor_full_left_val,omitempty"`
	SensorFullRightVal uint32  `protobuf:"varint,6,opt,name=sensor_full_right_val,json=sensorFullRightVal,proto3" json:"sensor_full_right_val,omitempty"`
}

func (x *WheelAngleCalState) Reset() {
	*x = WheelAngleCalState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WheelAngleCalState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WheelAngleCalState) ProtoMessage() {}

func (x *WheelAngleCalState) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WheelAngleCalState.ProtoReflect.Descriptor instead.
func (*WheelAngleCalState) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{13}
}

func (x *WheelAngleCalState) GetSensorDegPerBit() float32 {
	if x != nil {
		return x.SensorDegPerBit
	}
	return 0
}

func (x *WheelAngleCalState) GetSensorCenterVal() uint32 {
	if x != nil {
		return x.SensorCenterVal
	}
	return 0
}

func (x *WheelAngleCalState) GetCenterTrimDeg() float32 {
	if x != nil {
		return x.CenterTrimDeg
	}
	return 0
}

func (x *WheelAngleCalState) GetRightLockDeg() float32 {
	if x != nil {
		return x.RightLockDeg
	}
	return 0
}

func (x *WheelAngleCalState) GetSensorFullLeftVal() uint32 {
	if x != nil {
		return x.SensorFullLeftVal
	}
	return 0
}

func (x *WheelAngleCalState) GetSensorFullRightVal() uint32 {
	if x != nil {
		return x.SensorFullRightVal
	}
	return 0
}

type FuelLevel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FuelLevel float32 `protobuf:"fixed32,1,opt,name=fuel_level,json=fuelLevel,proto3" json:"fuel_level,omitempty"`
}

func (x *FuelLevel) Reset() {
	*x = FuelLevel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FuelLevel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FuelLevel) ProtoMessage() {}

func (x *FuelLevel) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FuelLevel.ProtoReflect.Descriptor instead.
func (*FuelLevel) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{14}
}

func (x *FuelLevel) GetFuelLevel() float32 {
	if x != nil {
		return x.FuelLevel
	}
	return 0
}

type EngineTemp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EngineTemp float32 `protobuf:"fixed32,1,opt,name=engine_temp,json=engineTemp,proto3" json:"engine_temp,omitempty"`
}

func (x *EngineTemp) Reset() {
	*x = EngineTemp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EngineTemp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EngineTemp) ProtoMessage() {}

func (x *EngineTemp) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EngineTemp.ProtoReflect.Descriptor instead.
func (*EngineTemp) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{15}
}

func (x *EngineTemp) GetEngineTemp() float32 {
	if x != nil {
		return x.EngineTemp
	}
	return 0
}

type IgnitionErrorCodes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BadGear bool `protobuf:"varint,1,opt,name=bad_gear,json=badGear,proto3" json:"bad_gear,omitempty"`
	BadRpms bool `protobuf:"varint,2,opt,name=bad_rpms,json=badRpms,proto3" json:"bad_rpms,omitempty"`
	BadPto  bool `protobuf:"varint,3,opt,name=bad_pto,json=badPto,proto3" json:"bad_pto,omitempty"`
}

func (x *IgnitionErrorCodes) Reset() {
	*x = IgnitionErrorCodes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnitionErrorCodes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnitionErrorCodes) ProtoMessage() {}

func (x *IgnitionErrorCodes) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnitionErrorCodes.ProtoReflect.Descriptor instead.
func (*IgnitionErrorCodes) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{16}
}

func (x *IgnitionErrorCodes) GetBadGear() bool {
	if x != nil {
		return x.BadGear
	}
	return false
}

func (x *IgnitionErrorCodes) GetBadRpms() bool {
	if x != nil {
		return x.BadRpms
	}
	return false
}

func (x *IgnitionErrorCodes) GetBadPto() bool {
	if x != nil {
		return x.BadPto
	}
	return false
}

type IgnitionOffResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Decision:
	//
	//	*IgnitionOffResponse_TargetIgnitionState
	//	*IgnitionOffResponse_Failure
	Decision isIgnitionOffResponse_Decision `protobuf_oneof:"decision"`
}

func (x *IgnitionOffResponse) Reset() {
	*x = IgnitionOffResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IgnitionOffResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IgnitionOffResponse) ProtoMessage() {}

func (x *IgnitionOffResponse) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IgnitionOffResponse.ProtoReflect.Descriptor instead.
func (*IgnitionOffResponse) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{17}
}

func (m *IgnitionOffResponse) GetDecision() isIgnitionOffResponse_Decision {
	if m != nil {
		return m.Decision
	}
	return nil
}

func (x *IgnitionOffResponse) GetTargetIgnitionState() bool {
	if x, ok := x.GetDecision().(*IgnitionOffResponse_TargetIgnitionState); ok {
		return x.TargetIgnitionState
	}
	return false
}

func (x *IgnitionOffResponse) GetFailure() *IgnitionErrorCodes {
	if x, ok := x.GetDecision().(*IgnitionOffResponse_Failure); ok {
		return x.Failure
	}
	return nil
}

type isIgnitionOffResponse_Decision interface {
	isIgnitionOffResponse_Decision()
}

type IgnitionOffResponse_TargetIgnitionState struct {
	TargetIgnitionState bool `protobuf:"varint,1,opt,name=target_ignition_state,json=targetIgnitionState,proto3,oneof"`
}

type IgnitionOffResponse_Failure struct {
	Failure *IgnitionErrorCodes `protobuf:"bytes,2,opt,name=failure,proto3,oneof"`
}

func (*IgnitionOffResponse_TargetIgnitionState) isIgnitionOffResponse_Decision() {}

func (*IgnitionOffResponse_Failure) isIgnitionOffResponse_Decision() {}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{18}
}

type SetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Set:
	//
	//	*SetRequest_Gear
	//	*SetRequest_Lights
	//	*SetRequest_SpeedControl
	//	*SetRequest_Hitch
	//	*SetRequest_Scv
	//	*SetRequest_Rpms
	//	*SetRequest_FrontPto
	//	*SetRequest_RearPto
	//	*SetRequest_Variant
	//	*SetRequest_WheelCal
	//	*SetRequest_IgnitionOff
	//	*SetRequest_Hitch_V2
	//	*SetRequest_PowerCycle
	Set isSetRequest_Set `protobuf_oneof:"set"`
}

func (x *SetRequest) Reset() {
	*x = SetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetRequest) ProtoMessage() {}

func (x *SetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetRequest.ProtoReflect.Descriptor instead.
func (*SetRequest) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{19}
}

func (m *SetRequest) GetSet() isSetRequest_Set {
	if m != nil {
		return m.Set
	}
	return nil
}

func (x *SetRequest) GetGear() *GearState {
	if x, ok := x.GetSet().(*SetRequest_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *SetRequest) GetLights() *LightsState {
	if x, ok := x.GetSet().(*SetRequest_Lights); ok {
		return x.Lights
	}
	return nil
}

func (x *SetRequest) GetSpeedControl() *SpeedControlState {
	if x, ok := x.GetSet().(*SetRequest_SpeedControl); ok {
		return x.SpeedControl
	}
	return nil
}

func (x *SetRequest) GetHitch() *HitchRequest {
	if x, ok := x.GetSet().(*SetRequest_Hitch); ok {
		return x.Hitch
	}
	return nil
}

func (x *SetRequest) GetScv() *ScvRequest {
	if x, ok := x.GetSet().(*SetRequest_Scv); ok {
		return x.Scv
	}
	return nil
}

func (x *SetRequest) GetRpms() *RpmDialState {
	if x, ok := x.GetSet().(*SetRequest_Rpms); ok {
		return x.Rpms
	}
	return nil
}

func (x *SetRequest) GetFrontPto() *PTOCommandState {
	if x, ok := x.GetSet().(*SetRequest_FrontPto); ok {
		return x.FrontPto
	}
	return nil
}

// Deprecated: Marked as deprecated in ots_tractor.proto.
func (x *SetRequest) GetRearPto() *Empty {
	if x, ok := x.GetSet().(*SetRequest_RearPto); ok {
		return x.RearPto
	}
	return nil
}

func (x *SetRequest) GetVariant() *TractorVariantState {
	if x, ok := x.GetSet().(*SetRequest_Variant); ok {
		return x.Variant
	}
	return nil
}

func (x *SetRequest) GetWheelCal() *WheelAngleCalState {
	if x, ok := x.GetSet().(*SetRequest_WheelCal); ok {
		return x.WheelCal
	}
	return nil
}

func (x *SetRequest) GetIgnitionOff() *Empty {
	if x, ok := x.GetSet().(*SetRequest_IgnitionOff); ok {
		return x.IgnitionOff
	}
	return nil
}

func (x *SetRequest) GetHitch_V2() *HitchV2Request {
	if x, ok := x.GetSet().(*SetRequest_Hitch_V2); ok {
		return x.Hitch_V2
	}
	return nil
}

func (x *SetRequest) GetPowerCycle() *Empty {
	if x, ok := x.GetSet().(*SetRequest_PowerCycle); ok {
		return x.PowerCycle
	}
	return nil
}

type isSetRequest_Set interface {
	isSetRequest_Set()
}

type SetRequest_Gear struct {
	Gear *GearState `protobuf:"bytes,1,opt,name=gear,proto3,oneof"`
}

type SetRequest_Lights struct {
	Lights *LightsState `protobuf:"bytes,2,opt,name=lights,proto3,oneof"`
}

type SetRequest_SpeedControl struct {
	SpeedControl *SpeedControlState `protobuf:"bytes,3,opt,name=speed_control,json=speedControl,proto3,oneof"`
}

type SetRequest_Hitch struct {
	Hitch *HitchRequest `protobuf:"bytes,4,opt,name=hitch,proto3,oneof"`
}

type SetRequest_Scv struct {
	Scv *ScvRequest `protobuf:"bytes,5,opt,name=scv,proto3,oneof"`
}

type SetRequest_Rpms struct {
	Rpms *RpmDialState `protobuf:"bytes,6,opt,name=rpms,proto3,oneof"`
}

type SetRequest_FrontPto struct {
	FrontPto *PTOCommandState `protobuf:"bytes,7,opt,name=front_pto,json=frontPto,proto3,oneof"`
}

type SetRequest_RearPto struct {
	// Deprecated: Marked as deprecated in ots_tractor.proto.
	RearPto *Empty `protobuf:"bytes,8,opt,name=rear_pto,json=rearPto,proto3,oneof"`
}

type SetRequest_Variant struct {
	Variant *TractorVariantState `protobuf:"bytes,9,opt,name=variant,proto3,oneof"`
}

type SetRequest_WheelCal struct {
	WheelCal *WheelAngleCalState `protobuf:"bytes,10,opt,name=wheel_cal,json=wheelCal,proto3,oneof"`
}

type SetRequest_IgnitionOff struct {
	IgnitionOff *Empty `protobuf:"bytes,11,opt,name=ignition_off,json=ignitionOff,proto3,oneof"`
}

type SetRequest_Hitch_V2 struct {
	Hitch_V2 *HitchV2Request `protobuf:"bytes,12,opt,name=hitch_V2,json=hitchV2,proto3,oneof"`
}

type SetRequest_PowerCycle struct {
	PowerCycle *Empty `protobuf:"bytes,13,opt,name=power_cycle,json=powerCycle,proto3,oneof"`
}

func (*SetRequest_Gear) isSetRequest_Set() {}

func (*SetRequest_Lights) isSetRequest_Set() {}

func (*SetRequest_SpeedControl) isSetRequest_Set() {}

func (*SetRequest_Hitch) isSetRequest_Set() {}

func (*SetRequest_Scv) isSetRequest_Set() {}

func (*SetRequest_Rpms) isSetRequest_Set() {}

func (*SetRequest_FrontPto) isSetRequest_Set() {}

func (*SetRequest_RearPto) isSetRequest_Set() {}

func (*SetRequest_Variant) isSetRequest_Set() {}

func (*SetRequest_WheelCal) isSetRequest_Set() {}

func (*SetRequest_IgnitionOff) isSetRequest_Set() {}

func (*SetRequest_Hitch_V2) isSetRequest_Set() {}

func (*SetRequest_PowerCycle) isSetRequest_Set() {}

type SetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Set:
	//
	//	*SetReply_Gear
	//	*SetReply_Lights
	//	*SetReply_SpeedControl
	//	*SetReply_Hitch
	//	*SetReply_Scv
	//	*SetReply_Rpms
	//	*SetReply_FrontPto
	//	*SetReply_RearPto
	//	*SetReply_Variant
	//	*SetReply_WheelCal
	//	*SetReply_IgnitionOff
	//	*SetReply_Hitch_V2
	//	*SetReply_PowerCycle
	Set isSetReply_Set `protobuf_oneof:"set"`
}

func (x *SetReply) Reset() {
	*x = SetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetReply) ProtoMessage() {}

func (x *SetReply) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetReply.ProtoReflect.Descriptor instead.
func (*SetReply) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{20}
}

func (m *SetReply) GetSet() isSetReply_Set {
	if m != nil {
		return m.Set
	}
	return nil
}

func (x *SetReply) GetGear() *GearState {
	if x, ok := x.GetSet().(*SetReply_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *SetReply) GetLights() *LightsState {
	if x, ok := x.GetSet().(*SetReply_Lights); ok {
		return x.Lights
	}
	return nil
}

func (x *SetReply) GetSpeedControl() *SpeedControlState {
	if x, ok := x.GetSet().(*SetReply_SpeedControl); ok {
		return x.SpeedControl
	}
	return nil
}

func (x *SetReply) GetHitch() *HitchReply {
	if x, ok := x.GetSet().(*SetReply_Hitch); ok {
		return x.Hitch
	}
	return nil
}

func (x *SetReply) GetScv() *ScvReply {
	if x, ok := x.GetSet().(*SetReply_Scv); ok {
		return x.Scv
	}
	return nil
}

func (x *SetReply) GetRpms() *RpmDialState {
	if x, ok := x.GetSet().(*SetReply_Rpms); ok {
		return x.Rpms
	}
	return nil
}

func (x *SetReply) GetFrontPto() *PtoState {
	if x, ok := x.GetSet().(*SetReply_FrontPto); ok {
		return x.FrontPto
	}
	return nil
}

// Deprecated: Marked as deprecated in ots_tractor.proto.
func (x *SetReply) GetRearPto() *Empty {
	if x, ok := x.GetSet().(*SetReply_RearPto); ok {
		return x.RearPto
	}
	return nil
}

func (x *SetReply) GetVariant() *TractorVariantState {
	if x, ok := x.GetSet().(*SetReply_Variant); ok {
		return x.Variant
	}
	return nil
}

func (x *SetReply) GetWheelCal() *WheelAngleCalState {
	if x, ok := x.GetSet().(*SetReply_WheelCal); ok {
		return x.WheelCal
	}
	return nil
}

func (x *SetReply) GetIgnitionOff() *IgnitionOffResponse {
	if x, ok := x.GetSet().(*SetReply_IgnitionOff); ok {
		return x.IgnitionOff
	}
	return nil
}

func (x *SetReply) GetHitch_V2() *HitchReply {
	if x, ok := x.GetSet().(*SetReply_Hitch_V2); ok {
		return x.Hitch_V2
	}
	return nil
}

func (x *SetReply) GetPowerCycle() *IgnitionOffResponse {
	if x, ok := x.GetSet().(*SetReply_PowerCycle); ok {
		return x.PowerCycle
	}
	return nil
}

type isSetReply_Set interface {
	isSetReply_Set()
}

type SetReply_Gear struct {
	Gear *GearState `protobuf:"bytes,1,opt,name=gear,proto3,oneof"`
}

type SetReply_Lights struct {
	Lights *LightsState `protobuf:"bytes,2,opt,name=lights,proto3,oneof"`
}

type SetReply_SpeedControl struct {
	SpeedControl *SpeedControlState `protobuf:"bytes,3,opt,name=speed_control,json=speedControl,proto3,oneof"`
}

type SetReply_Hitch struct {
	Hitch *HitchReply `protobuf:"bytes,4,opt,name=hitch,proto3,oneof"`
}

type SetReply_Scv struct {
	Scv *ScvReply `protobuf:"bytes,5,opt,name=scv,proto3,oneof"`
}

type SetReply_Rpms struct {
	Rpms *RpmDialState `protobuf:"bytes,6,opt,name=rpms,proto3,oneof"`
}

type SetReply_FrontPto struct {
	FrontPto *PtoState `protobuf:"bytes,7,opt,name=front_pto,json=frontPto,proto3,oneof"`
}

type SetReply_RearPto struct {
	// Deprecated: Marked as deprecated in ots_tractor.proto.
	RearPto *Empty `protobuf:"bytes,8,opt,name=rear_pto,json=rearPto,proto3,oneof"`
}

type SetReply_Variant struct {
	Variant *TractorVariantState `protobuf:"bytes,9,opt,name=variant,proto3,oneof"`
}

type SetReply_WheelCal struct {
	WheelCal *WheelAngleCalState `protobuf:"bytes,10,opt,name=wheel_cal,json=wheelCal,proto3,oneof"`
}

type SetReply_IgnitionOff struct {
	IgnitionOff *IgnitionOffResponse `protobuf:"bytes,11,opt,name=ignition_off,json=ignitionOff,proto3,oneof"`
}

type SetReply_Hitch_V2 struct {
	Hitch_V2 *HitchReply `protobuf:"bytes,12,opt,name=hitch_V2,json=hitchV2,proto3,oneof"`
}

type SetReply_PowerCycle struct {
	PowerCycle *IgnitionOffResponse `protobuf:"bytes,13,opt,name=power_cycle,json=powerCycle,proto3,oneof"`
}

func (*SetReply_Gear) isSetReply_Set() {}

func (*SetReply_Lights) isSetReply_Set() {}

func (*SetReply_SpeedControl) isSetReply_Set() {}

func (*SetReply_Hitch) isSetReply_Set() {}

func (*SetReply_Scv) isSetReply_Set() {}

func (*SetReply_Rpms) isSetReply_Set() {}

func (*SetReply_FrontPto) isSetReply_Set() {}

func (*SetReply_RearPto) isSetReply_Set() {}

func (*SetReply_Variant) isSetReply_Set() {}

func (*SetReply_WheelCal) isSetReply_Set() {}

func (*SetReply_IgnitionOff) isSetReply_Set() {}

func (*SetReply_Hitch_V2) isSetReply_Set() {}

func (*SetReply_PowerCycle) isSetReply_Set() {}

type GetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Get:
	//
	//	*GetRequest_Gear
	//	*GetRequest_Lights
	//	*GetRequest_SpeedControl
	//	*GetRequest_Rpms
	//	*GetRequest_FrontPto
	//	*GetRequest_RearPto
	//	*GetRequest_Variant
	//	*GetRequest_WheelCal
	//	*GetRequest_FuelLevel
	//	*GetRequest_EngineTemp
	Get isGetRequest_Get `protobuf_oneof:"get"`
}

func (x *GetRequest) Reset() {
	*x = GetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequest) ProtoMessage() {}

func (x *GetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequest.ProtoReflect.Descriptor instead.
func (*GetRequest) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{21}
}

func (m *GetRequest) GetGet() isGetRequest_Get {
	if m != nil {
		return m.Get
	}
	return nil
}

func (x *GetRequest) GetGear() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *GetRequest) GetLights() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Lights); ok {
		return x.Lights
	}
	return nil
}

func (x *GetRequest) GetSpeedControl() *Empty {
	if x, ok := x.GetGet().(*GetRequest_SpeedControl); ok {
		return x.SpeedControl
	}
	return nil
}

func (x *GetRequest) GetRpms() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Rpms); ok {
		return x.Rpms
	}
	return nil
}

func (x *GetRequest) GetFrontPto() *Empty {
	if x, ok := x.GetGet().(*GetRequest_FrontPto); ok {
		return x.FrontPto
	}
	return nil
}

// Deprecated: Marked as deprecated in ots_tractor.proto.
func (x *GetRequest) GetRearPto() *Empty {
	if x, ok := x.GetGet().(*GetRequest_RearPto); ok {
		return x.RearPto
	}
	return nil
}

func (x *GetRequest) GetVariant() *Empty {
	if x, ok := x.GetGet().(*GetRequest_Variant); ok {
		return x.Variant
	}
	return nil
}

func (x *GetRequest) GetWheelCal() *Empty {
	if x, ok := x.GetGet().(*GetRequest_WheelCal); ok {
		return x.WheelCal
	}
	return nil
}

func (x *GetRequest) GetFuelLevel() *Empty {
	if x, ok := x.GetGet().(*GetRequest_FuelLevel); ok {
		return x.FuelLevel
	}
	return nil
}

func (x *GetRequest) GetEngineTemp() *Empty {
	if x, ok := x.GetGet().(*GetRequest_EngineTemp); ok {
		return x.EngineTemp
	}
	return nil
}

type isGetRequest_Get interface {
	isGetRequest_Get()
}

type GetRequest_Gear struct {
	Gear *Empty `protobuf:"bytes,1,opt,name=gear,proto3,oneof"`
}

type GetRequest_Lights struct {
	Lights *Empty `protobuf:"bytes,2,opt,name=lights,proto3,oneof"`
}

type GetRequest_SpeedControl struct {
	SpeedControl *Empty `protobuf:"bytes,3,opt,name=speed_control,json=speedControl,proto3,oneof"`
}

type GetRequest_Rpms struct {
	Rpms *Empty `protobuf:"bytes,4,opt,name=rpms,proto3,oneof"`
}

type GetRequest_FrontPto struct {
	FrontPto *Empty `protobuf:"bytes,5,opt,name=front_pto,json=frontPto,proto3,oneof"`
}

type GetRequest_RearPto struct {
	// Deprecated: Marked as deprecated in ots_tractor.proto.
	RearPto *Empty `protobuf:"bytes,6,opt,name=rear_pto,json=rearPto,proto3,oneof"`
}

type GetRequest_Variant struct {
	Variant *Empty `protobuf:"bytes,7,opt,name=variant,proto3,oneof"`
}

type GetRequest_WheelCal struct {
	WheelCal *Empty `protobuf:"bytes,8,opt,name=wheel_cal,json=wheelCal,proto3,oneof"`
}

type GetRequest_FuelLevel struct {
	FuelLevel *Empty `protobuf:"bytes,9,opt,name=fuel_level,json=fuelLevel,proto3,oneof"`
}

type GetRequest_EngineTemp struct {
	EngineTemp *Empty `protobuf:"bytes,10,opt,name=engine_temp,json=engineTemp,proto3,oneof"`
}

func (*GetRequest_Gear) isGetRequest_Get() {}

func (*GetRequest_Lights) isGetRequest_Get() {}

func (*GetRequest_SpeedControl) isGetRequest_Get() {}

func (*GetRequest_Rpms) isGetRequest_Get() {}

func (*GetRequest_FrontPto) isGetRequest_Get() {}

func (*GetRequest_RearPto) isGetRequest_Get() {}

func (*GetRequest_Variant) isGetRequest_Get() {}

func (*GetRequest_WheelCal) isGetRequest_Get() {}

func (*GetRequest_FuelLevel) isGetRequest_Get() {}

func (*GetRequest_EngineTemp) isGetRequest_Get() {}

type GetReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Get:
	//
	//	*GetReply_Gear
	//	*GetReply_Lights
	//	*GetReply_SpeedControl
	//	*GetReply_Rpms
	//	*GetReply_FrontPto
	//	*GetReply_RearPto
	//	*GetReply_Variant
	//	*GetReply_WheelCal
	//	*GetReply_FuelLevel
	//	*GetReply_EngineTemp
	Get isGetReply_Get `protobuf_oneof:"get"`
}

func (x *GetReply) Reset() {
	*x = GetReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReply) ProtoMessage() {}

func (x *GetReply) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReply.ProtoReflect.Descriptor instead.
func (*GetReply) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{22}
}

func (m *GetReply) GetGet() isGetReply_Get {
	if m != nil {
		return m.Get
	}
	return nil
}

func (x *GetReply) GetGear() *GearState {
	if x, ok := x.GetGet().(*GetReply_Gear); ok {
		return x.Gear
	}
	return nil
}

func (x *GetReply) GetLights() *LightsState {
	if x, ok := x.GetGet().(*GetReply_Lights); ok {
		return x.Lights
	}
	return nil
}

func (x *GetReply) GetSpeedControl() *SpeedControlState {
	if x, ok := x.GetGet().(*GetReply_SpeedControl); ok {
		return x.SpeedControl
	}
	return nil
}

func (x *GetReply) GetRpms() *RpmDialState {
	if x, ok := x.GetGet().(*GetReply_Rpms); ok {
		return x.Rpms
	}
	return nil
}

func (x *GetReply) GetFrontPto() bool {
	if x, ok := x.GetGet().(*GetReply_FrontPto); ok {
		return x.FrontPto
	}
	return false
}

// Deprecated: Marked as deprecated in ots_tractor.proto.
func (x *GetReply) GetRearPto() *Empty {
	if x, ok := x.GetGet().(*GetReply_RearPto); ok {
		return x.RearPto
	}
	return nil
}

func (x *GetReply) GetVariant() *TractorVariantState {
	if x, ok := x.GetGet().(*GetReply_Variant); ok {
		return x.Variant
	}
	return nil
}

func (x *GetReply) GetWheelCal() *WheelAngleCalState {
	if x, ok := x.GetGet().(*GetReply_WheelCal); ok {
		return x.WheelCal
	}
	return nil
}

func (x *GetReply) GetFuelLevel() *FuelLevel {
	if x, ok := x.GetGet().(*GetReply_FuelLevel); ok {
		return x.FuelLevel
	}
	return nil
}

func (x *GetReply) GetEngineTemp() *EngineTemp {
	if x, ok := x.GetGet().(*GetReply_EngineTemp); ok {
		return x.EngineTemp
	}
	return nil
}

type isGetReply_Get interface {
	isGetReply_Get()
}

type GetReply_Gear struct {
	Gear *GearState `protobuf:"bytes,1,opt,name=gear,proto3,oneof"`
}

type GetReply_Lights struct {
	Lights *LightsState `protobuf:"bytes,2,opt,name=lights,proto3,oneof"`
}

type GetReply_SpeedControl struct {
	SpeedControl *SpeedControlState `protobuf:"bytes,3,opt,name=speed_control,json=speedControl,proto3,oneof"`
}

type GetReply_Rpms struct {
	Rpms *RpmDialState `protobuf:"bytes,4,opt,name=rpms,proto3,oneof"`
}

type GetReply_FrontPto struct {
	FrontPto bool `protobuf:"varint,5,opt,name=front_pto,json=frontPto,proto3,oneof"` // Actual PTO state, not commanded state
}

type GetReply_RearPto struct {
	// Deprecated: Marked as deprecated in ots_tractor.proto.
	RearPto *Empty `protobuf:"bytes,6,opt,name=rear_pto,json=rearPto,proto3,oneof"`
}

type GetReply_Variant struct {
	Variant *TractorVariantState `protobuf:"bytes,7,opt,name=variant,proto3,oneof"`
}

type GetReply_WheelCal struct {
	WheelCal *WheelAngleCalState `protobuf:"bytes,8,opt,name=wheel_cal,json=wheelCal,proto3,oneof"`
}

type GetReply_FuelLevel struct {
	FuelLevel *FuelLevel `protobuf:"bytes,9,opt,name=fuel_level,json=fuelLevel,proto3,oneof"`
}

type GetReply_EngineTemp struct {
	EngineTemp *EngineTemp `protobuf:"bytes,10,opt,name=engine_temp,json=engineTemp,proto3,oneof"`
}

func (*GetReply_Gear) isGetReply_Get() {}

func (*GetReply_Lights) isGetReply_Get() {}

func (*GetReply_SpeedControl) isGetReply_Get() {}

func (*GetReply_Rpms) isGetReply_Get() {}

func (*GetReply_FrontPto) isGetReply_Get() {}

func (*GetReply_RearPto) isGetReply_Get() {}

func (*GetReply_Variant) isGetReply_Get() {}

func (*GetReply_WheelCal) isGetReply_Get() {}

func (*GetReply_FuelLevel) isGetReply_Get() {}

func (*GetReply_EngineTemp) isGetReply_Get() {}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Request:
	//
	//	*Request_Set
	//	*Request_Get
	Request isRequest_Request `protobuf_oneof:"request"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{23}
}

func (m *Request) GetRequest() isRequest_Request {
	if m != nil {
		return m.Request
	}
	return nil
}

func (x *Request) GetSet() *SetRequest {
	if x, ok := x.GetRequest().(*Request_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Request) GetGet() *GetRequest {
	if x, ok := x.GetRequest().(*Request_Get); ok {
		return x.Get
	}
	return nil
}

type isRequest_Request interface {
	isRequest_Request()
}

type Request_Set struct {
	Set *SetRequest `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Request_Get struct {
	Get *GetRequest `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

func (*Request_Set) isRequest_Request() {}

func (*Request_Get) isRequest_Request() {}

type Reply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Reply:
	//
	//	*Reply_Set
	//	*Reply_Get
	Reply isReply_Reply `protobuf_oneof:"reply"`
}

func (x *Reply) Reset() {
	*x = Reply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_ots_tractor_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reply) ProtoMessage() {}

func (x *Reply) ProtoReflect() protoreflect.Message {
	mi := &file_ots_tractor_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reply.ProtoReflect.Descriptor instead.
func (*Reply) Descriptor() ([]byte, []int) {
	return file_ots_tractor_proto_rawDescGZIP(), []int{24}
}

func (m *Reply) GetReply() isReply_Reply {
	if m != nil {
		return m.Reply
	}
	return nil
}

func (x *Reply) GetSet() *SetReply {
	if x, ok := x.GetReply().(*Reply_Set); ok {
		return x.Set
	}
	return nil
}

func (x *Reply) GetGet() *GetReply {
	if x, ok := x.GetReply().(*Reply_Get); ok {
		return x.Get
	}
	return nil
}

type isReply_Reply interface {
	isReply_Reply()
}

type Reply_Set struct {
	Set *SetReply `protobuf:"bytes,1,opt,name=set,proto3,oneof"`
}

type Reply_Get struct {
	Get *GetReply `protobuf:"bytes,2,opt,name=get,proto3,oneof"`
}

func (*Reply_Set) isReply_Reply() {}

func (*Reply_Get) isReply_Reply() {}

var File_ots_tractor_proto protoreflect.FileDescriptor

var file_ots_tractor_proto_rawDesc = []byte{
	0x0a, 0x11, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x22, 0x32, 0x0a, 0x09, 0x47, 0x65, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a,
	0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x52, 0x04,
	0x67, 0x65, 0x61, 0x72, 0x22, 0x3a, 0x0a, 0x0b, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x2b, 0x0a, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x52, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x22, 0x29, 0x0a, 0x11, 0x53, 0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x73, 0x70, 0x65, 0x65, 0x64, 0x22, 0x22, 0x0a, 0x0c, 0x52,
	0x70, 0x6d, 0x44, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x70, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x22,
	0x43, 0x0a, 0x0b, 0x50, 0x54, 0x4f, 0x46, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x61, 0x64, 0x5f, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x07, 0x62, 0x61, 0x64, 0x47, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x64,
	0x5f, 0x72, 0x70, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x61, 0x64,
	0x52, 0x70, 0x6d, 0x73, 0x22, 0x2b, 0x0a, 0x0f, 0x50, 0x54, 0x4f, 0x43, 0x6f, 0x6d, 0x6d, 0x61,
	0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x64, 0x22, 0x82, 0x01, 0x0a, 0x08, 0x50, 0x74, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x34,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x54, 0x4f, 0x43,
	0x6f, 0x6d, 0x6d, 0x61, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x50, 0x54, 0x4f, 0x46, 0x61, 0x69, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x48,
	0x00, 0x52, 0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x64, 0x65,
	0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x71, 0x0a, 0x0e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x56,
	0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x05, 0x68, 0x69, 0x74, 0x63,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6d, 0x64, 0x52, 0x05,
	0x68, 0x69, 0x74, 0x63, 0x68, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x65,
	0x5f, 0x68, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x70, 0x72, 0x65, 0x63, 0x69, 0x73, 0x65, 0x48, 0x69, 0x74,
	0x63, 0x68, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x22, 0x38, 0x0a, 0x0c, 0x48, 0x69, 0x74,
	0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x6c, 0x69, 0x66, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0e, 0x68, 0x69, 0x74, 0x63, 0x68, 0x4c, 0x69, 0x66, 0x74, 0x46, 0x6f,
	0x72, 0x63, 0x65, 0x22, 0x0c, 0x0a, 0x0a, 0x48, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x59, 0x0a, 0x0a, 0x53, 0x63, 0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x63, 0x76, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x73, 0x63, 0x76, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x12, 0x1e, 0x0a, 0x0b,
	0x63, 0x6d, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x63, 0x6d, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x4d, 0x73, 0x22, 0x0a, 0x0a, 0x08,
	0x53, 0x63, 0x76, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x50, 0x0a, 0x13, 0x54, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x39, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1f, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x22, 0x9f, 0x02, 0x0a, 0x12, 0x57,
	0x68, 0x65, 0x65, 0x6c, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x2b, 0x0a, 0x12, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x67, 0x5f,
	0x70, 0x65, 0x72, 0x5f, 0x62, 0x69, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0f, 0x73,
	0x65, 0x6e, 0x73, 0x6f, 0x72, 0x44, 0x65, 0x67, 0x50, 0x65, 0x72, 0x42, 0x69, 0x74, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x5f, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f,
	0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f, 0x73, 0x65, 0x6e, 0x73, 0x6f,
	0x72, 0x43, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x65,
	0x6e, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x72, 0x69, 0x6d, 0x5f, 0x64, 0x65, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x0d, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x54, 0x72, 0x69, 0x6d, 0x44,
	0x65, 0x67, 0x12, 0x24, 0x0a, 0x0e, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x6b,
	0x5f, 0x64, 0x65, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0c, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x4c, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x67, 0x12, 0x2f, 0x0a, 0x14, 0x73, 0x65, 0x6e, 0x73,
	0x6f, 0x72, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x61, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72, 0x46, 0x75,
	0x6c, 0x6c, 0x4c, 0x65, 0x66, 0x74, 0x56, 0x61, 0x6c, 0x12, 0x31, 0x0a, 0x15, 0x73, 0x65, 0x6e,
	0x73, 0x6f, 0x72, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x76,
	0x61, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x12, 0x73, 0x65, 0x6e, 0x73, 0x6f, 0x72,
	0x46, 0x75, 0x6c, 0x6c, 0x52, 0x69, 0x67, 0x68, 0x74, 0x56, 0x61, 0x6c, 0x22, 0x2a, 0x0a, 0x09,
	0x46, 0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x75, 0x65,
	0x6c, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x66,
	0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x22, 0x2d, 0x0a, 0x0a, 0x45, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x0a, 0x65, 0x6e, 0x67,
	0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x22, 0x63, 0x0a, 0x12, 0x49, 0x67, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x62, 0x61, 0x64, 0x5f, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x07, 0x62, 0x61, 0x64, 0x47, 0x65, 0x61, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x64, 0x5f,
	0x72, 0x70, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x62, 0x61, 0x64, 0x52,
	0x70, 0x6d, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x61, 0x64, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x62, 0x61, 0x64, 0x50, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a,
	0x13, 0x49, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x34, 0x0a, 0x15, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69,
	0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x13, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x67, 0x6e,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x07, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x67, 0x6e, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x73, 0x48, 0x00, 0x52, 0x07,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xe7, 0x05, 0x0a,
	0x0a, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x04, 0x67,
	0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x74, 0x73, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72, 0x12, 0x32, 0x0a, 0x06, 0x6c, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x74, 0x73, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x45, 0x0a,
	0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e,
	0x74, 0x72, 0x6f, 0x6c, 0x12, 0x31, 0x0a, 0x05, 0x68, 0x69, 0x74, 0x63, 0x68, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00,
	0x52, 0x05, 0x68, 0x69, 0x74, 0x63, 0x68, 0x12, 0x2b, 0x0a, 0x03, 0x73, 0x63, 0x76, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x53, 0x63, 0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x03, 0x73, 0x63, 0x76, 0x12, 0x2f, 0x0a, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x52, 0x70, 0x6d, 0x44, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52,
	0x04, 0x72, 0x70, 0x6d, 0x73, 0x12, 0x3b, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x70,
	0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x54, 0x4f, 0x43, 0x6f, 0x6d, 0x6d, 0x61, 0x6e,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50,
	0x74, 0x6f, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x72, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07,
	0x72, 0x65, 0x61, 0x72, 0x50, 0x74, 0x6f, 0x12, 0x3c, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x6e, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61,
	0x72, 0x69, 0x61, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x63,
	0x61, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x41, 0x6e, 0x67, 0x6c,
	0x65, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x77, 0x68, 0x65,
	0x65, 0x6c, 0x43, 0x61, 0x6c, 0x12, 0x37, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x0b, 0x69, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x38,
	0x0a, 0x08, 0x68, 0x69, 0x74, 0x63, 0x68, 0x5f, 0x56, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48,
	0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52,
	0x07, 0x68, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x12, 0x35, 0x0a, 0x0b, 0x70, 0x6f, 0x77, 0x65,
	0x72, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x43, 0x79, 0x63, 0x6c, 0x65, 0x42,
	0x05, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x22, 0xf2, 0x05, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x47, 0x65, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61,
	0x72, 0x12, 0x32, 0x0a, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x4c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x6c,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f,
	0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c,
	0x73, 0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x05,
	0x68, 0x69, 0x74, 0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x05, 0x68, 0x69, 0x74, 0x63, 0x68, 0x12, 0x29, 0x0a,
	0x03, 0x73, 0x63, 0x76, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x74, 0x73,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x63, 0x76, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x48, 0x00, 0x52, 0x03, 0x73, 0x63, 0x76, 0x12, 0x2f, 0x0a, 0x04, 0x72, 0x70, 0x6d, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x70, 0x6d, 0x44, 0x69, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x48, 0x00, 0x52, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x12, 0x34, 0x0a, 0x09, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f,
	0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x50, 0x74, 0x6f, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x74, 0x6f, 0x12,
	0x33, 0x0a, 0x08, 0x72, 0x65, 0x61, 0x72, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x61,
	0x72, 0x50, 0x74, 0x6f, 0x12, 0x3c, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61,
	0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61,
	0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x09, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x6c, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x2e, 0x57, 0x68, 0x65, 0x65, 0x6c, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x61,
	0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x43,
	0x61, 0x6c, 0x12, 0x45, 0x0a, 0x0c, 0x69, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6f,
	0x66, 0x66, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x49, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f,
	0x66, 0x66, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0b, 0x69, 0x67,
	0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x12, 0x34, 0x0a, 0x08, 0x68, 0x69, 0x74,
	0x63, 0x68, 0x5f, 0x56, 0x32, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x48, 0x69, 0x74, 0x63, 0x68, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x07, 0x68, 0x69, 0x74, 0x63, 0x68, 0x56, 0x32, 0x12,
	0x43, 0x0a, 0x0b, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x5f, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x18, 0x0d,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x49, 0x67, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x6f, 0x77, 0x65, 0x72, 0x43,
	0x79, 0x63, 0x6c, 0x65, 0x42, 0x05, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x22, 0x87, 0x04, 0x0a, 0x0a,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x04, 0x67, 0x65,
	0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x04,
	0x67, 0x65, 0x61, 0x72, 0x12, 0x2c, 0x0a, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x06, 0x6c, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x74,
	0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52,
	0x0c, 0x73, 0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x28, 0x0a,
	0x04, 0x72, 0x70, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x12, 0x31, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x5f, 0x70, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73,
	0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00,
	0x52, 0x08, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x74, 0x6f, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65,
	0x61, 0x72, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f,
	0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x61, 0x72, 0x50, 0x74, 0x6f, 0x12,
	0x2e, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x12,
	0x31, 0x0a, 0x09, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x08, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x43,
	0x61, 0x6c, 0x12, 0x33, 0x0a, 0x0a, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x09, 0x66, 0x75,
	0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x35, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x6f,
	0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x48, 0x00, 0x52, 0x0a, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x42, 0x05,
	0x0a, 0x03, 0x67, 0x65, 0x74, 0x22, 0xb2, 0x04, 0x0a, 0x08, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x12, 0x2c, 0x0a, 0x04, 0x67, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47,
	0x65, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x04, 0x67, 0x65, 0x61, 0x72,
	0x12, 0x32, 0x0a, 0x06, 0x6c, 0x69, 0x67, 0x68, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4c,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x06, 0x6c, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x73, 0x70, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x70, 0x65, 0x65, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x73,
	0x70, 0x65, 0x65, 0x64, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x2f, 0x0a, 0x04, 0x72,
	0x70, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x6f, 0x74, 0x73, 0x5f,
	0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x52, 0x70, 0x6d, 0x44, 0x69, 0x61, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x04, 0x72, 0x70, 0x6d, 0x73, 0x12, 0x1d, 0x0a, 0x09,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48,
	0x00, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x74, 0x6f, 0x12, 0x33, 0x0a, 0x08, 0x72,
	0x65, 0x61, 0x72, 0x5f, 0x70, 0x74, 0x6f, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x07, 0x72, 0x65, 0x61, 0x72, 0x50, 0x74, 0x6f,
	0x12, 0x3c, 0x0a, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x07, 0x76, 0x61, 0x72, 0x69, 0x61, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x09, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x63, 0x61, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e,
	0x57, 0x68, 0x65, 0x65, 0x6c, 0x41, 0x6e, 0x67, 0x6c, 0x65, 0x43, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x48, 0x00, 0x52, 0x08, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x43, 0x61, 0x6c, 0x12, 0x37,
	0x0a, 0x0a, 0x66, 0x75, 0x65, 0x6c, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x46, 0x75, 0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x48, 0x00, 0x52, 0x09, 0x66, 0x75,
	0x65, 0x6c, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x3a, 0x0a, 0x0b, 0x65, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x5f, 0x74, 0x65, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f,
	0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x42, 0x05, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x22, 0x6e, 0x0a, 0x07, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x2e, 0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x73,
	0x65, 0x74, 0x12, 0x2b, 0x0a, 0x03, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74, 0x42,
	0x09, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x66, 0x0a, 0x05, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x29, 0x0a, 0x03, 0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x6f, 0x74, 0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x53,
	0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x73, 0x65, 0x74, 0x12, 0x29,
	0x0a, 0x03, 0x67, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x6f, 0x74,
	0x73, 0x5f, 0x74, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x48, 0x00, 0x52, 0x03, 0x67, 0x65, 0x74, 0x42, 0x07, 0x0a, 0x05, 0x72, 0x65, 0x70,
	0x6c, 0x79, 0x2a, 0x81, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61,
	0x72, 0x69, 0x61, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x56, 0x5f,
	0x4a, 0x44, 0x5f, 0x36, 0x4c, 0x48, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a,
	0x44, 0x5f, 0x36, 0x4c, 0x48, 0x4d, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a,
	0x44, 0x5f, 0x36, 0x50, 0x52, 0x4f, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x56, 0x5f, 0x4a,
	0x44, 0x5f, 0x37, 0x4c, 0x48, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x56, 0x5f, 0x4a, 0x44,
	0x5f, 0x37, 0x50, 0x52, 0x4f, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x56, 0x5f, 0x4a, 0x44,
	0x5f, 0x38, 0x52, 0x48, 0x10, 0x06, 0x2a, 0x5f, 0x0a, 0x04, 0x47, 0x65, 0x61, 0x72, 0x12, 0x0d,
	0x0a, 0x09, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x50, 0x41, 0x52, 0x4b, 0x10, 0x00, 0x12, 0x10, 0x0a,
	0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x10, 0x01, 0x12,
	0x10, 0x0a, 0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x4e, 0x45, 0x55, 0x54, 0x52, 0x41, 0x4c, 0x10,
	0x02, 0x12, 0x10, 0x0a, 0x0c, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52,
	0x44, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x45, 0x41, 0x52, 0x5f, 0x50, 0x4f, 0x57, 0x45,
	0x52, 0x5a, 0x45, 0x52, 0x4f, 0x10, 0x04, 0x2a, 0x39, 0x0a, 0x06, 0x4c, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x4f, 0x46, 0x46, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x4c, 0x4f, 0x57, 0x10,
	0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x49, 0x47, 0x48, 0x54, 0x53, 0x5f, 0x48, 0x49, 0x47, 0x48,
	0x10, 0x02, 0x2a, 0x2c, 0x0a, 0x08, 0x48, 0x69, 0x74, 0x63, 0x68, 0x43, 0x6d, 0x64, 0x12, 0x08,
	0x0a, 0x04, 0x4c, 0x49, 0x46, 0x54, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x4f, 0x57, 0x45,
	0x52, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x52, 0x45, 0x43, 0x49, 0x53, 0x45, 0x10, 0x02,
	0x42, 0x14, 0x5a, 0x12, 0x6e, 0x61, 0x6e, 0x6f, 0x70, 0x62, 0x2f, 0x6f, 0x74, 0x73, 0x5f, 0x74,
	0x72, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_ots_tractor_proto_rawDescOnce sync.Once
	file_ots_tractor_proto_rawDescData = file_ots_tractor_proto_rawDesc
)

func file_ots_tractor_proto_rawDescGZIP() []byte {
	file_ots_tractor_proto_rawDescOnce.Do(func() {
		file_ots_tractor_proto_rawDescData = protoimpl.X.CompressGZIP(file_ots_tractor_proto_rawDescData)
	})
	return file_ots_tractor_proto_rawDescData
}

var file_ots_tractor_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_ots_tractor_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_ots_tractor_proto_goTypes = []interface{}{
	(TractorVariantType)(0),     // 0: ots_tractor.TractorVariantType
	(Gear)(0),                   // 1: ots_tractor.Gear
	(Lights)(0),                 // 2: ots_tractor.Lights
	(HitchCmd)(0),               // 3: ots_tractor.HitchCmd
	(*GearState)(nil),           // 4: ots_tractor.GearState
	(*LightsState)(nil),         // 5: ots_tractor.LightsState
	(*SpeedControlState)(nil),   // 6: ots_tractor.SpeedControlState
	(*RpmDialState)(nil),        // 7: ots_tractor.RpmDialState
	(*PTOFailType)(nil),         // 8: ots_tractor.PTOFailType
	(*PTOCommandState)(nil),     // 9: ots_tractor.PTOCommandState
	(*PtoState)(nil),            // 10: ots_tractor.PtoState
	(*HitchV2Request)(nil),      // 11: ots_tractor.HitchV2Request
	(*HitchRequest)(nil),        // 12: ots_tractor.HitchRequest
	(*HitchReply)(nil),          // 13: ots_tractor.HitchReply
	(*ScvRequest)(nil),          // 14: ots_tractor.ScvRequest
	(*ScvReply)(nil),            // 15: ots_tractor.ScvReply
	(*TractorVariantState)(nil), // 16: ots_tractor.TractorVariantState
	(*WheelAngleCalState)(nil),  // 17: ots_tractor.WheelAngleCalState
	(*FuelLevel)(nil),           // 18: ots_tractor.FuelLevel
	(*EngineTemp)(nil),          // 19: ots_tractor.EngineTemp
	(*IgnitionErrorCodes)(nil),  // 20: ots_tractor.IgnitionErrorCodes
	(*IgnitionOffResponse)(nil), // 21: ots_tractor.IgnitionOffResponse
	(*Empty)(nil),               // 22: ots_tractor.Empty
	(*SetRequest)(nil),          // 23: ots_tractor.SetRequest
	(*SetReply)(nil),            // 24: ots_tractor.SetReply
	(*GetRequest)(nil),          // 25: ots_tractor.GetRequest
	(*GetReply)(nil),            // 26: ots_tractor.GetReply
	(*Request)(nil),             // 27: ots_tractor.Request
	(*Reply)(nil),               // 28: ots_tractor.Reply
}
var file_ots_tractor_proto_depIdxs = []int32{
	1,  // 0: ots_tractor.GearState.gear:type_name -> ots_tractor.Gear
	2,  // 1: ots_tractor.LightsState.lights:type_name -> ots_tractor.Lights
	9,  // 2: ots_tractor.PtoState.state:type_name -> ots_tractor.PTOCommandState
	8,  // 3: ots_tractor.PtoState.failure:type_name -> ots_tractor.PTOFailType
	3,  // 4: ots_tractor.HitchV2Request.hitch:type_name -> ots_tractor.HitchCmd
	0,  // 5: ots_tractor.TractorVariantState.variant:type_name -> ots_tractor.TractorVariantType
	20, // 6: ots_tractor.IgnitionOffResponse.failure:type_name -> ots_tractor.IgnitionErrorCodes
	4,  // 7: ots_tractor.SetRequest.gear:type_name -> ots_tractor.GearState
	5,  // 8: ots_tractor.SetRequest.lights:type_name -> ots_tractor.LightsState
	6,  // 9: ots_tractor.SetRequest.speed_control:type_name -> ots_tractor.SpeedControlState
	12, // 10: ots_tractor.SetRequest.hitch:type_name -> ots_tractor.HitchRequest
	14, // 11: ots_tractor.SetRequest.scv:type_name -> ots_tractor.ScvRequest
	7,  // 12: ots_tractor.SetRequest.rpms:type_name -> ots_tractor.RpmDialState
	9,  // 13: ots_tractor.SetRequest.front_pto:type_name -> ots_tractor.PTOCommandState
	22, // 14: ots_tractor.SetRequest.rear_pto:type_name -> ots_tractor.Empty
	16, // 15: ots_tractor.SetRequest.variant:type_name -> ots_tractor.TractorVariantState
	17, // 16: ots_tractor.SetRequest.wheel_cal:type_name -> ots_tractor.WheelAngleCalState
	22, // 17: ots_tractor.SetRequest.ignition_off:type_name -> ots_tractor.Empty
	11, // 18: ots_tractor.SetRequest.hitch_V2:type_name -> ots_tractor.HitchV2Request
	22, // 19: ots_tractor.SetRequest.power_cycle:type_name -> ots_tractor.Empty
	4,  // 20: ots_tractor.SetReply.gear:type_name -> ots_tractor.GearState
	5,  // 21: ots_tractor.SetReply.lights:type_name -> ots_tractor.LightsState
	6,  // 22: ots_tractor.SetReply.speed_control:type_name -> ots_tractor.SpeedControlState
	13, // 23: ots_tractor.SetReply.hitch:type_name -> ots_tractor.HitchReply
	15, // 24: ots_tractor.SetReply.scv:type_name -> ots_tractor.ScvReply
	7,  // 25: ots_tractor.SetReply.rpms:type_name -> ots_tractor.RpmDialState
	10, // 26: ots_tractor.SetReply.front_pto:type_name -> ots_tractor.PtoState
	22, // 27: ots_tractor.SetReply.rear_pto:type_name -> ots_tractor.Empty
	16, // 28: ots_tractor.SetReply.variant:type_name -> ots_tractor.TractorVariantState
	17, // 29: ots_tractor.SetReply.wheel_cal:type_name -> ots_tractor.WheelAngleCalState
	21, // 30: ots_tractor.SetReply.ignition_off:type_name -> ots_tractor.IgnitionOffResponse
	13, // 31: ots_tractor.SetReply.hitch_V2:type_name -> ots_tractor.HitchReply
	21, // 32: ots_tractor.SetReply.power_cycle:type_name -> ots_tractor.IgnitionOffResponse
	22, // 33: ots_tractor.GetRequest.gear:type_name -> ots_tractor.Empty
	22, // 34: ots_tractor.GetRequest.lights:type_name -> ots_tractor.Empty
	22, // 35: ots_tractor.GetRequest.speed_control:type_name -> ots_tractor.Empty
	22, // 36: ots_tractor.GetRequest.rpms:type_name -> ots_tractor.Empty
	22, // 37: ots_tractor.GetRequest.front_pto:type_name -> ots_tractor.Empty
	22, // 38: ots_tractor.GetRequest.rear_pto:type_name -> ots_tractor.Empty
	22, // 39: ots_tractor.GetRequest.variant:type_name -> ots_tractor.Empty
	22, // 40: ots_tractor.GetRequest.wheel_cal:type_name -> ots_tractor.Empty
	22, // 41: ots_tractor.GetRequest.fuel_level:type_name -> ots_tractor.Empty
	22, // 42: ots_tractor.GetRequest.engine_temp:type_name -> ots_tractor.Empty
	4,  // 43: ots_tractor.GetReply.gear:type_name -> ots_tractor.GearState
	5,  // 44: ots_tractor.GetReply.lights:type_name -> ots_tractor.LightsState
	6,  // 45: ots_tractor.GetReply.speed_control:type_name -> ots_tractor.SpeedControlState
	7,  // 46: ots_tractor.GetReply.rpms:type_name -> ots_tractor.RpmDialState
	22, // 47: ots_tractor.GetReply.rear_pto:type_name -> ots_tractor.Empty
	16, // 48: ots_tractor.GetReply.variant:type_name -> ots_tractor.TractorVariantState
	17, // 49: ots_tractor.GetReply.wheel_cal:type_name -> ots_tractor.WheelAngleCalState
	18, // 50: ots_tractor.GetReply.fuel_level:type_name -> ots_tractor.FuelLevel
	19, // 51: ots_tractor.GetReply.engine_temp:type_name -> ots_tractor.EngineTemp
	23, // 52: ots_tractor.Request.set:type_name -> ots_tractor.SetRequest
	25, // 53: ots_tractor.Request.get:type_name -> ots_tractor.GetRequest
	24, // 54: ots_tractor.Reply.set:type_name -> ots_tractor.SetReply
	26, // 55: ots_tractor.Reply.get:type_name -> ots_tractor.GetReply
	56, // [56:56] is the sub-list for method output_type
	56, // [56:56] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_ots_tractor_proto_init() }
func file_ots_tractor_proto_init() {
	if File_ots_tractor_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_ots_tractor_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GearState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LightsState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SpeedControlState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RpmDialState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PTOFailType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PTOCommandState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PtoState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HitchV2Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HitchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HitchReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScvRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ScvReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TractorVariantState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WheelAngleCalState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FuelLevel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EngineTemp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IgnitionErrorCodes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IgnitionOffResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_ots_tractor_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_ots_tractor_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*PtoState_State)(nil),
		(*PtoState_Failure)(nil),
	}
	file_ots_tractor_proto_msgTypes[17].OneofWrappers = []interface{}{
		(*IgnitionOffResponse_TargetIgnitionState)(nil),
		(*IgnitionOffResponse_Failure)(nil),
	}
	file_ots_tractor_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*SetRequest_Gear)(nil),
		(*SetRequest_Lights)(nil),
		(*SetRequest_SpeedControl)(nil),
		(*SetRequest_Hitch)(nil),
		(*SetRequest_Scv)(nil),
		(*SetRequest_Rpms)(nil),
		(*SetRequest_FrontPto)(nil),
		(*SetRequest_RearPto)(nil),
		(*SetRequest_Variant)(nil),
		(*SetRequest_WheelCal)(nil),
		(*SetRequest_IgnitionOff)(nil),
		(*SetRequest_Hitch_V2)(nil),
		(*SetRequest_PowerCycle)(nil),
	}
	file_ots_tractor_proto_msgTypes[20].OneofWrappers = []interface{}{
		(*SetReply_Gear)(nil),
		(*SetReply_Lights)(nil),
		(*SetReply_SpeedControl)(nil),
		(*SetReply_Hitch)(nil),
		(*SetReply_Scv)(nil),
		(*SetReply_Rpms)(nil),
		(*SetReply_FrontPto)(nil),
		(*SetReply_RearPto)(nil),
		(*SetReply_Variant)(nil),
		(*SetReply_WheelCal)(nil),
		(*SetReply_IgnitionOff)(nil),
		(*SetReply_Hitch_V2)(nil),
		(*SetReply_PowerCycle)(nil),
	}
	file_ots_tractor_proto_msgTypes[21].OneofWrappers = []interface{}{
		(*GetRequest_Gear)(nil),
		(*GetRequest_Lights)(nil),
		(*GetRequest_SpeedControl)(nil),
		(*GetRequest_Rpms)(nil),
		(*GetRequest_FrontPto)(nil),
		(*GetRequest_RearPto)(nil),
		(*GetRequest_Variant)(nil),
		(*GetRequest_WheelCal)(nil),
		(*GetRequest_FuelLevel)(nil),
		(*GetRequest_EngineTemp)(nil),
	}
	file_ots_tractor_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*GetReply_Gear)(nil),
		(*GetReply_Lights)(nil),
		(*GetReply_SpeedControl)(nil),
		(*GetReply_Rpms)(nil),
		(*GetReply_FrontPto)(nil),
		(*GetReply_RearPto)(nil),
		(*GetReply_Variant)(nil),
		(*GetReply_WheelCal)(nil),
		(*GetReply_FuelLevel)(nil),
		(*GetReply_EngineTemp)(nil),
	}
	file_ots_tractor_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*Request_Set)(nil),
		(*Request_Get)(nil),
	}
	file_ots_tractor_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*Reply_Set)(nil),
		(*Reply_Get)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_ots_tractor_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_ots_tractor_proto_goTypes,
		DependencyIndexes: file_ots_tractor_proto_depIdxs,
		EnumInfos:         file_ots_tractor_proto_enumTypes,
		MessageInfos:      file_ots_tractor_proto_msgTypes,
	}.Build()
	File_ots_tractor_proto = out.File
	file_ots_tractor_proto_rawDesc = nil
	file_ots_tractor_proto_goTypes = nil
	file_ots_tractor_proto_depIdxs = nil
}
