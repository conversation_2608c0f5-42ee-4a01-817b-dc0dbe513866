package veselka

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetRecommendedModelParams_Validate(t *testing.T) {
	tests := []struct {
		name        string
		params      GetRecommendedModelParams
		expectError bool
	}{
		{
			"happy path deepweed",
			GetRecommendedModelParams{
				CropID:              "111-222-333",
				RobotName:           "foo1",
				Geohash:             "abcd",
				CurrentModelVersion: 1,
				ModelType:           ModelTypeDeepweed,
			},
			false,
		},
		{
			"model type required",
			GetRecommendedModelParams{},
			true,
		},
		{
			"deepweed requires geohash",
			GetRecommendedModelParams{
				ModelType:           ModelTypeDeepweed,
				CropID:              "111-222-333",
				RobotName:           "foo1",
				Geohash:             "",
				CurrentModelVersion: 1,
			},
			true,
		},
		{
			"deepweed requires cropID",
			GetRecommendedModelParams{
				ModelType:           ModelTypeDeepweed,
				CropID:              "",
				RobotName:           "foo1",
				G<PERSON>hash:             "2sdfsd",
				CurrentModelVersion: 1,
			},
			true,
		},
		{
			"deepweed requires robot",
			GetRecommendedModelParams{
				ModelType:           ModelTypeDeepweed,
				CropID:              "111-222-333",
				RobotName:           "",
				Geohash:             "2sdfsd",
				CurrentModelVersion: 1,
			},
			true,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			err := test.params.Validate()
			if test.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
