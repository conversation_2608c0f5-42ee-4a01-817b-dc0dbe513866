package redis

import (
	"context"

	"github.com/go-redis/redis/v8"
)

type RedisClient interface {
	ReadInt64(key string, def int64) (int64, error)
	WriteInt64(key string, i int64) error
	HSet(hash string, key string, val string) error
	HSetWithContext(ctx context.Context, hash string, val ...any) error
	HDel(key string, member string) error
	HGetAll(hash string) (map[string]string, error)
	HGetAllWithContext(ctx context.Context, hash string) (map[string]string, error)
	HGetWithContext(ctx context.Context, hash string, key string) (string, error)
	SMembers(key string) ([]string, error)
	SAdd(key string, member string) error
	SRem(key string, member string) error
	ZAdd(ctx context.Context, key string, members ...*redis.Z) (int64, error)
	ZRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error)
	ZRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) ([]redis.Z, error)
	ZRemRangeByScore(ctx context.Context, key, min, max string) (int64, error)
}
