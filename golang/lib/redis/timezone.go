package redis

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
)

func (c *Client) GetTimezone() (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
	defer cancel()
	return c.GetTimezoneWithContext(ctx)
}
func (c *Client) GetTimezoneWithContext(ctx context.Context) (string, error) {
	tz, err := c.HGetWithContext(ctx, "timezone", "zone")
	if err == nil && len(tz) == 0 {
		err = redis.Nil
	}
	return tz, err
}
