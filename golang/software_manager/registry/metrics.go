package registry

import (
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

const subsystem = "software_manager"

var (
	// version is current or update
	imageDownloadStatusGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "image_download_status",
		Help:      "boolean of image download status",
	}, []string{"image", "version"})
	imageDowloadFailedTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "image_download_failed_total",
		Help:      "total number of image download failures",
	}, []string{"image", "version"})
	imagePushFailedTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "image_push_failed_total",
		Help:      "total number of image push failures",
	}, []string{"image", "version"})
	imageChecksumFailedTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "image_checksum_failed_total",
		Help:      "total number of image checksum failures",
	}, []string{"image", "version"})
	systemVersionDownloadStatusGauge = promauto.NewGaugeVec(prometheus.GaugeOpts{
		Subsystem: subsystem,
		Name:      "system_version_download_status",
		Help:      "boolean of system version download status",
	}, []string{"version"})
	systemVersionDownloadFailedTotal = promauto.NewCounterVec(prometheus.CounterOpts{
		Subsystem: subsystem,
		Name:      "system_version_download_failed_total",
		Help:      "total number of system version download failures",
	}, []string{"version"})
)

func boolToFloat(b bool) float64 {
	if b {
		return 1
	}
	return 0
}

func getVersionLabel(current bool) string {
	if current {
		return "current"
	}
	return "update"
}
