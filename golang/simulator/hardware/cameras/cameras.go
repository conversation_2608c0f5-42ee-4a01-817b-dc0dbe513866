package cameras

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/generate"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/simulation"
	"github.com/carbonrobotics/robot/golang/simulator/hardware/types"
	"github.com/carbonrobotics/robot/golang/swig/geometric_cam"
	"github.com/sirupsen/logrus"
)

type SimulationSpace struct {
	cams    []Camera
	images  map[string]*Image
	imgCond *sync.Cond

	predictFPS float64

	predictions []*Prediction
	lock        sync.Mutex

	configNode   *config.ConfigTree
	configClient *config.ConfigClient

	field *generate.Field

	TerminateChannel1 chan bool
	TerminateChannel2 chan bool
	TerminateChannel3 chan bool
	TerminateChannel4 chan bool
	StoppedChannel1   chan bool
	StoppedChannel2   chan bool
	StoppedChannel3   chan bool
	StoppedChannel4   chan bool

	replayer        *simulation.RecordingReplayer
	replayerStarted atomic.Bool
	mode            *atomic.Value
}

type Camera struct {
	geoCam *geometric_cam.GeometricCam
	name   string
	roiX   float64
	roiY   float64
}

func NewSimulationSpace(configSubscriber *config.ConfigSubscriber, field *generate.Field, replayer *simulation.RecordingReplayer) *SimulationSpace {
	s := &SimulationSpace{
		cams:        createCams(),
		images:      make(map[string]*Image),
		imgCond:     sync.NewCond(&sync.Mutex{}),
		predictions: make([]*Prediction, 0),
		configNode:  configSubscriber.GetConfigNode("simulator", ""),

		field:             field,
		TerminateChannel1: make(chan bool, 1),
		TerminateChannel2: make(chan bool, 1),
		TerminateChannel3: make(chan bool, 1),
		TerminateChannel4: make(chan bool, 1),
		StoppedChannel1:   make(chan bool, 1),
		StoppedChannel2:   make(chan bool, 1),
		StoppedChannel3:   make(chan bool, 1),
		StoppedChannel4:   make(chan bool, 1),

		replayer: replayer,
	}
	s.configNode.RegisterCallback(s.readConfig)
	s.readConfig()
	return s
}

func (s *SimulationSpace) Reload(replayer *simulation.RecordingReplayer, field *generate.Field) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.replayer = replayer
	if field != nil {
		s.field = field
	}
	s.imgCond.Broadcast()
}

func (s *SimulationSpace) SetMode(mode *atomic.Value) {
	s.mode = mode
}

func (s *SimulationSpace) readConfig() {
	s.predictFPS = s.configNode.GetNode("predict_fps").GetFloatValue()
}

func createCams() []Camera {
	cams := make([]Camera, 0)

	predictPos := [][]float64{{2000.0, 2000.0, 0.0}, {2454.2, 2000.0, 0.0}, {2911.4, 2000.0, 0.0}, {3368.6, 2000.0, 0.0}}
	for i, pos := range predictPos {
		name := fmt.Sprintf("predict%v", i+1)

		cam_matrix := geometric_cam.NewVectorVectorDouble()
		row1 := geometric_cam.NewVector()
		row1.Add(1006.8596609987492)
		row1.Add(0.0)
		row1.Add(2213.1361596237657)
		cam_matrix.Add(row1)
		row2 := geometric_cam.NewVector()
		row2.Add(0.0)
		row2.Add(1006.8596609987492)
		row2.Add(1635.7397296767194)
		cam_matrix.Add(row2)
		row3 := geometric_cam.NewVector()
		row3.Add(0.0)
		row3.Add(0.0)
		row3.Add(1.0)
		cam_matrix.Add(row3)

		dist_coeff := geometric_cam.NewVector()
		dist_coeff.Add(0.36242610759266347)
		dist_coeff.Add(0.011046237292746433)
		dist_coeff.Add(-0.0005676734136916729)
		dist_coeff.Add(-0.000831387913648286)
		dist_coeff.Add(-0.0007575470947153185)
		dist_coeff.Add(0.3691801677550639)
		dist_coeff.Add(0.012950732527897201)
		dist_coeff.Add(-0.0005677492012479488)
		dist_coeff.Add(0.0)
		dist_coeff.Add(0.0)
		dist_coeff.Add(0.0)
		dist_coeff.Add(0.0)
		dist_coeff.Add(0.0)
		dist_coeff.Add(0.0)

		cam := geometric_cam.NewGeometricCam(
			name,
			pos[0], pos[1], pos[2],
			uint(4096), uint(3000), // image_size
			16.0,             // focal_length
			-26.643,          // top_to_vertex
			14.1864, 10.3776, // sensor_size
			cam_matrix,
			dist_coeff,
		)

		cams = append(cams, Camera{
			&cam,
			name,
			4096.0,
			3000.0,
		})
	}

	logrus.Info("Created cameras")
	return cams
}

func (s *SimulationSpace) Start() {
	if s.mode.Load() == types.Generate {
		s.GrabImages(s.TerminateChannel1, s.StoppedChannel1)
	}
}

func (s *SimulationSpace) Stop() {
	logrus.Infof("SimulationSpace: sending terminate to worker threads")

	if s.mode.Load() == types.Generate {
		s.TerminateChannel1 <- true
		<-s.StoppedChannel1
	}
	logrus.Infof("SimulationSpace: replay stopped")
}

func (s *SimulationSpace) waitForTerminateSignal(sleepTimeMs uint64, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("SimulationSpace: Received request to terminate replay")
		return true
	case <-time.After(time.Duration(sleepTimeMs) * time.Millisecond):
		return false
	}
}
