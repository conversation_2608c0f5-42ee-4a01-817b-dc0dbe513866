package gps_provider

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/geo_service"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"
)

type GeoServiceClient struct {
	addr   string
	mutex  sync.Mutex
	conn   *grpc.ClientConn
	client geo_service.GeoServiceClient
	opts   []grpc.DialOption
}

func destroyConnection(c *GeoServiceClient) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}
}

func NewGeoServiceClient() *GeoServiceClient {
	client := &GeoServiceClient{
		addr: "0.0.0.0:64656",
	}
	client.opts = append(client.opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	runtime.SetFinalizer(client, destroyConnection)
	return client
}

func (c *GeoServiceClient) resetConnection() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = nil
	c.client = nil
}

func getDefaultClientContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), time.Duration(time.Millisecond*500))
}

func (c *GeoServiceClient) getClient() (geo_service.GeoServiceClient, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.conn == nil {
		conn, err := grpc.Dial(c.addr, c.opts...)
		if err != nil {
			return nil, err
		}
		c.conn = conn
	}

	if c.client == nil {
		c.client = geo_service.NewGeoServiceClient(c.conn)
	}

	return c.client, nil
}

func (c *GeoServiceClient) sendRequest(f func(geo_service.GeoServiceClient) (interface{}, error)) (interface{}, error) {
	client, err := c.getClient()

	if err != nil {
		c.resetConnection()
		return nil, err
	}

	result, err := f(client)

	if err != nil {
		if e, ok := status.FromError(err); ok {
			switch e.Code() {
			case codes.Unavailable:
				c.resetConnection()
			case codes.DeadlineExceeded:
				logrus.Warningf("Resetting connection due to deadline exceeded error.")
				c.resetConnection()
			default:
			}
		}
		return nil, err
	}

	return result, nil
}

func (c *GeoServiceClient) GeodFwd(lat, lng, azimuth, dist_m float64) (*geo_service.GeoLL, error) {
	ctx, cancel := getDefaultClientContext()
	defer cancel()
	result, err := c.sendRequest(func(client geo_service.GeoServiceClient) (interface{}, error) {
		return client.GeodFwd(ctx, &geo_service.GeodFwdRequest{Start: &geo_service.GeoLL{Lat: lat, Lng: lng}, AzimuthDeg: azimuth, DistMeters: dist_m})
	})

	if err != nil {
		return nil, err
	}

	return result.(*geo_service.GeodFwdResponse).GetPos(), nil
}
