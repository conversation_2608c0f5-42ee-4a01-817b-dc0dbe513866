package gps_provider

import (
	"math"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/gps_utils"
	"github.com/sirupsen/logrus"
)

type pos struct {
	x     float64 // meters
	y     float64 // meters
	theta float64 // radians
}

type Vehicle interface {
	GetVelocityMPH() float32
	GetWheelAngleDeg() float32
}

type LocalGpsProvider struct {
	client *GeoServiceClient

	// lock guards curPos and startPt.
	lock    sync.Mutex
	curPos  pos
	startPt LLA

	vf         Vehicle
	configNode *config.ConfigTree
	stopSig    chan bool
}

func NewLocalGpsProvider(vf Vehicle, configSubscriber *config.ConfigSubscriber) *LocalGpsProvider {
	var simConfig *config.ConfigTree = nil
	if configSubscriber != nil {
		simConfig = configSubscriber.GetConfigNode("simulator", "")
	}

	g := &LocalGpsProvider{
		client:     NewGeoServiceClient(),
		curPos:     pos{x: 0, y: 0, theta: gps_utils.DefaultTheta}, // heading is 0 aka North which is 90 in XY plane
		vf:         vf,
		configNode: simConfig,
		stopSig:    make(chan bool, 0),
	}

	if g.configNode != nil {
		g.configNode.RegisterCallback(g.readConfig)
		g.readConfig()
	}

	return g
}

func (g *LocalGpsProvider) readConfig() {
	g.lock.Lock()
	defer g.lock.Unlock()

	lat := g.configNode.GetNode("gps_origin_lat").GetFloatValue()
	lon := g.configNode.GetNode("gps_origin_lon").GetFloatValue()
	alt := g.configNode.GetNode("gps_origin_alt").GetFloatValue()
	g.startPt = LLA{Lat: lat, Lng: lon, Alt: alt, Heading: 0}
}

func (g *LocalGpsProvider) waitForTerminateSignal(sleepTime time.Duration, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("LocalGpsProvider: received request to terminate")
		return true
	case <-time.After(sleepTime):
		return false
	}
}

func (g *LocalGpsProvider) Start(wg *sync.WaitGroup) {
	logrus.Info("LocalGpsProvider started")
	stopSig := make(chan bool, 1)
	g.stopSig = stopSig
	var wheelbase = float64(3) // Avg tractor wheelbase, to give turning speed decent accuracy.
	wg.Add(1)
	go func() {
		defer wg.Done()
		sleepTime := 10 * time.Millisecond
		dt := sleepTime.Seconds()
		for {
			if g.waitForTerminateSignal(sleepTime, stopSig) {
				break
			}
			vel := mph2Mps(g.vf.GetVelocityMPH())
			if vel == 0.0 {
				continue
			}
			delta := gps_utils.Deg2Rad(float64(-g.vf.GetWheelAngleDeg()))

			g.lock.Lock()
			alpha := math.Atan2(0, wheelbase)
			xdot := vel * math.Cos(g.curPos.theta+alpha)
			ydot := vel * math.Sin(g.curPos.theta+alpha)
			thdot := vel / wheelbase * math.Tan(delta)

			g.curPos.x = g.curPos.x + (xdot * dt)
			g.curPos.y = g.curPos.y + (ydot * dt)
			g.curPos.theta = g.curPos.theta + (thdot * dt)
			g.lock.Unlock()
		}
	}()
}

func (g *LocalGpsProvider) Stop() {
	g.stopSig <- true
}

func (g *LocalGpsProvider) GetLLA() LLA {
	g.lock.Lock()
	cur := g.curPos
	startPt := g.startPt
	g.lock.Unlock()

	heading := gps_utils.XYAngleToAzimuth(math.Atan2(cur.y, cur.x))
	dist_m := math.Sqrt(math.Pow(cur.x, 2) + math.Pow(cur.y, 2))
	pos, err := g.client.GeodFwd(startPt.Lat, startPt.Lng, heading, dist_m)
	if err != nil {
		logrus.Warnf("Failed to calculate current gps pos %v", err)
		return startPt
	}
	return LLA{
		Lat:     pos.GetLat(),
		Lng:     pos.GetLng(),
		Alt:     startPt.Alt,
		Heading: gps_utils.XYAngleToAzimuth(cur.theta),
	}
}

func mph2Mps(mph float32) float64 {
	return float64(mph) * 0.44704
}
