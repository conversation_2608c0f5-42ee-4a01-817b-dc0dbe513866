package web_server_rtc

import (
	"context"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/rtc_sim_UI"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/hasselhoff"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
)

const streamMaxIdle = 5 * time.Minute

type RTCSimulatorUIService struct {
	rtc_sim_UI.UnimplementedRTCSimulatorUIServiceServer

	hhBoard *hasselhoff.HHBoardSim

	TerminateChannel1 chan bool
	StoppedChannel1   chan bool
}

func NewRTCSimulatorUIService(grpcServer *grpc.Server, hhBoard *hasselhoff.HHBoardSim) *RTCSimulatorUIService {
	service := &RTCSimulatorUIService{
		hhBoard:           hhBoard,
		TerminateChannel1: make(chan bool, 1),
		StoppedChannel1:   make(chan bool, 1),
	}
	rtc_sim_UI.RegisterRTCSimulatorUIServiceServer(grpcServer, service)
	return service
}

func (s *RTCSimulatorUIService) Start() {
	s.hhBoard.Start()
	logrus.Info("gRPC webserver started")
	for {
		select {
		case <-s.TerminateChannel1:
			logrus.Infof("Web Server: Received request to terminate")
			s.StoppedChannel1 <- true
			return
		case <-time.After(streamMaxIdle):
		}
	}
}

func (s *RTCSimulatorUIService) Stop() {
	s.hhBoard.Stop()
	s.TerminateChannel1 <- true
	<-s.StoppedChannel1
	logrus.Infof("Web Server: Stopped")
}

func (s *RTCSimulatorUIService) SetInCabSwitch(ctx context.Context, req *rtc_sim_UI.EnableRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in cab switch")
	s.hhBoard.InCabSwitch(req.GetEnabled())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetSafetySensors(ctx context.Context, req *rtc_sim_UI.SafetySensorsRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in safety sensors")
	s.hhBoard.SetSafetySensors(req.GetSensor_1(), req.GetSensor_2(), req.GetSensor_3(), req.GetSensor_4())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetSpeed(ctx context.Context, req *rtc_sim_UI.SetSpeedRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set speed")
	s.hhBoard.SetSpeed(req.GetSpeedMph())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetGear(ctx context.Context, req *rtc_sim_UI.SetGearRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set gear")
	s.hhBoard.SetGear(int32(req.GetGear()))
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetLights(ctx context.Context, req *rtc_sim_UI.SetLightsRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set lights")
	s.hhBoard.SetGear(int32(req.GetLights()))
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetEstop(ctx context.Context, req *rtc_sim_UI.EnableRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set estop")
	s.hhBoard.SetEstop(req.GetEnabled())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetEngineRpm(ctx context.Context, req *rtc_sim_UI.SetEngineRpmRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set front pto")
	s.hhBoard.SetEngineRpm(req.GetRpms())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetFrontPto(ctx context.Context, req *rtc_sim_UI.EnableRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set front pto")
	s.hhBoard.SetFrontPto(req.GetEnabled())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetErrorFlag(ctx context.Context, req *rtc_sim_UI.SetErrorFlagRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set error flag")
	s.hhBoard.SetErrorFlag(req.GetErrorFlag())
	return &rtc_sim_UI.Empty{}, nil
}
func (s *RTCSimulatorUIService) SetFuelLevel(ctx context.Context, req *rtc_sim_UI.SetFuelLevelRequest) (*rtc_sim_UI.Empty, error) {
	logrus.Infof("gRPC webserver: in set fuel level")
	s.hhBoard.SetFuelLevel(req.GetFuelLevel())
	return &rtc_sim_UI.Empty{}, nil
}
