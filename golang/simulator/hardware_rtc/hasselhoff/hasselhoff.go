package hasselhoff

import (
	"encoding/json"
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/carbon_tractor"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/diagnostic"
	nanopb "github.com/carbonrobotics/robot/golang/generated/proto/nanopb/hasselhoff_board"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/ots_tractor"
	"github.com/carbonrobotics/robot/golang/generated/proto/nanopb/request"
	"github.com/carbonrobotics/robot/golang/lib/config"
	"github.com/carbonrobotics/robot/golang/lib/redis"
	lwSim "github.com/carbonrobotics/robot/golang/simulator/hardware/client"
	"github.com/carbonrobotics/robot/golang/simulator/hardware_rtc/motion_controller"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/proto"
)

const (
	wheelAngleCalKey  = "hh_sim/wheelAngleCal"
	steeringCfgKey    = "hh_sim/steeringCfg"
	tractorVariantKey = "hh_sim/tractorVariant"
	speedLimitMphKey  = "hh_sim/speedLimitMph"
	rpmsMin           = 500  // TODO move to config
	rpmsMax           = 1865 // TODO move to config
)

type safetySubsystem struct {
	sensor1 bool
	sensor2 bool
	sensor3 bool
	sensor4 bool
	bypass  bool
}

func (s *safetySubsystem) tripped() bool {
	if s.bypass {
		return false
	}
	return s.triggered()
}
func (s *safetySubsystem) triggered() bool {
	return s.sensor1 || s.sensor2 || s.sensor3 || s.sensor4
}

type wheelAngleCalType struct {
	SensorDegPerBit    float32 `json:"sensorDegPerBit"`
	SensorCenterVal    uint32  `json:"sensorCenterVal"`
	CenterTrim         float32 `json:"centerTrim"`
	RightLock          float32 `json:"rightLock"`
	SensorFullLeftVal  uint32  `json:"sensorFullLeftVal"`
	SensorFullRightVal uint32  `json:"sensorFullRightVal"`
}

type steeringCfgType struct {
	Kp                      float32 `json:"kp"`
	Ki                      float32 `json:"ki"`
	Kd                      float32 `json:"kd"`
	IntegralLimit           float32 `json:"integralLimit"`
	UpdateRate              int32   `json:"updateRate"`
	MinSteeringValveCurrent uint32  `json:"minSteeringValveCurrent"`
	MaxSteeringValveCurrent uint32  `json:"maxSteeringValveCurrent"`
}

type CabState struct {
	gear        ots_tractor.Gear
	lights      ots_tractor.Lights
	velocityMPH float32
	rtcLockout  bool
	rpms        int32
	front_pto   bool
}

type HHBoardSim struct {
	lastPet       time.Time
	hhState       carbon_tractor.HHStateStatus
	gear          ots_tractor.Gear
	lights        ots_tractor.Lights
	safety        safetySubsystem
	lift          float32
	brakes        carbon_tractor.BrakeState
	rpms          int32
	front_pto     bool
	cabState      CabState
	variant       ots_tractor.TractorVariantType
	wheelAngleCal wheelAngleCalType
	steeringCfg   steeringCfgType
	errorFlag     int32
	petLostState  carbon_tractor.HHStateStatus
	speedLimitMph float32
	fuelLevel     float32
	redisClient   *redis.Client
	lwSimClient   *lwSim.HWSimClient
	stopSig       []chan bool
	controller    *motion_controller.MotionController
	wg            sync.WaitGroup

	lock sync.Mutex
}

func NewHHBoardSim(configSubscriber *config.ConfigSubscriber, controller *motion_controller.MotionController) *HHBoardSim {
	var lwClient *lwSim.HWSimClient = nil
	if configSubscriber != nil {
		cfgNode := configSubscriber.GetConfigNode("simulator", "rtc_update_lw_sim")
		if cfgNode.GetBoolValue() {
			defer config.PreDeleteConfigTree(cfgNode)
			lwClient = lwSim.NewHWSimClient()
		}
	}
	s := &HHBoardSim{
		hhState:       carbon_tractor.HHStateStatus_HH_DISABLED,
		gear:          ots_tractor.Gear_GEAR_PARK,
		lift:          0.0,
		variant:       ots_tractor.TractorVariantType_TV_JD_6LH,
		petLostState:  carbon_tractor.HHStateStatus_HH_SAFE,
		speedLimitMph: -1.0, // No limit default
		redisClient:   redis.NewLocalClient(),
		lwSimClient:   lwClient,
		fuelLevel:     1.0,
		stopSig:       make([]chan bool, 0),
		controller:    controller,
	}

	variantVal, err := s.redisClient.ReadInt64(tractorVariantKey, -1)
	if err == nil && variantVal > 0 {
		s.variant = ots_tractor.TractorVariantType(variantVal)
	}
	wacStr, err := s.redisClient.ReadString(wheelAngleCalKey, "")
	if err == nil && len(wacStr) > 0 {
		if err := json.Unmarshal(json.RawMessage(wacStr), &s.wheelAngleCal); err != nil {
			logrus.Warn(err)
		}
	}
	steeringCfgStr, err := s.redisClient.ReadString(steeringCfgKey, "")
	if err == nil && len(steeringCfgStr) > 0 {
		if err := json.Unmarshal(json.RawMessage(steeringCfgStr), &s.steeringCfg); err != nil {
			logrus.Warn(err)
		}
	}
	speedLimitMph, err := s.redisClient.ReadFloat(speedLimitMphKey, -1.0)
	if err == nil {
		s.speedLimitMph = float32(speedLimitMph)
	}
	return s
}

func (s *HHBoardSim) Start() {
	logrus.Info("HH board simulation started")
	s.listenAndServe()
	s.watchDawg()
}

func (s *HHBoardSim) Stop() {
	for _, channel := range s.stopSig {
		channel <- true
	}
	s.wg.Wait()
}

func (s *HHBoardSim) waitForTerminateSignal(sleepTime time.Duration, channel chan bool) bool {
	select {
	case <-channel:
		logrus.Infof("HH_Sim: received request to terminate replay")
		return true
	case <-time.After(sleepTime):
		return false
	}
}

func (s *HHBoardSim) listenAndServe() {
	stopSig := make(chan bool, 1)
	s.stopSig = append(s.stopSig, stopSig)
	s.wg.Add(1)
	go func() {
		sleepTime := 5 * time.Millisecond

		pc, err := net.ListenPacket("udp", ":64559")
		if err != nil {
			log.Fatal(err)
		}
		defer pc.Close()

		for {
			if s.waitForTerminateSignal(sleepTime, stopSig) {
				break
			}
			buf := make([]byte, 2048)
			n, addr, err := pc.ReadFrom(buf)
			if err != nil {
				continue
			}
			go s.serve(pc, addr, buf[:n])
		}
		s.wg.Done()
	}()
}
func (s *HHBoardSim) watchDawg() {
	stopSig := make(chan bool, 1)
	s.stopSig = append(s.stopSig, stopSig)
	s.wg.Add(1)
	go func() {
		sleepTime := 1 * time.Second
		prevState := true
		for {
			if s.waitForTerminateSignal(sleepTime, stopSig) {
				break
			}
			now := time.Now()
			s.lock.Lock()
			if s.lastPet.Add(1 * time.Second).Before(now) {
				if prevState {
					logrus.Infof("Dawg pet timeout")
					prevState = false
				}
				if s.hhState != carbon_tractor.HHStateStatus_HH_DISABLED && s.hhState != carbon_tractor.HHStateStatus_HH_ESTOP {
					s.setStateStatus(s.petLostState)
				}
			} else {
				if !prevState {
					logrus.Infof("Dawg petting reconnected")
					prevState = true
				}
			}
			s.lock.Unlock()
		}
		s.wg.Done()
	}()
}

// s.lock must be held to call this function
func (s *HHBoardSim) setSpeed(speed float32) {
	s.controller.SetSpeed(speed, s.gear == ots_tractor.Gear_GEAR_REVERSE)
}

func (s *HHBoardSim) setWheelAngle(wheelAngle float32) {
	s.controller.SetWheelAngle(wheelAngle)
}

func (s *HHBoardSim) forwardVel() {
	if s.lwSimClient != nil {
		s.lwSimClient.SetVelocity(s.controller.GetSpeed())
	}
}
func (s *HHBoardSim) setLimpMode() {
	s.gear = ots_tractor.Gear_GEAR_PARK
	s.cabState.gear = s.gear
	s.setSpeed(0.0)
	s.cabState.velocityMPH = s.controller.GetSpeed()
	s.lights = ots_tractor.Lights_LIGHTS_OFF
	s.cabState.lights = s.lights
	s.rpms = rpmsMin
	s.cabState.rpms = s.rpms
	s.front_pto = false
	s.cabState.front_pto = s.front_pto
	s.forwardVel()
}
func (s *HHBoardSim) setStateStatus(hhStateStatus carbon_tractor.HHStateStatus) {
	logrus.Infof("Current state = %v, requested state = %v", s.hhState, hhStateStatus)
	if s.hhState == carbon_tractor.HHStateStatus_HH_ESTOP && hhStateStatus != carbon_tractor.HHStateStatus_HH_DISABLED {
		logrus.Warn("Estop can only transition to disabled")
		return
	}
	switch hhStateStatus {
	case carbon_tractor.HHStateStatus_HH_ESTOP, carbon_tractor.HHStateStatus_HH_DISABLED:
		s.setLimpMode()
		s.hhState = hhStateStatus
	default:
		if s.hhState == carbon_tractor.HHStateStatus_HH_DISABLED {
			if s.cabState.rtcLockout {
				logrus.Info("Cannot move to an enabled state when locked out")
				return
			}
			s.setLimpMode()
		}
		if hhStateStatus == carbon_tractor.HHStateStatus_HH_SAFE {
			s.setLimpMode()
			s.hhState = hhStateStatus
		} else if s.safety.tripped() {
			s.hhState = carbon_tractor.HHStateStatus_HH_STOPPED
		} else {
			s.hhState = hhStateStatus
		}
		if s.hhState == carbon_tractor.HHStateStatus_HH_STOPPED {
			s.gear = ots_tractor.Gear_GEAR_PARK
			s.setSpeed(0.0)
			s.forwardVel()
		}
	}
}

func (s *HHBoardSim) carbonReq(carbonReq *carbon_tractor.Request, reply *nanopb.Reply) error {
	carbonReply := &nanopb.Reply_CarbonTractor{CarbonTractor: &carbon_tractor.Reply{}}
	switch req := carbonReq.Request.(type) {
	case *carbon_tractor.Request_Set:
		sReply := &carbon_tractor.Reply_Set{Set: &carbon_tractor.SetReply{}}
		switch sReq := req.Set.Set.(type) {
		case *carbon_tractor.SetRequest_HhState:
			if s.hhState != carbon_tractor.HHStateStatus_HH_ESTOP {
				s.setStateStatus(sReq.HhState.GetState())
			}
			sReply.Set.Set = &carbon_tractor.SetReply_HhState{HhState: &carbon_tractor.HHState{State: s.hhState}}
		case *carbon_tractor.SetRequest_Brakes:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				s.brakes.ForceLeft = sReq.Brakes.GetForceLeft()
				s.brakes.ForceRight = sReq.Brakes.GetForceRight()
			}
			sReply.Set.Set = &carbon_tractor.SetReply_Brakes{Brakes: &s.brakes}
		case *carbon_tractor.SetRequest_SafetyBypass:
			if s.hhState != carbon_tractor.HHStateStatus_HH_ESTOP && s.hhState != carbon_tractor.HHStateStatus_HH_DISABLED {
				s.safety.bypass = sReq.SafetyBypass.GetBypass()
			}
			if s.safety.tripped() {
				s.setStateStatus(carbon_tractor.HHStateStatus_HH_STOPPED)
			}
			sReply.Set.Set = &carbon_tractor.SetReply_SafetyBypass{SafetyBypass: &carbon_tractor.SafetySensorBypassState{Bypass: s.safety.bypass}}
		case *carbon_tractor.SetRequest_Steering:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				s.setWheelAngle(sReq.Steering.GetAngle())
			}
			sReply.Set.Set = &carbon_tractor.SetReply_Steering{Steering: &carbon_tractor.SteeringState{Angle: s.controller.GetWheelAngle()}}
		case *carbon_tractor.SetRequest_SteeringCfg:
			s.steeringCfg.Kp = sReq.SteeringCfg.GetKp()
			s.steeringCfg.Ki = sReq.SteeringCfg.GetKi()
			s.steeringCfg.Kd = sReq.SteeringCfg.GetKd()
			s.steeringCfg.IntegralLimit = sReq.SteeringCfg.GetIntegralLimit()
			s.steeringCfg.UpdateRate = sReq.SteeringCfg.GetUpdateRateHz()
			s.steeringCfg.MinSteeringValveCurrent = sReq.SteeringCfg.GetMinSteeringValveCurrent()
			s.steeringCfg.MaxSteeringValveCurrent = sReq.SteeringCfg.GetMaxSteeringValveCurrent()
			encodedJSON, err := json.Marshal(s.steeringCfg)
			if err != nil {
				logrus.Warn(err)
			} else {
				s.redisClient.WriteString(steeringCfgKey, string(encodedJSON[:]))
			}
			sReply.Set.Set = &carbon_tractor.SetReply_SteeringCfg{SteeringCfg: &carbon_tractor.SteeringCfgState{
				Kp:                      s.steeringCfg.Kp,
				Ki:                      s.steeringCfg.Ki,
				Kd:                      s.steeringCfg.Kd,
				IntegralLimit:           s.steeringCfg.IntegralLimit,
				UpdateRateHz:            s.steeringCfg.UpdateRate,
				MinSteeringValveCurrent: s.steeringCfg.MinSteeringValveCurrent,
				MaxSteeringValveCurrent: s.steeringCfg.MaxSteeringValveCurrent,
			}}
		case *carbon_tractor.SetRequest_PetLoss:
			if sReq.PetLoss.UseStop {
				s.petLostState = carbon_tractor.HHStateStatus_HH_STOPPED
			} else {
				s.petLostState = carbon_tractor.HHStateStatus_HH_SAFE
			}
			sReply.Set.Set = &carbon_tractor.SetReply_PetLoss{PetLoss: &carbon_tractor.PetLossState{UseStop: s.petLostState == carbon_tractor.HHStateStatus_HH_STOPPED}}
		case *carbon_tractor.SetRequest_SpeedLimit:
			s.speedLimitMph = sReq.SpeedLimit.SpeedLimitMph
			s.redisClient.WriteFloat(speedLimitMphKey, float64(s.speedLimitMph))
			sReply.Set.Set = &carbon_tractor.SetReply_SpeedLimit{SpeedLimit: &carbon_tractor.SpeedLimitState{SpeedLimitMph: s.speedLimitMph}}
		default:
			return fmt.Errorf("unknown carbon set request type")
		}
		carbonReply.CarbonTractor.Reply = sReply
	case *carbon_tractor.Request_Get:
		gReply := &carbon_tractor.Reply_Get{Get: &carbon_tractor.GetReply{}}
		switch req.Get.Get.(type) {
		case *carbon_tractor.GetRequest_Status:
			gReply.Get.Get = &carbon_tractor.GetReply_Status{Status: s.getStatus()}
		case *carbon_tractor.GetRequest_HhState:
			gReply.Get.Get = &carbon_tractor.GetReply_HhState{HhState: &carbon_tractor.HHState{State: s.hhState}}
		case *carbon_tractor.GetRequest_Brakes:
			gReply.Get.Get = &carbon_tractor.GetReply_Brakes{Brakes: &s.brakes}
		case *carbon_tractor.GetRequest_Safety:
			gReply.Get.Get = &carbon_tractor.GetReply_Safety{Safety: &carbon_tractor.SafetySensorsState{TriggeredSensor_1: s.safety.sensor1, TriggeredSensor_2: s.safety.sensor2, TriggeredSensor_3: s.safety.sensor3, TriggeredSensor_4: s.safety.sensor4}}
		case *carbon_tractor.GetRequest_SafetyBypass:
			gReply.Get.Get = &carbon_tractor.GetReply_SafetyBypass{SafetyBypass: &carbon_tractor.SafetySensorBypassState{Bypass: s.safety.bypass}}
		case *carbon_tractor.GetRequest_Steering:
			gReply.Get.Get = &carbon_tractor.GetReply_Steering{Steering: &carbon_tractor.SteeringState{Angle: s.controller.GetWheelAngle()}}
		case *carbon_tractor.GetRequest_SteeringCfg:
			gReply.Get.Get = &carbon_tractor.GetReply_SteeringCfg{SteeringCfg: &carbon_tractor.SteeringCfgState{
				Kp:                      s.steeringCfg.Kp,
				Ki:                      s.steeringCfg.Ki,
				Kd:                      s.steeringCfg.Kd,
				IntegralLimit:           s.steeringCfg.IntegralLimit,
				UpdateRateHz:            s.steeringCfg.UpdateRate,
				MinSteeringValveCurrent: s.steeringCfg.MinSteeringValveCurrent,
				MaxSteeringValveCurrent: s.steeringCfg.MaxSteeringValveCurrent,
			}}
		case *carbon_tractor.GetRequest_PetLoss:
			gReply.Get.Get = &carbon_tractor.GetReply_PetLoss{PetLoss: &carbon_tractor.PetLossState{UseStop: s.petLostState == carbon_tractor.HHStateStatus_HH_STOPPED}}
		case *carbon_tractor.GetRequest_SpeedLimit:
			gReply.Get.Get = &carbon_tractor.GetReply_SpeedLimit{SpeedLimit: &carbon_tractor.SpeedLimitState{SpeedLimitMph: s.speedLimitMph}}
		default:
			return fmt.Errorf("unknown carbon get request type")
		}
		carbonReply.CarbonTractor.Reply = gReply
	case *carbon_tractor.Request_Pet:
		s.lastPet = time.Now()
		carbonReply.CarbonTractor.Reply = &carbon_tractor.Reply_Pet{Pet: s.getStatus()}
	default:
		return fmt.Errorf("unknown carbon request type")
	}
	reply.Reply = carbonReply
	return nil
}
func (s *HHBoardSim) otsReq(otsReq *ots_tractor.Request, reply *nanopb.Reply) error {
	otsReply := &nanopb.Reply_OtsTractor{OtsTractor: &ots_tractor.Reply{}}
	switch req := otsReq.Request.(type) {
	case *ots_tractor.Request_Set:
		sReply := &ots_tractor.Reply_Set{Set: &ots_tractor.SetReply{}}
		switch sReq := req.Set.Set.(type) {
		case *ots_tractor.SetRequest_Gear:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				if s.gear != sReq.Gear.GetGear() {
					s.gear = sReq.Gear.GetGear()
					s.setSpeed(0)
					s.forwardVel()
				}
			}
			sReply.Set.Set = &ots_tractor.SetReply_Gear{Gear: &ots_tractor.GearState{Gear: s.gear}}
		case *ots_tractor.SetRequest_Lights:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				s.lights = sReq.Lights.GetLights()
			}
			sReply.Set.Set = &ots_tractor.SetReply_Lights{Lights: &ots_tractor.LightsState{Lights: s.lights}}
		case *ots_tractor.SetRequest_SpeedControl:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL && (s.gear == ots_tractor.Gear_GEAR_FORWARD || s.gear == ots_tractor.Gear_GEAR_REVERSE) {
				if s.speedLimitMph < 0.0 {
					s.setSpeed(sReq.SpeedControl.GetSpeed())
					s.forwardVel()
				} else {
					s.setSpeed(min(sReq.SpeedControl.GetSpeed(), s.speedLimitMph))
					s.forwardVel()
				}
			}
			sReply.Set.Set = &ots_tractor.SetReply_SpeedControl{SpeedControl: &ots_tractor.SpeedControlState{Speed: s.controller.GetSpeed()}}
		case *ots_tractor.SetRequest_Hitch:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				val := sReq.Hitch.GetHitchLiftForce()
				if val == 0x0 {
					s.lift = 100.0
				} else if val == 0xFA {
					s.lift = 0.0
				}
				logrus.Infof("Hitch controls for force %v not yet implemented", val)
				// TODO handle other cases
			}
			sReply.Set.Set = &ots_tractor.SetReply_Hitch{Hitch: &ots_tractor.HitchReply{}}
		case *ots_tractor.SetRequest_Scv:
			//TODO implement me
			logrus.Info("SCV controls not yet implemented")
			sReply.Set.Set = &ots_tractor.SetReply_Scv{Scv: &ots_tractor.ScvReply{}}
		case *ots_tractor.SetRequest_Rpms:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				s.rpms = int32(sReq.Rpms.GetRpms()*float32(rpmsMax-rpmsMin)) + rpmsMin
			}
			sReply.Set.Set = &ots_tractor.SetReply_Rpms{Rpms: &ots_tractor.RpmDialState{Rpms: sReq.Rpms.GetRpms()}}
		case *ots_tractor.SetRequest_FrontPto:
			if s.hhState == carbon_tractor.HHStateStatus_HH_OPERATIONAL {
				s.front_pto = sReq.FrontPto.GetEnabled()
			}
			sReply.Set.Set = &ots_tractor.SetReply_FrontPto{FrontPto: &ots_tractor.PtoState{Decision: &ots_tractor.PtoState_State{State: &ots_tractor.PTOCommandState{Enabled: s.front_pto}}}}
		case *ots_tractor.SetRequest_Variant:
			s.variant = sReq.Variant.GetVariant()
			s.redisClient.WriteInt64(tractorVariantKey, int64(s.variant))
			sReply.Set.Set = &ots_tractor.SetReply_Variant{Variant: &ots_tractor.TractorVariantState{Variant: s.variant}}
		case *ots_tractor.SetRequest_WheelCal:
			s.wheelAngleCal.SensorDegPerBit = sReq.WheelCal.GetSensorDegPerBit()
			s.wheelAngleCal.SensorCenterVal = sReq.WheelCal.GetSensorCenterVal()
			s.wheelAngleCal.CenterTrim = sReq.WheelCal.GetCenterTrimDeg()
			s.wheelAngleCal.RightLock = sReq.WheelCal.GetRightLockDeg()
			s.wheelAngleCal.SensorFullLeftVal = sReq.WheelCal.GetSensorFullLeftVal()
			s.wheelAngleCal.SensorFullRightVal = sReq.WheelCal.GetSensorFullRightVal()
			encodedJSON, err := json.Marshal(s.wheelAngleCal)
			if err != nil {
				logrus.Warn(err)
			} else {
				s.redisClient.WriteString(wheelAngleCalKey, string(encodedJSON[:]))
			}
			sReply.Set.Set = &ots_tractor.SetReply_WheelCal{WheelCal: &ots_tractor.WheelAngleCalState{
				SensorDegPerBit:    s.wheelAngleCal.SensorDegPerBit,
				SensorCenterVal:    s.wheelAngleCal.SensorCenterVal,
				CenterTrimDeg:      s.wheelAngleCal.CenterTrim,
				RightLockDeg:       s.wheelAngleCal.RightLock,
				SensorFullLeftVal:  s.wheelAngleCal.SensorFullLeftVal,
				SensorFullRightVal: s.wheelAngleCal.SensorFullRightVal,
			}}
		case *ots_tractor.SetRequest_IgnitionOff:
			logrus.Info("Shutdown requested, but simulator can never die.")
			sReply.Set.Set = &ots_tractor.SetReply_IgnitionOff{IgnitionOff: &ots_tractor.IgnitionOffResponse{Decision: &ots_tractor.IgnitionOffResponse_TargetIgnitionState{TargetIgnitionState: false}}}
		default:
			return fmt.Errorf("unknown ots set request type")
		}
		otsReply.OtsTractor.Reply = sReply
	case *ots_tractor.Request_Get:
		gReply := &ots_tractor.Reply_Get{Get: &ots_tractor.GetReply{}}
		switch req.Get.Get.(type) {
		case *ots_tractor.GetRequest_Gear:
			gReply.Get.Get = &ots_tractor.GetReply_Gear{Gear: &ots_tractor.GearState{Gear: s.gear}}
		case *ots_tractor.GetRequest_Lights:
			gReply.Get.Get = &ots_tractor.GetReply_Lights{Lights: &ots_tractor.LightsState{Lights: s.lights}}
		case *ots_tractor.GetRequest_SpeedControl:
			gReply.Get.Get = &ots_tractor.GetReply_SpeedControl{SpeedControl: &ots_tractor.SpeedControlState{Speed: s.controller.GetSpeed()}}
		case *ots_tractor.GetRequest_Variant:
			gReply.Get.Get = &ots_tractor.GetReply_Variant{Variant: &ots_tractor.TractorVariantState{Variant: s.variant}}
		case *ots_tractor.GetRequest_WheelCal:
			gReply.Get.Get = &ots_tractor.GetReply_WheelCal{WheelCal: &ots_tractor.WheelAngleCalState{
				SensorDegPerBit:    s.wheelAngleCal.SensorDegPerBit,
				SensorCenterVal:    s.wheelAngleCal.SensorCenterVal,
				CenterTrimDeg:      s.wheelAngleCal.CenterTrim,
				RightLockDeg:       s.wheelAngleCal.RightLock,
				SensorFullLeftVal:  s.wheelAngleCal.SensorFullLeftVal,
				SensorFullRightVal: s.wheelAngleCal.SensorFullRightVal,
			}}
		case *ots_tractor.GetRequest_FuelLevel:
			gReply.Get.Get = &ots_tractor.GetReply_FuelLevel{FuelLevel: &ots_tractor.FuelLevel{FuelLevel: s.fuelLevel}}
		default:
			return fmt.Errorf("unknown ots get request type")
		}
		otsReply.OtsTractor.Reply = gReply
	default:
		return fmt.Errorf("unknown ots request type")
	}
	reply.Reply = otsReply
	return nil
}

func (s *HHBoardSim) serve(pc net.PacketConn, addr net.Addr, buf []byte) {
	logrus.Debugf("HH_Sim: received request from %v, content_len=(%v)", addr, len(buf))

	req := &nanopb.Request{}
	var err error = nil
	proto.Unmarshal(buf, req)
	var reply *nanopb.Reply = &nanopb.Reply{
		Header: &request.RequestHeader{
			RequestId: req.GetHeader().RequestId,
		}}
	func() {
		s.lock.Lock()
		defer s.lock.Unlock()
		switch req.Request.(type) {
		case *nanopb.Request_CarbonTractor:
			err = s.carbonReq(req.GetCarbonTractor(), reply)
		case *nanopb.Request_OtsTractor:
			err = s.otsReq(req.GetOtsTractor(), reply)
		case *nanopb.Request_Ping:
			reply.Reply = &nanopb.Reply_Pong{Pong: &diagnostic.Pong{X: req.GetPing().GetX()}}
		default:
			err = fmt.Errorf("unknown request from %v", addr)
		}
	}()
	if err != nil {
		logrus.Error(err)
		return
	}
	msg, err := proto.Marshal(reply)
	if err != nil {
		logrus.Error(err)
	} else {
		n, err := pc.WriteTo(msg, addr)
		logrus.Debugf("HH_sim: Write done to %v with %v and err=%v", addr, n, err)
	}
}
func (s *HHBoardSim) getStatus() *carbon_tractor.TractorStatus {
	return &carbon_tractor.TractorStatus{
		State:               &carbon_tractor.HHState{State: s.hhState},
		ErrorFlag:           s.errorFlag,
		GroundSpeed:         s.controller.GetSpeed(),
		WheelAngle:          s.controller.GetWheelAngle(),
		HitchLiftPercentage: s.lift,
		Gear:                &ots_tractor.GearState{Gear: s.gear},
		SafetyTriggered:     s.safety.triggered(),
		SafetyBypass:        s.safety.bypass,
		RemoteLockout:       s.cabState.rtcLockout,
		Rpms:                s.rpms,
	}
}
func (s *HHBoardSim) SetSafetySensors(sensor_1, sensor_2, sensor_3, sensor_4 bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.safety.sensor1 = sensor_1
	s.safety.sensor2 = sensor_2
	s.safety.sensor3 = sensor_3
	s.safety.sensor4 = sensor_4
	logrus.Infof("Setting safety here, tripped=%v, triggered=%v", s.safety.tripped(), s.safety.triggered())
	if s.safety.tripped() {
		s.setStateStatus(carbon_tractor.HHStateStatus_HH_STOPPED)
	}
}

func (s *HHBoardSim) InCabSwitch(enabled bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.cabState.rtcLockout = enabled
	if enabled {
		s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	}
}

func (s *HHBoardSim) SetSpeed(speed_mph float32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	s.cabState.velocityMPH = speed_mph
}
func (s *HHBoardSim) SetGear(gear int32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	s.cabState.gear = ots_tractor.Gear(gear)
}
func (s *HHBoardSim) SetEstop(enabled bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	if enabled {
		s.setStateStatus(carbon_tractor.HHStateStatus_HH_ESTOP)
	} else if s.hhState == carbon_tractor.HHStateStatus_HH_ESTOP {
		s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	}
}
func (s *HHBoardSim) SetLights(lights int32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	s.cabState.lights = ots_tractor.Lights(lights)
}
func (s *HHBoardSim) SetEngineRpm(rpms int32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	s.cabState.rpms = rpms
}
func (s *HHBoardSim) SetFrontPto(enabled bool) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.setStateStatus(carbon_tractor.HHStateStatus_HH_DISABLED)
	s.cabState.front_pto = enabled
}
func (s *HHBoardSim) SetErrorFlag(errorFlag int32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.errorFlag = errorFlag
	if errorFlag != 0 {
		s.setStateStatus(carbon_tractor.HHStateStatus_HH_SAFE)
	}
}

func (s *HHBoardSim) GetVelocityMPH() float32 {
	speed := s.controller.GetSpeed()
	mult := float32(1.0)
	s.lock.Lock()
	defer s.lock.Unlock()
	if s.gear == ots_tractor.Gear_GEAR_REVERSE {
		mult = float32(-1.0)
	}
	return speed * mult
}
func (s *HHBoardSim) GetWheelAngleDeg() float32 {
	return s.controller.GetWheelAngle()
}

func (s *HHBoardSim) SetFuelLevel(level float32) {
	s.lock.Lock()
	defer s.lock.Unlock()
	s.fuelLevel = level
}
