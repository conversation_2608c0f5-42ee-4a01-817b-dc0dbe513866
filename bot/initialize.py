#!/usr/bin/python3

import argparse
import os
import subprocess
import sys
from typing import List

import context


def setup(args: argparse.Namespace) -> None:
    if args.name is None:
        robot_def = context.RobotDef.from_input()
    else:
        robot_def = context.RobotDef.from_namespace(args)

    checkout_def = context.CheckoutDef.default()
    env_def = context.EnvDef.default()

    robot_def.save(args.test)
    checkout_def.save(args.test)
    env_def.save_default(args.test)


# Robot name is 'slayerCI' just so it will pass the idm whitelist
def ci(args: argparse.Namespace) -> None:
    robot_def = context.RobotDef(
        name="slayerCI",
        generation="slayer",
        computer={"role": args.role or "production", "row": 0},
        robot_username=os.getenv("CI_ROBOT_USERNAME", "none"),
        robot_password=os.getenv("CI_ROBOT_PASSWORD", "none"),
    )
    checkout_def = context.CheckoutDef.user_manual("carbon")
    env_def = context.EnvDef.default()
    env_def.robot = args.repo
    if args.data_dir:
        env_def.data = args.data_dir
    if args.config_dir:
        env_def.config = args.config_dir
    if args.calibration_dir:
        env_def.calibration = args.calibration_dir
    if args.logs_dir:
        env_def.logs = args.logs_dir

    ccache_config = os.path.join(env_def.calibration, ".cache/.ccache/ccache.conf")
    os.makedirs(os.path.dirname(ccache_config), exist_ok=True)
    with open(ccache_config, "w") as fp:
        fp.write("max_size = 20.0G\n")

    robot_def.save(args.test)
    checkout_def.save(args.test)
    env_def.save_default(args.test)
    env_def.save_user("carbon", args.test)


def user(args: argparse.Namespace) -> None:
    checkout_def = context.CheckoutDef.user_manual(user=args.name)
    env_def = context.EnvDef.user(base=args.base)

    checkout_def.save(args.test)
    env_def.save_user(args.name, args.test)


def pull(args: argparse.Namespace) -> None:
    command: List[str] = ["docker", "pull", f"ghcr.io/carbonrobotics/robot/gobot:{args.tag}"]
    print(" ".join(command))
    process = subprocess.Popen(command)
    code = process.wait()
    sys.exit(code)


def main() -> None:
    parser = argparse.ArgumentParser("Bot Initialization CLI")
    parser.add_argument(
        "--test", action="store_true", help="Enable test mode (output files instead of writing)", default=False
    )
    subparsers = parser.add_subparsers(help="Command", dest="cmd")
    subparsers.required = True

    subparser = subparsers.add_parser("setup")
    subparser.add_argument("--name", type=str, help="Robot name")
    subparser.add_argument("--generation", type=str, help="Robot generation")
    subparser.add_argument("--role", type=str, help="Robot computer type")
    subparser.add_argument("--row", type=int, help="Robot row, if computer == row")
    subparser.add_argument("--environment", type=str, help="Environment if not production")
    subparser.add_argument("--robot_username", type=str, help="Robot auth username")
    subparser.add_argument("--robot_password", type=str, help="Robot auth password")
    subparser.set_defaults(func=setup)

    subparser = subparsers.add_parser("ci")
    subparser.add_argument("--repo", type=str, help="Robot Repository Path", required=True)
    subparser.add_argument("--data_dir", type=str, help="Robot data dir")
    subparser.add_argument("--config_dir", type=str, help="Robot config dir")
    subparser.add_argument("--calibration_dir", type=str, help="Robot calibration dir")
    subparser.add_argument("--logs_dir", type=str, help="Robot logs dir")
    subparser.add_argument("--models_dir", type=str, help="Robot models dir")
    subparser.add_argument("--role", type=str, help="Role")
    subparser.set_defaults(func=ci)

    subparser = subparsers.add_parser("user")
    subparser.add_argument("--name", type=str, help="Username", default=context.get_username())
    subparser.add_argument("--base", type=str, help="Base User Dir", default=None)
    subparser.set_defaults(func=user)

    subparser = subparsers.add_parser("pull")
    subparser.add_argument("--tag", type=str, help="Container Tag to Pull", default="latest")
    subparser.set_defaults(func=pull)

    try:
        args = parser.parse_args()
    except SystemExit as e:
        print(f"Invalid use of the tool: {e}")
        raise e

    args.func(args)
    raise SystemExit(0)


if __name__ == "__main__":
    main()
