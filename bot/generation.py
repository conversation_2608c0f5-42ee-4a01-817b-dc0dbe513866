from enum import IntEnum, auto
from typing import Optional, Set


class GENERATION(IntEnum):
    # MUST be all upper case
    BUD = auto()
    SLAYER = auto()
    REAPER = auto()
    RTC = auto()

    @staticmethod
    def generation_strs() -> Set[str]:
        return set(map(lambda x: x.lower(), GENERATION.__members__.keys()))

    @staticmethod
    def from_str(gen: str) -> Optional["GENERATION"]:
        gen = gen.upper()
        if gen in GENERATION.__members__:
            return GENERATION.__members__[gen]
        return None

    @staticmethod
    def from_str_with_default(gen: str, default: "GENERATION") -> "GENERATION":
        opt_gen = GENERATION.from_str(gen)
        if opt_gen is not None:
            return opt_gen
        return default

    def to_str(self) -> str:
        return self.name.lower()
