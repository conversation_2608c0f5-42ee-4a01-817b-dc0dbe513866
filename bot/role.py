from enum import IntEnum, auto
from typing import Optional, Set


class ROLE(IntEnum):
    # MUST be all upper case
    BUD = auto()
    COMMAND = auto()
    ROW = auto()
    MODULE = auto()
    VESELKA_CV = auto()
    PRODUCTION = auto()
    DEEPLEARNING = auto()
    ROBOT_UI = auto()
    SCANNER_VALIDATION = auto()
    SIMULATOR = auto()
    SIMULATOR_MINICOMPUTERS = auto()
    ROW_PRIMARY = auto()
    ROW_SECONDARY = auto()

    @staticmethod
    def role_strs() -> Set[str]:
        return set(map(lambda x: x.lower(), ROLE.__members__.keys()))

    @staticmethod
    def from_str(role: str) -> Optional["ROLE"]:
        role = role.upper()
        role = role.replace("-", "_")
        if role in ROLE.__members__:
            return ROLE.__members__[role]
        return None

    @staticmethod
    def from_str_with_default(role: str, default: "ROLE") -> "ROLE":
        opt_role = ROLE.from_str(role)
        if opt_role is not None:
            return opt_role
        return default

    def to_str(self) -> str:
        if self.value == ROLE.SIMULATOR_MINICOMPUTERS.value:
            # Special case for the simulator mini computer that uses underscores in string
            return self.name.lower()
        return self.name.lower().replace("_", "-")


def is_row(role: ROLE) -> bool:
    return role == ROLE.ROW or role == ROLE.ROW_PRIMARY or role == ROLE.ROW_SECONDARY


def is_numeric_role(role: ROLE) -> bool:
    return is_row(role) or role == ROLE.MODULE
