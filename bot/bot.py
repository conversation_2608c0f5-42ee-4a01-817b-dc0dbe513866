#!/usr/bin/python3

import os
import platform
import subprocess
import sys
from typing import List, Optional

import context


def get_platform() -> str:
    if platform.processor() == "x86_64":
        return "linux/amd64"
    elif platform.processor() == "aarch64":
        return "linux/arm64"
    elif platform.processor() == "arm":  # This is Mac
        return "linux/arm64"
    raise Exception("Unsupported Platform architecture")


def get_gobot_command(checkout: context.CheckoutDef, env: context.EnvDef, version: context.VersionDef) -> List[str]:
    command: List[str] = []
    command.extend(["docker", "run", "--rm"])
    if context.get_username() != "invalid" and os.isatty(sys.stdin.fileno()):
        command.extend(["-it"])
    command.extend(["--platform", get_platform()])
    command.extend(["--network", "host"])
    command.extend(["--env", f"CARBON_USER={context.get_username()}"])
    command.extend(["--env", f"HOST_PLATFORM={platform.system()}"])
    command.extend(["--env", "GOCACHE=/calibration/.cache/go-build"])
    command.extend(["--env", "GOMODCACHE=/calibration/.cache/go-mod-cache"])
    command.extend(["--env", "GOPRIVATE=github.com/carbonrobotics/*"])
    command.extend(["--env", "GITHUB_TOKEN"])

    ssh_auth_sock: Optional[str] = None
    if checkout.mode == "user":
        ssh_auth_sock = None if "SSH_AUTH_SOCK" not in os.environ else os.environ["SSH_AUTH_SOCK"]
        if ssh_auth_sock is not None:
            command.extend(["--env", "SSH_AUTH_SOCK=" + ssh_auth_sock])
        command.extend(["--volume", f"{env.robot}:/robot:rw"])
    command.extend(["--volume", f"{env.bot}:/bot:rw"])
    command.extend(["--volume", f"{env.calibration}:/calibration:rw"])
    command.extend(["--volume", f"{env.config}:/config:rw"])
    command.extend(["--volume", f"{env.data}:/data:rw"])
    command.extend(["--volume", f"{env.logs}:/logs:rw"])
    command.extend(["--volume", f"{env.models}:/models:rw"])
    command.extend(["--volume", "/var/run/docker.sock:/var/run/docker.sock"])
    command.extend(["--volume", "/dev:/dev:rw"])
    command.extend(["--volume", "/dev/shm:/dev/shm:rw"])
    if platform.system() != "Darwin":
        command.extend(["--volume", "/other:/other:ro"])
        command.extend(["--volume", "/:/old:ro"])
        command.extend(["--volume", "/carbon:/carbon:ro"])

    if checkout.mode == "user" and ssh_auth_sock is not None:
        command.extend(["--volume", f"{os.path.expanduser('~')}/.ssh/known_hosts:/root/.ssh/known_hosts:rw"])
        if platform.system() != "Darwin":
            command.extend(["--volume", ssh_auth_sock + ":" + ssh_auth_sock + ":rw"])
        else:
            command.extend(["--volume", "/run/host-services/ssh-auth.sock" + ":" + ssh_auth_sock + ":rw"])
    if os.path.exists(f"{os.path.expanduser('~')}/.docker"):
        command.extend(["--volume", f"{os.path.expanduser('~')}/.docker:/root/.docker:rw"])
    command.extend([f"ghcr.io/carbonrobotics/robot/gobot:{version.tag}"])
    if checkout.mode == "user":
        command.append("/robot/bin/gobot.sh")  # This uses go run
    else:
        command.append("/robot/bin/gobot")
    return command


def main() -> None:
    checkout = context.CheckoutDef.read()
    env = context.EnvDef.read(checkout)
    version = context.VersionDef.read(checkout.mode == "prod")  # TODO Read System Version from somewhere and pass it in

    command = get_gobot_command(checkout=checkout, env=env, version=version) + sys.argv[1:]
    print(" ".join(command))
    my_env = os.environ.copy()
    process = subprocess.Popen(command, env=my_env)
    try:
        code = process.wait()
    except KeyboardInterrupt:
        code = 0
    sys.exit(code)


if __name__ == "__main__":
    main()
