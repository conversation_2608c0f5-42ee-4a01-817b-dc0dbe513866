#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "boundary_checker/cpp/boundary_checker.hpp"
#include "boundary_checker/cpp/farm_data.hpp"
#include "boundary_checker/cpp/tractor_keeper.hpp"
#include "boundary_checker/cpp/tractor_state.hpp"

#include <lib/common/cpp/utils/thread_pool_background.hpp>

#include <geodesic.h>

namespace py = pybind11;

namespace carbon::boundary_checker {

PYBIND11_MODULE(boundary_checker_python, m) {
  py::class_<geos::geom::CoordinateSequence, std::shared_ptr<geos::geom::CoordinateSequence>>(m, "CoordinateSequence")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("reserve", &geos::geom::CoordinateSequence::reserve, py::arg("capacity"),
           py::call_guard<py::gil_scoped_release>())
      .def(
          "add", [](geos::geom::CoordinateSequence *cs, double x, double y) { cs->add(x, y); }, py::arg("x"),
          py::arg("y"), py::call_guard<py::gil_scoped_release>());

  py::class_<BoundaryState, std::shared_ptr<BoundaryState>>(m, "BoundaryState")
      .def_readonly("intersect", &BoundaryState::intersects)
      .def_readonly("inside", &BoundaryState::inside);
  py::class_<BoundaryChecker, std::shared_ptr<BoundaryChecker>>(m, "BoundaryChecker")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("clear", &BoundaryChecker::clear, py::call_guard<py::gil_scoped_release>())
      .def("add_point", &BoundaryChecker::add_point, py::arg("id"), py::arg("builder"), py::arg("buffer"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_line_string", &BoundaryChecker::add_line_string, py::arg("id"), py::arg("builder"), py::arg("buffer"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_polygon", &BoundaryChecker::add_polygon, py::arg("id"), py::arg("builder"), py::arg("holes_builder"),
           py::arg("buffer"), py::call_guard<py::gil_scoped_release>())
      .def("update_robot", &BoundaryChecker::update_robot, py::arg("builder"), py::arg("buffer"),
           py::call_guard<py::gil_scoped_release>())
      .def("get_next", &BoundaryChecker::get_next, py::arg("prev"), py::call_guard<py::gil_scoped_release>());

  py::class_<TractorState, std::unique_ptr<TractorState, py::nodelete>>(m, "TractorState")
      .def(py::init([]() { return std::unique_ptr<TractorState, py::nodelete>(&TractorState::get()); }))
      .def_property("speed_mph", &TractorState::speed_mph, &TractorState::set_speed_mph,
                    py::call_guard<py::gil_scoped_release>())
      .def_property("wheel_angle_rad", &TractorState::wheel_angle_rad, &TractorState::set_wheel_angle_rad,
                    py::call_guard<py::gil_scoped_release>())
      .def_property("reverse", &TractorState::reverse, &TractorState::set_reverse,
                    py::call_guard<py::gil_scoped_release>());

  py::enum_<TractorKeeperState>(m, "TractorKeeperState")
      .value("TKS_Running", TractorKeeperState::TKS_Running)
      .value("TKS_UpdateFailed", TractorKeeperState::TKS_UpdateFailed)
      .value("TKS_RuleViolated", TractorKeeperState::TKS_RuleViolated)
      .export_values();

  py::class_<TractorKeeper, std::shared_ptr<TractorKeeper>>(m, "TractorKeeper")
      .def(py::init<std::shared_ptr<config::ConfigTree>, StateChangeCB, double, const std::vector<proj::Point> &>(),
           py::arg("tree"), py::arg("change_cb"), py::arg("wheelbase_meters"), py::arg("relative_tractor"),
           py::call_guard<py::gil_scoped_release>())
      .def("set_farm", &TractorKeeper::set_farm, py::arg("farm"), py::call_guard<py::gil_scoped_release>())
      .def("set_enforced", &TractorKeeper::set_enforced, py::arg("enforced"), py::call_guard<py::gil_scoped_release>())
      .def("get_next", &TractorKeeper::get_next, py::arg("prev"), py::call_guard<py::gil_scoped_release>());

  py::enum_<BoundaryType>(m, "BoundaryType")
      .value("BT_Point", BoundaryType::BT_Point)
      .value("BT_LineStr", BoundaryType::BT_LineStr)
      .value("BT_Polygon", BoundaryType::BT_Polygon)
      .export_values();

  py::enum_<ZoneType>(m, "ZoneType")
      .value("ZT_Farm", ZoneType::ZT_Farm)
      .value("ZT_Field", ZoneType::ZT_Field)
      .value("ZT_Headland", ZoneType::ZT_Headland)
      .value("ZT_PrivateRoad", ZoneType::ZT_PrivateRoad)
      .value("ZT_Obstacle", ZoneType::ZT_Obstacle)
      .export_values();

  py::class_<FarmData, std::shared_ptr<FarmData>>(m, "FarmData")
      .def(py::init<>(), py::call_guard<py::gil_scoped_release>())
      .def("add_point", &FarmData::add_point, py::arg("id"), py::arg("lon"), py::arg("lat"),
           py::call_guard<py::gil_scoped_release>())
      .def("add_zone", &FarmData::add_zone, py::arg("id"), py::arg("ztype"), py::arg("btype"), py::arg("points"),
           py::arg("buffer"), py::call_guard<py::gil_scoped_release>())
      .def("add_poly_zone", &FarmData::add_poly_zone, py::arg("id"), py::arg("ztype"), py::arg("points"),
           py::arg("holes"), py::arg("buffer"), py::call_guard<py::gil_scoped_release>())
      .def("is_valid", &FarmData::is_valid, py::call_guard<py::gil_scoped_release>());
}

} // namespace carbon::boundary_checker