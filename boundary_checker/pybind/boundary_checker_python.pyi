from typing import Callable, List, Optional, Set

from config.client.cpp.config_client_python import ConfigTree
from proj_utils.pybind.proj_utils_python import Point

class CoordinateSequence:
    def __init__(self) -> None: ...
    def reserve(self, size: int) -> None: ...
    def add(self, x: float, y: float) -> None: ...

class BoundaryState:
    @property
    def intersect(self) -> Set[str]: ...
    @property
    def inside(self) -> Set[str]: ...

class BoundaryChecker:
    def __init__(self) -> None: ...
    def clear(self) -> None: ...
    def add_point(self, id: str, builder: CoordinateSequence, buffer: float) -> None: ...
    def add_line_string(self, id: str, builder: CoordinateSequence, buffer: float) -> None: ...
    def add_polygon(
        self, id: str, builder: CoordinateSequence, holes_builder: List[CoordinateSequence], buffer: float
    ) -> None: ...
    def update_robot(self, builder: CoordinateSequence, buffer: float) -> None: ...
    def get_next(self, prev: Optional[BoundaryState]) -> Optional[BoundaryState]: ...

class TractorState:
    def __init__(self) -> None: ...
    speed_mph: float
    wheel_angle_rad: float
    reverse: bool

class TractorKeeperState:
    TKS_Running: "TractorKeeperState"
    TKS_UpdateFailed: "TractorKeeperState"
    TKS_RuleViolated: "TractorKeeperState"

class TractorKeeper:
    def __init__(
        self,
        tree: ConfigTree,
        change_cb: Callable[[TractorKeeperState, str], None],
        wheelbase_meters: float,
        relative_tractor: List[Point],
    ) -> None: ...
    def set_farm(self, farm: FarmData) -> None: ...
    def set_enforced(self, enforced: bool) -> None: ...
    def get_next(self, prev: Optional[BoundaryState]) -> Optional[BoundaryState]: ...

class BoundaryType:
    BT_Point: "BoundaryType"
    BT_LineStr: "BoundaryType"
    BT_Polygon: "BoundaryType"

class ZoneType:
    ZT_Farm: "ZoneType"
    ZT_Field: "ZoneType"
    ZT_Headland: "ZoneType"
    ZT_PrivateRoad: "ZoneType"
    ZT_Obstacle: "ZoneType"

class FarmData:
    def __init__(self) -> None: ...
    def add_point(self, id: str, lon: float, lat: float) -> None: ...
    def add_zone(self, id: str, ztype: ZoneType, btype: BoundaryType, points: List[str], buffer: float) -> None: ...
    def add_poly_zone(
        self, id: str, ztype: ZoneType, points: List[str], holes: List[List[str]], buffer: float
    ) -> None: ...
    def is_valid(self) -> bool: ...
