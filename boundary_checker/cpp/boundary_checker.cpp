#include "boundary_checker/cpp/boundary_checker.hpp"

#include <stdexcept>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

#include <fmt/format.h>
#include <geos/algorithm/MinimumBoundingCircle.h>
#include <geos/geom/prep/PreparedGeometryFactory.h>
#include <spdlog/spdlog.h>

constexpr double RELOAD_DIST = 2000.0;                // Distance we can go before reloading local farm
constexpr double LOCAL_FARM_DIST = RELOAD_DIST * 1.5; // Distance to search for what is in our local farm

namespace carbon::boundary_checker {

BoundaryChecker::BoundaryChecker()
    : factory_(geos::geom::GeometryFactory::create()), curr_state_(nullptr), shutdown_(false),
      worker_(&BoundaryChecker::run, this) {}
void BoundaryChecker::clear() {
  std::lock_guard lk(farm_mut_);
  farm_.clear();
  reset_local_ = true;
}

void BoundaryChecker::add_point(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder,
                                double buffer) {
  if (buffer <= 0.0) {
    throw std::runtime_error(fmt::format("Invalid point {}, points require a buffer.", id));
  }
  std::unique_ptr<geos::geom::Point> pt(factory_->createPoint(*builder));
  auto buffered = pt->buffer(buffer);
  geos::algorithm::MinimumBoundingCircle circle(buffered.get());
  std::unique_ptr<geos::geom::Point> center(factory_->createPoint(circle.getCentre()));

  std::shared_ptr<Zone> new_zone(nullptr);
  {
    std::lock_guard lk(farm_mut_);
    farm_.emplace_back(std::make_shared<Zone>(id, nullptr,
                                              geos::geom::prep::PreparedGeometryFactory::prepare(buffered.release()),
                                              std::move(center), circle.getRadius()));
    new_zone = farm_.back();
  }
  if (new_zone) {
    spdlog::debug("Adding zone {} with centroid ({}, {}) and radius {}", id, new_zone->centroid->getX(),
                  new_zone->centroid->getY(), new_zone->bounding_radius);
    local_ingest_.emplace_add_no_notify(new_zone);
  }
}
void BoundaryChecker::add_line_string(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder,
                                      double buffer) {

  if (buffer <= 0.0) {
    throw std::runtime_error(fmt::format("Invalid line string {}, line strings require a buffer.", id));
  }
  std::unique_ptr<geos::geom::LineString> ls(factory_->createLineString(*builder));
  auto buffered = ls->buffer(buffer);
  geos::algorithm::MinimumBoundingCircle circle(buffered.get());
  std::unique_ptr<geos::geom::Point> center(factory_->createPoint(circle.getCentre()));

  std::shared_ptr<Zone> new_zone(nullptr);
  {
    std::lock_guard lk(farm_mut_);
    farm_.emplace_back(std::make_shared<Zone>(id, nullptr,
                                              geos::geom::prep::PreparedGeometryFactory::prepare(buffered.release()),
                                              std::move(center), circle.getRadius()));
    new_zone = farm_.back();
  }
  if (new_zone) {
    local_ingest_.emplace_add_no_notify(new_zone);
    spdlog::debug("Adding zone {} with centroid ({}, {}) and radius {}", id, new_zone->centroid->getX(),
                  new_zone->centroid->getY(), new_zone->bounding_radius);
  }
}
void BoundaryChecker::add_polygon(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder,
                                  std::vector<std::shared_ptr<geos::geom::CoordinateSequence>> holes_builder,
                                  double buffer) {

  std::unique_ptr<geos::geom::prep::PreparedGeometry> buffered(nullptr);
  builder->closeRing(true);
  std::unique_ptr<geos::geom::LinearRing> lr_bounds(factory_->createLinearRing(*builder));
  std::vector<std::unique_ptr<geos::geom::LinearRing>> holes;
  for (auto hb : holes_builder) {
    hb->closeRing(true);
    holes.emplace_back(factory_->createLinearRing(*hb));
  }
  auto pg = geos::geom::prep::PreparedGeometryFactory::prepare(
      factory_->createPolygon(std::move(lr_bounds), std::move(holes)).release());
  if (buffer > 0.0) {
    auto tmp = geos::geom::prep::PreparedGeometryFactory::prepare(pg->getGeometry().buffer(buffer).release());
    buffered.swap(tmp);
  }
  geos::algorithm::MinimumBoundingCircle circle(buffered ? &buffered->getGeometry() : &pg->getGeometry());
  std::unique_ptr<geos::geom::Point> center(factory_->createPoint(circle.getCentre()));

  std::shared_ptr<Zone> new_zone(nullptr);
  {
    std::lock_guard lk(farm_mut_);
    farm_.emplace_back(
        std::make_shared<Zone>(id, std::move(pg), std::move(buffered), std::move(center), circle.getRadius()));
    new_zone = farm_.back();
  }
  if (new_zone) {
    local_ingest_.emplace_add_no_notify(new_zone);
    spdlog::debug("Adding zone {} with centroid ({}, {}) and radius {}", id, new_zone->centroid->getX(),
                  new_zone->centroid->getY(), new_zone->bounding_radius);
  }
}
void BoundaryChecker::update_robot(std::shared_ptr<geos::geom::CoordinateSequence> builder, double buffer) {
  builder->closeRing(true);
  std::unique_ptr<geos::geom::Geometry> pg(factory_->createPolygon(factory_->createLinearRing(*builder)));
  if (buffer > 0.0) {
    auto tmp = pg->buffer(buffer);
    pg.swap(tmp);
  }
  std::lock_guard lk(robot_mut_);
  robot_.swap(pg);
  robot_cv_.notify_all();
}
std::shared_ptr<BoundaryState> BoundaryChecker::get_next(std::shared_ptr<BoundaryState> prev) {
  std::chrono::steady_clock::time_point pt =
      prev ? prev->time : std::chrono::steady_clock::now() - std::chrono::hours(1);
  std::unique_lock lk(state_mut_);
  if (curr_state_ && curr_state_->time > pt) {
    return curr_state_;
  }
  state_cv_.wait(lk, [&] { return shutdown_ || (curr_state_ && curr_state_->time > pt); });
  return curr_state_;
}
void BoundaryChecker::run() {
  auto bse =
      lib::common::bot::BotStopHandler::get().create_scoped_event("boundary_checker", [&] { robot_cv_.notify_all(); });
  while (!bse.is_stopped()) {
    std::unique_ptr<geos::geom::Geometry> tmp_robot(nullptr);
    {
      std::unique_lock lk(robot_mut_);
      robot_cv_.wait(lk);
      tmp_robot.swap(robot_);
    }
    if (!tmp_robot) {
      continue;
    }
    if (reset_local_.exchange(false)) {
      local_ingest_.clear(); // we will reload direct from farm instead
      local_farm_area_.reset();
    }
    auto robot = geos::geom::prep::PreparedGeometryFactory::prepare(tmp_robot.release());
    auto centroid = robot->getGeometry().getCentroid();
    if (!local_farm_area_ || robot->within(local_farm_area_.get())) {
      local_farm_area_ = centroid->buffer(RELOAD_DIST);
      local_farm_.clear();
      std::lock_guard lk(farm_mut_);
      for (auto zone : farm_) {
        if (centroid->distance(zone->centroid.get()) <= (LOCAL_FARM_DIST + zone->bounding_radius)) {
          local_farm_.emplace_back(zone);
        }
      }
    }
    auto new_zones = local_ingest_.pop_all();
    for (auto zone : new_zones) {
      if (centroid->distance(zone->centroid.get()) <= (LOCAL_FARM_DIST + zone->bounding_radius)) {
        local_farm_.emplace_back(zone);
      }
    }
    geos::algorithm::MinimumBoundingCircle circle(&robot->getGeometry());
    std::unique_ptr<geos::geom::Point> center(factory_->createPoint(circle.getCentre()));

    auto next_state = std::make_shared<BoundaryState>();
    for (auto &zone : local_farm_) {
      auto zone_state = boundary_check(robot.get(), center.get(), circle.getRadius(), zone.get());
      if (zone_state == CONTAINS_ROBOT) {
        next_state->inside.emplace(zone->id);
      } else if (zone_state == INTERSECTS_WITH_ROBOT) {
        next_state->intersects.emplace(zone->id);
      }
    }
    {
      std::lock_guard lk(state_mut_);
      curr_state_.swap(next_state);
      state_cv_.notify_all();
    }
  }
  shutdown_ = true;
}
BoundaryChecker::ZoneState BoundaryChecker::boundary_check(const geos::geom::prep::PreparedGeometry *robot,
                                                           const geos::geom::Point *center, const double &radius,
                                                           const Zone *zone) const {
  if (center->distance(zone->centroid.get()) > (zone->bounding_radius + radius)) {
    return NO_INTERACTION;
  }
  bool intersects = false;
  if (zone->buffered) {
    intersects = zone->buffered->intersects(&robot->getGeometry());
    if (!intersects) {
      return NO_INTERACTION;
    }
    if (!zone->baseline) {
      // since there is no inside boundary can only be intersection
      return INTERSECTS_WITH_ROBOT;
    }
  }
  if (robot->within(&zone->baseline->getGeometry())) {
    return CONTAINS_ROBOT;
  }
  if (!intersects) {
    intersects = zone->baseline->intersects(&robot->getGeometry());
  }
  if (intersects) {
    return INTERSECTS_WITH_ROBOT;
  }
  return NO_INTERACTION;
}
} // namespace carbon::boundary_checker