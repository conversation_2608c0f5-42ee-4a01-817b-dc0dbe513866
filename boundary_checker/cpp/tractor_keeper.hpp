#pragma once

#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <vector>

#include <boundary_checker/cpp/boundary_checker.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <proj_utils/cpp/relative_pos_coord.hpp>

namespace carbon::boundary_checker {
class BoundaryChecker;
class FarmData;
enum TractorKeeperState {
  TKS_Running = 0,
  TKS_UpdateFailed = 1,
  TKS_RuleViolated = 2,
};
using StateChangeCB = std::function<void(TractorKeeperState, std::string)>;
class TractorKeeper {
public:
  TractorKeeper(std::shared_ptr<config::ConfigTree> tree, StateChangeCB change_cb, double wheelbase_meters,
                const std::vector<proj::Point> &relative_tractor);
  void set_farm(std::shared_ptr<FarmData> farm);
  inline void set_enforced(bool enforced) { enforced_ = enforced; }
  inline std::shared_ptr<BoundaryState> get_next(std::shared_ptr<BoundaryState> prev) { return bc_.get_next(prev); }

private:
  void gps_update_loop();
  void pos_update_loop();
  bool check_and_reload();
  void rule_enforcer_loop();
  void set_state(TractorKeeperState state, const std::string &msg);
  // variables
  struct PosData {
    std::mutex mut;
    double heading_xy_rad;
    proj::Point local_pos;
    proj::Point geo_pos;
    int64_t local_update_ms;
    int64_t gps_update_ms;
    int carr_soln;
    std::condition_variable init_cv;
    PosData() : heading_xy_rad(0.0), local_update_ms(0), gps_update_ms(0), carr_soln(0) {}
  };
  StateChangeCB change_cb_;
  double wheelbase_meters_;
  double turn_alpha_rad_;
  std::vector<proj::Point> relative_tractor_;

  proj::RelativePosCoord plotter_;
  BoundaryChecker bc_;

  std::atomic<bool> reload_required_;
  std::atomic<bool> enforced_;
  std::mutex farm_mut_;

  std::shared_ptr<FarmData> farm_;
  PosData pos_data_;
  std::vector<config::ConfigAtomicAccessor<float>> gps_quality_buffer_;
  std::atomic<TractorKeeperState> state_;
  // Threads at the end
  std::thread gps_update_;
  std::thread pos_update_;
  std::thread rule_enforcer_;
};
} // namespace carbon::boundary_checker