#pragma once

#include <memory>
#include <optional>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include <proj_utils/cpp/point.hpp>
namespace geos::geom {
class CoordinateSequence;
}
namespace carbon::proj {
class RelativePosCoord;
}
namespace carbon::boundary_checker {
enum BoundaryType {
  BT_Point,
  BT_LineStr,
  BT_Polygon,
};

enum ZoneType {
  ZT_Farm,
  ZT_Field,
  ZT_Headland,
  ZT_PrivateRoad,
  ZT_Obstacle,
};

struct Zone {
  BoundaryType type;
  std::vector<proj::Point> boundary;
  std::vector<std::vector<proj::Point>> holes;
  double buffer;
  Zone() : type(BT_Point), buffer(0.0) {}
  Zone(BoundaryType _type, double _buffer) : type(_type), buffer(_buffer) {}

  std::shared_ptr<geos::geom::CoordinateSequence> boundary_builder(proj::RelativePosCoord *plotter) const;
  std::vector<std::shared_ptr<geos::geom::CoordinateSequence>> hole_builder(proj::RelativePosCoord *plotter) const;
};

class FarmData {
public:
  FarmData();
  void add_point(const std::string &id, const double &lon, const double &lat);
  void add_zone(const std::string &id, ZoneType ztype, BoundaryType btype, const std::vector<std::string> points,
                double buffer);
  void add_poly_zone(const std::string &id, ZoneType ztype, const std::vector<std::string> &points,
                     const std::vector<std::vector<std::string>> &holes, double buffer);
  bool is_valid() const;

  inline const std::unordered_set<std::string> &obstacles() const { return obstacles_; }
  inline const std::string &farm_zone_id() const { return farm_zone_id_; }
  inline const std::unordered_map<std::string, Zone> &zones() const { return farm_zones_; }

private:
  std::unordered_map<std::string, proj::Point> farm_points_;
  std::unordered_map<std::string, Zone> farm_zones_;
  std::string farm_zone_id_;
  size_t farm_count_;
  std::unordered_set<std::string> obstacles_;
};
} // namespace carbon::boundary_checker