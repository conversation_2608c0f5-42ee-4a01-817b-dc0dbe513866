#include "boundary_checker/cpp/farm_data.hpp"

#include <exception>

#include <proj_utils/cpp/relative_pos_coord.hpp>

#include <fmt/format.h>
#include <geos/geom/Geometry.h>
#include <spdlog/spdlog.h>

namespace carbon::boundary_checker {
std::shared_ptr<geos::geom::CoordinateSequence> Zone::boundary_builder(proj::RelativePosCoord *plotter) const {
  auto builder = std::make_shared<geos::geom::CoordinateSequence>();
  builder->reserve(boundary.size());
  for (const auto &geo_pt : boundary) {
    auto local_pt = plotter->geo_to_rel(geo_pt);
    builder->add(local_pt.x(), local_pt.y());
  }
  return builder;
}
std::vector<std::shared_ptr<geos::geom::CoordinateSequence>> Zone::hole_builder(proj::RelativePosCoord *plotter) const {
  std::vector<std::shared_ptr<geos::geom::CoordinateSequence>> builder;
  for (const auto &hole : holes) {
    auto &hb = builder.emplace_back(std::make_shared<geos::geom::CoordinateSequence>());
    hb->reserve(hole.size());
    for (const auto &geo_pt : hole) {
      auto local_pt = plotter->geo_to_rel(geo_pt);
      hb->add(local_pt.x(), local_pt.y());
    }
  }
  return builder;
}

FarmData::FarmData() : farm_count_(0) {}
void FarmData::add_point(const std::string &id, const double &lon, const double &lat) {
  farm_points_.emplace(std::piecewise_construct, std::forward_as_tuple(id), std::forward_as_tuple(lon, lat));
}
void FarmData::add_zone(const std::string &id, ZoneType ztype, BoundaryType btype,
                        const std::vector<std::string> points, double buffer) {
  if (btype != BT_Polygon && buffer <= 0.0) {
    throw std::runtime_error(fmt::format("Invalid zone {}, missing buffer", id));
  }
  auto pair =
      farm_zones_.emplace(std::piecewise_construct, std::forward_as_tuple(id), std::forward_as_tuple(btype, buffer));
  if (!pair.second) {
    throw std::runtime_error(fmt::format("failed to add zone {}", id));
  }
  for (const auto &pid : points) {
    auto pit = farm_points_.find(pid);
    if (pit == farm_points_.end()) {
      throw std::runtime_error(fmt::format("zone {} contains unknown point {}", id, pid));
    }
    pair.first->second.boundary.emplace_back(pit->second);
  }
  if (ztype == ZT_Farm) {
    farm_zone_id_ = id;
    ++farm_count_;
  } else if (ztype == ZT_Obstacle) {
    obstacles_.emplace(id);
  }
}
void FarmData::add_poly_zone(const std::string &id, ZoneType ztype, const std::vector<std::string> &points,
                             const std::vector<std::vector<std::string>> &holes, double buffer) {

  auto pair = farm_zones_.emplace(std::piecewise_construct, std::forward_as_tuple(id),
                                  std::forward_as_tuple(BT_Polygon, buffer));
  if (!pair.second) {
    throw std::runtime_error(fmt::format("failed to add zone {}", id));
  }
  for (const auto &pid : points) {
    auto pit = farm_points_.find(pid);
    if (pit == farm_points_.end()) {
      throw std::runtime_error(fmt::format("zone {} contains unknown point {}", id, pid));
    }
    pair.first->second.boundary.emplace_back(pit->second);
  }
  for (const auto &hole : holes) {
    auto &zone_hole = pair.first->second.holes.emplace_back();
    for (const auto &pid : hole) {
      auto pit = farm_points_.find(pid);
      if (pit == farm_points_.end()) {
        throw std::runtime_error(fmt::format("zone {} contains unknown point {}", id, pid));
      }
      zone_hole.emplace_back(pit->second);
    }
  }
  if (ztype == ZT_Farm) {
    farm_zone_id_ = id;
    ++farm_count_;
  } else if (ztype == ZT_Obstacle) {
    obstacles_.emplace(id);
  }
}
bool FarmData::is_valid() const { return farm_count_ == 1; }

} // namespace carbon::boundary_checker
