#pragma once

#include <atomic>
namespace carbon::boundary_checker {
class TractorState {
public:
  TractorState(const TractorState &ts) = delete;
  TractorState &operator=(const TractorState &) = delete;
  static TractorState &get();
  inline float speed_mph() const { return speed_mph_; }
  inline float wheel_angle_rad() const { return wheel_angle_rad_; }
  inline bool reverse() const { return reverse_; }
  inline void set_speed_mph(float speed_mph) { speed_mph_ = speed_mph; }
  inline void set_wheel_angle_rad(float wheel_angle_rad) { wheel_angle_rad_ = wheel_angle_rad; }
  inline void set_reverse(bool reverse) { reverse_ = reverse; }

private:
  std::atomic<float> speed_mph_;
  std::atomic<float> wheel_angle_rad_;
  std::atomic<bool> reverse_;

private:
  TractorState();
};
} // namespace carbon::boundary_checker