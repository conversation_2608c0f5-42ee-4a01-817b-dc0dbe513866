#pragma once
#include <atomic>
#include <chrono>
#include <condition_variable>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_set>
#include <vector>

#include <lib/common/cpp/utils/thread_safe_queue.hpp>

#include <geos/geom/Geometry.h>
#include <geos/geom/GeometryFactory.h>
#include <geos/geom/prep/PreparedGeometry.h>
namespace carbon::boundary_checker {
struct BoundaryState {
  std::chrono::steady_clock::time_point time;
  std::unordered_set<std::string> intersects;
  std::unordered_set<std::string> inside;
  BoundaryState() : time(std::chrono::steady_clock::now()) {}
};
class BoundaryChecker {
public:
  BoundaryChecker();
  void clear();
  void add_point(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder, double buffer);
  void add_line_string(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder, double buffer);
  void add_polygon(const std::string &id, std::shared_ptr<geos::geom::CoordinateSequence> builder,
                   std::vector<std::shared_ptr<geos::geom::CoordinateSequence>> holes_builder, double buffer);
  void update_robot(std::shared_ptr<geos::geom::CoordinateSequence> builder, double buffer);
  std::shared_ptr<BoundaryState> get_next(std::shared_ptr<BoundaryState> prev);

private:
  enum ZoneState {
    NO_INTERACTION,
    CONTAINS_ROBOT,
    INTERSECTS_WITH_ROBOT,
  };
  struct Zone {
    std::string id;
    std::unique_ptr<geos::geom::prep::PreparedGeometry> baseline;
    std::unique_ptr<geos::geom::prep::PreparedGeometry> buffered;
    std::unique_ptr<geos::geom::Point> centroid;
    double bounding_radius;
    Zone(const std::string &_id, std::unique_ptr<geos::geom::prep::PreparedGeometry> _baseline,
         std::unique_ptr<geos::geom::prep::PreparedGeometry> _buffered, std::unique_ptr<geos::geom::Point> _centroid,
         double _bounding_radius)
        : id(_id), baseline(std::move(_baseline)), buffered(std::move(_buffered)), centroid(std::move(_centroid)),
          bounding_radius(_bounding_radius) {}
  };
  void run();
  ZoneState boundary_check(const geos::geom::prep::PreparedGeometry *robot, const geos::geom::Point *center,
                           const double &radius, const Zone *zone) const;

  geos::geom::GeometryFactory::Ptr factory_;
  std::vector<std::shared_ptr<Zone>> farm_;
  std::vector<std::shared_ptr<Zone>> local_farm_;
  std::atomic_bool reset_local_;
  std::shared_ptr<BoundaryState> curr_state_;
  std::unique_ptr<geos::geom::Geometry> robot_;
  std::unique_ptr<geos::geom::Geometry> local_farm_area_;

  std::mutex farm_mut_;
  std::mutex state_mut_;
  std::mutex robot_mut_;
  common::ThreadSafeQueue<std::shared_ptr<Zone>> local_ingest_;

  std::condition_variable state_cv_;
  std::condition_variable robot_cv_;
  std::atomic_bool shutdown_;

  std::thread worker_; // Threads are last for construction
};
} // namespace carbon::boundary_checker