#pragma once

#include <c10/cuda/CUDAGuard.h>
#include <opencv2/cudaimgproc.hpp>

#include "cv/runtime/cpp/node.h"
#include "lib/common/camera/cpp/camera.h"
#include "lib/common/camera/cpp/exceptions.h"
#include "lib/common/image/cpp/focus_metric.h"

namespace cv {
namespace runtime {
namespace nodes {

using namespace lib::common::camera;

class FrequencyFocusMetric : public CVNodeImpl {
public:
  FrequencyFocusMetric(Camera cam, Input<CameraImage> input);
  std::optional<float> get_last_frequency_focus_metric() const;
  Output<CameraImage> &get_output();

protected:
  int64_t tick() override;

  void close_connectors() override;

private:
  Input<CameraImage> input_;
  std::unique_ptr<c10::cuda::CUDAStream> cuda_stream_;
  Output<CameraImage> output_;
  std::unique_ptr<lib::common::image::FrequencyBasedFocusMetric> frequency_focus_metric_;
  std::atomic<std::optional<float>> last_frequency_focus_metric_;
};

} // namespace nodes
} // namespace runtime
} // namespace cv
