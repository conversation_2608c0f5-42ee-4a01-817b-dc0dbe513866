#include <gtest/gtest.h>

#include "weed_tracking_libs/deduplicator/cpp/deduplication_algorithms.hpp"
#include "weed_tracking_libs/test/test_utils.hpp"
#include "wheel_encoder/cpp/wheel_encoder.hpp"
#include <lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp>

#include <almanac/cpp/almanac.hpp>
#include <almanac/cpp/discriminator.hpp>
#include <almanac/cpp/modelinator.hpp>

#include <trajectory/cpp/decisions.hpp>
#include <trajectory/cpp/enums.hpp>
#include <trajectory/cpp/tracked_item_centroid.hpp>
#include <trajectory/cpp/trajectory.hpp>

#include <lib/common/cpp/exceptions.h>
#include <lib/common/cpp/time.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>

#include <weed_tracking_libs/decision_line/cpp/decision_line.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>

#include <spdlog/spdlog.h>

namespace carbon {

class DeduplicationAlgorithmsIntegrationTest : public ::testing::Test {
protected:
  static std::shared_ptr<config::ConfigSubscriber> config_subscriber_;
  static std::shared_ptr<trajectory::TestingTrajectoryFactory> factory_;
  static std::shared_ptr<decision_line::MockDecisionLine> decision_line_;

  static void SetUpTestSuite() {
    carbon::wheel_encoder::WheelEncoder::set(
        std::make_shared<carbon::nanopb::nofx_board::WheelEncoderNOFXSim>(false, 100, 100));

    if (config_subscriber_ == nullptr) {
      config_subscriber_ = config::get_global_config_subscriber();
      config_subscriber_->add_config_tree("common", "common", "services/common.yaml");
      config_subscriber_->add_config_tree("aimbot", "aimbot", "services/aimbot.yaml");
    }
    if (factory_ == nullptr) {
      factory_ = std::make_shared<trajectory::TestingTrajectoryFactory>();
    }
    if (decision_line_ == nullptr) {
      decision_line_ = std::make_shared<decision_line::MockDecisionLine>();
    }
    decision_line::DecisionLine::set(decision_line_);
  }

  static void TearDownTestSuite() {
    // Thread pool in deduplication algorithms use the bot stop handler but will shut down gracefully
    // without it via destructor.
    // If stop is called here, it will segfault due to undefined destruction order since the bot stop handler
    // is a singleton.

    // better solution is to store a reference to the bot stop handler so it is considered in scope for the
    // entire test suite rather than possibly constructed and destroyed in each test.
  }

  void get_default_trajectories(std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                                CarbonClock &clock) {
    {
      factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
      factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

      factory_->tic_factory.size_mm = 3.0f;
      factory_->tic_factory.score = 0.9f;
      factory_->tic_factory.weed_score = 0.9f;
      factory_->tic_factory.crop_score = 0.1f;
      factory_->tic_factory.plant_score = 0.9f;
      factory_->tic_factory.x_mm = 10.0f;
      factory_->tic_factory.y_mm = 1.0f;
      factory_->tic_factory.has_perspective = true;
      factory_->tracker_id = 1;
      factory_->tic_factory.detection_classes = {
          {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
    }
    auto traj_predict1_1 = factory_->create_trajectory();

    {
      factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
      factory_->tic_factory.x_mm = 20.0f;
      factory_->tic_factory.y_mm = 1.0f;
      factory_->tracker_id = 2;
    }
    auto traj_predict2_1 = factory_->create_trajectory();

    {
      factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
      factory_->tic_factory.x_mm = 50.0f;
      factory_->tic_factory.y_mm = 1.0f;
      factory_->tracker_id = 1;
    }
    auto traj_predict1_2 = factory_->create_trajectory();

    {
      factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
      factory_->tic_factory.x_mm = 60.0f;
      factory_->tic_factory.y_mm = 1.0f;
      factory_->tracker_id = 2;
    }
    auto traj_predict2_2 = factory_->create_trajectory();

    trajectories.push_back(traj_predict1_1);
    trajectories.push_back(traj_predict1_2);
    trajectories.push_back(traj_predict2_1);
    trajectories.push_back(traj_predict2_2);
  }
};

std::shared_ptr<config::ConfigSubscriber> DeduplicationAlgorithmsIntegrationTest::config_subscriber_ = nullptr;
std::shared_ptr<trajectory::TestingTrajectoryFactory> DeduplicationAlgorithmsIntegrationTest::factory_ = nullptr;
std::shared_ptr<decision_line::MockDecisionLine> DeduplicationAlgorithmsIntegrationTest::decision_line_ = nullptr;

using namespace carbon::deduplicator;

// Tests basic classic deduplication with 4 trajectories from 2 adjacent trackers
// that are close enough to match. Verifies correct duplicate pairs are found.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_HappyPath) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);
  ASSERT_TRUE(dup_ids[0].primary == traj_predict2_2->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict1_1->id());

  ASSERT_TRUE(traj_predict2_2->get_duplicate_trajectory() == traj_predict1_2);
  ASSERT_TRUE(traj_predict1_2->get_primary_trajectory_id() == traj_predict2_2->id());
  ASSERT_TRUE(traj_predict2_1->get_duplicate_trajectory() == traj_predict1_1);
  ASSERT_TRUE(traj_predict1_1->get_primary_trajectory_id() == traj_predict2_1->id());
}

// Tests classic deduplication when trajectories are too far apart to match.
// Uses smaller radius so no duplicates should be found, all become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_TooFar) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(88, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 0);

  ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict1_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests basic RANSAC deduplication with 4 trajectories from 2 adjacent trackers.
// Verifies RANSAC algorithm finds correct duplicate pairs.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_HappyPath) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_2->id());

  ASSERT_TRUE(traj_predict1_2->get_duplicate_trajectory() == traj_predict2_2);
  ASSERT_TRUE(traj_predict2_2->get_primary_trajectory_id() == traj_predict1_2->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
}

// Tests RANSAC deduplication when trajectories are too far apart to match.
// Uses smaller radius so no duplicates should be found, all become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_TooFar) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(88, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 0);

  ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict1_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with an extra trajectory on the right tracker that doesn't match.
// Verifies the extra trajectory becomes unique while others form duplicate pairs.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_ExtraRight) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 15.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_3 = factory_->create_trajectory();

  trajectory_map[traj_predict2_3->id()] = traj_predict2_3;

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_2->id());

  ASSERT_TRUE(traj_predict1_2->get_duplicate_trajectory() == traj_predict2_2);
  ASSERT_TRUE(traj_predict2_2->get_primary_trajectory_id() == traj_predict1_2->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  ASSERT_TRUE(traj_predict2_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with an extra trajectory on the left tracker that doesn't match well.
// Verifies the extra trajectory becomes unique while others form duplicate pairs.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_LopsidedLeft) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 30.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_3 = factory_->create_trajectory();
  trajectory_map[traj_predict1_3->id()] = traj_predict1_3;

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_2->id());

  ASSERT_TRUE(traj_predict1_2->get_duplicate_trajectory() == traj_predict2_2);
  ASSERT_TRUE(traj_predict2_2->get_primary_trajectory_id() == traj_predict1_2->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  ASSERT_TRUE(traj_predict1_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with an extra trajectory on the right tracker that doesn't match well.
// Verifies the extra trajectory becomes unique while others form duplicate pairs.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_LopsidedRight) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 30.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_3 = factory_->create_trajectory();

  trajectory_map[traj_predict2_3->id()] = traj_predict2_3;

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_2->id());

  ASSERT_TRUE(traj_predict1_2->get_duplicate_trajectory() == traj_predict2_2);
  ASSERT_TRUE(traj_predict2_2->get_primary_trajectory_id() == traj_predict1_2->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  ASSERT_TRUE(traj_predict2_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with two trajectories that are too far apart to have any overlap.
// Verifies no duplicates are found and both become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_NoOverlap) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 200.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_1 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict2_1->id(), traj_predict2_1},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 0);
  ASSERT_TRUE(traj_predict2_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with extra trajectories on both sides that are far from the main clusters.
// Verifies the main clusters form duplicates while extra items become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_ExtraItems) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  std::vector<std::shared_ptr<carbon::trajectory::Trajectory>> trajectories;
  get_default_trajectories(trajectories, clock);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map;

  for (size_t i = 0; i < trajectories.size(); i++) {
    trajectory_map[trajectories[i]->id()] = trajectories[i];
  }

  auto traj_predict1_1 = trajectories[0];
  auto traj_predict1_2 = trajectories[1];
  auto traj_predict2_1 = trajectories[2];
  auto traj_predict2_2 = trajectories[3];

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = -200.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_3 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 200.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_3 = factory_->create_trajectory();

  trajectory_map[traj_predict1_3->id()] = traj_predict1_3;
  trajectory_map[traj_predict2_3->id()] = traj_predict2_3;

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_2->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_2->id());

  ASSERT_TRUE(traj_predict1_2->get_duplicate_trajectory() == traj_predict2_2);
  ASSERT_TRUE(traj_predict2_2->get_primary_trajectory_id() == traj_predict1_2->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  ASSERT_TRUE(traj_predict1_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with trajectories only on the left tracker (no right tracker trajectories).
// Verifies all trajectories become unique since there's nothing to match with.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_OnlyInLeft) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 50.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_2 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = -200.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_3 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict1_2->id(), traj_predict1_2},
          {traj_predict1_3->id(), traj_predict1_3},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(101, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 0);

  ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict1_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict1_3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with trajectories that are within query radius but outside match radius.
// Some pairs should match while others become unique due to match radius constraints.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_OutsideOfMatchRadius) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 20.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 50.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_2 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 63.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_2 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 60.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 1;
  }
  auto traj_predict1_3 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 70.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_3 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1}, {traj_predict2_1->id(), traj_predict2_1},
          {traj_predict1_2->id(), traj_predict1_2}, {traj_predict2_2->id(), traj_predict2_2},
          {traj_predict1_3->id(), traj_predict1_3}, {traj_predict2_3->id(), traj_predict2_3},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(200, 4, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 2);

  ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
  ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());
  ASSERT_TRUE(dup_ids[1].primary == traj_predict1_3->id());
  ASSERT_TRUE(dup_ids[1].duplicate == traj_predict2_3->id());

  ASSERT_TRUE(traj_predict1_3->get_duplicate_trajectory() == traj_predict2_3);
  ASSERT_TRUE(traj_predict2_3->get_primary_trajectory_id() == traj_predict1_3->id());
  ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
  ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  ASSERT_TRUE(traj_predict1_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict2_2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with trajectories from non-adjacent trackers (tracker 1 and 3).
// Verifies no duplicates are found since only adjacent trackers can match.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_NoSubsequentTrackers) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 20.0f;
    factory_->tic_factory.y_mm = 1.0f;
    factory_->tracker_id = 3;
  }
  auto traj_predict3_1 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict3_1->id(), traj_predict3_1},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(200, 4, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  ASSERT_TRUE(dup_ids.size() == 0);

  ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_predict3_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests RANSAC with one trajectory on each adjacent tracker that are close enough to match.
// Verifies they form a duplicate pair.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_OneLeftOneRight) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 20.0f;
    factory_->tic_factory.y_mm = 20.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_1 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict2_1->id(), traj_predict2_1},
      };

  {
    carbon::deduplicator::DeduplicationParameters deduplication_parameters(201, 5, 100);
    std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
    carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
    deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

    ASSERT_TRUE(dup_ids.size() == 1);

    ASSERT_TRUE(dup_ids[0].primary == traj_predict1_1->id());
    ASSERT_TRUE(dup_ids[0].duplicate == traj_predict2_1->id());

    ASSERT_TRUE(traj_predict1_1->get_duplicate_trajectory() == traj_predict2_1);
    ASSERT_TRUE(traj_predict2_1->get_primary_trajectory_id() == traj_predict1_1->id());
  }
}

// Tests RANSAC with one trajectory on each adjacent tracker that are too far apart to match.
// Verifies no duplicates are found and both become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_OneLeftOneRightTooFar) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 20.0f;
    factory_->tic_factory.y_mm = 20.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_1 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict2_1->id(), traj_predict2_1},
      };

  {
    carbon::deduplicator::DeduplicationParameters deduplication_parameters(199, 5, 100);
    std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
    carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
    deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

    ASSERT_TRUE(dup_ids.size() == 0);

    ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
    ASSERT_TRUE(traj_predict2_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  }
}

// Tests the key hash function used for trajectory pair mapping.
// Verifies that pairs (i,j) and (j,i) are treated as different keys.
TEST_F(DeduplicationAlgorithmsIntegrationTest, KeyHashFunction) {
  std::vector<std::pair<uint32_t, uint32_t>> pairs;
  for (size_t i = 0; i < 100; i++) {
    pairs.push_back(std::pair(i, i + 1));
    pairs.push_back(std::pair(i + 1, i));
  }

  std::unordered_map<std::pair<uint32_t, uint32_t>, uint32_t, key_hash_function> unique_items;
  for (size_t i = 0; i < 100; i++) {
    unique_items[std::pair(i, i + 1)] = 1;
    unique_items[std::pair(i + 1, i)] = 1;
  }

  ASSERT_EQ(unique_items.size(), 200);
}

// Tests RANSAC with trajectories at large coordinate values that are too far apart.
// Verifies distance calculations work correctly with large numbers.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_OneLeftOneRightTooFar2) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  decision_line_->line_mm.store(0.0f);

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));

    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 2988.0f;
    factory_->tic_factory.y_mm = 3032.0f;
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_predict1_1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 2983.0f;
    factory_->tic_factory.y_mm = 3004.0f;
    factory_->tracker_id = 2;
  }
  auto traj_predict2_1 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_predict1_1->id(), traj_predict1_1},
          {traj_predict2_1->id(), traj_predict2_1},
      };

  {
    carbon::deduplicator::DeduplicationParameters deduplication_parameters(201, 5, 100);
    std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
    carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
    deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

    ASSERT_TRUE(dup_ids.size() == 0);

    ASSERT_TRUE(traj_predict1_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
    ASSERT_TRUE(traj_predict2_1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
  }
}

// Tests classic deduplication when a unique trajectory matches with an unset trajectory after decision line.
// Verifies the unique trajectory becomes primary and the unset becomes duplicate.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_UnsetMatchesPreviouslyUniqueAfterDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f so trajectories with y >= 5.0f are considered passed
  decision_line_->line_mm.store(5.0f);

  // FIRST ITERATION: Create a trajectory that will become unique by passing decision line alone
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_unique->id(), traj_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Add a new unset trajectory that should match with the unique one
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 15.0f; // Close enough to match
    factory_->tic_factory.y_mm = 12.0f; // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_unique->id(), traj_unique},
          {traj_unset->id(), traj_unset},
      };

  // Verify initial states for second iteration
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);

  // Run second iteration to test unique-to-unset matching
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_classic(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find one duplicate pair
  ASSERT_EQ(dup_ids_second.size(), 1);

  // Verify the duplicate relationship is established correctly
  // The previously unique trajectory should become primary, unset should become duplicate
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);

  // Verify the duplicate relationship
  ASSERT_TRUE(traj_unique->get_duplicate_trajectory() == traj_unset);
  ASSERT_TRUE(traj_unset->get_primary_trajectory_id() == traj_unique->id());
}

// Tests RANSAC deduplication when a unique trajectory matches with an unset trajectory after decision line.
// Verifies the unique trajectory becomes primary and the unset becomes duplicate.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_UnsetMatchesPreviouslyUniqueAfterDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // FIRST ITERATION: Create a trajectory that will become unique by passing decision line alone
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_unique->id(), traj_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Add a new unset trajectory that should match with the unique one
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 15.0f; // Close enough to match
    factory_->tic_factory.y_mm = 12.0f; // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_unique->id(), traj_unique},
          {traj_unset->id(), traj_unset},
      };

  // Verify initial states for second iteration
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);

  // Run second iteration to test unique-to-unset matching
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_ransac(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find one duplicate pair
  ASSERT_EQ(dup_ids_second.size(), 1);

  // Verify the duplicate relationship is established correctly
  // The previously unique trajectory should become primary, unset should become duplicate
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);

  // Verify the duplicate relationship
  ASSERT_TRUE(traj_unique->get_duplicate_trajectory() == traj_unset);
  ASSERT_TRUE(traj_unset->get_primary_trajectory_id() == traj_unique->id());
}

// Tests classic deduplication with a pre-existing unique trajectory that matches an unset trajectory.
// Verifies the unique trajectory becomes primary and maintains correct duplicate relationships.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_UniqueBecomesSecondaryWhenMatchedWithUnset) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // FIRST ITERATION: Create trajectory that will become unique first (passes decision line)
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_will_be_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_will_be_unique->id(), traj_will_be_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Create unset trajectory that will match with the unique one
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 8.0f;  // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_will_be_unique->id(), traj_will_be_unique},
          {traj_unset->id(), traj_unset},
      };

  // Verify initial states for second iteration
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);

  // Run second iteration to test unique-to-unset matching
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_classic(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find one duplicate pair
  ASSERT_EQ(dup_ids_second.size(), 1);

  // The previously unique trajectory should become primary, unset should become duplicate
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);

  // Verify the duplicate relationship
  ASSERT_TRUE(traj_will_be_unique->get_duplicate_trajectory() == traj_unset);
  ASSERT_TRUE(traj_unset->get_primary_trajectory_id() == traj_will_be_unique->id());
}

// Tests RANSAC deduplication with a pre-existing unique trajectory that matches an unset trajectory.
// Verifies the unique trajectory becomes primary and maintains correct duplicate relationships.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_UniqueBecomesSecondaryWhenMatchedWithUnset) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // FIRST ITERATION: Create trajectory that will become unique first
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_will_be_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_will_be_unique->id(), traj_will_be_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Create unset trajectory that will match
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 8.0f;  // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_will_be_unique->id(), traj_will_be_unique},
          {traj_unset->id(), traj_unset},
      };

  // Verify initial states for second iteration
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);

  // Run second iteration to test unique-to-unset matching
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_ransac(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find one duplicate pair
  ASSERT_EQ(dup_ids_second.size(), 1);

  // The previously unique trajectory should become primary, unset should become duplicate
  ASSERT_TRUE(traj_will_be_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);
}

// Tests classic deduplication when both trajectories are before the decision line.
// Verifies no matching occurs and both remain unset since neither passed the decision line.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_NoMatchWhenBothBelowDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=15.0f so trajectories before it won't be processed
  decision_line_->line_mm.store(15.0f);

  // Create two trajectories before decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 5.0f; // Before decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 7.0f;  // Before decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj2 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj1->id(), traj1},
          {traj2->id(), traj2},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  // Should find no duplicates since neither passed decision line
  ASSERT_EQ(dup_ids.size(), 0);

  // Both should remain unset
  ASSERT_TRUE(traj1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
  ASSERT_TRUE(traj2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
}

// Tests RANSAC deduplication when both trajectories are before the decision line.
// Verifies no matching occurs and both remain unset since neither passed the decision line.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_NoMatchWhenBothBelowDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=15.0f
  decision_line_->line_mm.store(15.0f);

  // Create two trajectories before decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 5.0f; // Before decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 7.0f;  // Before decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj2 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj1->id(), traj1},
          {traj2->id(), traj2},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  // Should find no duplicates since neither passed decision line
  ASSERT_EQ(dup_ids.size(), 0);

  // Both should remain unset
  ASSERT_TRUE(traj1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
  ASSERT_TRUE(traj2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
}

// Tests classic deduplication when only one trajectory passes the decision line.
// Verifies matching occurs since at least one trajectory passed the decision line.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_MatchWhenOnlyOnePassesDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=8.0f
  decision_line_->line_mm.store(8.0f);

  // Create trajectory that passes decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_above = factory_->create_trajectory();

  // Create trajectory that doesn't pass decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 5.0f;  // Before decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_below = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_above->id(), traj_above},
          {traj_below->id(), traj_below},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  // Should find one duplicate pair since one passed decision line (either_passed = true)
  ASSERT_EQ(dup_ids.size(), 1);

  // Verify duplicate relationship is established
  ASSERT_TRUE(traj_above->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary ||
              traj_below->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_above->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate ||
              traj_below->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);
}

// Tests RANSAC deduplication when only one trajectory passes the decision line.
// Verifies matching occurs since at least one trajectory passed the decision line.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Ransac_MatchWhenOnlyOnePassesDecisionLine) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=8.0f
  decision_line_->line_mm.store(8.0f);

  // Create trajectory that passes decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_above = factory_->create_trajectory();

  // Create trajectory that doesn't pass decision line
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 5.0f;  // Before decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_below = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_above->id(), traj_above},
          {traj_below->id(), traj_below},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_ransac(trajectory_map, deduplication_parameters, dup_ids);

  // Should find one duplicate pair since one passed decision line
  ASSERT_EQ(dup_ids.size(), 1);

  // Verify duplicate relationship is established
  ASSERT_TRUE(traj_above->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary ||
              traj_below->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_above->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate ||
              traj_below->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);
}

// Tests classic deduplication with trajectories already marked as primary and duplicate.
// Verifies no new duplicates are found since both trajectories are already processed.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_NoMatchWithAlreadyProcessedTrajectories) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // Create trajectory already marked as primary
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_primary = factory_->create_trajectory();
  traj_primary->duplicate_status(carbon::trajectory::DuplicateStatus::kPrimary);

  // Create trajectory already marked as duplicate
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 8.0f;  // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_duplicate = factory_->create_trajectory();
  traj_duplicate->duplicate_status(carbon::trajectory::DuplicateStatus::kDuplicate);

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj_primary->id(), traj_primary},
          {traj_duplicate->id(), traj_duplicate},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  // Should find no new duplicates since both are already processed
  ASSERT_EQ(dup_ids.size(), 0);

  // Status should remain unchanged
  ASSERT_TRUE(traj_primary->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_duplicate->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);
}

// Tests classic deduplication with multiple unset trajectories and one unique trajectory.
// Verifies the unique matches with nearby unset while distant unset becomes unique.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_MultipleUnsetTrajectoriesWithOneUnique) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // FIRST ITERATION: Create trajectory that will become unique
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_unique->id(), traj_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Create unset trajectories that will be tested against the unique one
  // Create first unset trajectory that can match with unique
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough to match
    factory_->tic_factory.y_mm = 8.0f;  // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset1 = factory_->create_trajectory();

  // Create second unset trajectory that's far away
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 100.0f; // Too far to match
    factory_->tic_factory.y_mm = 15.0f;  // Past decision line
    factory_->tracker_id = 3;            // Adjacent to tracker 2
  }
  auto traj_unset2 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_unique->id(), traj_unique},
          {traj_unset1->id(), traj_unset1},
          {traj_unset2->id(), traj_unset2},
      };

  // Run second iteration to test unique-to-unset matching
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_classic(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find one duplicate pair (unique + unset1)
  ASSERT_EQ(dup_ids_second.size(), 1);

  // The unique should become primary, unset1 should become duplicate
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset1->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);

  // The far away unset trajectory should become unique
  ASSERT_TRUE(traj_unset2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests classic deduplication with trajectories from non-adjacent trackers (1 and 3).
// Verifies no duplicates are found since only adjacent trackers can match.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_NoMatchWithNonAdjacentTrackers) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // Create trajectory from tracker 1
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj1 = factory_->create_trajectory();

  // Create trajectory from tracker 3 (not adjacent to tracker 1)
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close enough spatially
    factory_->tic_factory.y_mm = 8.0f;  // Past decision line
    factory_->tracker_id = 3;           // NOT adjacent to tracker 1
  }
  auto traj3 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj1->id(), traj1},
          {traj3->id(), traj3},
      };

  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  // Should find no duplicates since trackers are not adjacent
  ASSERT_EQ(dup_ids.size(), 0);

  // Both should become unique since they passed decision line
  ASSERT_TRUE(traj1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj3->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests classic deduplication with trajectories that are too far apart spatially.
// Verifies no duplicates are found due to distance constraints and both become unique.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Classic_NoMatchWhenTooFarApart) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=5.0f
  decision_line_->line_mm.store(5.0f);

  // Create first trajectory
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj1 = factory_->create_trajectory();

  // Create second trajectory too far away
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 200.0f; // Too far apart
    factory_->tic_factory.y_mm = 8.0f;   // Past decision line
    factory_->tracker_id = 2;            // Adjacent tracker
  }
  auto traj2 = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map{
          {traj1->id(), traj1},
          {traj2->id(), traj2},
      };

  // Use small radius so trajectories are too far apart
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(50, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map, deduplication_parameters, dup_ids);

  // Should find no duplicates since they're too far apart
  ASSERT_EQ(dup_ids.size(), 0);

  // Both should become unique since they passed decision line
  ASSERT_TRUE(traj1->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
  ASSERT_TRUE(traj2->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);
}

// Tests classic deduplication with a complex mix of trajectory types and statuses.
// Verifies correct handling of unique, unset, and below-decision-line trajectories in one scenario.
// This test uses two iterations: first to establish unique status, second to test matching.
TEST_F(DeduplicationAlgorithmsIntegrationTest, Comprehensive_MixedScenarioWithNewFunctionality) {
  CarbonClock &clock = CarbonClock::instance();
  clock.set_manual_control(true);

  // Set decision line at y=8.0f
  decision_line_->line_mm.store(8.0f);

  // FIRST ITERATION: Create a trajectory that will become unique
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->set_tic_location_from_px(std::make_pair(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 4));
    factory_->tic_factory.size_mm = 3.0f;
    factory_->tic_factory.score = 0.9f;
    factory_->tic_factory.weed_score = 0.9f;
    factory_->tic_factory.crop_score = 0.1f;
    factory_->tic_factory.plant_score = 0.9f;
    factory_->tic_factory.x_mm = 10.0f;
    factory_->tic_factory.y_mm = 15.0f; // Past decision line
    factory_->tic_factory.has_perspective = true;
    factory_->tracker_id = 1;
    factory_->tic_factory.detection_classes = {
        {"BROADLEAF", 0.9f}, {"GRASS", 0.1f}, {"OFFSHOOT", 0.3f}, {"PURSLANE", 0.4f}};
  }
  auto traj_unique = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_first{
          {traj_unique->id(), traj_unique},
      };

  // Run first iteration to establish unique status
  carbon::deduplicator::DeduplicationParameters deduplication_parameters(100, 5, 100);
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_first;
  carbon::deduplicator::DeduplicationAlgorithms deduplication_algorithms;
  deduplication_algorithms.deduplicate_classic(trajectory_map_first, deduplication_parameters, dup_ids_first);

  // Verify first iteration results: no duplicates found, trajectory becomes unique
  ASSERT_EQ(dup_ids_first.size(), 0);
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnique);

  // SECOND ITERATION: Create additional trajectories to test complex matching scenarios

  // Create an unset trajectory that will match with unique
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 12.0f; // Close to unique
    factory_->tic_factory.y_mm = 12.0f; // Past decision line
    factory_->tracker_id = 2;           // Adjacent tracker
  }
  auto traj_unset_match = factory_->create_trajectory();

  // Create two unset trajectories that will match each other
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 50.0f;
    factory_->tic_factory.y_mm = 10.0f; // Past decision line
    factory_->tracker_id = 3;
  }
  auto traj_unset1 = factory_->create_trajectory();

  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 52.0f; // Close to traj_unset1
    factory_->tic_factory.y_mm = 11.0f; // Past decision line
    factory_->tracker_id = 4;           // Adjacent to tracker 3
  }
  auto traj_unset2 = factory_->create_trajectory();

  // Create a trajectory before decision line (should remain unset)
  {
    factory_->tic_factory.timestamp_ms = clock.timestamp_ms();
    factory_->tic_factory.x_mm = 100.0f;
    factory_->tic_factory.y_mm = 5.0f; // Before decision line
    factory_->tracker_id = 5;
  }
  auto traj_below_line = factory_->create_trajectory();

  std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
      trajectory_map_second{
          {traj_unique->id(), traj_unique},         {traj_unset_match->id(), traj_unset_match},
          {traj_unset1->id(), traj_unset1},         {traj_unset2->id(), traj_unset2},
          {traj_below_line->id(), traj_below_line},
      };

  // Run second iteration to test complex matching scenarios
  std::vector<carbon::deduplicator::DuplicateTraj> dup_ids_second;
  deduplication_algorithms.deduplicate_classic(trajectory_map_second, deduplication_parameters, dup_ids_second);

  // Should find 2 duplicate pairs
  ASSERT_EQ(dup_ids_second.size(), 2);

  // Verify the unique trajectory became primary when matched with unset
  ASSERT_TRUE(traj_unique->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(traj_unset_match->duplicate_status() == carbon::trajectory::DuplicateStatus::kDuplicate);

  // Verify the two unset trajectories formed a duplicate pair
  bool unset1_is_primary = (traj_unset1->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  bool unset2_is_primary = (traj_unset2->duplicate_status() == carbon::trajectory::DuplicateStatus::kPrimary);
  ASSERT_TRUE(unset1_is_primary != unset2_is_primary); // Exactly one should be primary

  // Trajectory before decision line should remain unset
  ASSERT_TRUE(traj_below_line->duplicate_status() == carbon::trajectory::DuplicateStatus::kUnset);
}

} // namespace carbon