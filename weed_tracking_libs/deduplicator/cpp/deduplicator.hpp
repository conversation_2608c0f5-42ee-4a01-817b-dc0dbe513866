#pragma once
#include <ingest/cpp/ingest_client.hpp>

#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <lib/common/cpp/utils/thread_safe_moving_average.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <weed_tracking_libs/deduplicator/cpp/deduplication_algorithms.hpp>

#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

namespace carbon::deduplicator {

class DeDupImp {
public:
  using CfgCallback = std::function<void(void)>;
  struct Config {
    std::atomic<bool> enabled;
    float radius_sqrd;
    uint32_t delta_time_ms;
    std::string algorithm;
    float match_radius_sqrd;
    double inclusion_tolerance;
    bool use_latest_trajectory_timestamp;
    Config(std::shared_ptr<config::ConfigTree> tree);
    void update(std::shared_ptr<config::ConfigTree> tree);
  };
  DeDupImp(const std::string &name);
  ~DeDupImp();
  using Callback = std::function<void(const std::vector<DuplicateTraj> &duplicates)>;
  void register_callback(Callback cb, uint32_t id);
  void unregister_callback(uint32_t id);
  void request(const ingest::Request<> &req);
  float work_loop_latency_ms() { return work_loop_latency_ms_.avg(); }
  bool enabled() { return cfg_.enabled; }
  void wake() { cv_.notify_all(); }

private:
  std::mutex mut_;
  std::condition_variable cv_;
  std::string name_;
  config::AtomicFlagConfigScopedCallback scoped_tree_;
  config::ConfigScopedCallback scoped_tree_cb_;
  Config cfg_;
  common::ThreadSafeQueue<ingest::Request<>> new_requests_;
  std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> trajectories_;
  std::unordered_map<uint32_t, Callback> callbacks_;
  common::ThreadSafeMovingAverage<float> work_loop_latency_ms_;
  DeduplicationAlgorithms deduplication_algorithms_;

  std::thread thread_;

  void work_loop();
  void dedup();
};
class DeDuplicator : public ingest::IngestClient<> {
public:
  virtual void trajectory_request(const ingest::Request<> &req) override;
  uint32_t register_callback(DeDupImp::Callback cb);
  void unregister_callback(uint32_t id);
  static std::shared_ptr<DeDuplicator> get();
  float work_loop_latency_ms() { return plants_.work_loop_latency_ms(); }
  bool enabled() { return plants_.enabled(); }

private:
  DeDuplicator();
  DeDupImp plants_;
  uint32_t next_id_;
};

} // namespace carbon::deduplicator