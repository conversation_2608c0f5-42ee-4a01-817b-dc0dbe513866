#pragma once

#include <lib/common/cpp/utils/thread_pool.hpp>
#include <nanoflann.hpp>
#include <trajectory/cpp/trajectory.hpp>
#include <unordered_map>
#include <weed_tracking/cpp/deduplication/deduplication.h>

std::string_view constexpr kClassic = "classic";
std::string_view constexpr kRansac = "ransac";

namespace carbon {
namespace deduplicator {
struct key_hash_function {
  template <class T1, class T2>
  size_t operator()(const std::pair<T1, T2> &p) const {
    return std::hash<T1>{}(p.first) ^ (std::hash<T2>{}(p.second) << 4);
  }
};

struct filterOpts {
  bool only_use_predict_space;
  double inclusion_tolerance;
  bool use_latest_trajectory_timestamp;
};

void populate_id_positions(
    const std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories,
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> &items,
    std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>> &excluded,
    filterOpts &opts);

class KDTreeAdaptor
    : public carbon::kd_tree::KDTreeAdaptorBase<carbon::trajectory::Trajectory::ID,
                                                carbon::trajectory::TrackedItemPosition, 2, nanoflann::metric_L2> {
public:
  typedef typename nanoflann::metric_L2::template traits<float, KDTreeAdaptor>::distance_t Metric_t;
  typedef nanoflann::KDTreeSingleIndexAdaptor<Metric_t, KDTreeAdaptor, 2> Index_t;

  KDTreeAdaptor(
      const std::vector<std::pair<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition>>
          &positions,
      const size_t leaf_max_size = 10)
      : KDTreeAdaptorBase<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition, 2,
                          nanoflann::metric_L2>(positions, leaf_max_size) {}

  inline size_t query_radius(size_t idx, float radius,
                             std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> &out_points) const {
    std::array<float, 2> query_point = {items_[idx].second.get_x(), items_[idx].second.get_y()};
    std::vector<std::pair<size_t, float>> tmp_out;
    size_t count = index_->radiusSearch(query_point.data(), radius, tmp_out, nanoflann::SearchParams());
    for (const auto &p : tmp_out) {
      out_points.emplace_back(items_[p.first].first, p.second); // convert to trajectory id and distance;
    }
    return count;
  }

  inline size_t query_radius(float x, float y, float radius,
                             std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> &out_points) const {
    std::array<float, 2> query_point = {x, y};
    std::vector<std::pair<size_t, float>> tmp_out;
    size_t count = index_->radiusSearch(query_point.data(), radius, tmp_out, nanoflann::SearchParams());
    for (const auto &p : tmp_out) {
      out_points.emplace_back(items_[p.first].first, p.second);
    }
    return count;
  }

  inline size_t query_nn(float x, float y, size_t k,
                         std::vector<std::pair<carbon::trajectory::Trajectory::ID, float>> &out_points) const {
    std::array<float, 2> query_point = {x, y};
    // preallocate output vectors of size k
    std::vector<size_t> nearest_index(k, 0);
    std::vector<float> dists(k, 0.0f);
    size_t count = index_->knnSearch(query_point.data(), k, nearest_index.data(), dists.data());
    for (size_t i = 0; i < count; i++) {
      out_points.emplace_back(items_[nearest_index[i]].first, dists[i]);
    }
    return count;
  }

  inline carbon::trajectory::Trajectory::ID get_id_for_index(size_t index) const { return items_[index].first; }

  inline carbon::trajectory::TrackedItemPosition get_position_for_index(size_t index) const {
    return items_[index].second;
  }
};

struct DuplicateTraj {
  trajectory::Trajectory::ID primary;
  trajectory::Trajectory::ID duplicate;
  DuplicateTraj(trajectory::Trajectory::ID _primary, trajectory::Trajectory::ID _duplicate)
      : primary(_primary), duplicate(_duplicate) {}
};

struct DuplicateTrajPtr {
  std::shared_ptr<trajectory::Trajectory> primary;
  std::shared_ptr<trajectory::Trajectory> duplicate;
  DuplicateTrajPtr(std::shared_ptr<trajectory::Trajectory> _primary, std::shared_ptr<trajectory::Trajectory> _duplicate)
      : primary(_primary), duplicate(_duplicate) {}
};

struct DeduplicationParameters {
  DeduplicationParameters(float _radius_sqrd, float _match_radius_sqrd, double _inclusion_tolerance = 100,
                          bool _use_latest_trajectory_timestamp = false)
      : radius_sqrd(_radius_sqrd), match_radius_sqrd(_match_radius_sqrd), inclusion_tolerance(_inclusion_tolerance),
        use_latest_trajectory_timestamp(_use_latest_trajectory_timestamp) {}
  float radius_sqrd;
  float match_radius_sqrd;
  double inclusion_tolerance;
  bool use_latest_trajectory_timestamp;
};

class DeduplicationAlgorithms {
public:
  DeduplicationAlgorithms();
  DeduplicationAlgorithms(const DeduplicationAlgorithms &) = delete;
  DeduplicationAlgorithms &operator=(const DeduplicationAlgorithms &) = delete;
  void deduplicate_classic(
      std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> &trajectories,
      DeduplicationParameters deduplication_parameters, std::vector<DuplicateTraj> &dup_ids);
  void deduplicate_ransac(
      std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> &trajectories,
      DeduplicationParameters deduplication_parameters, std::vector<DuplicateTraj> &dup_ids);

protected:
  void ransac(KDTreeAdaptor &tree_left, KDTreeAdaptor &tree_right, DeduplicationParameters dedup_params,
              std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
                  &trajectories,
              std::unordered_map<carbon::trajectory::Trajectory::ID, carbon::trajectory::TrackedItemPosition> &pos_map,
              std::vector<DuplicateTrajPtr> &dups, std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques);
  float get_error(KDTreeAdaptor &tree_left, KDTreeAdaptor &tree_right, float shift_x, float shift_y);
  std::vector<DuplicateTraj>
  assign_via_shifts(std::unordered_map<trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>,
                    KDTreeAdaptor &tree_left, KDTreeAdaptor &tree_right, float shift_x, float shift_y,
                    float deduplication_radius);

  inline common::ThreadPool::JobID add_job(common::ThreadPool::Func func) { return pool_.add_job(func); }
  inline bool await_job(common::ThreadPool::JobID id) { return pool_.await_job(id); }

private:
  common::ThreadPool pool_;
};

void assign_duplicates(std::unordered_map<carbon::trajectory::Trajectory::ID,
                                          std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                       std::vector<DuplicateTrajPtr> &dups, std::vector<DuplicateTraj> &dup_ids, bool remove = false);
void add_dup(std::shared_ptr<trajectory::Trajectory> trajectory_a, std::shared_ptr<trajectory::Trajectory> trajectory_b,
             std::vector<DuplicateTrajPtr> &dups);

void assign_uniques(std::unordered_map<carbon::trajectory::Trajectory::ID,
                                       std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                    std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques);

template <typename T>
void add_passed_decision_line(const std::unordered_map<carbon::trajectory::Trajectory::ID,
                                                       std::shared_ptr<carbon::trajectory::Trajectory>> &trajectories,
                              std::unordered_set<carbon::trajectory::Trajectory::ID> &uniques, T &iterable_id_pos);

std::optional<int64_t> get_latest_timestamp(
    const std::unordered_map<carbon::trajectory::Trajectory::ID, std::shared_ptr<carbon::trajectory::Trajectory>>
        &trajectories);

} // namespace deduplicator
} // namespace carbon