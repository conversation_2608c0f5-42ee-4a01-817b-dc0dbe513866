#include <weed_tracking_libs/deduplicator/cpp/deduplication_algorithms.hpp>
#include <weed_tracking_libs/deduplicator/cpp/deduplicator.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>

#include <lib/common/cpp/time.h>

#include <set>

#include <spdlog/spdlog.h>

namespace carbon::deduplicator {

DeDupImp::Config::Config(std::shared_ptr<config::ConfigTree> tree) { update(tree); }
void DeDupImp::Config::update(std::shared_ptr<config::ConfigTree> tree) {
  this->enabled = tree->get_node("enabled")->get_value<bool>();
  this->radius_sqrd = tree->get_node("radius")->get_value<float>();
  this->radius_sqrd = radius_sqrd * radius_sqrd;
  this->delta_time_ms = tree->get_node("delta_time_ms")->get_value<uint32_t>();
  this->algorithm = tree->get_node("algorithm")->get_value<std::string>();
  this->match_radius_sqrd = tree->get_node("match_radius")->get_value<float>();
  this->match_radius_sqrd = match_radius_sqrd * match_radius_sqrd;
  this->inclusion_tolerance = tree->get_node("inclusion_tolerance")->get_value<double>();
  this->use_latest_trajectory_timestamp = tree->get_node("use_latest_trajectory_timestamp")->get_value<bool>();
}

DeDupImp::DeDupImp(const std::string &name)
    : name_(name), scoped_tree_(carbon::config::get_global_config_subscriber()->get_config_node(
                       "common", fmt::format("cross_cam_deduplication/{}", name))),
      scoped_tree_cb_(scoped_tree_.tree(), std::bind(&DeDupImp::wake, this)), cfg_(scoped_tree_.tree()),
      work_loop_latency_ms_(100), thread_(&DeDupImp::work_loop, this) {}
void DeDupImp::register_callback(Callback cb, uint32_t id) {
  const std::unique_lock lock(mut_);
  callbacks_.emplace(id, cb);
}
void DeDupImp::unregister_callback(uint32_t id) {
  const std::unique_lock lock(mut_);
  callbacks_.erase(id);
}

void DeDupImp::request(const ingest::Request<> &req) {
  if (!cfg_.enabled) {
    return;
  }
  new_requests_.add(req);
}
DeDupImp::~DeDupImp() {
  try {
    if (thread_.joinable()) {
      thread_.join();
    }
  } catch (const std::exception &e) {
    spdlog::error("DeDupImp::~DeDupImp: {}", e.what());
  }
}

void DeDupImp::work_loop() {
  bool first_pass = true;

  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    auto start = std::chrono::steady_clock::now();
    if (scoped_tree_.reload_required()) {
      cfg_.update(scoped_tree_.tree());
    }
    if (cfg_.enabled) {
      first_pass = true;
      for (const auto &req : new_requests_.pop_all()) {
        if (req.type == ingest::INGEST_CLIENT_ADD) {
          if (!req.traj->is_plant_captcha_only()) {
            trajectories_.emplace(req.traj->id(), req.traj);
          }
        } else {
          trajectories_.erase(req.traj->id());
        }
      }
      dedup();

      auto delta_time_ms =
          std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::steady_clock::now() - start).count();
      work_loop_latency_ms_.add(delta_time_ms);
      if (delta_time_ms < cfg_.delta_time_ms) {
        std::this_thread::sleep_for(std::chrono::milliseconds(cfg_.delta_time_ms - delta_time_ms));
      } else {
        spdlog::warn("Cross predict de-duplication logic is too slow!");
      }
    } else {
      {
        std::unique_lock<std::mutex> lk(mut_);
        cv_.wait_for(lk, std::chrono::seconds(5));
      }
      if (first_pass) {
        // Only need to do this the first time as we should only have added to requests when enabled.
        new_requests_.clear();
        first_pass = false;
      }
    }
  }
}

void DeDupImp::dedup() {
  spdlog::info("Running deduplication");
  std::vector<DuplicateTraj> dup_ids;

  DeduplicationParameters deduplication_parameters(cfg_.radius_sqrd, cfg_.match_radius_sqrd, cfg_.inclusion_tolerance,
                                                   cfg_.use_latest_trajectory_timestamp);
  if (cfg_.algorithm == kClassic) {
    deduplication_algorithms_.deduplicate_classic(trajectories_, deduplication_parameters, dup_ids);
  } else if (cfg_.algorithm == kRansac) {
    deduplication_algorithms_.deduplicate_ransac(trajectories_, deduplication_parameters, dup_ids);
  } else {
    throw maka_error("DeduplicationAlgorithm not set");
  }
  {
    const std::unique_lock lock(mut_);
    if (dup_ids.size() == 0) {
      return;
    }
    for (auto &cb : callbacks_) {
      cb.second(dup_ids);
    }
  }
  spdlog::info("Finished deduplication");
}

void DeDuplicator::trajectory_request(const ingest::Request<> &req) { plants_.request(req); }
uint32_t DeDuplicator::register_callback(DeDupImp::Callback cb) {
  uint32_t id = ++next_id_;
  plants_.register_callback(cb, id);
  return id;
}
void DeDuplicator::unregister_callback(uint32_t id) { plants_.unregister_callback(id); }

DeDuplicator::DeDuplicator() : plants_("plant"), next_id_(0) {}

std::shared_ptr<DeDuplicator> DeDuplicator::get() {
  static std::shared_ptr<DeDuplicator> de_duplicator{new DeDuplicator()};
  return de_duplicator;
}
} // namespace carbon::deduplicator