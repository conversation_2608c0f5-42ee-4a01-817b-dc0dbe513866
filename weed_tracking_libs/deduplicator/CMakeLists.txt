add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(deduplicator SHARED ${SOURCES})
target_compile_definitions(deduplicator PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(deduplicator PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib trajectory utils ingest bot_stop decision_line)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(deduplicator_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(deduplicator_python PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(deduplicator_python PUBLIC deduplicator)
set_target_properties(deduplicator_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)

add_executable(deduplication_algorithms_test test/deduplication_algorithms_test.cpp)
target_link_libraries(deduplication_algorithms_test m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib gtest_main deduplicator weed_tracking_test_utils nanopb wheel_encoder decision_line)
add_test(NAME deduplication_algorithms_test COMMAND deduplication_algorithms_test)