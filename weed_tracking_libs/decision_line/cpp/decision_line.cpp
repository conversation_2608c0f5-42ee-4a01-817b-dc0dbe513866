#include "weed_tracking_libs/decision_line/cpp/decision_line.hpp"

#include <optional>
#include <tuple>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_atomic_accessor.hpp>
#include <lib/common/cpp/exceptions.h>
#include <lib/common/cpp/utils/unit_conversion.hpp>
#include <scanner/cpp/scanner_wrapper.h>
#include <targeting/cpp/targeting_mode_watcher.hpp>
#include <trajectory/cpp/tracked_item_position.hpp>

namespace carbon::decision_line {

constexpr float kHalfPredictSpaceAtNominalHeight = common::in_to_mm(7.5f);

DecisionLine *DecisionLine::get() { return DecisionLineOwner::get().get_decision_line(); }
void DecisionLine::set(std::shared_ptr<DecisionLine> decision_line) { DecisionLineOwner::get().set(decision_line); }

MMSpaceDecisionLine::MMSpaceDecisionLine(float middle_of_predict_space_mm)
    : weeding_line_offset_mm_(carbon::config::get_global_config_subscriber()->get_config_node(
                                  "common", "decision_line/weeding_scanner_offset_in"),
                              &common::in_to_mm<float>),
      thinning_line_offset_mm_(carbon::config::get_global_config_subscriber()->get_config_node(
                                   "common", "decision_line/thinning_scanner_offset_in"),
                               &common::in_to_mm<float>),
      use_middle_of_predict_space_(carbon::config::get_global_config_subscriber()->get_config_node(
          "common", "decision_line/use_middle_of_predict_space")),
      scanners_(scanner::ScannerWrapperOwner::get().get_scanners()),
      middle_of_predict_space_mm_(middle_of_predict_space_mm) {}

std::optional<std::tuple<float, float>>
MMSpaceDecisionLine::get_decision_and_targeting_line(const double &height_mm) const {
  if (use_middle_of_predict_space_.get_value()) {
    return std::make_tuple(middle_of_predict_space_mm_, middle_of_predict_space_mm_ + kHalfPredictSpaceAtNominalHeight);
  }
  // get the average top of target space
  float target_space_top = 0.0f;
  uint32_t count = 0;
  // use all scanners, not just viable or enabled ones, we want to make decisions even if the scanners are not enabled
  for (auto &[scanner_id, scanner] : scanners_) {
    int pan_center = scanner->pan_middle();
    auto [min_tilt, max_tilt] = scanner->tilt_limits();
    auto pos_3d = scanner->geo_scanner()->get_abs_position_from_servo_with_z(
        std::make_tuple<int32_t, int32_t>(std::forward<int32_t>(pan_center), std::forward<int32_t>(max_tilt)),
        height_mm);
    target_space_top += static_cast<float>(std::get<1>(pos_3d));
    ++count;
  }
  if (count == 0) {
    return std::nullopt;
  }
  target_space_top /= static_cast<float>(count);
  auto line = target_space_top;
  if (targeting::TargetingModeWatcher::get().thinning_enabled()) {
    line -= std::max(weeding_line_offset_mm_.get_value(), thinning_line_offset_mm_.get_value());
  } else { // default to weeding even if its not enabled
    line -= weeding_line_offset_mm_.get_value();
  }
  return std::make_tuple(line, target_space_top);
}

bool MMSpaceDecisionLine::trajectory_passed_line(const trajectory::TrackedItemPosition &pos) const {
  auto opt_tuple = get_decision_and_targeting_line(pos.get_height_mm());
  if (!opt_tuple) {
    return false;
  }
  auto [line, target_space_top] = opt_tuple.value();
  return pos.get_y() >= line;
}

std::optional<std::tuple<float, float>>
MockDecisionLine::get_decision_and_targeting_line(const double &height_mm) const {
  (void)height_mm;
  return std::make_tuple(line_mm.load(), 0.0f);
}
bool MockDecisionLine::trajectory_passed_line(const trajectory::TrackedItemPosition &pos) const {
  return pos.get_y() >= line_mm;
}

} // namespace carbon::decision_line