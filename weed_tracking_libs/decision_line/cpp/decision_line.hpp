#pragma once

#include <memory>
#include <mutex>
#include <optional>
#include <tuple>

#include <config/tree/cpp/config_atomic_accessor.hpp>

namespace carbon {

namespace trajectory {
class TrackedItemPosition;
class Trajectory;
} // namespace trajectory

namespace scanner {
class ScannerWrapper;
} // namespace scanner

namespace decision_line {

class DecisionLine {
public:
  static DecisionLine *get();
  static void set(std::shared_ptr<DecisionLine> decision_line);

  virtual std::optional<std::tuple<float, float>> get_decision_and_targeting_line(const double &height_mm) const = 0;
  virtual bool trajectory_passed_line(const trajectory::TrackedItemPosition &pos) const = 0;
};

class MMSpaceDecisionLine : public DecisionLine {
public:
  MMSpaceDecisionLine(float middle_of_predict_space_mm);

  virtual std::optional<std::tuple<float, float>> get_decision_and_targeting_line(const double &height_mm) const;
  virtual bool trajectory_passed_line(const trajectory::TrackedItemPosition &pos) const;

private:
  config::ConfigAtomicAccessor<float> weeding_line_offset_mm_;
  config::ConfigAtomicAccessor<float> thinning_line_offset_mm_;
  config::ConfigAtomicAccessor<bool> use_middle_of_predict_space_;
  std::unordered_map<uint32_t, std::shared_ptr<scanner::ScannerWrapper>> scanners_;
  float middle_of_predict_space_mm_;
};

class MockDecisionLine : public DecisionLine {
public:
  std::atomic<float> line_mm;

  MockDecisionLine() : line_mm(0.0f) {}

  virtual std::optional<std::tuple<float, float>> get_decision_and_targeting_line(const double &height_mm) const;
  virtual bool trajectory_passed_line(const trajectory::TrackedItemPosition &pos) const;
};

class DecisionLineOwner {
public:
  static DecisionLineOwner &get() {
    static DecisionLineOwner instance;
    return instance;
  }
  DecisionLine *get_decision_line() {
    const std::lock_guard<std::mutex> lock(mut_);
    if (!decision_line_) {
      throw std::runtime_error("Decision line has not been set yet.");
    }
    return decision_line_.get();
  }
  void set(std::shared_ptr<DecisionLine> decision_line) {
    const std::lock_guard<std::mutex> lock(mut_);
    if (decision_line_) {
      throw std::runtime_error("Decision line has already been set.");
    }
    decision_line_ = decision_line;
  }

private:
  std::mutex mut_;
  std::shared_ptr<DecisionLine> decision_line_;
  DecisionLineOwner() = default;
  DecisionLineOwner(const DecisionLineOwner &) = delete;
  DecisionLineOwner &operator=(const DecisionLineOwner &) = delete;
  DecisionLineOwner(DecisionLineOwner &&) = delete;
  DecisionLineOwner &operator=(DecisionLineOwner &&) = delete;
  ~DecisionLineOwner() = default;
};

} // namespace decision_line
} // namespace carbon