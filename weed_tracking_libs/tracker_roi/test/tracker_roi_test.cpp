#include "weed_tracking_libs/test/test_utils.hpp"
#include "weed_tracking_libs/tracker_roi/cpp/tracker_roi.hpp"
#include "gtest/gtest.h"

#include <memory>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <generated/cv/runtime/proto/cv_runtime.pb.h>
#include <trajectory/cpp/tracked_item_centroid.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon {
namespace tracker_roi {
// For unit testing individual functions
class TestTrackerRoiAccessor : public TrackerRoiAccessor {
public:
  using TrackerRoiAccessor::_reload;
  using TrackerRoiAccessor::roi_settings_;
  using TrackerRoiAccessor::TrackerRoiAccessor;
};
} // namespace tracker_roi

class TrackerROITest : public ::testing::Test {
public:
  static std::shared_ptr<config::ConfigSubscriber> config_subscriber_;
  static std::shared_ptr<trajectory::TestingTrajectoryFactory> factory_;

  static void SetUpTestSuite() {
    if (config_subscriber_ == nullptr) {
      config_subscriber_ = config::get_global_config_subscriber();
      config_subscriber_->add_config_tree("aimbot", "aimbot", "services/aimbot.yaml");
    }
    if (factory_ == nullptr) {
      factory_ = std::make_shared<trajectory::TestingTrajectoryFactory>();
    }
  }

  static void TearDownTestSuite() {
    // Clean up any global state if needed
  }

protected:
  void SetUp() override {
    // Clear any existing ROI config before each test
    clearROIConfig();
  }

  void TearDown() override {
    // Clean up after each test
    clearROIConfig();
  }

  // Helper method to create a TrackedItemCentroid for testing
  trajectory::TrackedItemCentroid createTestCentroid(float x, float y) {
    factory_->tic_factory.x = x;
    factory_->tic_factory.y = y;
    factory_->tic_factory.timestamp_ms = 1000; // supress warnings
    return factory_->get_test_coord();
  }

  // Helper method to set ROI config for a specific tracker
  void setROIConfig(uint32_t tracker_id, int64_t offset_x, int64_t offset_y, int64_t width, int64_t height) {
    auto roi_node = config_subscriber_->get_config_node("aimbot", "weed_tracking/roi");

    // Create a new ROI config node
    std::string roi_key = fmt::format("roi_{}", tracker_id);
    auto roi_config = roi_node->get_node(roi_key);
    if (!roi_config) {
      // If the node doesn't exist, we need to create it
      roi_node->add_child(std::make_shared<config::ConfigTree>(roi_key, roi_node->get_def()->get_child("item")));
      roi_config = roi_node->get_node(roi_key);
    }
    roi_config->get_node("tracker_id")->set_value<uint64_t>(tracker_id, 0, false);
    roi_config->get_node("offset_x")->set_value<int64_t>(offset_x, 0, false);
    roi_config->get_node("offset_y")->set_value<int64_t>(offset_y, 0, false);
    roi_config->get_node("width")->set_value<int64_t>(width, 0, false);
    roi_config->get_node("height")->set_value<int64_t>(height, 0, false);
  }

  // Helper method to clear ROI config
  void clearROIConfig() {
    auto roi_node = config_subscriber_->get_config_node("aimbot", "weed_tracking/roi");
    // Clear all ROI configurations by removing child nodes
    auto children = roi_node->get_children_names();
    for (const auto &child_name : children) {
      roi_node->remove_child(child_name);
    }
  }

  // Helper method to remove specific ROI config
  void removeROIConfig(uint32_t tracker_id) {
    auto roi_node = config_subscriber_->get_config_node("aimbot", "weed_tracking/roi");
    std::string roi_key = "roi_" + std::to_string(tracker_id);
    roi_node->remove_child(roi_key);
  }
};

std::shared_ptr<config::ConfigSubscriber> TrackerROITest::config_subscriber_ = nullptr;
std::shared_ptr<trajectory::TestingTrajectoryFactory> TrackerROITest::factory_ = nullptr;

// Unit Tests

// Test the basics of the ROISetting class
TEST_F(TrackerROITest, Unit_RoiSetting) {
  // Test basic construction and accessors
  tracker_roi::ROISetting roi(10, 20, 30, 40);
  EXPECT_EQ(roi.get_start_x(), 10);
  EXPECT_EQ(roi.get_start_y(), 20);
  EXPECT_EQ(roi.get_end_x(), 30);
  EXPECT_EQ(roi.get_end_y(), 40);

  // Test in_bounds method
  auto centroid_inside = createTestCentroid(25.0f, 25.0f);
  auto centroid_outside = createTestCentroid(50.0f, 50.0f);

  EXPECT_TRUE(roi.in_bounds(centroid_inside));
  EXPECT_FALSE(roi.in_bounds(centroid_outside));
}

// Test the TrackerRoiAccessor class
TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_get_roi_setting_id) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Add ROI config for tracker id 1
  roi_accessor.roi_settings_.emplace(1, tracker_roi::ROISetting(10, 20, 30, 40));

  // Test get_roi_setting method
  auto roi = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi.has_value());
  EXPECT_EQ(roi->get_start_x(), 10);
  EXPECT_EQ(roi->get_start_y(), 20);
  EXPECT_EQ(roi->get_end_x(), 30);
  EXPECT_EQ(roi->get_end_y(), 40);
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_get_roi_setting_predict_name) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Add ROI config for tracker id 1
  roi_accessor.roi_settings_.emplace(1, tracker_roi::ROISetting(10, 20, 30, 40));

  // Test get_roi_setting method
  auto roi = roi_accessor.get_roi_setting("predict1");
  EXPECT_TRUE(roi.has_value());
  EXPECT_EQ(roi->get_start_x(), 10);
  EXPECT_EQ(roi->get_start_y(), 20);
  EXPECT_EQ(roi->get_end_x(), 30);
  EXPECT_EQ(roi->get_end_y(), 40);
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_get_roi_setting_predict_name_invalid) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Add ROI config for tracker id 1
  roi_accessor.roi_settings_.emplace(1, tracker_roi::ROISetting(10, 20, 30, 40));

  // Test get_roi_setting method
  auto roi = roi_accessor.get_roi_setting("predict_invalid1");
  EXPECT_FALSE(roi.has_value());

  roi = roi_accessor.get_roi_setting("invalid_predict1");
  EXPECT_FALSE(roi.has_value());

  roi = roi_accessor.get_roi_setting("nonsense");
  EXPECT_FALSE(roi.has_value());
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_in_bounds) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Add ROI config for tracker id 1
  roi_accessor.roi_settings_.emplace(1, tracker_roi::ROISetting(10, 20, 30, 40));

  // Test in_bounds method
  auto centroid_inside = createTestCentroid(25.0f, 25.0f);
  auto centroid_outside = createTestCentroid(50.0f, 50.0f);

  EXPECT_TRUE(roi_accessor.in_bounds(1, centroid_inside));
  EXPECT_FALSE(roi_accessor.in_bounds(1, centroid_outside));
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_in_bounds_no_roi) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Test in_bounds method when no ROI is defined
  auto centroid = createTestCentroid(25.0f, 25.0f);
  EXPECT_TRUE(roi_accessor.in_bounds(1, centroid));
  EXPECT_TRUE(roi_accessor.in_bounds(2, centroid));
  EXPECT_TRUE(roi_accessor.in_bounds(3, centroid));
  EXPECT_TRUE(roi_accessor.in_bounds(4, centroid));
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_in_bounds_matches) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Add ROI config for tracker id 1
  roi_accessor.roi_settings_.emplace(1, tracker_roi::ROISetting(10, 20, 30, 40));

  // Test in_bounds method with wrong tracker id
  auto centroid = createTestCentroid(25.0f, 25.0f);
  EXPECT_TRUE(roi_accessor.in_bounds(1, centroid));
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_TRUE(roi_setting->in_bounds(centroid));
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_reload) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  auto roi_node = config_subscriber_->get_config_node("aimbot", "weed_tracking/roi");
  auto roi_config_tree = std::make_shared<config::ConfigTree>("roi_1", roi_node->get_def()->get_child("item"));
  roi_config_tree->get_node("tracker_id")->set_value<uint64_t>(1, 0, false);
  roi_config_tree->get_node("offset_x")->set_value<int64_t>(10, 0, false);
  roi_config_tree->get_node("offset_y")->set_value<int64_t>(20, 0, false);
  roi_config_tree->get_node("width")->set_value<int64_t>(20, 0, false);
  roi_config_tree->get_node("height")->set_value<int64_t>(20, 0, false);

  roi_accessor._reload(roi_config_tree);
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 30);
  EXPECT_EQ(roi_setting->get_end_y(), 40);
}

TEST_F(TrackerROITest, Unit_TrackerRoiAccessor_reload_null) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // Test reload with null config
  roi_accessor._reload(nullptr);
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 0);
}

// Integration Tests

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_basic) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_multiple) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  setROIConfig(2, 30, 40, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_multiple_with_predict_name) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  setROIConfig(2, 30, 40, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  auto roi_setting = roi_accessor.get_roi_setting("predict1");
  EXPECT_TRUE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting("predict2");
  EXPECT_TRUE(roi_setting.has_value());
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_multiple_with_in_bounds) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  setROIConfig(2, 30, 40, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  auto centroid_inside = createTestCentroid(25.0f, 25.0f);
  auto centroid_outside = createTestCentroid(50.0f, 50.0f);
  EXPECT_TRUE(roi_accessor.in_bounds(1, centroid_inside));
  EXPECT_FALSE(roi_accessor.in_bounds(1, centroid_outside));
  EXPECT_TRUE(roi_accessor.in_bounds(2, centroid_outside));
  EXPECT_FALSE(roi_accessor.in_bounds(2, centroid_inside));
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_add_modify_remove) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 30);
  EXPECT_EQ(roi_setting->get_end_y(), 40);

  // modify the config
  setROIConfig(1, 10, 20, 30, 40);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 40);
  EXPECT_EQ(roi_setting->get_end_y(), 60);

  // remove the config
  removeROIConfig(1);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 0);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_FALSE(roi_setting.has_value());
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_add_modify_remove_multiple) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  setROIConfig(2, 30, 40, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());

  // modify the config
  setROIConfig(1, 10, 20, 30, 40);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 40);
  EXPECT_EQ(roi_setting->get_end_y(), 60);
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 30);
  EXPECT_EQ(roi_setting->get_start_y(), 40);
  EXPECT_EQ(roi_setting->get_end_x(), 50);
  EXPECT_EQ(roi_setting->get_end_y(), 60);

  // remove the config
  removeROIConfig(1);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_FALSE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_add_modify_remove_one_changes) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  setROIConfig(2, 30, 40, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());

  // modify the config
  setROIConfig(1, 10, 20, 30, 40);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 2);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 40);
  EXPECT_EQ(roi_setting->get_end_y(), 60);
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 30);
  EXPECT_EQ(roi_setting->get_start_y(), 40);
  EXPECT_EQ(roi_setting->get_end_x(), 50);
  EXPECT_EQ(roi_setting->get_end_y(), 60);

  // remove the config
  removeROIConfig(1);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_FALSE(roi_setting.has_value());
  roi_setting = roi_accessor.get_roi_setting(2);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 30);
  EXPECT_EQ(roi_setting->get_start_y(), 40);
  EXPECT_EQ(roi_setting->get_end_x(), 50);
  EXPECT_EQ(roi_setting->get_end_y(), 60);
}

TEST_F(TrackerROITest, Integration_TrackerRoiAccessor_reload_add_modify_remove_add_remove) {
  // Test basic construction
  tracker_roi::TestTrackerRoiAccessor roi_accessor;

  // build a config tree
  setROIConfig(1, 10, 20, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  auto roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());

  // modify the config
  setROIConfig(1, 10, 20, 30, 40);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 40);
  EXPECT_EQ(roi_setting->get_end_y(), 60);

  // remove the config
  removeROIConfig(1);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 0);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_FALSE(roi_setting.has_value());

  // add the config back
  setROIConfig(1, 10, 20, 20, 20);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 1);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_TRUE(roi_setting.has_value());
  EXPECT_EQ(roi_setting->get_start_x(), 10);
  EXPECT_EQ(roi_setting->get_start_y(), 20);
  EXPECT_EQ(roi_setting->get_end_x(), 30);
  EXPECT_EQ(roi_setting->get_end_y(), 40);

  // remove the config again
  removeROIConfig(1);
  roi_accessor.reload();
  EXPECT_EQ(roi_accessor.roi_settings_.size(), 0);
  roi_setting = roi_accessor.get_roi_setting(1);
  EXPECT_FALSE(roi_setting.has_value());
}

} // namespace carbon