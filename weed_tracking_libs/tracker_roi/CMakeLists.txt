add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(tracker_roi SHARED ${SOURCES})
target_compile_definitions(tracker_roi PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(tracker_roi PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib trajectory)

add_executable(tracker_roi_test test/tracker_roi_test.cpp)
target_link_directories(tracker_roi_test PUBLIC /usr/local/lib /usr/local/cuda/lib64/ /opt/hpcx/ompi/lib/)
target_link_libraries(tracker_roi_test PUBLIC tracker_roi config_client_lib config_tree_lib gtest_main trajectory cv_runtime_proto fmt weed_tracking_test_utils)
add_test(NAME tracker_roi_test COMMAND tracker_roi_test)