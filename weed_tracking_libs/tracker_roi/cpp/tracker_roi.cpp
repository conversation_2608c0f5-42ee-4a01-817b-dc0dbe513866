#include "weed_tracking_libs/tracker_roi/cpp/tracker_roi.hpp"

#include <optional>
#include <shared_mutex>
#include <unordered_map>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <trajectory/cpp/tracked_item_centroid.hpp>

#include <fmt/format.h>
#include <spdlog/spdlog.h>

namespace carbon::tracker_roi {
constexpr std::string_view predict_name_prefix("predict");

bool ROISetting::in_bounds(const trajectory::TrackedItemCentroid &centroid) const {
  int pos_x = static_cast<int>(centroid.get_x());
  int pos_y = static_cast<int>(centroid.get_y());

  return pos_x >= start_x_ && pos_x <= end_x_ && pos_y >= start_y_ && pos_y <= end_y_;
}

TrackerRoiAccessor::TrackerRoiAccessor()
    : scoped_tree_(carbon::config::get_global_config_subscriber()->get_config_node("aimbot", "weed_tracking/roi"),
                   std::bind(&TrackerRoiAccessor::reload, this)) {
  reload();
}

std::optional<ROISetting> TrackerRoiAccessor::get_roi_setting(uint32_t tracker_id) const {
  std::shared_lock<std::shared_mutex> lock(mut_);
  if (roi_settings_.count(tracker_id) > 0) {
    return roi_settings_.at(tracker_id);
  }
  return std::nullopt;
}

std::optional<ROISetting> TrackerRoiAccessor::get_roi_setting(const std::string &predict_name) const {
  std::shared_lock<std::shared_mutex> lock(mut_);
  if (predict_name.find(predict_name_prefix) != 0) {
    return std::nullopt;
  }
  try {
    auto id = std::stoi(predict_name.substr(predict_name_prefix.length()));
    return get_roi_setting(id);
  } catch (const std::exception &ex) {
    return std::nullopt;
  }
}

bool TrackerRoiAccessor::in_bounds(uint32_t tracker_id, const trajectory::TrackedItemCentroid &centroid) const {
  std::shared_lock<std::shared_mutex> lock(mut_);
  if (roi_settings_.count(tracker_id) > 0) {
    return roi_settings_.at(tracker_id).in_bounds(centroid);
  }
  return true; // No cfg defined is no ROI so all centroids are in bounds
}

void TrackerRoiAccessor::reload() {
  std::unique_lock<std::shared_mutex> lock(mut_);
  roi_settings_.clear(); // clear out old settings, important if a node was removed
  for (auto roiCfg : scoped_tree_->get_children_nodes()) {
    _reload(roiCfg);
  }
}

void TrackerRoiAccessor::_reload(std::shared_ptr<config::ConfigTree> tree) {
  if (!tree) {
    spdlog::error("Roi config is null");
    return;
  }
  auto start_x = tree->get_node("offset_x")->get_value<int>();
  auto start_y = tree->get_node("offset_y")->get_value<int>();
  auto end_x = tree->get_node("width")->get_value<int>() + start_x;
  auto end_y = tree->get_node("height")->get_value<int>() + start_y;
  auto tracker_id = tree->get_node("tracker_id")->get_value<uint32_t>();
  roi_settings_[tracker_id] = ROISetting{start_x, start_y, end_x, end_y};
  spdlog::debug("ROI for tracker {} defined as ({}, {}) -> ({}, {})", tracker_id, start_x, start_y, end_x, end_y);
}

} // namespace carbon::tracker_roi
