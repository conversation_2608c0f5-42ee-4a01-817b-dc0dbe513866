#pragma once

#include <optional>
#include <shared_mutex>
#include <unordered_map>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>
#include <trajectory/cpp/tracked_item_centroid.hpp>

namespace carbon::tracker_roi {
// Represents a single ROI setting
// Meant to be lock free access, should be passed by value
class ROISetting {
public:
  ROISetting(int start_x, int start_y, int end_x, int end_y)
      : start_x_(start_x), start_y_(start_y), end_x_(end_x), end_y_(end_y) {}
  ROISetting() = default;
  ROISetting(const ROISetting &other) = default;

  int get_start_x() const { return start_x_; }
  int get_start_y() const { return start_y_; }
  int get_end_x() const { return end_x_; }
  int get_end_y() const { return end_y_; }
  bool in_bounds(const trajectory::TrackedItemCentroid &centroid) const;

private:
  int start_x_;
  int start_y_;
  int end_x_;
  int end_y_;
};

class TrackerRoiAccessor {
public:
  TrackerRoiAccessor();
  std::optional<ROISetting> get_roi_setting(uint32_t tracker_id) const;
  // predict_name is of the form "predict<id>"
  std::optional<ROISetting> get_roi_setting(const std::string &predict_name) const;
  bool in_bounds(uint32_t tracker_id, const trajectory::TrackedItemCentroid &centroid) const;
  void reload();

protected:
  // tracker_id -> ROISetting
  std::unordered_map<uint32_t, ROISetting> roi_settings_;
  // adds the roi setting for the given tree node to the roi_settings_ map
  void _reload(std::shared_ptr<config::ConfigTree> tree);

private:
  mutable std::shared_mutex mut_;
  // weed_tracking/roi list node
  carbon::config::ConfigScopedCallback scoped_tree_;
};

} // namespace carbon::tracker_roi