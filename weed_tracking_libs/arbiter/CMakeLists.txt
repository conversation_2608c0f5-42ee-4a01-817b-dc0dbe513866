add_compile_options(-fvisibility=default)

file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)

add_library(arbiter SHARED ${SOURCES})
target_compile_definitions(arbiter PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
target_link_libraries(arbiter PUBLIC m stdc++fs pthread rt exceptions fmt utils config_tree_lib trajectory almanac decision_line interest_scores deduplicator)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(arbiter_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(arbiter_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
target_link_libraries(arbiter_python PUBLIC arbiter)
set_target_properties(arbiter_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)

add_executable(decision_algorithms_test test/decision_algorithm_test.cpp)
target_link_libraries(decision_algorithms_test PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib gtest_main arbiter wheel_encoder nanopb weed_tracking_test_utils)
add_test(NAME decision_algorithms_test COMMAND decision_algorithms_test)

add_executable(arbiter_test test/arbiter_test.cpp)
target_link_libraries(arbiter_test PUBLIC m stdc++fs pthread rt exceptions fmt config_tree_lib config_client_lib gtest_main arbiter wheel_encoder nanopb geometric weed_tracking_test_utils deduplicator)
add_test(NAME arbiter_test COMMAND arbiter_test)