#include "weed_tracking_libs/arbiter/cpp/arbiter.hpp"

#include <boost/unordered/concurrent_flat_map.hpp>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <lib/common/cpp/exceptions.h>
#include <lib/common/cpp/time.h>
#include <lib/common/cpp/utils/thread_pool_batch.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>

#include <trajectory/cpp/decisions.hpp>
#include <trajectory/cpp/enums.hpp>
#include <trajectory/cpp/trajectory.hpp>

#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_trigger.hpp>

#include <weed_tracking_libs/arbiter/cpp/decision_algorithms.hpp>
#include <weed_tracking_libs/arbiter/cpp/decision_algorithms_exceptions.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <config/tree/cpp/config_tree.hpp>

#include <lib/common/bot/cpp/stop_handler/stop_handler.hpp>
#include <targeting/cpp/targeting_mode_watcher.hpp>

#include <weed_tracking_libs/decision_line/cpp/decision_line.hpp>

#include <weed_tracking_libs/deduplicator/cpp/deduplicator.hpp>

#include <chrono>

#include <spdlog/spdlog.h>

constexpr size_t starting_ht_size = 256;
constexpr size_t num_worker_threads =
    5; // TODO(jfroel): profile and adjust. Rn this is just the number of trackers on slayer/max on reaper
constexpr int monitor_interval_seconds = 5;

namespace carbon::arbiter {

Arbiter::Arbiter(const std::vector<std::shared_ptr<ArbiterTrigger>> &triggers,
                 const std::vector<std::shared_ptr<ArbiterClient<>>> &clients, const bool monitor)
    : next_id_(0), config_(config::get_global_config_subscriber()->get_config_node("aimbot", "arbiter"),
                           config::get_global_config_subscriber()->get_config_node("common", "")),
      clients_(clients), unprocessed_(starting_ht_size), processed_(starting_ht_size), monitor_(monitor),
      total_num_processed_(0), total_num_failed_(0), job_latency_us_(100), thread_pool_(num_worker_threads) {
  for (auto trigger : triggers) {
    trigger->set_position_update_cb(std::bind(&Arbiter::position_update, this, std::placeholders::_1));
  }

  if (monitor) {
    monitor_thread_ = std::thread(&Arbiter::monitor_jobs, this);
  }
}

Arbiter::~Arbiter() {
  if (monitor_thread_.joinable()) {
    monitor_thread_.join();
  }
}

ARBITER_CLIENT_ID Arbiter::add_client(std::shared_ptr<ArbiterClient<>> client) {
  ARBITER_CLIENT_ID id = ++next_id_;
  dynamic_clients_.try_emplace(id, client);
  return id;
}

void Arbiter::remove_client(ARBITER_CLIENT_ID id) { dynamic_clients_.erase(id); }

void Arbiter::trajectory_request(const ingest::Request<> &req) {
  if (req.type == ingest::INGEST_CLIENT_ADD) {
    unprocessed_.try_emplace(req.traj->id(), req.traj);
  } else { // INGEST_CLIENT_REMOVE
    remove_trajectory(req.traj);
  }
}

void Arbiter::position_update(std::vector<trajectory::Trajectory::ID> &ids_in) {
  if (ids_in.empty()) {
    return;
  }
  // at some point I would like to profile and see if 1 big job for all trajectories is faster than 1 job per trajectory
  thread_pool_.add([this, ids = std::move(ids_in)]() {
    auto start = std::chrono::steady_clock::now();
    int64_t now = CarbonClock::instance().timestamp_ms();
    for (auto id : ids) {
      std::shared_ptr<trajectory::Trajectory> traj;
      bool found = this->unprocessed_.visit(id, [&](const auto &kv) { traj = kv.second; });
      if (found) {
        // if x-cam deduplication is enabled, arbitrate trajectories that have had their status set.
        // otherwise, arbitrate after the trajectories have passed the decision line.
        if (deduplicator::DeDuplicator::get()->enabled()) {
          if (traj->duplicate_status() == trajectory::DuplicateStatus::kUnset) {
            continue;
          } else if (traj->duplicate_status() == trajectory::DuplicateStatus::kDuplicate) {
            this->unprocessed_.erase(id);
            continue;
          }
        } else {
          if (!decision_line::DecisionLine::get()->trajectory_passed_line(
                  traj->get_estimated_position_mm_at_timestamp(now))) {
            continue;
          }
        }

        // DO NOT THROW EXCEPTIONS IN HERE. https://stackoverflow.com/a/15311812
        std::call_once(traj->get_classification_once_flag(), [&]() {
          try {
            this->arbitrate(traj);
            this->unify_based_on_targeting_mode(traj);
            this->broadcast(traj, ARBITER_CLIENT_ADD);
            this->unprocessed_.erase(id);
            this->processed_.try_emplace(id, traj);
            if (monitor_) {
              this->total_num_processed_++;
            }
          } catch (const std::exception &e) {
            spdlog::error("Arbiter: Error arbitrating trajectory {}: {}", traj->id(), e.what());
          }
        });
      }
    }
    auto end = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    job_latency_us_.add(duration.count());
  });
}

void Arbiter::arbitrate(std::shared_ptr<trajectory::Trajectory> traj) {
  auto strategy = config_.algorithm();
  if (strategy == kModelinatorAlgorithm) {
    decision_algorithms_.calculate_decision_flags_using_modelinator(traj, config_.combine_duplicate_information());
  } else if (strategy == kModelinatorWithHighestScoreAlgorithm) {
    decision_algorithms_.calculate_decision_flags_using_modelinator_with_highest_score(
        traj, config_.combine_duplicate_information());
  } else if (strategy == kEmbeddingsAlgorithm) {
    decision_algorithms_.calculate_decision_flags_using_embeddings(
        traj, config_.algorithm_parameters().embedding_parameters, config_.combine_duplicate_information());
  } else if (strategy == kEmbeddingsWithFallbackAlgorithm) {
    decision_algorithms_.calculate_decision_flags_using_embeddings_with_fallback(
        traj, config_.algorithm_parameters().embedding_fallback_parameters, config_.combine_duplicate_information());
  } else {
    throw maka_error("Arbitration strategy not set");
  }
}

void Arbiter::unify_based_on_targeting_mode(std::shared_ptr<trajectory::Trajectory> traj) {
  std::unique_lock lock(traj->mut_);

  bool weeding_enabled = traj->decisions_[trajectory::DecisionFlag::kWeedingWeed] &&
                         targeting::TargetingModeWatcher::get().weeding_enabled();
  bool thinning_enabled = traj->decisions_[trajectory::DecisionFlag::kThinningCrop] &&
                          targeting::TargetingModeWatcher::get().thinning_enabled();
  traj->kill_time_ =
      std::max(weeding_enabled ? traj->weed_kill_time_ : 0, thinning_enabled ? traj->crop_kill_time_ : 0);
  traj->discriminator_data_.avoid = (weeding_enabled ? traj->weed_discriminator_data_.avoid : false) ||
                                    (thinning_enabled ? traj->crop_discriminator_data_.avoid : false);
  traj->discriminator_data_.ignorable = (weeding_enabled ? traj->weed_discriminator_data_.ignorable : false) ||
                                        (thinning_enabled ? traj->crop_discriminator_data_.ignorable : false);
}

void Arbiter::broadcast(std::shared_ptr<trajectory::Trajectory> traj, RequestType type) {
  auto req = Request(traj, type);
  for (auto &client : clients_) {
    client->trajectory_request(req);
  }
  dynamic_clients_.visit_all([&](const auto &kv) { kv.second->trajectory_request(req); });
}

void Arbiter::remove_trajectory(std::shared_ptr<trajectory::Trajectory> traj) {
  thread_pool_.add([this, traj]() {
    auto id = traj->id();
    if (this->unprocessed_.erase(id)) {
      spdlog::warn("Arbiter: Removed trajectory {} before it was processed", id);
      if (monitor_) {
        this->total_num_failed_++;
      }
    }
    this->processed_.erase(id);
    this->broadcast(traj, ARBITER_CLIENT_REMOVE);
  });
}

void Arbiter::await_jobs() { thread_pool_.await(); }

void Arbiter::monitor_jobs() {
  while (!lib::common::bot::BotStopHandler::get().is_stopped()) {
    auto num_unprocessed = unprocessed_.size();
    auto num_processed = processed_.size();
    auto in_progress = thread_pool_.in_progress();
    auto job_latency = get_job_latency_us(); // TODO(jfroel), make gauge

    spdlog::info("Arbiter: Current: {} unprocessed trajectories, {} processed trajectories, {} in progress jobs, "
                 "{:.3f}us avg job latency. "
                 "Totals: {} succeed, {} failed",
                 num_unprocessed, num_processed, in_progress, job_latency, total_num_processed_, total_num_failed_);

    std::this_thread::sleep_for(std::chrono::seconds(monitor_interval_seconds));
  }
}

} // namespace carbon::arbiter