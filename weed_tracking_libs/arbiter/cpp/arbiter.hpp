#pragma once

#include <boost/unordered/concurrent_flat_map.hpp>
#include <shared_mutex>
#include <thread>

#include <config/tree/cpp/config_scoped_callback.hpp>
#include <ingest/cpp/ingest_client.hpp>
#include <lib/common/cpp/utils/thread_pool_background.hpp>
#include <lib/common/cpp/utils/thread_safe_moving_average.hpp>
#include <lib/common/cpp/utils/thread_safe_queue.hpp>
#include <trajectory/cpp/trajectory.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_trigger.hpp>
#include <weed_tracking_libs/arbiter/cpp/decision_algorithms.hpp>

// aka Judge Shooty
namespace carbon::arbiter {

class Arbiter : public ingest::IngestClient<> {
public:
  Arbiter(const std::vector<std::shared_ptr<ArbiterTrigger>> &triggers,
          const std::vector<std::shared_ptr<ArbiterClient<>>> &clients, const bool monitor = false);
  ~Arbiter();

  void trajectory_request(const ingest::Request<> &req); // override;

  ARBITER_CLIENT_ID add_client(std::shared_ptr<ArbiterClient<>> client);
  void remove_client(ARBITER_CLIENT_ID id);
  double get_job_latency_us() const { return job_latency_us_.avg(); }

protected:
  void position_update(std::vector<trajectory::Trajectory::ID> &ids);
  /**
   * Responsible for calculating and setting the decision flags for the given trajectory.
   * @param traj The trajectory to arbitrate
   * @throws maka_error if the calculation or setting of the decision flags fails.
   * @throws maka_error if the strategy is not set by config.
   */
  void arbitrate(std::shared_ptr<trajectory::Trajectory> traj);
  void unify_based_on_targeting_mode(std::shared_ptr<trajectory::Trajectory> traj);
  void broadcast(std::shared_ptr<trajectory::Trajectory> traj, RequestType type);
  void remove_trajectory(std::shared_ptr<trajectory::Trajectory> traj);
  void await_jobs();
  void monitor_jobs();

private:
  class Config {
  public:
    Config(std::shared_ptr<config::ConfigTree> tree, std::shared_ptr<config::ConfigTree> common_tree)
        : scoped_tree_(tree, std::bind(&arbiter::Arbiter::Config::reload, this), false),
          scoped_embeddings_ff_node_(common_tree->get_node("feature_flags/embedding_based_classification_feature"),
                                     std::bind(&arbiter::Arbiter::Config::reload, this), false) {
      reload();
    }

    void reload() {
      spdlog::info("Arbiter: Reloading Arbiter config");
      std::unique_lock lock(mutex_);
      if (scoped_embeddings_ff_node_->get_value<bool>()) {
        algorithm_ = scoped_tree_->get_node("algorithm")->get_value<std::string>();
      } else {
        algorithm_ = scoped_tree_->get_node("non_embedding_algorithm")->get_value<std::string>();
      }
      algorithm_parameters_ = AlgorithmParameters(scoped_tree_->get_node("algorithm_parameters"));
      combine_duplicate_information_ = scoped_tree_->get_node("combine_duplicate_information")->get_value<bool>();
      spdlog::info("Arbiter: Arbitration algorithm set to: {}", algorithm_);
    }

    std::string algorithm() const {
      std::shared_lock lock(mutex_);
      return algorithm_;
    }
    AlgorithmParameters algorithm_parameters() const {
      std::shared_lock lock(mutex_);
      return algorithm_parameters_;
    }
    bool combine_duplicate_information() const {
      std::shared_lock lock(mutex_);
      return combine_duplicate_information_;
    }

  private:
    config::ConfigScopedCallback scoped_tree_;
    config::ConfigScopedCallback scoped_embeddings_ff_node_;
    mutable std::shared_mutex mutex_;
    std::string algorithm_;
    AlgorithmParameters algorithm_parameters_;
    bool combine_duplicate_information_;
  };
  std::atomic<ARBITER_CLIENT_ID> next_id_;
  Config config_;
  DecisionAlgorithms decision_algorithms_;
  std::vector<std::shared_ptr<ArbiterClient<>>> clients_;
  boost::concurrent_flat_map<ARBITER_CLIENT_ID, std::shared_ptr<ArbiterClient<>>> dynamic_clients_;
  boost::concurrent_flat_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> unprocessed_;
  boost::concurrent_flat_map<trajectory::Trajectory::ID, std::shared_ptr<trajectory::Trajectory>> processed_;

  // observability
  bool monitor_;
  std::atomic<uint64_t> total_num_processed_;
  std::atomic<uint64_t> total_num_failed_;
  common::ThreadSafeMovingAverage<float> job_latency_us_;
  std::thread monitor_thread_; // keep at bottom but above thread_pool_

  common::ThreadPoolBackground thread_pool_; // keep at bottom
};

} // namespace carbon::arbiter