#pragma once

#include <almanac/cpp/modelinator.hpp>
#include <config/tree/cpp/config_scoped_callback.hpp>
#include <trajectory/cpp/decisions.hpp>

std::string_view constexpr kModelinatorAlgorithm = "modelinator";
std::string_view constexpr kModelinatorWithHighestScoreAlgorithm = "highest_score";
std::string_view constexpr kEmbeddingsAlgorithm = "embedding_classification";
std::string_view constexpr kEmbeddingsWithFallbackAlgorithm = "embedding_with_fallback_classification";

namespace carbon {

namespace trajectory {
class Trajectory;
} // namespace trajectory

namespace arbiter {

struct ScoreCounts {
  float weeding_weed_detections = 0;
  float weeding_crop_detections = 0;
  float thinning_weed_detections = 0;
  float thinning_crop_detections = 0;
  float keepable_crop_detections = 0;
  float banding_crop_detections = 0;
  float detection_count = 0;
  float denom = 0; // This needs to be broken into pieces
  float total_weed_score = 0;
  float total_crop_score = 0;
};

struct EmbeddingFallbackParameters {
  std::string fallback_algorithm;
  float ambiguity_threshold;
  float strength_threshold;
  float plant_mindoo;

  EmbeddingFallbackParameters(std::shared_ptr<config::ConfigTree> parameter_node);
  EmbeddingFallbackParameters();
};

struct EmbeddingParameters {
  float plant_mindoo;

  EmbeddingParameters(std::shared_ptr<config::ConfigTree> parameter_node);
  EmbeddingParameters();
};

struct AlgorithmParameters {
  EmbeddingFallbackParameters embedding_fallback_parameters;
  EmbeddingParameters embedding_parameters;

  AlgorithmParameters(std::shared_ptr<config::ConfigTree> algorithm_parameter_node);
  AlgorithmParameters();
};

class DecisionAlgorithms {
public:
  DecisionAlgorithms();
  DecisionAlgorithms(const DecisionAlgorithms &) = delete;
  DecisionAlgorithms &operator=(const DecisionAlgorithms &) = delete;

  /**
   * @brief Algorithms are responsible for setting all of the following members of a trajectory:
   *
   *  decisions_;
   *  weed_kill_time_;
   *  crop_kill_time_;
   *  weed_discriminator_data_;
   *  crop_discriminator_data_;
   *  detections_over_opportunities_;
   */

  void calculate_decision_flags_using_modelinator(std::shared_ptr<trajectory::Trajectory> traj,
                                                  bool combine_duplicate_information);
  void calculate_decision_flags_using_modelinator_with_highest_score(std::shared_ptr<trajectory::Trajectory> traj,
                                                                     bool combine_duplicate_information);
  void calculate_decision_flags_using_embeddings(std::shared_ptr<trajectory::Trajectory> traj,
                                                 EmbeddingParameters embedding_parameters,
                                                 bool combine_duplicate_information);
  void
  calculate_decision_flags_using_embeddings_with_fallback(std::shared_ptr<trajectory::Trajectory> traj,
                                                          EmbeddingFallbackParameters embedding_fallback_parameters,
                                                          bool combine_duplicate_information);

protected:
  trajectory::Decisions _calculate_decision_flags_using_modelinator(std::shared_ptr<trajectory::Trajectory> traj,
                                                                    almanac::Modelinator::Data &weed_modelinator_data,
                                                                    almanac::Modelinator::Data &crop_modelinator_data,
                                                                    bool combine_duplicate_information);
  trajectory::Decisions _calculate_decision_flags_using_modelinator_with_highest_score(
      std::shared_ptr<trajectory::Trajectory> traj, almanac::Modelinator::Data &weed_modelinator_data,
      almanac::Modelinator::Data &crop_modelinator_data, bool combine_duplicate_information);

  std::tuple<trajectory::Decisions, std::string, std::string, bool, bool>
  _calculate_decision_flags_using_embeddings(std::shared_ptr<trajectory::Trajectory> traj, float plant_mindoo,
                                             bool combine_duplicate_information);

  void get_score_counts(std::shared_ptr<carbon::trajectory::Trajectory> traj, ScoreCounts &score_counts,
                        almanac::Modelinator::Data &weed_modelinator_data,
                        almanac::Modelinator::Data &crop_modelinator_data);
  void set_decision_flags(std::shared_ptr<carbon::trajectory::Trajectory> traj, const trajectory::Decisions &decisions,
                          std::string weed_class, std::string crop_class, float size_mm,
                          std::string decision_algorithm);
};

} // namespace arbiter
} // namespace carbon