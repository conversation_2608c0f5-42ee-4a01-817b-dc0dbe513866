#include "weed_tracking_libs/arbiter/cpp/arbiter.hpp"
#include "weed_tracking_libs/test/test_utils.hpp"

#include <gtest/gtest.h>

#include <weed_tracking_libs/arbiter/cpp/arbiter_client.hpp>
#include <weed_tracking_libs/arbiter/cpp/arbiter_trigger.hpp>

#include <weed_tracking_libs/decision_line/cpp/decision_line.hpp>

#include <trajectory/cpp/trajectory.hpp>

#include <lib/common/cpp/exceptions.h>
#include <lib/common/cpp/time.h>
#include <lib/common/geometric/cpp/geometric_cam.hpp>
#include <lib/common/geometric/cpp/geometric_height_estimator.hpp>
#include <lib/common/geometric/cpp/global_height_estimator_collection.hpp>

#include <config/client/cpp/config_subscriber.hpp>
#include <config/tree/cpp/config_tree.hpp>

#include <almanac/cpp/almanac.hpp>
#include <almanac/cpp/discriminator.hpp>
#include <almanac/cpp/modelinator.hpp>

#include <ingest/cpp/ingest_client.hpp>

#include <lib/drivers/nanopb/nofx_board/cpp/wheel_encoder.hpp>
#include <wheel_encoder/cpp/wheel_encoder.hpp>

#include <weed_tracking_libs/deduplicator/cpp/deduplicator.hpp>

#include <spdlog/spdlog.h>

namespace carbon {
namespace arbiter {
class test_Arbiter : public Arbiter {
public:
  test_Arbiter(const std::vector<std::shared_ptr<ArbiterTrigger>> &triggers,
               const std::vector<std::shared_ptr<ArbiterClient<>>> &clients)
      : Arbiter(triggers, clients) {}

  void position_update_wrapper(std::vector<trajectory::Trajectory::ID> ids) { position_update(ids); }

  void arbitrate_wrapper(std::shared_ptr<trajectory::Trajectory> traj) { arbitrate(traj); }

  void broadcast_wrapper(std::shared_ptr<trajectory::Trajectory> traj, RequestType type) { broadcast(traj, type); }

  // position update and ingest removes are async, so we need to wait for them to finish
  void await_jobs_wrapper() { await_jobs(); }
};
} // namespace arbiter

template <typename TrajectoryType = trajectory::Trajectory>
class test_ArbiterClient : public arbiter::ArbiterClient<TrajectoryType> {
public:
  std::atomic<int> broadcast_count = 0;
  std::unordered_map<typename TrajectoryType::ID, std::shared_ptr<TrajectoryType>> trajectories;
  void trajectory_request(const arbiter::Request<TrajectoryType> &req) override {
    ++broadcast_count;
    if (req.type == arbiter::ARBITER_CLIENT_ADD) {
      trajectories[req.trajectory->id()] = req.trajectory;
    } else {
      trajectories.erase(req.trajectory->id());
    }
  }
};

class ArbiterTest : public ::testing::Test {
protected:
  static std::shared_ptr<config::ConfigSubscriber> config_subscriber_;
  static std::shared_ptr<decision_line::MockDecisionLine> decision_line_;
  // keep a copy of the shared_ptr to avoid static constructor/destructor order fiasco
  static std::shared_ptr<deduplicator::DeDuplicator> deduplicator_;

  void set_config(const std::string &algorithm, const bool x_cam_dedup_on) {
    config_subscriber_->get_config_node("aimbot", "arbiter/algorithm")->set_value(algorithm, 0, false);
    config_subscriber_->get_config_node("common", "cross_cam_deduplication/plant/enabled")
        ->set_value(x_cam_dedup_on, 0, false);
  }

  static void SetUpTestSuite() {
    if (config_subscriber_ == nullptr) {
      config_subscriber_ = config::get_global_config_subscriber();
      config_subscriber_->add_config_tree("common", "common", "services/common.yaml");
      config_subscriber_->add_config_tree("aimbot", "aimbot", "services/aimbot.yaml");
    }
    CarbonClock::instance().set_manual_control(true);
    carbon::wheel_encoder::WheelEncoder::set(
        std::make_shared<carbon::nanopb::nofx_board::WheelEncoderNOFXSim>(false, 100, 100));
    if (decision_line_ == nullptr) {
      decision_line_ = std::make_shared<decision_line::MockDecisionLine>();
    }
    decision_line::DecisionLine::set(decision_line_);
    if (deduplicator_ == nullptr) {
      deduplicator_ = deduplicator::DeDuplicator::get();
    }
  }

  static void TearDownTestSuite() {
    try {
      lib::common::bot::BotStopHandler::get().stop();
      lib::common::bot::BotStopHandler::get().wait();
    } catch (const std::exception &ex) {
      spdlog::warn("Failed to stop bot: {}", ex.what());
    }
  }
};

std::shared_ptr<config::ConfigSubscriber> ArbiterTest::config_subscriber_ = nullptr;
std::shared_ptr<decision_line::MockDecisionLine> ArbiterTest::decision_line_ = nullptr;
std::shared_ptr<deduplicator::DeDuplicator> ArbiterTest::deduplicator_ = nullptr;

std::shared_ptr<arbiter::test_Arbiter>
get_test_arbiter(const std::vector<std::shared_ptr<arbiter::ArbiterTrigger>> &triggers,
                 const std::vector<std::shared_ptr<arbiter::ArbiterClient<>>> &clients) {
  return std::make_shared<arbiter::test_Arbiter>(triggers, clients);
}

TEST_F(ArbiterTest, SimpleBroadcast) {
  auto &clock = CarbonClock::instance();
  auto client = std::make_shared<test_ArbiterClient<>>();
  auto arbiter = get_test_arbiter({}, {client});

  trajectory::TestingTrajectoryFactory::get().tic_factory.timestamp_ms = clock.timestamp_ms();
  auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();

  arbiter->broadcast_wrapper(traj, arbiter::ARBITER_CLIENT_ADD);

  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_EQ(client->trajectories[1]->id(), 1);

  arbiter->broadcast_wrapper(traj, arbiter::ARBITER_CLIENT_REMOVE);
  ASSERT_EQ(client->trajectories.size(), 0);
}

TEST_F(ArbiterTest, DynamicClient) {
  auto &clock = CarbonClock::instance();
  auto static_client = std::make_shared<test_ArbiterClient<>>();
  auto dynamic_client = std::make_shared<test_ArbiterClient<>>();
  auto arbiter = get_test_arbiter({}, {static_client});

  trajectory::TestingTrajectoryFactory::get().tic_factory.timestamp_ms = clock.timestamp_ms();

  // broadcast 3 trajectories
  for (int i = 0; i < 3; i++) {
    auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();
    arbiter->broadcast_wrapper(traj, arbiter::ARBITER_CLIENT_ADD);
  }

  ASSERT_EQ(static_client->trajectories.size(), 3);

  // add the dynamic client
  arbiter::ARBITER_CLIENT_ID id = arbiter->add_client(dynamic_client);
  ASSERT_EQ(id, 1);

  // broadcast 3 more trajectories
  for (int i = 0; i < 3; i++) {
    auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();
    arbiter->broadcast_wrapper(traj, arbiter::ARBITER_CLIENT_ADD);
  }

  ASSERT_EQ(static_client->trajectories.size(), 6);
  ASSERT_EQ(dynamic_client->trajectories.size(), 3);

  arbiter->remove_client(id);

  // broadcast 3 more trajectories
  for (int i = 0; i < 3; i++) {
    auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();
    arbiter->broadcast_wrapper(traj, arbiter::ARBITER_CLIENT_ADD);
  }

  ASSERT_EQ(static_client->trajectories.size(), 9);
  ASSERT_EQ(dynamic_client->trajectories.size(), 3);
}

TEST_F(ArbiterTest, SimpleArbitration) {
  set_config("modelinator", false);
  auto &clock = CarbonClock::instance();
  auto arbiter = get_test_arbiter({}, {});

  trajectory::TestingTrajectoryFactory::get().tic_factory.timestamp_ms = clock.timestamp_ms();

  auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();
  ASSERT_FALSE(traj->decisions_set());

  ASSERT_NO_THROW(arbiter->arbitrate_wrapper(traj));

  ASSERT_TRUE(traj->decisions_set());
}

TEST_F(ArbiterTest, ArbitrationException) {
  set_config("modelinator", false);
  auto &clock = CarbonClock::instance();
  auto arbiter = get_test_arbiter({}, {});

  trajectory::TestingTrajectoryFactory::get().tic_factory.timestamp_ms = clock.timestamp_ms();
  trajectory::TestingTrajectoryFactory::get().tic_factory.has_perspective = false;
  auto traj = trajectory::TestingTrajectoryFactory::get().create_trajectory();
  ASSERT_FALSE(traj->decisions_set());

  ASSERT_THROW(arbiter->arbitrate_wrapper(traj), maka_error);
}

TEST_F(ArbiterTest, HappyPath) {
  set_config("modelinator", false);
  auto &clock = CarbonClock::instance();
  auto client = std::make_shared<test_ArbiterClient<>>();
  auto arbiter = get_test_arbiter({}, {client});

  // wait for dedup to disable because it's a separate thread
  while (deduplicator::DeDuplicator::get()->enabled()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }

  auto factory = trajectory::TestingTrajectoryFactory::get();
  {
    auto [abs_position_x, abs_position_y, abs_position_z] =
        factory.geo_cam->get_abs_position_from_undistorted_px(std::make_tuple(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 2));
    decision_line_->line_mm.store(static_cast<float>(abs_position_y));
  }
  {
    // move the trajectory above the decision line
    factory.tic_factory.timestamp_ms = clock.timestamp_ms();
    factory.tic_factory.x = IMAGE_WIDTH / 2;
    factory.tic_factory.y = IMAGE_HEIGHT / 4;
    auto [abs_position_x, abs_position_y, abs_position_z] = factory.geo_cam->get_abs_position_from_undistorted_px(
        std::make_tuple(factory.tic_factory.x, factory.tic_factory.y));
    factory.tic_factory.x_mm = abs_position_x;
    factory.tic_factory.y_mm = abs_position_y;
    factory.tic_factory.z_mm = abs_position_z;
    factory.tic_factory.has_perspective = true;
  }
  auto traj = factory.create_trajectory();
  traj->duplicate_status(trajectory::DuplicateStatus::kUnique);

  // ingest adds the trajectory to the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_ADD));

  // above the decision line, so no arbitration and no broadcast
  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();
  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_EQ(client->broadcast_count, 0);
  ASSERT_FALSE(traj->decisions_set());

  {
    // move the trajectory below the decision line
    clock += 1000;
    factory.tic_factory.timestamp_ms = clock.timestamp_ms();
    factory.tic_factory.y = 0.6 * IMAGE_HEIGHT;
    auto [abs_position_x, abs_position_y, abs_position_z] = factory.geo_cam->get_abs_position_from_undistorted_px(
        std::make_tuple(factory.tic_factory.x, factory.tic_factory.y));
    factory.tic_factory.x_mm = abs_position_x;
    factory.tic_factory.y_mm = abs_position_y;
    factory.tic_factory.z_mm = abs_position_z;
  }
  traj->append(factory.get_test_coord());

  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();

  // arbitration should have happened and the trajectory should have been broadcast
  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // update again, no change in position, should not broadcast
  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // ingest removes the trajectory from the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_REMOVE));
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 2);
}

TEST_F(ArbiterTest, DuplicateUpdates) {
  set_config("modelinator", false);
  auto &clock = CarbonClock::instance();
  auto client = std::make_shared<test_ArbiterClient<>>();
  auto arbiter = get_test_arbiter({}, {client});

  // wait for dedup to disable because it's a separate thread
  while (deduplicator::DeDuplicator::get()->enabled()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }

  auto factory = trajectory::TestingTrajectoryFactory::get();
  {
    auto [abs_position_x, abs_position_y, abs_position_z] =
        factory.geo_cam->get_abs_position_from_undistorted_px(std::make_tuple(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 2));
    decision_line_->line_mm.store(static_cast<float>(abs_position_y));
  }
  {
    // move the trajectory above the decision line
    factory.tic_factory.timestamp_ms = clock.timestamp_ms();
    factory.tic_factory.x = IMAGE_WIDTH / 2;
    factory.tic_factory.y = IMAGE_HEIGHT / 4;
    auto [abs_position_x, abs_position_y, abs_position_z] = factory.geo_cam->get_abs_position_from_undistorted_px(
        std::make_tuple(factory.tic_factory.x, factory.tic_factory.y));
    factory.tic_factory.x_mm = abs_position_x;
    factory.tic_factory.y_mm = abs_position_y;
    factory.tic_factory.z_mm = abs_position_z;
    factory.tic_factory.has_perspective = true;
  }
  auto traj = factory.create_trajectory();
  traj->duplicate_status(trajectory::DuplicateStatus::kUnique);

  // ingest adds the trajectory to the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_ADD));

  // above the decision line, so no arbitration and no broadcast
  for (int i = 0; i < 10; i++) {
    arbiter->position_update_wrapper({traj->id()});
  }
  arbiter->await_jobs_wrapper();
  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_EQ(client->broadcast_count, 0);
  ASSERT_FALSE(traj->decisions_set());

  {
    // move the trajectory below the decision line
    clock += 1000;
    factory.tic_factory.timestamp_ms = clock.timestamp_ms();
    factory.tic_factory.y = 0.6 * IMAGE_HEIGHT;
    auto [abs_position_x, abs_position_y, abs_position_z] = factory.geo_cam->get_abs_position_from_undistorted_px(
        std::make_tuple(factory.tic_factory.x, factory.tic_factory.y));
    factory.tic_factory.x_mm = abs_position_x;
    factory.tic_factory.y_mm = abs_position_y;
    factory.tic_factory.z_mm = abs_position_z;
  }
  traj->append(factory.get_test_coord());

  for (int i = 0; i < 10; i++) {
    arbiter->position_update_wrapper({traj->id()});
  }
  arbiter->await_jobs_wrapper();

  // arbitration should have happened and the trajectory should have been broadcast
  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // update again, no change in position, should not broadcast
  for (int i = 0; i < 10; i++) {
    arbiter->position_update_wrapper({traj->id()});
  }
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // ingest removes the trajectory from the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_REMOVE));
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 2);
}

TEST_F(ArbiterTest, HappyPath_XCamDedup) {
  set_config("modelinator", true);
  auto &clock = CarbonClock::instance();
  auto client = std::make_shared<test_ArbiterClient<>>();
  auto arbiter = get_test_arbiter({}, {client});

  // wait for dedup to enable because it's a separate thread
  while (!deduplicator::DeDuplicator::get()->enabled()) {
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }

  auto factory = trajectory::TestingTrajectoryFactory::get();
  {
    auto [abs_position_x, abs_position_y, abs_position_z] =
        factory.geo_cam->get_abs_position_from_undistorted_px(std::make_tuple(IMAGE_WIDTH / 2, IMAGE_HEIGHT / 2));
    decision_line_->line_mm.store(static_cast<float>(abs_position_y));
  }
  {
    // move the trajectory above the decision line
    factory.tic_factory.timestamp_ms = clock.timestamp_ms();
    factory.tic_factory.x = IMAGE_WIDTH / 2;
    factory.tic_factory.y = IMAGE_HEIGHT / 4;
    auto [abs_position_x, abs_position_y, abs_position_z] = factory.geo_cam->get_abs_position_from_undistorted_px(
        std::make_tuple(factory.tic_factory.x, factory.tic_factory.y));
    factory.tic_factory.x_mm = abs_position_x;
    factory.tic_factory.y_mm = abs_position_y;
    factory.tic_factory.z_mm = abs_position_z;
    factory.tic_factory.has_perspective = true;
  }
  auto traj = factory.create_trajectory();
  traj->duplicate_status(trajectory::DuplicateStatus::kUnset);

  // ingest adds the trajectory to the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_ADD));

  // status unset, so no arbitration and no broadcast
  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();
  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_EQ(client->broadcast_count, 0);
  ASSERT_FALSE(traj->decisions_set());

  // status unique, arbitrate and broadcast
  traj->duplicate_status(trajectory::DuplicateStatus::kUnique);

  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();

  // arbitration should have happened and the trajectory should have been broadcast
  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // update again, no change in position, should not broadcast
  arbiter->position_update_wrapper({traj->id()});
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 1);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 1);

  // ingest removes the trajectory from the arbiter
  arbiter->trajectory_request(ingest::Request<trajectory::Trajectory>(traj, ingest::INGEST_CLIENT_REMOVE));
  arbiter->await_jobs_wrapper();

  ASSERT_EQ(client->trajectories.size(), 0);
  ASSERT_TRUE(traj->decisions_set());
  ASSERT_EQ(client->broadcast_count, 2);
}

} // namespace carbon
