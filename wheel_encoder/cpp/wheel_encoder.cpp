#include "wheel_encoder/cpp/wheel_encoder.hpp"

#include <chrono>
#include <config/client/cpp/config_subscriber.hpp>
#include <lib/common/cpp/utils/generation.hpp>
#include <math.h>
#include <memory>
#include <mutex>
#include <stdexcept>

#include <spdlog/spdlog.h>

static constexpr float IN_TO_MM(float inch) { return inch * 25.4f; }
static constexpr float TREADKILL_WHEEL_DIAM_IN = 4.096f;
static constexpr float TREADKILL_WHEEL_DIAM_MM = IN_TO_MM(TREADKILL_WHEEL_DIAM_IN);

inline float ticks_to_mm(float ticks, float diameter_mm, uint32_t resolution) {
  return (ticks / static_cast<float>(resolution)) * static_cast<float>(M_PI) * diameter_mm;
}
inline float ticks_to_mm(int32_t ticks, float diameter_mm, uint32_t resolution) {
  return ticks_to_mm(static_cast<float>(ticks), diameter_mm, resolution);
}
namespace carbon {
namespace wheel_encoder {

class WheelEncoderOwner {
public:
  WheelEncoderOwner(const WheelEncoderOwner &we) = delete;
  WheelEncoderOwner &operator=(const WheelEncoderOwner &) = delete;
  static WheelEncoderOwner &get() {
    static WheelEncoderOwner weo;
    return weo;
  }
  void set(std::shared_ptr<WheelEncoder> we) {
    const std::unique_lock lock(mut_);
    if (encoder_) {
      spdlog::info("Ignoring call to set wheel encoder, as this has already been done.");
      return;
    }
    encoder_ = we;
  }
  std::shared_ptr<WheelEncoder> get_shared_encoder() {
    static bool run_once_hack = check_exists();
    (void)run_once_hack;
    return encoder_;
  }
  WheelEncoder *get_encoder() {
    // Trick to force this check to run only first time
    static bool run_once_hack = check_exists();
    (void)run_once_hack;
    return encoder_.get();
  }

private:
  std::mutex mut_;
  std::shared_ptr<WheelEncoder> encoder_;
  WheelEncoderOwner() : encoder_(nullptr) {}
  bool check_exists() {
    const std::unique_lock lock(this->mut_);
    if (!this->encoder_) {
      throw std::runtime_error("Encoder has not been set yet.");
    }
    return true;
  }
};

EnabledEncoders get_cfg_enabled_encoders(std::shared_ptr<config::ConfigTree> we_tree) {
  return EnabledEncoders(we_tree->get_node("front_left/enabled")->get_value<bool>(),
                         we_tree->get_node("front_right/enabled")->get_value<bool>(),
                         we_tree->get_node("back_left/enabled")->get_value<bool>(),
                         we_tree->get_node("back_right/enabled")->get_value<bool>());
}
WheelEncoder::Config::Config(std::shared_ptr<config::ConfigSubscriber> cfg)
    : use_treadkill(true), use_max_value(false) {
  if (cfg && cfg->started()) {
    use_treadkill = cfg->get_config_node("common", "use_treadkill")->get_value<bool>();
  }
  if (use_treadkill) {
    // Reaper only has two wheel encoder channels (front)
    if (common::is_reaper()) {
      enabled_fl = true;
      enabled_fr = false;
      enabled_bl = false;
      enabled_br = false;
    } else {
      enabled_fl = false;
      enabled_fr = false;
      enabled_bl = true;
      enabled_br = false;
    }
    diam_fl = TREADKILL_WHEEL_DIAM_MM;
    diam_fr = TREADKILL_WHEEL_DIAM_MM;
    diam_bl = TREADKILL_WHEEL_DIAM_MM;
    diam_br = TREADKILL_WHEEL_DIAM_MM;
  } else {
    auto we_tree = cfg->get_config_node("common", "wheel_encoders");
    auto tmp_enabled = get_cfg_enabled_encoders(we_tree);
    enabled_fl = tmp_enabled.front_left;
    enabled_fr = tmp_enabled.front_right;
    enabled_bl = tmp_enabled.back_left && !common::is_reaper();  // Reaper does not have back left encoder
    enabled_br = tmp_enabled.back_right && !common::is_reaper(); // Reaper does not have back right encoder
    diam_fl = IN_TO_MM(we_tree->get_node("front_left/diameter_in")->get_value<float>());
    diam_fr = IN_TO_MM(we_tree->get_node("front_right/diameter_in")->get_value<float>());
    diam_bl = IN_TO_MM(we_tree->get_node("back_left/diameter_in")->get_value<float>());
    diam_br = IN_TO_MM(we_tree->get_node("back_right/diameter_in")->get_value<float>());
    use_max_value = we_tree->get_node("use_max_value")->get_value<bool>();
  }
}

WheelEncoder::WheelEncoder(bool reverse_polarity, uint32_t rotary_resolution, uint32_t poll_interval_ms)
    : ready_(false), reverse_polarity_(reverse_polarity), rotary_resolution_(rotary_resolution),
      poll_inverval_ms_(poll_interval_ms), initialized_(false), total_travel_m_(0.0f), reload_required_(false),
      stopped_(false), accumulator_(std::bind(&WheelEncoder::get_next_pos, this, std::placeholders::_1)),
      config_(std::make_unique<Config>(config::get_global_config_subscriber())),
      poll_thread_(&WheelEncoder::poll_loop, this) {
  auto subscriber = config::get_global_config_subscriber();
  if (subscriber && subscriber->started()) {
    subscriber->get_config_node("common", "wheel_encoders")
        ->register_callback(std::bind(&WheelEncoder::reload_callback, this));
    subscriber->get_config_node("common", "use_treadkill")
        ->register_callback(std::bind(&WheelEncoder::reload_callback, this));
  }
}
WheelEncoder::~WheelEncoder() { stop(); }

void WheelEncoder::stop() {
  if (stopped_) {
    return;
  }
  stopped_ = true;
  cv_.notify_all();
  poll_thread_.join();
}

void WheelEncoder::set(std::shared_ptr<WheelEncoder> we) { WheelEncoderOwner::get().set(we); }
WheelEncoder *WheelEncoder::get() { return WheelEncoderOwner::get().get_encoder(); }
std::shared_ptr<WheelEncoder> WheelEncoder::get_shared() { return WheelEncoderOwner::get().get_shared_encoder(); }

void WheelEncoder::update(const EncoderData &latest) {
  if (!initialized_) {
    const std::unique_lock lock(mut_);
    cur_data_ = latest;
    initialized_ = true;
  } else {
    mut_.lock_shared();
    auto cur_usec = cur_data_.usec;
    auto delta_ticks_val = delta_ticks(cur_data_, latest);
    mut_.unlock_shared();
    float delta_t = (static_cast<float>(latest.usec - cur_usec) / 1000.0f);

    VelocityData tmp_vel_data(static_cast<float>(delta_ticks_val.front_left) / delta_t,
                              static_cast<float>(delta_ticks_val.front_right) / delta_t,
                              static_cast<float>(delta_ticks_val.back_left) / delta_t,
                              static_cast<float>(delta_ticks_val.back_right) / delta_t,
                              (cur_usec / 1000 + (uint64_t)std::round(delta_t / 2.0f)));

    float tmp_travel = _avg_dist(delta_ticks_val) / 1000.0f;
    {
      const std::unique_lock lock(mut_);
      cur_data_ = latest;
      cur_vel_data_ = tmp_vel_data;
      total_travel_m_ += tmp_travel;
    }
    cv_.notify_all();
  }
}
void WheelEncoder::poll_loop() {
  while (!ready_) {
    std::this_thread::sleep_for(std::chrono::milliseconds(poll_inverval_ms_));
  }
  _poll_loop();
}
EncoderData WheelEncoder::get_next(uint64_t timestamp_ms) const {
  std::shared_lock lock(mut_);
  while (!initialized_ || timestamp_ms >= (cur_data_.usec / 1000)) {
    cv_.wait(lock);
  }
  return cur_data_;
}
std::tuple<EncoderData, FilterData> WheelEncoder::get_next_and_filter(uint64_t timestamp_ms) {
  std::shared_lock lock(mut_);
  while (!initialized_ || timestamp_ms >= (cur_data_.usec / 1000)) {
    cv_.wait(lock);
  }
  FilterData filter_data(config_->enabled_fl, config_->enabled_fr, config_->enabled_bl, config_->enabled_br,
                         cur_data_.usec);
  return std::tuple<EncoderData, FilterData>(cur_data_, filter_data);
}
EncoderData WheelEncoder::get_cur() const {
  std::shared_lock lock(mut_);
  return cur_data_;
}
VelocityData WheelEncoder::get_next_vel(uint64_t timestamp_ms) const {
  std::shared_lock lock(mut_);
  while (!initialized_ || timestamp_ms >= cur_vel_data_.msec) {
    cv_.wait(lock);
  }
  return cur_vel_data_;
}
VelocityData WheelEncoder::get_cur_vel() const {
  std::shared_lock lock(mut_);
  return cur_vel_data_;
}

AvgVelocityData WheelEncoder::_max_vel(const VelocityData &data) const {
  std::vector<float> ticks{
      config_->enabled_fl ? data.front_left : 0.0f,
      config_->enabled_fr ? data.front_right : 0.0f,
      config_->enabled_bl ? data.back_left : 0.0f,
      config_->enabled_br ? data.back_right : 0.0f,
  };
  std::vector<float> vels{
      config_->enabled_fl ? ticks_to_mm(data.front_left, config_->diam_fl, rotary_resolution_) : 0.0f,
      config_->enabled_fr ? ticks_to_mm(data.front_right, config_->diam_fr, rotary_resolution_) : 0.0f,
      config_->enabled_bl ? ticks_to_mm(data.back_left, config_->diam_bl, rotary_resolution_) : 0.0f,
      config_->enabled_br ? ticks_to_mm(data.back_right, config_->diam_br, rotary_resolution_) : 0.0f,
  };
  float max_vel = 0.0f;
  float max_ticks = 0.0f;
  // compare ticks_to_mm values since they will all be positive (if diameter configs are set correctly)
  for (size_t i = 0; i < vels.size(); ++i) {
    if (vels[i] > max_vel) {
      max_ticks = ticks[i];
      max_vel = vels[i];
    }
  }
  return AvgVelocityData(max_ticks, max_vel, data.msec);
}

AvgVelocityData WheelEncoder::_avg_vel(const VelocityData &data) const {
  float vel = 0.0f;
  float vel_ticks = 0.0f;
  float count = 0.0f;
  if (config_->use_max_value) {
    return _max_vel(data);
  }
  if (config_->enabled_fl) {
    vel += ticks_to_mm(data.front_left, config_->diam_fl, rotary_resolution_);
    vel_ticks += data.front_left;
    count += 1.0f;
  }
  if (config_->enabled_fr) {
    vel += ticks_to_mm(data.front_right, config_->diam_fr, rotary_resolution_);
    vel_ticks += data.front_right;
    count += 1.0f;
  }
  if (config_->enabled_bl) {
    vel += ticks_to_mm(data.back_left, config_->diam_bl, rotary_resolution_);
    vel_ticks += data.back_left;
    count += 1.0f;
  }
  if (config_->enabled_br) {
    vel += ticks_to_mm(data.back_right, config_->diam_br, rotary_resolution_);
    vel_ticks += data.back_right;
    count += 1.0f;
  }
  if (count > 0.0f) {
    vel /= count;
    vel_ticks /= count;
  }
  return AvgVelocityData(vel_ticks, vel, data.msec);
}

AvgVelocityData WheelEncoder::get_next_avg_vel(uint64_t timestamp_ms) const {
  auto data = get_next_vel(timestamp_ms);
  return _avg_vel(data);
}
AvgVelocityData WheelEncoder::get_cur_avg_vel() const {
  auto data = get_cur_vel();
  return _avg_vel(data);
}

float WheelEncoder::_max_dist(const EncoderData &delta_ticks_val) const {
  std::vector<float> dists{
      config_->enabled_fl ? ticks_to_mm(delta_ticks_val.front_left, config_->diam_fl, rotary_resolution_) : 0.0f,
      config_->enabled_fr ? ticks_to_mm(delta_ticks_val.front_right, config_->diam_fr, rotary_resolution_) : 0.0f,
      config_->enabled_bl ? ticks_to_mm(delta_ticks_val.back_left, config_->diam_bl, rotary_resolution_) : 0.0f,
      config_->enabled_br ? ticks_to_mm(delta_ticks_val.back_right, config_->diam_br, rotary_resolution_) : 0.0f,
  };
  auto it = std::max_element(dists.begin(), dists.end());
  if (it == dists.end()) {
    return 0.0f;
  }
  return *it;
}

float WheelEncoder::_avg_dist(const EncoderData &delta_ticks_val) const {
  float dist = 0.0f;
  float count = 0.0f;
  if (config_->use_max_value) {
    return _max_dist(delta_ticks_val);
  }
  if (config_->enabled_fl) {
    dist += ticks_to_mm(delta_ticks_val.front_left, config_->diam_fl, rotary_resolution_);
    count += 1.0f;
  }
  if (config_->enabled_fr) {
    dist += ticks_to_mm(delta_ticks_val.front_right, config_->diam_fr, rotary_resolution_);
    count += 1.0f;
  }
  if (config_->enabled_bl) {
    dist += ticks_to_mm(delta_ticks_val.back_left, config_->diam_bl, rotary_resolution_);
    count += 1.0f;
  }
  if (config_->enabled_br) {
    dist += ticks_to_mm(delta_ticks_val.back_right, config_->diam_br, rotary_resolution_);
    count += 1.0f;
  }
  if (count > 0.0f) {
    dist /= count;
  }
  return dist;
}
float WheelEncoder::avg_dist(const EncoderData &pos1, const EncoderData &pos2) {
  auto delta_pos = delta_ticks(pos1, pos2);
  return _avg_dist(delta_pos);
}

float WheelEncoder::avg_dist(EncoderTuple ticks1, EncoderTuple ticks2) {
  EncoderData pos1(std::get<0>(ticks1), std::get<1>(ticks1), std::get<2>(ticks1), std::get<3>(ticks1));
  EncoderData pos2(std::get<0>(ticks2), std::get<1>(ticks2), std::get<2>(ticks2), std::get<3>(ticks2));
  return avg_dist(pos1, pos2);
}
std::tuple<uint64_t, double> WheelEncoder::get_next_pos(uint64_t timestamp_ms) const {
  std::shared_lock lock(mut_);
  while ((!initialized_ || timestamp_ms >= cur_data_.msec()) && !stopped_) {
    cv_.wait(lock);
  }
  if (!initialized_) {
    return std::make_tuple<uint64_t, double>(0, 0.0);
  }
  double tmp = total_travel_m_;
  return std::make_tuple<uint64_t, double>(cur_data_.msec(), std::forward<double>(tmp));
}
std::tuple<uint64_t, double> WheelEncoder::get_cur_pos() const {
  std::shared_lock lock(mut_);
  double tmp = total_travel_m_;
  return std::make_tuple<uint64_t, double>(cur_data_.msec(), std::forward<double>(tmp));
}

void WheelEncoder::reload() {
  if (!reload_required_) {
    return;
  }
  reload_required_ = false;
  std::unique_ptr<Config> tmp = std::make_unique<Config>(config::get_global_config_subscriber());
  config_ = std::move(tmp);
}

/**
 * @brief Get absolute change in encoder ticks between two samples
 *
 * This implements logic (for Slayer) that reverses the right-hand encoders, as they will tick in
 * reverse compared to the left encoders. For Reaper, encoders on both sides of the machine tick
 * in the same direction.
 */
EncoderData WheelEncoder::delta_ticks(const EncoderData &pos1, const EncoderData &pos2) const {
  int32_t multiplier(reverse_polarity_ ? -1 : 1);

  int32_t delta_fl = pos2.front_left - pos1.front_left;
  int32_t delta_bl = pos2.back_left - pos1.back_left;

  int32_t delta_fr = pos2.front_right - pos1.front_right;
  int32_t delta_br = pos2.back_right - pos1.back_right;

  if (common::is_slayer()) {
    delta_fr *= -1;
    delta_br *= -1;
  }

  return EncoderData(multiplier * delta_fl, multiplier * delta_fr, multiplier * delta_bl, multiplier * delta_br,
                     (pos2.usec + pos1.usec) / 2);
}
DeltaPosData WheelEncoder::delta_pos(const EncoderData &pos1, const EncoderData &pos2) const {
  auto delta_pos = delta_ticks(pos1, pos2);
  return DeltaPosData(ticks_to_mm(delta_pos.front_left, config_->diam_fl, rotary_resolution_),
                      ticks_to_mm(delta_pos.front_right, config_->diam_fr, rotary_resolution_),
                      ticks_to_mm(delta_pos.back_left, config_->diam_bl, rotary_resolution_),
                      ticks_to_mm(delta_pos.back_right, config_->diam_br, rotary_resolution_));
}
EnabledEncoders WheelEncoder::enabled_encoders() const {
  return EnabledEncoders(config_->enabled_fl, config_->enabled_fr, config_->enabled_bl, config_->enabled_br);
}
EnabledEncoders WheelEncoder::cfg_enabled_encoders() const {
  auto cfg = config::get_global_config_subscriber();
  if (!cfg || !cfg->started()) {
    return EnabledEncoders();
  }
  auto we_tree = cfg->get_config_node("common", "wheel_encoders");
  return get_cfg_enabled_encoders(we_tree);
}
void WheelEncoder::override_enabled_encoders(const EnabledEncoders &enabled) {
  config_->enabled_fl = enabled.front_left;
  config_->enabled_fr = enabled.front_right;
  config_->enabled_bl = enabled.back_left;
  config_->enabled_br = enabled.back_right;
}

WheelEncoderCB::WheelEncoderCB(Callback cb, bool force_treadkill, bool reverse_polarity, uint32_t rotary_resolution,
                               uint32_t poll_interval_ms)
    : WheelEncoder(reverse_polarity, rotary_resolution, poll_interval_ms), cb_(cb) {
  if (force_treadkill) {
    if (common::is_reaper()) {
      config_->enabled_fl = true;
      config_->enabled_fr = false;
      config_->enabled_bl = false;
      config_->enabled_br = false;
      config_->diam_fl = TREADKILL_WHEEL_DIAM_MM;
    } else {
      config_->enabled_fl = false;
      config_->enabled_fr = false;
      config_->enabled_bl = true;
      config_->enabled_br = false;
      config_->diam_bl = TREADKILL_WHEEL_DIAM_MM;
    }
  }
  ready_ = true;
}
void WheelEncoderCB::_poll_loop() {
  while (!stopped_) {
    reload();
    auto data = cb_();
    update(EncoderData(std::get<1>(data), std::get<2>(data), std::get<3>(data), std::get<4>(data), std::get<0>(data)));
    std::this_thread::sleep_for(std::chrono::milliseconds(poll_inverval_ms_));
  }
}
} // namespace wheel_encoder
} // namespace carbon
