local sched = require 'sched'
local dt = require 'devicetree'
local json = require 'json'
local socket = require 'socket'
local system = require 'system'

local function handle_getParam(client, request)
  if request == nil then
    client:send(json.encode({error="No Request 'data' Provided"}))
    return
  end
  
  if request["path"] == nil then
    client:send(json.encode({error="'path' parameter not set in request"}))
    return
  end
  
  if type(request["path"]) ~= "string" then
    client:send(json.encode({error="'path' parameter is not a string"}))
    return
  end
  
  local result, extra = dt.get(request["path"])
  
  if result == nil then
    if type(extra) == "string" then
      client:send(json.encode({error=extra}))
    else
      client:send(json.encode({subtree=extra}))
    end
  else
    client:send(json.encode({value=result}))
  end 
end

local function handle_reboot(client, request)
  system.reboot("Good Bye, World!")  
end

local function handle_version(client, request)
  client:send(json.encode({version="0.1"}))
end

local function handle_request(client, request)
  if request["req"] == nil then
    client:send(json.encode({error="No Request Type Provided"}))
    return
  end
  
  local req = request["req"]
  if req == "getParam" then
    handle_getParam(client, request["data"])
  elseif req == "reboot" then
    handle_reboot(client, request["data"])
  elseif req == "version" then
    handle_version(client, request["data"])
  else
    client:send(json.encode({error="Unknown Request"}))
  end
end

local function handle_connection(client)
  local payload = client:receive('*l')
  print(payload)
  local status, data = pcall(json.decode, payload)
  if status then
    handle_request(client, data)
  else
    if type(data) == "string" then
      client:send(json.encode({error=data}))
  	else
  	  client:send(json.encode({error="Failed to JSON decode data"}))
  	end
  end
end

local function run_tcp_server()
  local tcp = socket.bind("0.0.0.0", 4243)
  while true do
	  local client, err = tcp:accept()
	  if client then
	    sched.run(handle_connection, client)
	  else
	    print(err)
	  end
  end
end

local function setup()
  dt.init()
  system.init()
end

sched.run(setup)
sched.run(run_tcp_server)
sched.loop()

