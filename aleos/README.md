# Carbon ALEOS Development

## To Setup Lua - Developer Studio For ALEOS

1. Download from here: https://source.sierrawireless.com/resources/airlink/aleos_af/aleos_af_studio/
2. Create workspace in this "aleos" directory in the root of the robot repo
3. Import existing project, (if using parallel go in This PC and find the extra hard drives mounted, one of them is the Mac's file system) and import Carbon Modem
4. Hit Run you will be prompted to create a New Connection "Host name" Should be the IP of the LTE modem
5. Userid: uasuser, password: worldwarweeds (Assuming it has been setup, to set it up navigate to the IP at port 9443 in a browser in the section admin enable the aaf user and set the password)
6. You should now be able to develop the scripts


Note: Useful Tutorial: https://source.sierrawireless.com/resources/airlink/aleos_af/tutorial_aaf_getting_started/