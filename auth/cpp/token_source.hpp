#pragma once

#include <atomic>
#include <httplib.h>

namespace httplib {
class Client;
} // namespace httplib

namespace carbon::auth {
class TokenSource {
public:
  static std::shared_ptr<TokenSource> get();
  static std::string get_token_proxy_ip();
  std::string get_formatted_bearer_token();

private:
  std::unique_ptr<httplib::Client> http_client_;

  std::atomic<std::chrono::system_clock::time_point> expiration_time_;
  std::string access_token_;

  TokenSource();
  void refresh_token();
};
} // namespace carbon::auth
