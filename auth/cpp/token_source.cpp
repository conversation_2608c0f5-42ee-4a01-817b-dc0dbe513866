#include "auth/cpp/token_source.hpp"
#include "lib/common/cpp/utils/environment.hpp"
#include <httplib.h>
#include <lib/common/cpp/utils/generation.hpp>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace carbon::auth {

TokenSource::TokenSource() : expiration_time_(std::chrono::system_clock::time_point::min()) {
  std::string ip = TokenSource::get_token_proxy_ip();
  http_client_ = std::make_unique<httplib::Client>(fmt::format("{}:{}", ip, "61900"));
  refresh_token();
}

std::string TokenSource::get_token_proxy_ip() {
  // rtc is a special case where token proxy is running on broadcaster on the
  // same computer as the config service
  if (common::is_rtc()) {
    return "localhost";
  }

  return common::get_command_ip();
}

std::shared_ptr<TokenSource> TokenSource::get() {
  static std::shared_ptr<TokenSource> token_source{new TokenSource()};
  return token_source;
}

std::string TokenSource::get_formatted_bearer_token() {
  if (std::chrono::system_clock::now() >= expiration_time_.load()) {
    refresh_token();
  }
  return "Bearer " + access_token_;
}

/**
 * Requests an access token from the token proxy on commander. Client ID, secret, and scopes
 * are all populated from configuration by commander.
 *
 * POST /oauth/token HTTP/1.1
 * Host: http://127.0.0.1:61900/oauth/token
 * Content-Type: application/x-www-form-urlencoded
 *
 * Response:
 * {
 *   "access_token": "eyJz93a...k4laUWw",
 *   "token_type": "Bearer",
 *   "expires_in": 86400
 *   "scope": "read:users write:users"
 * }
 **/
void TokenSource::refresh_token() {
  spdlog::debug("Fetching Auth Token from Token Proxy...");

  auto timeNow = std::chrono::system_clock::now();
  auto res = http_client_->Post("/oauth/token");
  if (!res) {
    spdlog::warn("Token Proxy returned an invalid response");
    return;
  } else if (res->status != 200) {
    spdlog::warn("Token Proxy returned status {}", res->status);
    return;
  }

  auto jsonResponse = nlohmann::json::parse(res->body);
  if (!(jsonResponse.contains("access_token")) || !(jsonResponse.contains("expires_in"))) {
    throw std::runtime_error("Access token or expires_in not found in response");
  }

  access_token_ = jsonResponse["access_token"];
  expiration_time_.store(timeNow + std::chrono::seconds(jsonResponse["expires_in"]));
}
} // namespace carbon::auth
