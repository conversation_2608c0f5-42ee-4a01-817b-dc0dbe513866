#include <pybind11/functional.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>

#include "atomic_data_share/cpp/atomic_data_share.hpp"

namespace py = pybind11;

namespace carbon::atomic_data_share {
template <typename T>
void declare_data_share(py::module &m, const std::string &typestr) {
  using Class = AtomicDataWrapper<T>;
  std::string pyclass_name = std::string("AtomicDataWrapper") + typestr;
  py::class_<Class, std::shared_ptr<Class>>(m, pyclass_name.c_str())
      .def(py::init<const T &>(), py::call_guard<py::gil_scoped_release>())
      .def("set", &Class::set, py::arg("val"), py::call_guard<py::gil_scoped_release>())
      .def("get", &Class::get, py::call_guard<py::gil_scoped_release>());
}

PYBIND11_MODULE(atomic_data_share_python, m) {
  declare_data_share<float>(m, "Float");
  declare_data_share<int>(m, "Int");
}

} // namespace carbon::atomic_data_share