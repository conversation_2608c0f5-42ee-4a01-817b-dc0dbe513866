add_compile_options(-fvisibility=default)

#file(GLOB SOURCES CONFIGURE_DEPENDS cpp/*.cpp cpp/*.c cpp/*.h cpp/*.hpp)
#
#add_library(atomic_data_share SHARED ${SOURCES})
#target_compile_definitions(atomic_data_share PRIVATE -DSPDLOG_FMT_EXTERNAL=1)
#target_link_libraries(atomic_data_share PUBLIC m stdc++fs)

file(GLOB SOURCES_PYBIND CONFIGURE_DEPENDS pybind/*.cpp)
pybind11_add_module(atomic_data_share_python SHARED ${SOURCES_PYBIND})
target_compile_definitions(atomic_data_share_python  PRIVATE -DSPDLOG_FMT_EXTERNAL=1 PYBIND)
#target_link_libraries(atomic_data_share_python PUBLIC atomic_data_share)
set_target_properties(atomic_data_share_python PROPERTIES PREFIX "" SUFFIX ".so" LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_LIST_DIR}/pybind)